import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب توزيعات القضايا
export async function GET() {
  try {
    // جلب توزيعات القضايا من قاعدة البيانات
    const result = await query(`
      SELECT
        cd.id,
        cd.issue_id,
        i.title as issue_title,
        i.case_number,
        i.amount as case_amount,
        cd.admin_amount,
        cd.remaining_amount,
        cd.created_date
      FROM case_distribution cd
      JOIN issues i ON cd.issue_id = i.id
      ORDER BY cd.created_date DESC
    `)

    // جلب تفاصيل الخدمات لكل توزيع
    const distributions = []
    for (const dist of result.rows) {
      const servicesResult = await query(`
        SELECT
          sd.service_id,
          s.name as service_name,
          sd.percentage,
          sd.amount,
          sd.lawyer_id,
          e.name as lawyer_name
        FROM service_distributions sd
        JOIN services s ON sd.service_id = s.id
        LEFT JOIN employees e ON sd.lawyer_id = e.id
        WHERE sd.case_distribution_id = $1
        ORDER BY sd.service_id
      `, [dist.id])

      distributions.push({
        ...dist,
        case_amount: parseFloat(dist.case_amount || 0),
        admin_amount: parseFloat(dist.admin_amount || 0),
        remaining_amount: parseFloat(dist.remaining_amount || 0),
        lineage_id: null,
        lineage_name: 'غير محدد',
        admin_percentage: 0,
        commission_percentage: 0,
        service_distributions: servicesResult.rows.map(service => ({
          ...service,
          percentage: parseFloat(service.percentage || 0),
          amount: parseFloat(service.amount || 0)
        }))
      })
    }

    return NextResponse.json({
      success: true,
      data: distributions
    })
  } catch (error) {
    console.error('Error fetching case distributions:', error)
    console.error('Error details:', error.message)
    return NextResponse.json(
      { success: false, error: `فشل في جلب بيانات توزيع القضايا: ${error.message}` },
      { status: 500 }
    )
  }
}

// POST - إضافة توزيع قضية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      issue_id,
      lineage_id,
      service_distributions
    } = body

    if (!issue_id || !lineage_id || !service_distributions) {
      return NextResponse.json(
        { success: false, error: 'القضية ومجموعة النسب وتوزيع الخدمات مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع نسب الخدمات لا يزيد عن 100%
    const totalPercentage = service_distributions.reduce((sum: number, dist: any) => sum + (dist.percentage || 0), 0)
    if (totalPercentage > 100) {
      return NextResponse.json(
        { success: false, error: `مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${totalPercentage}%` },
        { status: 400 }
      )
    }

    // جلب بيانات القضية
    const issueResult = await query(`
      SELECT amount FROM issues WHERE id = $1
    `, [issue_id])

    if (issueResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية المحددة غير موجودة' },
        { status: 404 }
      )
    }

    const caseAmount = parseFloat(issueResult.rows[0].amount || 0)

    // جلب بيانات النسب المالية
    const lineageResult = await query(`
      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1
    `, [lineage_id])

    if (lineageResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المحددة غير موجودة' },
        { status: 404 }
      )
    }

    const lineage = lineageResult.rows[0]
    const adminPercentage = parseFloat(lineage.admin_percentage || 0)
    const adminAmount = (caseAmount * adminPercentage) / 100
    const remainingAmount = caseAmount - adminAmount

    // حساب المبالغ للخدمات
    const totalServicePercentage = service_distributions.reduce((sum: number, dist: any) => sum + (dist.percentage || 0), 0)
    const serviceDistributionsWithAmounts = service_distributions.map((dist: any) => ({
      ...dist,
      amount: (remainingAmount * (dist.percentage || 0)) / 100
    }))

    // إدراج توزيع القضية
    const distributionResult = await query(`
      INSERT INTO case_distribution (issue_id, lineage_id, admin_amount, remaining_amount)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `, [issue_id, lineage_id, adminAmount, remainingAmount])

    const distributionId = distributionResult.rows[0].id

    // إدراج تفاصيل توزيع الخدمات
    for (const serviceDist of serviceDistributionsWithAmounts) {
      await query(`
        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)
        VALUES ($1, $2, $3, $4, $5)
      `, [distributionId, serviceDist.service_id, serviceDist.percentage, serviceDist.amount, serviceDist.lawyer_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة توزيع القضية بنجاح',
      data: {
        id: distributionId,
        issue_id,
        lineage_id,
        admin_amount: adminAmount,
        remaining_amount: remainingAmount,
        service_distributions: serviceDistributionsWithAmounts
      }
    })
  } catch (error) {
    console.error('Error creating case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة توزيع القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث توزيع قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      issue_id,
      lineage_id,
      service_distributions
    } = body

    if (!id || !issue_id || !lineage_id || !service_distributions) {
      return NextResponse.json(
        { success: false, error: 'المعرف والقضية ومجموعة النسب وتوزيع الخدمات مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن مجموع نسب الخدمات لا يزيد عن 100%
    const totalPercentage = service_distributions.reduce((sum: number, dist: any) => sum + (dist.percentage || 0), 0)
    if (totalPercentage > 100) {
      return NextResponse.json(
        { success: false, error: `مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${totalPercentage}%` },
        { status: 400 }
      )
    }

    // التحقق من وجود التوزيع
    const existingDistribution = await query(`
      SELECT * FROM case_distribution WHERE id = $1
    `, [id])

    if (existingDistribution.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'التوزيع المحدد غير موجود' },
        { status: 404 }
      )
    }

    // جلب بيانات القضية
    const issueResult = await query(`
      SELECT amount FROM issues WHERE id = $1
    `, [issue_id])

    if (issueResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية المحددة غير موجودة' },
        { status: 404 }
      )
    }

    const caseAmount = parseFloat(issueResult.rows[0].amount || 0)

    // جلب بيانات النسب المالية
    const lineageResult = await query(`
      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1
    `, [lineage_id])

    if (lineageResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المحددة غير موجودة' },
        { status: 404 }
      )
    }

    const lineage = lineageResult.rows[0]
    const adminPercentage = parseFloat(lineage.admin_percentage || 0)
    const adminAmount = (caseAmount * adminPercentage) / 100
    const remainingAmount = caseAmount - adminAmount

    // حساب المبالغ للخدمات
    const serviceDistributionsWithAmounts = service_distributions.map((dist: any) => ({
      ...dist,
      amount: (remainingAmount * (dist.percentage || 0)) / 100
    }))

    // تحديث توزيع القضية
    await query(`
      UPDATE case_distribution 
      SET issue_id = $1, lineage_id = $2, admin_amount = $3, remaining_amount = $4
      WHERE id = $5
    `, [issue_id, lineage_id, adminAmount, remainingAmount, id])

    // حذف تفاصيل الخدمات القديمة
    await query(`
      DELETE FROM service_distributions WHERE case_distribution_id = $1
    `, [id])

    // إدراج تفاصيل توزيع الخدمات الجديدة
    for (const serviceDist of serviceDistributionsWithAmounts) {
      await query(`
        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)
        VALUES ($1, $2, $3, $4, $5)
      `, [id, serviceDist.service_id, serviceDist.percentage, serviceDist.amount, serviceDist.lawyer_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث توزيع القضية بنجاح'
    })
  } catch (error) {
    console.error('Error updating case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث توزيع القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف توزيع قضية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف توزيع القضية مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود التوزيع
    const existingDistribution = await query(`
      SELECT * FROM case_distribution WHERE id = $1
    `, [id])

    if (existingDistribution.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'التوزيع المحدد غير موجود' },
        { status: 404 }
      )
    }

    // حذف تفاصيل توزيع الخدمات أولاً (CASCADE سيتولى هذا تلقائياً)
    await query(`
      DELETE FROM case_distribution WHERE id = $1
    `, [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف توزيع القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting case distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف توزيع القضية' },
      { status: 500 }
    )
  }
}
