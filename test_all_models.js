// اختبار جميع النماذج المتاحة
require('dotenv').config({ path: '.env.local' });

async function testAllModels() {
  console.log('🔄 اختبار جميع النماذج المتاحة...\n');
  
  const models = [
    {
      name: 'Llama 3.1 8B (Groq)',
      key: 'GROQ_API_KEY',
      type: 'groq',
      free: true,
      endpoint: 'https://api.groq.com/openai/v1/chat/completions',
      model: 'llama-3.1-8b-instant'
    },
    {
      name: 'Qwen 2.5 (Hugging Face)',
      key: 'HUGGINGFACE_API_KEY',
      type: 'huggingface',
      free: true,
      endpoint: 'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-72B-Instruct',
      model: 'Qwen/Qwen2.5-72B-Instruct'
    },
    {
      name: 'GPT-4o (OpenAI)',
      key: 'OPENAI_API_KEY',
      type: 'openai',
      free: false,
      endpoint: 'https://api.openai.com/v1/chat/completions',
      model: 'gpt-4o'
    }
  ];

  let workingModels = [];
  let failedModels = [];

  for (const model of models) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`🧪 اختبار: ${model.name} ${model.free ? '(مجاني)' : '(مدفوع)'}`);
    console.log(`${'='.repeat(50)}`);
    
    const apiKey = process.env[model.key];
    
    if (!apiKey || apiKey.includes('your_') || apiKey.includes('_here')) {
      console.log(`❌ ${model.key} غير متوفر`);
      console.log(`📝 يرجى إضافة المفتاح إلى ملف .env.local`);
      failedModels.push({ ...model, reason: 'API Key مفقود' });
      continue;
    }
    
    console.log(`✅ ${model.key} موجود`);
    console.log(`🔑 المفتاح يبدأ بـ: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}`);
    
    try {
      let response;
      
      if (model.type === 'groq' || model.type === 'openai') {
        // اختبار نماذج OpenAI-compatible
        response = await fetch(model.endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: model.model,
            messages: [
              {
                role: 'system',
                content: 'أنت مساعد قانوني ذكي. أجب باللغة العربية.'
              },
              {
                role: 'user',
                content: 'مرحباً، هل يمكنك مساعدتي في استشارة قانونية بسيطة؟'
              }
            ],
            max_tokens: 100,
            temperature: 0.7
          }),
          signal: AbortSignal.timeout(30000)
        });
        
      } else if (model.type === 'huggingface') {
        // اختبار Hugging Face
        response = await fetch(model.endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            inputs: "مرحباً، هل يمكنك مساعدتي في استشارة قانونية؟",
            parameters: {
              max_new_tokens: 100,
              temperature: 0.7,
              return_full_text: false
            }
          }),
          signal: AbortSignal.timeout(30000)
        });
      }
      
      console.log(`📡 رمز الاستجابة: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        let aiMessage = '';
        
        if (model.type === 'groq' || model.type === 'openai') {
          aiMessage = data.choices?.[0]?.message?.content || 'لا يوجد رد';
        } else if (model.type === 'huggingface') {
          aiMessage = data[0]?.generated_text || data.generated_text || 'لا يوجد رد';
        }
        
        console.log('✅ نجح الاختبار!');
        console.log('🤖 رد النموذج:');
        console.log(`   "${aiMessage.substring(0, 150)}${aiMessage.length > 150 ? '...' : ''}"`);
        
        workingModels.push({
          ...model,
          status: 'يعمل',
          response: aiMessage.substring(0, 100)
        });
        
      } else {
        const errorData = await response.text();
        console.log('❌ فشل الاختبار');
        console.log(`📝 رسالة الخطأ: ${errorData.substring(0, 200)}`);
        
        let reason = 'خطأ غير معروف';
        if (response.status === 401) {
          reason = 'مفتاح API غير صحيح';
        } else if (response.status === 403) {
          reason = 'ليس لديك صلاحية للوصول';
        } else if (response.status === 429) {
          reason = 'تجاوز حد الاستخدام';
        } else if (response.status === 503) {
          reason = 'النموذج مشغول أو قيد التحميل';
        }
        
        failedModels.push({ ...model, reason, status: response.status });
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
      failedModels.push({ ...model, reason: 'خطأ في الشبكة', error: error.message });
    }
  }

  // ملخص النتائج
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 ملخص نتائج الاختبار');
  console.log(`${'='.repeat(60)}`);
  
  console.log(`\n✅ النماذج التي تعمل (${workingModels.length}):`);
  if (workingModels.length === 0) {
    console.log('   لا توجد نماذج تعمل حالياً');
  } else {
    workingModels.forEach(model => {
      console.log(`   • ${model.name} ${model.free ? '🆓' : '💰'}`);
    });
  }
  
  console.log(`\n❌ النماذج التي لا تعمل (${failedModels.length}):`);
  if (failedModels.length === 0) {
    console.log('   جميع النماذج تعمل بشكل مثالي!');
  } else {
    failedModels.forEach(model => {
      console.log(`   • ${model.name}: ${model.reason}`);
    });
  }
  
  // توصيات
  console.log(`\n💡 التوصيات:`);
  
  const freeWorkingModels = workingModels.filter(m => m.free);
  const paidWorkingModels = workingModels.filter(m => !m.free);
  
  if (freeWorkingModels.length > 0) {
    console.log(`✅ لديك ${freeWorkingModels.length} نموذج مجاني يعمل:`);
    freeWorkingModels.forEach(model => {
      console.log(`   🆓 ${model.name} - موصى به للاستخدام اليومي`);
    });
  }
  
  if (paidWorkingModels.length > 0) {
    console.log(`💰 لديك ${paidWorkingModels.length} نموذج مدفوع يعمل:`);
    paidWorkingModels.forEach(model => {
      console.log(`   💎 ${model.name} - للحالات المعقدة`);
    });
  }
  
  if (workingModels.length === 0) {
    console.log('⚠️ لا توجد نماذج تعمل حالياً');
    console.log('📝 يرجى:');
    console.log('   1. إضافة مفاتيح API صحيحة');
    console.log('   2. التحقق من اتصال الإنترنت');
    console.log('   3. التأكد من وجود رصيد (للنماذج المدفوعة)');
  } else {
    console.log(`🎉 لديك ${workingModels.length} نموذج يعمل - النظام جاهز!`);
  }
  
  console.log(`\n${'='.repeat(60)}`);
}

// تشغيل الاختبار
testAllModels().catch(console.error);
