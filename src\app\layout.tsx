import type { Metadata } from "next";
import "./globals.css";
import "../styles/professional-theme.css";
import "../styles/tafahum-theme.css";

export const metadata: Metadata = {
  title: "نظام الإدارة القانونية",
  description: "نظام شامل لإدارة المكاتب القانونية والقضايا",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body>
        {children}
      </body>
    </html>
  );
}
