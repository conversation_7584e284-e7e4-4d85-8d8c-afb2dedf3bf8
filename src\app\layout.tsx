import type { Metadata } from "next";
import "./globals.css";
import "../styles/professional-theme.css";
import "../styles/tafahum-theme.css";

export const metadata: Metadata = {
  title: "نظام الإدارة القانونية",
  description: "نظام شامل لإدارة المكاتب القانونية والقضايا",
  charset: 'UTF-8',
  viewport: 'width=device-width, initial-scale=1',
  other: {
    'http-equiv': 'Content-Type',
    content: 'text/html; charset=UTF-8'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <meta charSet="UTF-8" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
