#!/usr/bin/env node

const { Client } = require('pg')

async function setupProductionDatabase() {
  console.log('🚀 إعداد قاعدة البيانات للإنتاج...')
  
  // قراءة إعدادات قاعدة البيانات من متغيرات البيئة
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'legal_system_production',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD
  }
  
  if (!dbConfig.password) {
    console.error('❌ كلمة مرور قاعدة البيانات مطلوبة في متغير DB_PASSWORD')
    process.exit(1)
  }
  
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    console.log('✅ تم الاتصال بقاعدة البيانات')
    
    // إنشاء الجداول الأساسية
    console.log('📋 إنشاء الجداول الأساسية...')
    
    // جدول الشركات
    await client.query(`
      CREATE TABLE IF NOT EXISTS companies (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        legal_name VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        phone VARCHAR(50),
        email VARCHAR(100),
        website VARCHAR(255),
        logo_url TEXT,
        logo_image_url TEXT,
        established_date DATE,
        registration_number VARCHAR(100),
        legal_form VARCHAR(100),
        capital NUMERIC DEFAULT 0,
        tax_number VARCHAR(100),
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        working_hours TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    // جدول المستخدمين
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255),
        name VARCHAR(100),
        position VARCHAR(100),
        role VARCHAR(50) DEFAULT 'user',
        user_type VARCHAR(20) DEFAULT 'user',
        permissions TEXT[] DEFAULT '{}',
        status VARCHAR(20) DEFAULT 'active',
        is_active BOOLEAN DEFAULT true,
        is_online BOOLEAN DEFAULT false,
        last_login TIMESTAMP,
        login_attempts INTEGER DEFAULT 0,
        employee_id INTEGER,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    // جدول الصلاحيات
    await client.query(`
      CREATE TABLE IF NOT EXISTS permissions (
        id SERIAL PRIMARY KEY,
        permission_key VARCHAR(100) UNIQUE NOT NULL,
        permission_name VARCHAR(200) NOT NULL,
        category VARCHAR(100) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    // جدول صلاحيات المستخدمين
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_permissions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        permission_key VARCHAR(100) NOT NULL,
        granted_by INTEGER REFERENCES users(id),
        granted_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        UNIQUE(user_id, permission_key)
      )
    `)
    
    // جدول الأدوار
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id SERIAL PRIMARY KEY,
        role_name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions TEXT[] DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    console.log('✅ تم إنشاء الجداول الأساسية')
    
    // إدراج البيانات الأساسية
    await insertBasicData(client)
    
    console.log('🎯 تم إعداد قاعدة البيانات للإنتاج بنجاح!')
    
  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

async function insertBasicData(client) {
  console.log('📊 إدراج البيانات الأساسية...')
  
  // إدراج دور المدير
  await client.query(`
    INSERT INTO user_roles (role_name, display_name, description, permissions)
    VALUES ('admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام', ARRAY['system_admin'])
    ON CONFLICT (role_name) DO NOTHING
  `)
  
  // إدراج الصلاحيات الأساسية
  const basicPermissions = [
    ['system_admin', 'مدير النظام الكامل', 'صلاحيات خاصة'],
    ['users_view', 'عرض المستخدمين', 'إدارة النظام'],
    ['users_add', 'إضافة مستخدم جديد', 'إدارة النظام'],
    ['users_edit', 'تعديل بيانات المستخدمين', 'إدارة النظام'],
    ['users_delete', 'حذف المستخدمين', 'إدارة النظام'],
    ['users_permissions', 'إدارة صلاحيات المستخدمين', 'إدارة النظام']
  ]
  
  for (const [key, name, category] of basicPermissions) {
    await client.query(`
      INSERT INTO permissions (permission_key, permission_name, category)
      VALUES ($1, $2, $3)
      ON CONFLICT (permission_key) DO NOTHING
    `, [key, name, category])
  }
  
  console.log('✅ تم إدراج البيانات الأساسية')
}

// تشغيل السكريبت
setupProductionDatabase()