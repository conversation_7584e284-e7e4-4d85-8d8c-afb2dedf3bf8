"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Eye,EyeOff,Lock,LogIn,Smartphone,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, login } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('user');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        deviceId: ''\n    });\n    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للصفحة المناسبة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (user) {\n                if (user.type === 'client') {\n                    router.push('/client-portal');\n                } else {\n                    router.push('/dashboard');\n                }\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const endpoint = loginType === 'user' ? '/api/auth/users' : '/api/client-portal/auth/simple';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    username: formData.username.trim(),\n                    password: formData.password.trim(),\n                    deviceId: loginType === 'user' ? formData.deviceId || 'default-device' : undefined\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                if (loginType === 'user') {\n                    // استخدام hook المصادقة لحفظ بيانات المستخدم\n                    const userData = {\n                        id: result.user.id,\n                        username: result.user.username,\n                        type: loginType,\n                        name: result.user.name,\n                        role: result.user.role,\n                        role_display_name: result.user.role_display_name,\n                        permissions: result.user.permissions,\n                        user_type: result.user.user_type,\n                        token: result.token\n                    };\n                    login(userData);\n                } else {\n                    // استخدام hook المصادقة لحفظ بيانات العميل\n                    const clientData = {\n                        id: result.data.client.client_id,\n                        username: result.data.client.username,\n                        type: loginType,\n                        name: result.data.client.client_name,\n                        email: result.data.client.email,\n                        token: result.data.token,\n                        sessionToken: result.data.sessionToken\n                    };\n                    localStorage.setItem('clientToken', result.data.token);\n                    login(clientData);\n                }\n            } else {\n                alert(result.error || 'فشل في تسجيل الدخول');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            alert('حدث خطأ في الاتصال');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // توليد معرف جهاز عشوائي\n    const generateDeviceId = ()=>{\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n        let result = 'gtx';\n        for(let i = 0; i < 6; i++){\n            result += Math.floor(Math.random() * 10);\n        }\n        result += '_';\n        for(let i = 0; i < 17; i++){\n            result += Math.floor(Math.random() * 10);\n        }\n        setFormData({\n            ...formData,\n            deviceId: result\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"مرحباً بك في نظام الإدارة المتخصص\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"قم بتسجيل الدخول للمتابعة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"shadow-xl border-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex rounded-lg bg-gray-100 p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: loginType === 'user' ? 'default' : 'ghost',\n                                        className: \"flex-1 \".concat(loginType === 'user' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-900'),\n                                        onClick: ()=>setLoginType('user'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"دخول مستخدم\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: loginType === 'client' ? 'default' : 'ghost',\n                                        className: \"flex-1 \".concat(loginType === 'client' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-900'),\n                                        onClick: ()=>setLoginType('client'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"دخول عميل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"username\",\n                                                    className: \"text-right block\",\n                                                    children: \"اسم المستخدم *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"username\",\n                                                            type: \"text\",\n                                                            value: formData.username,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            className: \"pl-10 text-right\",\n                                                            placeholder: \"أدخل اسم المستخدم\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-right block\",\n                                                    children: \"كلمة المرور *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? 'text' : 'password',\n                                                            value: formData.password,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    password: e.target.value\n                                                                }),\n                                                            className: \"pl-10 pr-10 text-right\",\n                                                            placeholder: \"••••••••\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        loginType === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"deviceId\",\n                                                    className: \"text-right block\",\n                                                    children: \"معرف الجهاز (اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"deviceId\",\n                                                            type: \"text\",\n                                                            value: formData.deviceId,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    deviceId: e.target.value\n                                                                }),\n                                                            className: \"pl-10 text-right font-mono text-sm\",\n                                                            placeholder: \"اتركه فارغاً أو اضغط لتوليد معرف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: generateDeviceId,\n                                                    className: \"w-full text-sm\",\n                                                    children: \"توليد معرف جهاز جديد\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 text-lg font-semibold\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"جاري تسجيل الدخول...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Eye_EyeOff_Lock_LogIn_Smartphone_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    loginType === 'user' ? 'تسجيل دخول المستخدم' : 'تسجيل دخول العميل'\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: loginType === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"للمستخدمين: يتطلب اسم المستخدم وكلمة المرور (معرف الجهاز اختياري)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"للعملاء: يتطلب اسم المستخدم وكلمة المرور فقط\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"هل تحتاج مساعدة؟\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-700 space-y-1 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"تواصل مع الإدارة للحصول على بيانات الدخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"أو لإعادة تعيين كلمة المرور\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        if (loginType === 'user') {\n                                                            setFormData({\n                                                                ...formData,\n                                                                username: 'admin',\n                                                                password: 'admin123'\n                                                            });\n                                                        } else {\n                                                            setFormData({\n                                                                ...formData,\n                                                                username: 'demo_client',\n                                                                password: 'password123'\n                                                            });\n                                                        }\n                                                    },\n                                                    className: \"text-xs\",\n                                                    children: \"ملء البيانات التجريبية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"\\xa9 2024 نظام الإدارة القانونية المتخصص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"noe343cjmbZ13EFcd4FTwiHc1oc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

});