(()=>{var e={};e.id=5055,e.ids=[5055],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>l});var n=s(64939),i=s(29021),o=s.n(i),a=s(33873),c=s.n(a),u=e([n]);n=(u.then?(await u)():u)[0];let p=null;try{let e=c().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],s=p.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new n.Pool(d);async function l(e,t){let s=await E.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54676:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>E,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var n=s(96559),i=s(48088),o=s(37719),a=s(55797),c=e([a]);a=(c.then?(await c)():c)[0];let l=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/client-portal/notifications/route",pathname:"/api/client-portal/notifications",filename:"route",bundlePath:"app/api/client-portal/notifications/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\client-portal\\notifications\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:E}=l;function u(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},55511:e=>{"use strict";e.exports=require("crypto")},55797:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>E,GET:()=>l,POST:()=>d,PUT:()=>p});var n=s(32190),i=s(5069),o=s(43205),a=s.n(o),c=e([i]);i=(c.then?(await c)():c)[0];let N=process.env.JWT_SECRET||"your-secret-key";async function u(e){let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return null;let s=t.substring(7);try{let e=a().verify(s,N);if("client"!==e.type)return null;return e}catch(e){return null}}async function l(e){try{let t=await u(e);if(!t)return n.NextResponse.json({success:!1,error:"غير مصرح بالوصول"},{status:401});let{searchParams:s}=new URL(e.url),r="true"===s.get("unread_only"),o=parseInt(s.get("page")||"1"),a=parseInt(s.get("limit")||"20"),c=["cn.client_id = $1"],l=[t.clientId];r&&c.push("cn.is_read = false");let p=c.join(" AND "),d=`
      SELECT 
        cn.*,
        i.title as case_title,
        i.case_number,
        COUNT(*) OVER() as total_count
      FROM client_notifications cn
      LEFT JOIN issues i ON cn.case_id = i.id
      WHERE ${p}
      ORDER BY cn.created_date DESC
      LIMIT $2 OFFSET $3
    `;l.push(a,(o-1)*a);let E=await (0,i.P)(d,l),N=`
      SELECT 
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN is_read = false THEN 1 END) as unread_count,
        COUNT(CASE WHEN type = 'info' THEN 1 END) as info_count,
        COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_count,
        COUNT(CASE WHEN type = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN type = 'error' THEN 1 END) as error_count
      FROM client_notifications
      WHERE client_id = $1
    `,R=await (0,i.P)(N,[t.clientId]);return n.NextResponse.json({success:!0,data:E.rows,pagination:{currentPage:o,totalPages:E.rows.length>0?Math.ceil(parseInt(E.rows[0].total_count)/a):0,totalCount:E.rows.length>0?parseInt(E.rows[0].total_count):0},statistics:R.rows[0]})}catch(e){return console.error("خطأ في جلب إشعارات العميل:",e),n.NextResponse.json({success:!1,error:"فشل في جلب الإشعارات"},{status:500})}}async function p(e){try{let t=await u(e);if(!t)return n.NextResponse.json({success:!1,error:"غير مصرح بالوصول"},{status:401});let{notificationId:s,isRead:r,markAllAsRead:o}=await e.json();if(o){let e=`
        UPDATE client_notifications 
        SET is_read = true, read_at = CURRENT_TIMESTAMP
        WHERE client_id = $1 AND is_read = false
        RETURNING id
      `,s=await (0,i.P)(e,[t.clientId]);return n.NextResponse.json({success:!0,message:`تم تحديد ${s.rows.length} إشعار كمقروء`,updatedCount:s.rows.length})}if(!s)return n.NextResponse.json({success:!1,error:"معرف الإشعار مطلوب"},{status:400});let a=`
      UPDATE client_notifications 
      SET 
        is_read = $2,
        read_at = CASE WHEN $2 = true THEN CURRENT_TIMESTAMP ELSE NULL END
      WHERE id = $1 AND client_id = $3
      RETURNING *
    `,c=await (0,i.P)(a,[s,r,t.clientId]);if(0===c.rows.length)return n.NextResponse.json({success:!1,error:"الإشعار غير موجود أو غير مصرح بالوصول إليه"},{status:404});return n.NextResponse.json({success:!0,data:c.rows[0],message:r?"تم تحديد الإشعار كمقروء":"تم تحديد الإشعار كغير مقروء"})}catch(e){return console.error("خطأ في تحديث الإشعار:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث الإشعار"},{status:500})}}async function d(e){try{let t=await u(e);if(!t)return n.NextResponse.json({success:!1,error:"غير مصرح بالوصول"},{status:401});let{caseId:s,requestType:r,title:o,description:a,priority:c}=await e.json();if(!r||!o||!a)return n.NextResponse.json({success:!1,error:"البيانات المطلوبة مفقودة"},{status:400});if(s){let e=await (0,i.P)("SELECT id FROM issues WHERE id = $1 AND client_id = $2",[s,t.clientId]);if(0===e.rows.length)return n.NextResponse.json({success:!1,error:"القضية غير موجودة أو غير مصرح بالوصول إليها"},{status:403})}let l=`
      INSERT INTO client_requests (
        client_id, case_id, request_type, title, description, priority
      ) VALUES (
        $1, $2, $3, $4, $5, $6
      ) RETURNING *
    `,p=[t.clientId,s||null,r,o,a,c||"medium"],d=await (0,i.P)(l,p);return await (0,i.P)(`
      INSERT INTO client_notifications (client_id, case_id, title, message, type)
      VALUES ($1, $2, $3, $4, $5)
    `,[t.clientId,s,"طلب جديد من العميل",`تم إنشاء طلب جديد: ${o}`,"info"]),n.NextResponse.json({success:!0,data:d.rows[0],message:"تم إنشاء الطلب بنجاح"})}catch(e){return console.error("خطأ في إنشاء الطلب:",e),n.NextResponse.json({success:!1,error:"فشل في إنشاء الطلب"},{status:500})}}async function E(e){try{let t=await u(e);if(!t)return n.NextResponse.json({success:!1,error:"غير مصرح بالوصول"},{status:401});let{searchParams:s}=new URL(e.url),r=s.get("id");if(!r)return n.NextResponse.json({success:!1,error:"معرف الإشعار مطلوب"},{status:400});let o=`
      DELETE FROM client_notifications 
      WHERE id = $1 AND client_id = $2
      RETURNING id, title
    `,a=await (0,i.P)(o,[parseInt(r),t.clientId]);if(0===a.rows.length)return n.NextResponse.json({success:!1,error:"الإشعار غير موجود أو غير مصرح بحذفه"},{status:404});return n.NextResponse.json({success:!0,message:"تم حذف الإشعار بنجاح"})}catch(e){return console.error("خطأ في حذف الإشعار:",e),n.NextResponse.json({success:!1,error:"فشل في حذف الإشعار"},{status:500})}}r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,3205],()=>s(54676));module.exports=r})();