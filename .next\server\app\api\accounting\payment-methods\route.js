(()=>{var e={};e.id=5411,e.ids=[5411],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>p});var o=s(64939),a=s(29021),n=s.n(a),c=s(33873),i=s.n(c),u=e([o]);o=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let m=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],s=d.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new o.Pool(m);async function p(e,t){let s=await l.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28331:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{GET:()=>c,POST:()=>i});var o=s(32190),a=s(5069),n=e([a]);async function c(){try{let e=await (0,a.P)(`
      SELECT 
        id,
        method_code,
        method_name,
        description,
        is_active,
        created_date
      FROM payment_methods
      WHERE is_active = true
      ORDER BY method_name
    `);return o.NextResponse.json({success:!0,methods:e.rows,total:e.rows.length,message:"تم جلب طرق الدفع بنجاح"})}catch(e){return console.error("خطأ في جلب طرق الدفع:",e),o.NextResponse.json({success:!1,error:"فشل في جلب طرق الدفع",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e){try{let{method_code:t,method_name:s,description:r}=await e.json();if(!t||!s)return o.NextResponse.json({success:!1,error:"رمز طريقة الدفع واسم طريقة الدفع مطلوبان"},{status:400});if((await (0,a.P)("SELECT id FROM payment_methods WHERE method_code = $1",[t])).rows.length>0)return o.NextResponse.json({success:!1,error:"رمز طريقة الدفع موجود مسبقاً"},{status:400});let n=await (0,a.P)(`
      INSERT INTO payment_methods (method_code, method_name, description)
      VALUES ($1, $2, $3)
      RETURNING *
    `,[t,s,r]);return o.NextResponse.json({success:!0,method:n.rows[0],message:"تم إضافة طريقة الدفع بنجاح"})}catch(e){return console.error("خطأ في إضافة طريقة الدفع:",e),o.NextResponse.json({success:!1,error:"فشل في إضافة طريقة الدفع",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}a=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},77038:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var o=s(96559),a=s(48088),n=s(37719),c=s(28331),i=e([c]);c=(i.then?(await i)():i)[0];let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/payment-methods/route",pathname:"/api/accounting/payment-methods",filename:"route",bundlePath:"app/api/accounting/payment-methods/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\payment-methods\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:l}=p;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}r()}catch(e){r(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(77038));module.exports=r})();