(()=>{var e={};e.id=3947,e.ids=[3947],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>i});var a=r(96559),n=r(48088),o=r(37719),u=r(32190);async function i(e){try{let t=await e.json();return console.log("\uD83D\uDCCB البيانات المستلمة:",t),u.NextResponse.json({success:!0,message:"API يعمل بشكل صحيح",receivedData:t})}catch(e){return console.error("❌ خطأ في API:",e),u.NextResponse.json({success:!1,error:e.message},{status:500})}}async function p(){return u.NextResponse.json({success:!0,message:"API اختبار يعمل"})}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/test/route",pathname:"/api/auth/test",filename:"route",bundlePath:"app/api/auth/test/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\auth\\test\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:l}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(26047));module.exports=s})();