// اختبار الردود المحسنة للنموذج
require('dotenv').config({ path: '.env.local' });

async function testImprovedResponses() {
  console.log('🔄 اختبار الردود المحسنة للنموذج...\n');
  
  const apiKey = process.env.GROQ_API_KEY;
  
  if (!apiKey) {
    console.log('❌ GROQ_API_KEY غير متوفر');
    return;
  }
  
  // أسئلة للاختبار
  const testQuestions = [
    {
      type: 'عام',
      question: 'أحتاج استشارة قانونية',
      expectedBehavior: 'يجب أن يطلب التفاصيل'
    },
    {
      type: 'عام',
      question: 'مساعدة قانونية',
      expectedBehavior: 'يجب أن يطلب التفاصيل'
    },
    {
      type: 'محدد',
      question: 'ما هي حقوق العامل في قانون العمل اليمني؟',
      expectedBehavior: 'يجب أن يجيب مباشرة'
    },
    {
      type: 'محدد',
      question: 'كيف أقسم الميراث حسب الشريعة الإسلامية؟',
      expectedBehavior: 'يجب أن يجيب مباشرة'
    },
    {
      type: 'محدد',
      question: 'ما هي إجراءات رفع دعوى قضائية في اليمن؟',
      expectedBehavior: 'يجب أن يجيب مباشرة'
    }
  ];
  
  console.log(`📋 سيتم اختبار ${testQuestions.length} أسئلة مختلفة...\n`);
  
  for (let i = 0; i < testQuestions.length; i++) {
    const test = testQuestions[i];
    
    console.log(`${'='.repeat(60)}`);
    console.log(`🧪 اختبار ${i + 1}: ${test.type}`);
    console.log(`❓ السؤال: "${test.question}"`);
    console.log(`🎯 السلوك المتوقع: ${test.expectedBehavior}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama-3.1-8b-instant',
          messages: [
            {
              role: 'system',
              content: `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.

قواعد مهمة للردود:
1. إذا كان السؤال عاماً (مثل "أحتاج استشارة قانونية")، اسأل عن التفاصيل المحددة
2. إذا كان السؤال محدداً، أجب مباشرة بمعلومات قانونية مفيدة
3. لا تعطي قوائم طويلة من الخيارات
4. لا تكرر نفس المعلومات
5. اجعل ردك مختصراً ومفيداً (100-200 كلمة كحد أقصى)

أسلوب الرد:
- للأسئلة العامة: "لأقدم لك استشارة قانونية دقيقة، يرجى توضيح موضوع استشارتك بالتفصيل. مثلاً: مشكلة في العمل، قضية ميراث، عقد، أو أي موضوع قانوني محدد."
- للأسئلة المحددة: أجب مباشرة بالمعلومات القانونية المطلوبة

خبرتك تشمل جميع فروع القانون اليمني والشريعة الإسلامية.`
            },
            {
              role: 'user',
              content: `سؤال العميل: "${test.question}"\n\nأجب بشكل محدد ومفيد. إذا كان السؤال عاماً، اطلب التفاصيل. إذا كان محدداً، قدم الإجابة القانونية المطلوبة.`
            }
          ],
          max_tokens: 300,
          temperature: 0.7
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        console.log('✅ نجح الاختبار!');
        console.log('🤖 رد النموذج:');
        console.log(`"${aiResponse}"`);
        
        // تحليل الرد
        const wordCount = aiResponse.split(' ').length;
        const hasLongList = aiResponse.includes('1.') && aiResponse.includes('2.') && aiResponse.includes('3.');
        const asksForDetails = aiResponse.includes('توضيح') || aiResponse.includes('تفاصيل') || aiResponse.includes('محدد');
        const givesDirectAnswer = !asksForDetails && (aiResponse.includes('حسب') || aiResponse.includes('وفقاً') || aiResponse.includes('القانون'));
        
        console.log('\n📊 تحليل الرد:');
        console.log(`   • عدد الكلمات: ${wordCount}`);
        console.log(`   • يحتوي على قائمة طويلة: ${hasLongList ? '❌ نعم' : '✅ لا'}`);
        console.log(`   • يطلب التفاصيل: ${asksForDetails ? '✅ نعم' : '❌ لا'}`);
        console.log(`   • يعطي إجابة مباشرة: ${givesDirectAnswer ? '✅ نعم' : '❌ لا'}`);
        
        // تقييم الرد
        let evaluation = '';
        if (test.type === 'عام') {
          if (asksForDetails && !hasLongList && wordCount < 100) {
            evaluation = '✅ ممتاز - يطلب التفاصيل بشكل مختصر';
          } else if (asksForDetails && hasLongList) {
            evaluation = '⚠️ جيد لكن يحتوي على قائمة طويلة';
          } else {
            evaluation = '❌ يحتاج تحسين - لا يطلب التفاصيل';
          }
        } else {
          if (givesDirectAnswer && !hasLongList && wordCount < 250) {
            evaluation = '✅ ممتاز - إجابة مباشرة ومفيدة';
          } else if (givesDirectAnswer && hasLongList) {
            evaluation = '⚠️ جيد لكن طويل أو يحتوي على قوائم';
          } else {
            evaluation = '❌ يحتاج تحسين - لا يعطي إجابة مباشرة';
          }
        }
        
        console.log(`   • التقييم: ${evaluation}`);
        
      } else {
        console.log('❌ فشل الاختبار');
        console.log(`📄 رمز الخطأ: ${response.status}`);
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
    }
    
    console.log('\n');
    
    // انتظار قصير بين الاختبارات
    if (i < testQuestions.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`${'='.repeat(60)}`);
  console.log('📋 ملخص الاختبار:');
  console.log('✅ تم اختبار الردود المحسنة');
  console.log('🎯 النموذج يجب أن:');
  console.log('   • يطلب التفاصيل للأسئلة العامة');
  console.log('   • يعطي إجابات مباشرة للأسئلة المحددة');
  console.log('   • يتجنب القوائم الطويلة');
  console.log('   • يكون مختصراً ومفيداً');
  console.log(`${'='.repeat(60)}`);
}

// تشغيل الاختبار
testImprovedResponses().catch(console.error);
