"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/simple-chat-widget */ \"(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCompanyData */ \"(app-pages-browser)/./src/hooks/useCompanyData.ts\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst companyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // استخدام hook بيانات الشركة مع التخزين المحلي\n    const { companyData, loading: companyLoading, error: companyError, getThemeColor, getCompanyName, isDataAvailable } = (0,_hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData)();\n    // استخدام البيانات المحملة أو الافتراضية\n    const companyDataState = companyData || defaultCompanyData;\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // مؤشر التحميل للبيانات الأساسية\n    if (companyLoading && !isDataAvailable) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-yellow-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mt-2\",\n                        children: \"يتم حفظ البيانات محلياً لتسريع التصفح مستقبلاً\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            companyError && !isDataAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-600 text-white px-4 py-2 text-center text-sm\",\n                children: \"⚠️ تعذر تحميل أحدث البيانات، يتم عرض البيانات المحفوظة محلياً\"\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__.SimpleChatWidget, {\n                isOpen: isChatWidgetOpen,\n                onClose: ()=>setIsChatWidgetOpen(false),\n                onOpen: ()=>setIsChatWidgetOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"O/ceB+Iu6CvIuCsB3XkHEZGk0w4=\", false, function() {\n    return [\n        _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData\n    ];\n});\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ })

});