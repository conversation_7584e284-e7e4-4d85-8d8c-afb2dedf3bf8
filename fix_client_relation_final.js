// إصلاح علاقة العملاء والقضايا النهائي وتحديث البيانات
const { Pool } = require('pg');

async function fixClientRelationFinal() {
  console.log('🔧 إصلاح علاقة العملاء والقضايا النهائي وتحديث البيانات...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص الوضع الحالي
      console.log('\n   🔍 فحص الوضع الحالي:');
      
      const currentRelations = await pool.query(`
        SELECT 
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        AND kcu.column_name = 'client_id'
      `);

      console.log('      العلاقات الحالية للعملاء:');
      currentRelations.rows.forEach(rel => {
        console.log(`         - ${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
      });

      // 2. حذف جميع العلاقات المكررة للعملاء
      console.log('\n   🗑️ حذف العلاقات المكررة للعملاء:');
      
      const clientConstraints = await pool.query(`
        SELECT constraint_name
        FROM information_schema.table_constraints
        WHERE table_name = 'issues'
        AND constraint_type = 'FOREIGN KEY'
        AND constraint_name LIKE '%client%'
      `);

      for (const constraint of clientConstraints.rows) {
        try {
          await pool.query(`ALTER TABLE issues DROP CONSTRAINT IF EXISTS ${constraint.constraint_name}`);
          console.log(`      ✅ تم حذف: ${constraint.constraint_name}`);
        } catch (error) {
          console.log(`      ⚠️ خطأ في حذف ${constraint.constraint_name}: ${error.message}`);
        }
      }

      // 3. إنشاء العلاقة الواحدة الصحيحة
      console.log('\n   🔗 إنشاء العلاقة الواحدة الصحيحة:');
      
      try {
        await pool.query(`
          ALTER TABLE issues 
          ADD CONSTRAINT fk_issues_client_id 
          FOREIGN KEY (client_id) REFERENCES clients(id)
          ON DELETE SET NULL
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء العلاقة الوحيدة: issues.client_id -> clients.id');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('      ✅ العلاقة موجودة مسبقاً');
        } else {
          console.log(`      ⚠️ خطأ في إنشاء العلاقة: ${error.message}`);
        }
      }

      // 4. تحديث trigger العملاء
      console.log('\n   🔄 تحديث trigger العملاء:');
      
      try {
        // إنشاء/تحديث trigger لتحديث client_name
        await pool.query(`
          CREATE OR REPLACE FUNCTION update_client_name()
          RETURNS TRIGGER AS $$
          BEGIN
            IF NEW.client_id IS NOT NULL THEN
              SELECT name INTO NEW.client_name 
              FROM clients 
              WHERE id = NEW.client_id;
            ELSE
              NEW.client_name := NULL;
            END IF;
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;
        `);

        await pool.query(`
          DROP TRIGGER IF EXISTS trigger_update_client_name ON issues;
          CREATE TRIGGER trigger_update_client_name
            BEFORE INSERT OR UPDATE OF client_id ON issues
            FOR EACH ROW
            EXECUTE FUNCTION update_client_name();
        `);

        console.log('      ✅ تم إنشاء trigger لتحديث client_name');
      } catch (error) {
        console.log(`      ⚠️ خطأ في إنشاء trigger العملاء: ${error.message}`);
      }

      // 5. تحديث جميع أسماء العملاء الحالية
      console.log('\n   📝 تحديث جميع أسماء العملاء الحالية:');
      
      const updateClientNames = await pool.query(`
        UPDATE issues 
        SET client_name = c.name
        FROM clients c
        WHERE issues.client_id = c.id
        AND (issues.client_name IS NULL 
             OR issues.client_name = '' 
             OR issues.client_name != c.name)
        RETURNING issues.case_number, issues.client_name, c.name as new_name
      `);

      if (updateClientNames.rows.length > 0) {
        console.log(`      ✅ تم تحديث ${updateClientNames.rows.length} قضية:`);
        updateClientNames.rows.forEach(row => {
          console.log(`         - ${row.case_number}: "${row.new_name}"`);
        });
      } else {
        console.log('      ✅ جميع أسماء العملاء محدثة');
      }

      // 6. معالجة القضايا بدون أسماء عملاء
      console.log('\n   🔄 معالجة القضايا بدون أسماء عملاء:');
      
      const issuesWithoutClientNames = await pool.query(`
        SELECT 
          i.id, 
          i.case_number, 
          i.client_id,
          c.name as client_name_from_relation
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        WHERE i.client_id IS NOT NULL 
        AND (i.client_name IS NULL OR i.client_name = '')
      `);

      if (issuesWithoutClientNames.rows.length > 0) {
        console.log(`      📋 معالجة ${issuesWithoutClientNames.rows.length} قضية بدون أسماء عملاء:`);
        
        for (const issue of issuesWithoutClientNames.rows) {
          if (issue.client_name_from_relation) {
            await pool.query(`
              UPDATE issues 
              SET client_name = $1
              WHERE id = $2
            `, [issue.client_name_from_relation, issue.id]);
            
            console.log(`         ✅ ${issue.case_number}: تم إضافة "${issue.client_name_from_relation}"`);
          } else {
            console.log(`         ⚠️ ${issue.case_number}: العميل ID ${issue.client_id} غير موجود`);
          }
        }
      } else {
        console.log('      ✅ جميع القضايا لها أسماء عملاء');
      }

      // 7. فحص العمود created_date وإصلاح created_at
      console.log('\n   📅 فحص وإصلاح أعمدة التاريخ:');
      
      const dateColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name IN ('created_date', 'created_at', 'updated_at', 'updated_date')
        ORDER BY column_name
      `);

      console.log('      الأعمدة الموجودة:');
      const existingColumns = dateColumns.rows.map(col => col.column_name);
      dateColumns.rows.forEach(col => {
        console.log(`         - ${col.column_name}: ${col.data_type}`);
      });

      // إضافة created_date إذا لم يكن موجوداً
      if (!existingColumns.includes('created_date')) {
        console.log('      ➕ إضافة العمود created_date...');
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        `);
        console.log('      ✅ تم إضافة العمود created_date');
      }

      // إضافة updated_date إذا لم يكن موجوداً
      if (!existingColumns.includes('updated_date')) {
        console.log('      ➕ إضافة العمود updated_date...');
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        `);
        console.log('      ✅ تم إضافة العمود updated_date');
      }

      // تحديث البيانات الفارغة في created_date
      const updateCreatedDates = await pool.query(`
        UPDATE issues 
        SET created_date = CURRENT_TIMESTAMP
        WHERE created_date IS NULL
        RETURNING case_number
      `);

      if (updateCreatedDates.rows.length > 0) {
        console.log(`      ✅ تم تحديث created_date لـ ${updateCreatedDates.rows.length} قضية`);
      }

      // 8. اختبار العلاقة النهائية
      console.log('\n   🧪 اختبار العلاقة النهائية:');
      
      const finalTest = await pool.query(`
        SELECT 
          i.case_number,
          i.client_id,
          i.client_name as stored_name,
          c.name as relation_name,
          CASE 
            WHEN i.client_name = c.name THEN '✅'
            WHEN i.client_name IS NULL AND c.name IS NULL THEN '✅'
            ELSE '❌'
          END as match_status
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        ORDER BY i.case_number
      `);

      console.log('      📊 نتائج الاختبار:');
      finalTest.rows.forEach(row => {
        console.log(`         ${row.match_status} ${row.case_number}: "${row.stored_name || 'فارغ'}" vs "${row.relation_name || 'غير موجود'}"`);
      });

      // 9. اختبار trigger العملاء
      console.log('\n   🔄 اختبار trigger العملاء:');
      
      const testIssue = await pool.query(`
        SELECT id, client_id FROM issues LIMIT 1
      `);

      if (testIssue.rows.length > 0) {
        const issueId = testIssue.rows[0].id;
        const clientId = testIssue.rows[0].client_id;
        
        console.log(`      🧪 اختبار trigger للقضية ${issueId}...`);
        
        // تحديث client_id لتفعيل الـ trigger
        await pool.query(`
          UPDATE issues 
          SET client_id = $1
          WHERE id = $2
        `, [clientId, issueId]);
        
        // فحص النتيجة
        const triggerResult = await pool.query(`
          SELECT 
            i.client_name,
            c.name as expected_name
          FROM issues i
          LEFT JOIN clients c ON i.client_id = c.id
          WHERE i.id = $1
        `, [issueId]);
        
        if (triggerResult.rows.length > 0) {
          const result = triggerResult.rows[0];
          if (result.client_name === result.expected_name) {
            console.log(`      ✅ trigger العملاء يعمل بنجاح: "${result.client_name}"`);
          } else {
            console.log(`      ❌ trigger العملاء لا يعمل: "${result.client_name}" != "${result.expected_name}"`);
          }
        }
      }

      // 10. إحصائيات نهائية
      console.log('\n   📊 إحصائيات نهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM issues WHERE client_id IS NOT NULL) as issues_with_client_id,
          (SELECT COUNT(*) FROM issues WHERE client_name IS NOT NULL AND client_name != '') as issues_with_client_name,
          (SELECT COUNT(*) FROM clients) as total_clients,
          (SELECT COUNT(*) FROM issues i JOIN clients c ON i.client_id = c.id WHERE i.client_name = c.name) as matching_names,
          (SELECT COUNT(*) FROM issues WHERE created_date IS NOT NULL) as issues_with_created_date
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - قضايا بها client_id: ${stats.issues_with_client_id}`);
      console.log(`      - قضايا بها client_name: ${stats.issues_with_client_name}`);
      console.log(`      - إجمالي العملاء: ${stats.total_clients}`);
      console.log(`      - أسماء متطابقة: ${stats.matching_names}`);
      console.log(`      - قضايا بها created_date: ${stats.issues_with_created_date}`);

      // 11. فحص العلاقة النهائية
      console.log('\n   🔍 فحص العلاقة النهائية:');
      
      const finalRelationCheck = await pool.query(`
        SELECT 
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        AND kcu.column_name = 'client_id'
      `);

      if (finalRelationCheck.rows.length === 1) {
        const relation = finalRelationCheck.rows[0];
        console.log(`      ✅ علاقة واحدة فقط: ${relation.column_name} -> ${relation.foreign_table_name}.${relation.foreign_column_name}`);
      } else if (finalRelationCheck.rows.length === 0) {
        console.log(`      ❌ لا توجد علاقة للعملاء!`);
      } else {
        console.log(`      ⚠️ يوجد ${finalRelationCheck.rows.length} علاقات (يجب أن تكون واحدة فقط)`);
        finalRelationCheck.rows.forEach(rel => {
          console.log(`         - ${rel.constraint_name}: ${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
        });
      }

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح علاقة العملاء والقضايا النهائي');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ حذف جميع العلاقات المكررة للعملاء');
  console.log('2. ✅ إنشاء علاقة واحدة فقط: clients.id -> issues.client_id');
  console.log('3. ✅ إنشاء trigger لتحديث client_name تلقائياً');
  console.log('4. ✅ تحديث جميع أسماء العملاء الحالية');
  console.log('5. ✅ إصلاح أعمدة التاريخ (created_date بدلاً من created_at)');
  console.log('6. ✅ اختبار العلاقة والـ triggers');
  console.log('7. ✅ ضمان تطابق البيانات المخزنة مع العلاقات');
}

// تشغيل الإصلاح
fixClientRelationFinal().catch(console.error);
