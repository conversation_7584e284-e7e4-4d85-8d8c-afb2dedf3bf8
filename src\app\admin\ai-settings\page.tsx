'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Bot,
  Settings,
  Save,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Zap,
  Clock,
  MessageSquare,
  Shield,
  Activity
} from 'lucide-react'

interface AIModel {
  key: string
  name: string
  endpoint: string
  model: string
  description: string
  status: 'available' | 'not_found' | 'service_unavailable' | 'offline' | 'api_key_required' | 'api_error'
  lastChecked: string
  error?: string
}

interface AISettings {
  enabled: boolean
  model: string
  delay_seconds: number
  working_hours_only: boolean
  working_hours_start: string
  working_hours_end: string
  working_days: string[]
  max_responses_per_conversation: number
  keywords_trigger: string[]
  excluded_keywords: string[]
  auto_responses: {
    greeting: string
    working_hours: string
    max_reached: string
  }
}

export default function AISettingsPage() {
  const [models, setModels] = useState<AIModel[]>([])
  const [settings, setSettings] = useState<AISettings>({
    enabled: true,
    model: 'groq-llama-8b',
    delay_seconds: 3,
    working_hours_only: false, // دائماً false - يعمل 24 ساعة
    working_hours_start: '00:00',
    working_hours_end: '23:59',
    working_days: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
    max_responses_per_conversation: 20,
    keywords_trigger: ['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'مرحبا', 'السلام', 'أهلا', 'استشارة', 'قانوني', 'محامي'],
    excluded_keywords: [],
    auto_responses: {
      greeting: 'مرحباً! كيف يمكنني مساعدتك؟',
      working_hours: 'أنا متاح للمساعدة على مدار 24 ساعة. كيف يمكنني مساعدتك؟',
      max_reached: 'تم الوصول للحد الأقصى من الردود التلقائية. سيقوم أحد المحامين بالرد عليك قريباً.'
    }
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testMessage, setTestMessage] = useState('مرحبا، أحتاج استشارة قانونية')
  const [testResponse, setTestResponse] = useState('')
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      // جلب النماذج المتاحة
      const modelsResponse = await fetch('/api/ai/local-models')
      const modelsData = await modelsResponse.json()

      // جلب الإعدادات من API الجديد
      const settingsResponse = await fetch('/api/ai/settings')
      const settingsData = await settingsResponse.json()

      if (modelsData.success) {
        setModels(modelsData.data.models || [])
      }

      if (settingsData.success) {
        setSettings(settingsData.data)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      setMessage({
        type: 'error',
        text: 'فشل في جلب البيانات'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    setMessage(null)

    try {
      const response = await fetch('/api/ai/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      })

      const result = await response.json()

      if (result.success) {
        setMessage({
          type: 'success',
          text: 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح'
        })

        // إعادة جلب الإعدادات للتأكد من الحفظ
        setTimeout(() => {
          fetchData()
        }, 1000)
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'فشل في حفظ الإعدادات'
        })
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      setMessage({
        type: 'error',
        text: 'حدث خطأ في حفظ الإعدادات'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleTestAI = async () => {
    if (!testMessage.trim()) {
      setMessage({
        type: 'error',
        text: 'يرجى إدخال رسالة للاختبار'
      })
      return
    }

    setTesting(true)
    setTestResponse('')
    setMessage(null)

    try {
      const response = await fetch('/api/ai/local-models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: testMessage,
          model: settings.model,
          conversationId: 'test-' + Date.now()
        })
      })

      const data = await response.json()


      if (data.success && data.response) {
        setTestResponse(data.response)
        setMessage({
          type: 'success',
          text: 'تم اختبار الذكاء الاصطناعي بنجاح!'
        })
      } else {
        throw new Error(data.error || 'لم يتم الحصول على رد من النموذج')
      }
    } catch (error) {
      console.error('Error testing AI:', error)
      setMessage({
        type: 'error',
        text: 'فشل في اختبار الذكاء الاصطناعي: ' + error.message
      })
    } finally {
      setTesting(false)
    }
  }

  const testAI = async () => {
    setTesting(true)
    setMessage(null)

    try {
      console.log('🧪 بدء اختبار الذكاء الاصطناعي...')
      console.log('النموذج المحدد:', settings.model)

      const response = await fetch('/api/ai/local-models', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'مرحبا، هذا اختبار للنظام',
          model: settings.model,
          conversationId: 'quick-test-' + Date.now()
        })
      })

      console.log('📡 استجابة الخادم:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status}`)
      }

      const result = await response.json()


      if (result.success && result.response) {
        setMessage({
          type: 'success',
          text: `تم اختبار النموذج بنجاح! الرد: ${result.response.substring(0, 100)}...`
        })
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'لم يتم الحصول على رد من النموذج'
        })
      }
    } catch (error) {
      console.error('❌ خطأ في اختبار الذكاء الاصطناعي:', error)
      setMessage({
        type: 'error',
        text: `فشل في الاتصال بالنموذج المحلي: ${error.message}`
      })
    } finally {
      setTesting(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800'
      case 'not_found': return 'bg-yellow-100 text-yellow-800'
      case 'service_unavailable': return 'bg-orange-100 text-orange-800'
      case 'offline': return 'bg-red-100 text-red-800'
      case 'api_key_required': return 'bg-blue-100 text-blue-800'
      case 'api_error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'متاح'
      case 'not_found': return 'غير موجود'
      case 'service_unavailable': return 'الخدمة غير متاحة'
      case 'offline': return 'غير متصل'
      case 'api_key_required': return 'يحتاج API Key'
      case 'api_error': return 'خطأ في API'
      default: return 'غير معروف'
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Bot className="h-8 w-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إعدادات الذكاء الاصطناعي</h1>
              <p className="text-gray-600">إدارة النماذج المحلية والرد التلقائي للعملاء</p>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button
              onClick={fetchData}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button
              onClick={testAI}
              variant="outline"
              disabled={testing || !settings.enabled}
            >
              {testing ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 ml-2" />
              )}
              اختبار النموذج
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              حفظ الإعدادات
            </Button>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {message.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* حالة النماذج */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Activity className="h-5 w-5 ml-2" />
                حالة النماذج المحلية والخارجية
                <span className="mr-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  محسن للسرعة
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchData}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {models.map((model) => (
                <Card key={model.key} className="border">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{model.name}</h3>
                      <Badge className={getStatusColor(model.status)}>
                        {getStatusText(model.status)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                    <div className="text-xs text-gray-500">
                      <p>النموذج: {model.model}</p>
                      <p>آخر فحص: {new Date(model.lastChecked).toLocaleString('ar-SA')}</p>
                      {model.error && (
                        <p className="text-red-500 mt-1">خطأ: {model.error}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* الإعدادات العامة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 ml-2" />
              الإعدادات العامة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">تفعيل الرد التلقائي</Label>
                <p className="text-sm text-gray-600">تشغيل أو إيقاف المساعد الذكي</p>
              </div>
              <Switch
                checked={settings.enabled}
                onCheckedChange={(checked) => setSettings({...settings, enabled: checked})}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>النموذج المستخدم</Label>
                <Select
                  value={settings.model}
                  onValueChange={(value) => setSettings({...settings, model: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {models.filter(m => m.status === 'available').map((model) => (
                      <SelectItem key={model.key} value={model.key}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>تأخير الرد (بالثواني)</Label>
                <Input
                  type="number"
                  value={settings.delay_seconds}
                  onChange={(e) => setSettings({...settings, delay_seconds: parseInt(e.target.value)})}
                  min="5"
                  max="300"
                />
              </div>
            </div>

            <div>
              <Label>الحد الأقصى للردود لكل محادثة</Label>
              <Input
                type="number"
                value={settings.max_responses_per_conversation}
                onChange={(e) => setSettings({...settings, max_responses_per_conversation: parseInt(e.target.value)})}
                min="1"
                max="20"
              />
            </div>
          </CardContent>
        </Card>

        {/* ساعات العمل - للعرض فقط */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 ml-2" />
              ساعات العمل
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">حالة التشغيل</Label>
                <p className="text-sm text-gray-600">النظام يعمل 24 ساعة طالما مفتاح التشغيل مفعل</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600">متاح 24/7</span>
              </div>
            </div>

            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                تم تعطيل قيود ساعات العمل. المساعد الذكي متاح على مدار 24 ساعة طالما أن مفتاح التشغيل مفعل.
              </AlertDescription>
            </Alert>

            {/* معلومات إضافية للعرض فقط */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 opacity-50">
              <div>
                <Label>ساعات العمل الافتراضية (للعرض فقط)</Label>
                <Input
                  type="time"
                  value="00:00"
                  disabled
                  className="bg-gray-100"
                />
                <p className="text-xs text-gray-500 mt-1">بداية: منتصف الليل</p>
              </div>

              <div>
                <Label>نهاية ساعات العمل (للعرض فقط)</Label>
                <Input
                  type="time"
                  value="23:59"
                  disabled
                  className="bg-gray-100"
                />
                <p className="text-xs text-gray-500 mt-1">نهاية: قبل منتصف الليل بدقيقة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* الكلمات المفتاحية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 ml-2" />
              الكلمات المفتاحية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>الكلمات المحفزة للرد (مفصولة بفاصلة)</Label>
              <Textarea
                value={settings.keywords_trigger.join(', ')}
                onChange={(e) => setSettings({
                  ...settings,
                  keywords_trigger: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                })}
                placeholder="مساعدة, استفسار, سؤال, معلومات"
              />
            </div>

            <div>
              <Label>الكلمات المستبعدة (مفصولة بفاصلة)</Label>
              <Textarea
                value={settings.excluded_keywords.join(', ')}
                onChange={(e) => setSettings({
                  ...settings,
                  excluded_keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                })}
                placeholder="عاجل, طارئ, مهم جداً, محامي"
              />
            </div>
          </CardContent>
        </Card>

        {/* الردود التلقائية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 ml-2" />
              الردود التلقائية المحددة مسبقاً
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>رسالة الترحيب</Label>
              <Textarea
                value={settings.auto_responses.greeting}
                onChange={(e) => setSettings({
                  ...settings,
                  auto_responses: {...settings.auto_responses, greeting: e.target.value}
                })}
              />
            </div>

            <div>
              <Label>رسالة خارج ساعات العمل</Label>
              <Textarea
                value={settings.auto_responses.working_hours}
                onChange={(e) => setSettings({
                  ...settings,
                  auto_responses: {...settings.auto_responses, working_hours: e.target.value}
                })}
              />
            </div>

            <div>
              <Label>رسالة الوصول للحد الأقصى</Label>
              <Textarea
                value={settings.auto_responses.max_reached}
                onChange={(e) => setSettings({
                  ...settings,
                  auto_responses: {...settings.auto_responses, max_reached: e.target.value}
                })}
              />
            </div>
          </CardContent>
        </Card>

        {/* اختبار الذكاء الاصطناعي */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 ml-2" />
              اختبار الذكاء الاصطناعي
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>رسالة الاختبار</Label>
              <Textarea
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="اكتب رسالة لاختبار الذكاء الاصطناعي..."
                rows={3}
              />
            </div>

            <Button
              onClick={handleTestAI}
              disabled={testing || !testMessage.trim()}
              className="w-full"
            >
              {testing ? (
                <>
                  <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                  جاري الاختبار...
                </>
              ) : (
                <>
                  <Activity className="h-4 w-4 ml-2" />
                  اختبار الذكاء الاصطناعي
                </>
              )}
            </Button>

            {testResponse && (
              <div className="mt-4">
                <Label>رد الذكاء الاصطناعي</Label>
                <div className="mt-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="whitespace-pre-wrap text-sm">
                    {testResponse}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}