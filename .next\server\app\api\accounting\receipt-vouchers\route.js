(()=>{var e={};e.id=5908,e.ids=[5908],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>_});var n=r(64939),s=r(29021),o=r.n(s),c=r(33873),u=r.n(c),i=e([n]);n=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new n.Pool(p);async function _(e,t){let r=await l.connect();try{return await r.query(e,t)}finally{r.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45004:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>i,POST:()=>_,PUT:()=>d});var n=r(32190),s=r(5069),o=e([s]);async function c(){await (0,s.P)(`
    CREATE TABLE IF NOT EXISTS receipt_vouchers (
      id SERIAL PRIMARY KEY,
      entry_number VARCHAR(50) UNIQUE NOT NULL,
      entry_date DATE NOT NULL,
      payer_name VARCHAR(255) NOT NULL,
      payer_type VARCHAR(50) DEFAULT 'external',
      debit_account_id INTEGER,
      credit_account_id INTEGER,
      amount DECIMAL(15,2) NOT NULL,
      description TEXT,
      reference_number VARCHAR(100),
      status VARCHAR(20) DEFAULT 'draft',
      created_by VARCHAR(100) DEFAULT 'النظام',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `)}async function u(){let e=await (0,s.P)(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries
    WHERE entry_number ~ '^RV[0-9]+$'
  `),t=e.rows[0]?.next_number||1;return`RV${String(t).padStart(6,"0")}`}async function i(e){try{await c();let{searchParams:t}=new URL(e.url),r=t.get("status"),a=t.get("date_from"),o=t.get("date_to"),u=`
      SELECT
        je.*,
        (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count
      FROM journal_entries je
      WHERE je.entry_type = 'receipt'
    `,i=[],_=1;r&&"all"!==r&&(u+=` AND je.status = $${_}`,i.push(r),_++),a&&(u+=` AND je.entry_date >= $${_}`,i.push(a),_++),o&&(u+=` AND je.entry_date <= $${_}`,i.push(o),_++),u+=" ORDER BY je.entry_date DESC, je.entry_number DESC",console.log("\uD83D\uDD0D SQL Query:",u),console.log("\uD83D\uDD0D Parameters:",i);let d=await (0,s.P)(u,i),p=[];for(let e of d.rows){let t=await (0,s.P)(`
        SELECT * FROM journal_entry_details
        WHERE journal_entry_id = $1
        ORDER BY line_order
      `,[e.id]),r=t.rows.find(e=>parseFloat(e.debit_amount)>0),a=t.rows.find(e=>parseFloat(e.credit_amount)>0);p.push({id:e.id,entry_number:e.entry_number,voucher_number:e.entry_number,entry_date:e.entry_date,voucher_date:e.entry_date,amount:parseFloat(e.total_debit||0),total_debit:parseFloat(e.total_debit||0),payer_name:e.party_name,payer_type:e.party_type,party_name:e.party_name,party_type:e.party_type,description:e.description,reference_number:e.reference_number,status:e.status,debit_account_id:r?.account_id,credit_account_id:a?.account_id,debit_account_name:r?.account_name,debit_account_code:r?.account_code,credit_account_name:a?.account_name,credit_account_code:a?.account_code,currency_id:1,payment_method_id:1,cost_center_id:null,case_id:null,service_id:null,created_by:e.created_by,created_date:e.created_date,details:t.rows})}return n.NextResponse.json({success:!0,vouchers:p,total:p.length,message:"تم جلب سندات القبض بنجاح"})}catch(e){return console.error("❌ خطأ في جلب سندات القبض:",e),n.NextResponse.json({success:!1,error:"فشل في جلب سندات القبض",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function _(e){try{await c();let t=await e.json();console.log("\uD83D\uDCE5 بيانات سند القبض المستلمة:",t);let{entry_date:r,payer_name:a,payer_type:o="external",beneficiary_name:i,beneficiary_type:_,debit_account_id:d,credit_account_id:p,amount:l,description:y,reference_number:E,status:m="draft"}=t,R=a||i;if(!r||!R||!d||!p||!l){let e=[];return r||e.push("تاريخ السند"),R||e.push("اسم الدافع"),d||e.push("الحساب المدين"),p||e.push("الحساب الدائن"),l||e.push("المبلغ"),n.NextResponse.json({success:!1,error:"البيانات المطلوبة مفقودة",details:`الحقول المفقودة: ${e.join(", ")}`},{status:400})}let A=await u(),T=(await (0,s.P)(`
      INSERT INTO journal_entries (
        entry_number, entry_type, entry_date, description,
        party_name, party_type, reference_number,
        total_debit, total_credit, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `,[A,"receipt",r,y,R,_||o||"external",E,l,l,m,"النظام"])).rows[0],b=T.id,S=await (0,s.P)(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `,[d]),$=await (0,s.P)(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `,[p]);await (0,s.P)(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `,[b,d,S.rows[0]?.account_name||"حساب غير معروف",S.rows[0]?.account_code||"",l,0,`استلام من ${R}`,1]),await (0,s.P)(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `,[b,p,$.rows[0]?.account_name||"حساب غير معروف",$.rows[0]?.account_code||"",0,l,`إيراد من ${R}`,2]);let h={id:T.id,entry_number:T.entry_number,entry_date:T.entry_date,payer_name:T.party_name,payer_type:T.party_type,amount:T.total_debit,description:T.description,reference_number:T.reference_number,status:T.status,created_by:T.created_by,created_date:T.created_date};return n.NextResponse.json({success:!0,voucher:h,message:`تم إنشاء سند القبض ${A} بنجاح`})}catch(e){return console.error("❌ خطأ في إنشاء سند القبض:",e),n.NextResponse.json({success:!1,error:"فشل في إنشاء سند القبض",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function d(e){try{let{id:t,...r}=await e.json();if(!t)return n.NextResponse.json({success:!1,error:"معرف السند مطلوب"},{status:400});let a=await (0,s.P)(`
      UPDATE receipt_vouchers 
      SET 
        entry_date = COALESCE($2, entry_date),
        payer_name = COALESCE($3, payer_name),
        payer_type = COALESCE($4, payer_type),
        debit_account_id = COALESCE($5, debit_account_id),
        credit_account_id = COALESCE($6, credit_account_id),
        amount = COALESCE($7, amount),
        description = COALESCE($8, description),
        reference_number = COALESCE($9, reference_number),
        status = COALESCE($10, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[t,r.entry_date,r.payer_name,r.payer_type,r.debit_account_id,r.credit_account_id,r.amount,r.description,r.reference_number,r.status]);if(0===a.rows.length)return n.NextResponse.json({success:!1,error:"السند غير موجود"},{status:404});return n.NextResponse.json({success:!0,voucher:a.rows[0],message:"تم تحديث سند القبض بنجاح"})}catch(e){return console.error("❌ خطأ في تحديث سند القبض:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث سند القبض",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}s=(o.then?(await o)():o)[0],a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},79140:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>i,routeModule:()=>_,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var n=r(96559),s=r(48088),o=r(37719),c=r(45004),u=e([c]);c=(u.then?(await u)():u)[0];let _=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/receipt-vouchers/route",pathname:"/api/accounting/receipt-vouchers",filename:"route",bundlePath:"app/api/accounting/receipt-vouchers/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\receipt-vouchers\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:l}=_;function i(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(79140));module.exports=a})();