/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/accounting/receipt-vouchers/route";
exports.ids = ["app/api/accounting/receipt-vouchers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&page=%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&page=%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_accounting_receipt_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/accounting/receipt-vouchers/route.ts */ \"(rsc)/./src/app/api/accounting/receipt-vouchers/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_accounting_receipt_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_accounting_receipt_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/accounting/receipt-vouchers/route\",\n        pathname: \"/api/accounting/receipt-vouchers\",\n        filename: \"route\",\n        bundlePath: \"app/api/accounting/receipt-vouchers/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\accounting\\\\receipt-vouchers\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_accounting_receipt_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhY2NvdW50aW5nJTJGcmVjZWlwdC12b3VjaGVycyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYWNjb3VudGluZyUyRnJlY2VpcHQtdm91Y2hlcnMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhY2NvdW50aW5nJTJGcmVjZWlwdC12b3VjaGVycyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbW9oYW1pbmV3JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbW9oYW1pbmV3JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNvQjtBQUNqRztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYscUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcbW9oYW1pbmV3XFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFjY291bnRpbmdcXFxccmVjZWlwdC12b3VjaGVyc1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWNjb3VudGluZy9yZWNlaXB0LXZvdWNoZXJzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYWNjb3VudGluZy9yZWNlaXB0LXZvdWNoZXJzXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9hY2NvdW50aW5nL3JlY2VpcHQtdm91Y2hlcnMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWNjb3VudGluZ1xcXFxyZWNlaXB0LXZvdWNoZXJzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&page=%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/accounting/receipt-vouchers/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/accounting/receipt-vouchers/route.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// إنشاء جدول سندات القبض إذا لم يكن موجوداً\nasync function ensureReceiptVouchersTable() {\n    await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    CREATE TABLE IF NOT EXISTS receipt_vouchers (\n      id SERIAL PRIMARY KEY,\n      entry_number VARCHAR(50) UNIQUE NOT NULL,\n      entry_date DATE NOT NULL,\n      payer_name VARCHAR(255) NOT NULL,\n      payer_type VARCHAR(50) DEFAULT 'external',\n      debit_account_id INTEGER,\n      credit_account_id INTEGER,\n      amount DECIMAL(15,2) NOT NULL,\n      description TEXT,\n      reference_number VARCHAR(100),\n      status VARCHAR(20) DEFAULT 'draft',\n      created_by VARCHAR(100) DEFAULT 'النظام',\n      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n}\n// توليد رقم سند جديد\nasync function generateVoucherNumber() {\n    const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number\n    FROM journal_entries\n    WHERE entry_number ~ '^RV[0-9]+$'\n  `);\n    const nextNumber = result.rows[0]?.next_number || 1;\n    return `RV${String(nextNumber).padStart(6, '0')}`;\n}\n// دالة لتوليد رقم قيد يومية جديد\nasync function generateJournalEntryNumber() {\n    const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number\n    FROM journal_entries\n    WHERE entry_number ~ '^JE[0-9]+$'\n  `);\n    const nextNumber = result.rows[0].next_number;\n    return `JE${nextNumber.toString().padStart(6, '0')}`;\n}\n// دالة لحفظ السند في القيود اليومية\nasync function createJournalEntryFromVoucher(voucher, voucherType) {\n    try {\n        // ضمان وجود جدول القيود اليومية\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        entry_date DATE NOT NULL,\n        description TEXT NOT NULL,\n        total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,\n        total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,\n        status VARCHAR(20) DEFAULT 'draft',\n        created_by VARCHAR(100) DEFAULT 'النظام',\n        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        voucher_type VARCHAR(20),\n        voucher_id INTEGER,\n        voucher_number VARCHAR(50)\n      )\n    `);\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE TABLE IF NOT EXISTS journal_entry_details (\n        id SERIAL PRIMARY KEY,\n        journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,\n        account_id INTEGER,\n        account_name VARCHAR(255),\n        debit_amount DECIMAL(15,2) DEFAULT 0,\n        credit_amount DECIMAL(15,2) DEFAULT 0,\n        description TEXT,\n        line_order INTEGER DEFAULT 1\n      )\n    `);\n        // توليد رقم قيد جديد\n        const entryNumber = await generateJournalEntryNumber();\n        // إنشاء القيد الرئيسي\n        const journalEntry = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entries (\n        entry_number, entry_date, description, total_debit, total_credit,\n        status, created_by, voucher_type, voucher_id, voucher_number\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)\n      RETURNING id\n    `, [\n            entryNumber,\n            voucher.entry_date,\n            `${voucherType === 'receipt' ? 'سند قبض' : 'سند صرف'} رقم ${voucher.entry_number} - ${voucher.description}`,\n            voucher.amount,\n            voucher.amount,\n            'draft',\n            'النظام',\n            voucherType,\n            voucher.id,\n            voucher.entry_number\n        ]);\n        const journalEntryId = journalEntry.rows[0].id;\n        // الحصول على أسماء الحسابات\n        const debitAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name FROM chart_of_accounts WHERE id = $1\n    `, [\n            voucher.debit_account_id\n        ]);\n        const creditAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name FROM chart_of_accounts WHERE id = $1\n    `, [\n            voucher.credit_account_id\n        ]);\n        // إنشاء السطر المدين\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7)\n    `, [\n            journalEntryId,\n            voucher.debit_account_id,\n            debitAccount.rows[0]?.account_name || 'حساب غير معروف',\n            voucher.amount,\n            0,\n            `${voucherType === 'receipt' ? 'استلام من' : 'دفع إلى'} ${voucher.payer_name}`,\n            1\n        ]);\n        // إنشاء السطر الدائن\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7)\n    `, [\n            journalEntryId,\n            voucher.credit_account_id,\n            creditAccount.rows[0]?.account_name || 'حساب غير معروف',\n            0,\n            voucher.amount,\n            `${voucherType === 'receipt' ? 'إيراد من' : 'مصروف لـ'} ${voucher.payer_name}`,\n            2\n        ]);\n        return entryNumber;\n    } catch (error) {\n        console.error('❌ خطأ في إنشاء القيد اليومية:', error);\n        throw error;\n    }\n}\n// GET - جلب جميع سندات القبض\nasync function GET(request) {\n    try {\n        await ensureReceiptVouchersTable();\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const dateFrom = searchParams.get('date_from');\n        const dateTo = searchParams.get('date_to');\n        let sql = `\n      SELECT\n        je.*,\n        (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count\n      FROM journal_entries je\n      WHERE je.entry_type = 'receipt'\n    `;\n        const params = [];\n        let paramIndex = 1;\n        // تصفية حسب الحالة\n        if (status && status !== 'all') {\n            sql += ` AND je.status = $${paramIndex}`;\n            params.push(status);\n            paramIndex++;\n        }\n        // تصفية حسب التاريخ\n        if (dateFrom) {\n            sql += ` AND je.entry_date >= $${paramIndex}`;\n            params.push(dateFrom);\n            paramIndex++;\n        }\n        if (dateTo) {\n            sql += ` AND je.entry_date <= $${paramIndex}`;\n            params.push(dateTo);\n            paramIndex++;\n        }\n        sql += ` ORDER BY je.entry_date DESC, je.entry_number DESC`;\n        console.log('🔍 SQL Query:', sql);\n        console.log('🔍 Parameters:', params);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(sql, params);\n        // جلب تفاصيل كل سند وتحويل البيانات للتوافق مع الواجهة\n        const vouchers = [];\n        for (const row of result.rows){\n            // جلب تفاصيل القيد\n            const detailsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        SELECT * FROM journal_entry_details\n        WHERE journal_entry_id = $1\n        ORDER BY line_order\n      `, [\n                row.id\n            ]);\n            // العثور على الحساب المدين والدائن\n            const debitDetail = detailsResult.rows.find((d)=>parseFloat(d.debit_amount) > 0);\n            const creditDetail = detailsResult.rows.find((d)=>parseFloat(d.credit_amount) > 0);\n            vouchers.push({\n                id: row.id,\n                entry_number: row.entry_number,\n                voucher_number: row.entry_number,\n                entry_date: row.entry_date,\n                voucher_date: row.entry_date,\n                amount: parseFloat(row.total_debit || 0),\n                total_debit: parseFloat(row.total_debit || 0),\n                payer_name: row.party_name,\n                payer_type: row.party_type,\n                party_name: row.party_name,\n                party_type: row.party_type,\n                description: row.description,\n                reference_number: row.reference_number,\n                status: row.status,\n                debit_account_id: debitDetail?.account_id,\n                credit_account_id: creditDetail?.account_id,\n                debit_account_name: debitDetail?.account_name,\n                debit_account_code: debitDetail?.account_code,\n                credit_account_name: creditDetail?.account_name,\n                credit_account_code: creditDetail?.account_code,\n                currency_id: 1,\n                payment_method_id: 1,\n                cost_center_id: null,\n                case_id: null,\n                service_id: null,\n                created_by: row.created_by,\n                created_date: row.created_date,\n                details: detailsResult.rows\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            vouchers,\n            total: vouchers.length,\n            message: 'تم جلب سندات القبض بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب سندات القبض:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب سندات القبض',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة سند قبض جديد\nasync function POST(request) {\n    try {\n        await ensureReceiptVouchersTable();\n        const body = await request.json();\n        console.log('📥 بيانات سند القبض المستلمة:', body);\n        const { entry_date, payer_name, payer_type = 'external', beneficiary_name, beneficiary_type, debit_account_id, credit_account_id, amount, description, reference_number, status = 'draft' } = body;\n        // دعم كلا من payer و beneficiary للتوافق مع النماذج المختلفة\n        const finalPayerName = payer_name || beneficiary_name;\n        const finalPayerType = beneficiary_type || payer_type || 'external';\n        // التحقق من البيانات المطلوبة\n        if (!entry_date || !finalPayerName || !debit_account_id || !credit_account_id || !amount) {\n            const missingFields = [];\n            if (!entry_date) missingFields.push('تاريخ السند');\n            if (!finalPayerName) missingFields.push('اسم الدافع');\n            if (!debit_account_id) missingFields.push('الحساب المدين');\n            if (!credit_account_id) missingFields.push('الحساب الدائن');\n            if (!amount) missingFields.push('المبلغ');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات المطلوبة مفقودة',\n                details: `الحقول المفقودة: ${missingFields.join(', ')}`\n            }, {\n                status: 400\n            });\n        }\n        // توليد رقم سند جديد\n        const entry_number = await generateVoucherNumber();\n        // إنشاء القيد في الهيكل الموحد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entries (\n        entry_number, entry_type, entry_date, description,\n        party_name, party_type, reference_number,\n        total_debit, total_credit, status, created_by\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\n      RETURNING *\n    `, [\n            entry_number,\n            'receipt',\n            entry_date,\n            description,\n            finalPayerName,\n            finalPayerType,\n            reference_number,\n            amount,\n            amount,\n            status,\n            'النظام'\n        ]);\n        const newEntry = result.rows[0];\n        const journalEntryId = newEntry.id;\n        // الحصول على بيانات الحسابات\n        const debitAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1\n    `, [\n            debit_account_id\n        ]);\n        const creditAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1\n    `, [\n            credit_account_id\n        ]);\n        // إضافة السطر المدين\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, account_code,\n        debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n    `, [\n            journalEntryId,\n            debit_account_id,\n            debitAccount.rows[0]?.account_name || 'حساب غير معروف',\n            debitAccount.rows[0]?.account_code || '',\n            amount,\n            0,\n            `استلام من ${finalPayerName}`,\n            1\n        ]);\n        // إضافة السطر الدائن\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, account_code,\n        debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n    `, [\n            journalEntryId,\n            credit_account_id,\n            creditAccount.rows[0]?.account_name || 'حساب غير معروف',\n            creditAccount.rows[0]?.account_code || '',\n            0,\n            amount,\n            `إيراد من ${finalPayerName}`,\n            2\n        ]);\n        // تحويل البيانات للتوافق مع الواجهة\n        const newVoucher = {\n            id: newEntry.id,\n            entry_number: newEntry.entry_number,\n            entry_date: newEntry.entry_date,\n            payer_name: newEntry.party_name,\n            payer_type: newEntry.party_type,\n            amount: newEntry.total_debit,\n            description: newEntry.description,\n            reference_number: newEntry.reference_number,\n            status: newEntry.status,\n            created_by: newEntry.created_by,\n            created_date: newEntry.created_date\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voucher: newVoucher,\n            message: `تم إنشاء سند القبض ${entry_number} بنجاح`\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إنشاء سند القبض:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء سند القبض',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث سند قبض\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, ...updateData } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف السند مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث السند\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE receipt_vouchers \n      SET \n        entry_date = COALESCE($2, entry_date),\n        payer_name = COALESCE($3, payer_name),\n        payer_type = COALESCE($4, payer_type),\n        debit_account_id = COALESCE($5, debit_account_id),\n        credit_account_id = COALESCE($6, credit_account_id),\n        amount = COALESCE($7, amount),\n        description = COALESCE($8, description),\n        reference_number = COALESCE($9, reference_number),\n        status = COALESCE($10, status),\n        updated_at = CURRENT_TIMESTAMP\n      WHERE id = $1\n      RETURNING *\n    `, [\n            id,\n            updateData.entry_date,\n            updateData.payer_name,\n            updateData.payer_type,\n            updateData.debit_account_id,\n            updateData.credit_account_id,\n            updateData.amount,\n            updateData.description,\n            updateData.reference_number,\n            updateData.status\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'السند غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voucher: result.rows[0],\n            message: 'تم تحديث سند القبض بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تحديث سند القبض:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث سند القبض',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/accounting/receipt-vouchers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&page=%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Freceipt-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();