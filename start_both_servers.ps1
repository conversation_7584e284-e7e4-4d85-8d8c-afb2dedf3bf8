# سكريپت تشغيل كلا الخادمين
# Start Both Servers Script

Write-Host "🌟 تشغيل نظامي إدارة المحاماة" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray

# التحقق من قواعد البيانات
Write-Host "`n🔍 التحقق من قواعد البيانات..." -ForegroundColor Yellow
$env:PGPASSWORD = "yemen123"

$mohammiExists = psql -U postgres -h localhost -lqt | Select-String "mohammi"
$rubaieExists = psql -U postgres -h localhost -lqt | Select-String "rubaie"

if (-not $mohammiExists) {
    Write-Host "❌ قاعدة البيانات 'mohammi' غير موجودة" -ForegroundColor Red
    exit 1
}

if (-not $rubaieExists) {
    Write-Host "❌ قاعدة البيانات 'rubaie' غير موجودة" -ForegroundColor Red
    Write-Host "💡 تشغيل نسخ قاعدة البيانات..." -ForegroundColor Yellow
    .\quick_copy_db.ps1
}

Write-Host "✅ قواعد البيانات متوفرة" -ForegroundColor Green

# إنشاء ملفات الإعدادات المؤقتة
Write-Host "`n📋 إعداد ملفات البيئة..." -ForegroundColor Yellow

# ملف إعدادات محمد
$mohammiEnv = @"
DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/mohammi
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mohammi
DB_USER=postgres
DB_PASSWORD=yemen123
PORT=7443
NODE_ENV=development
NEXTAUTH_SECRET=mohammi-secret-key-2025
NEXTAUTH_URL=http://localhost:7443
APP_NAME=نظام إدارة المحاماة - محمد
NEXT_TELEMETRY_DISABLED=1
"@

# ملف إعدادات الربعي
$rubaieEnv = @"
DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/rubaie
DB_HOST=localhost
DB_PORT=5432
DB_NAME=rubaie
DB_USER=postgres
DB_PASSWORD=yemen123
PORT=8914
NODE_ENV=development
NEXTAUTH_SECRET=rubaie-secret-key-2025
NEXTAUTH_URL=http://localhost:8914
APP_NAME=نظام إدارة المحاماة - الربعي
NEXT_TELEMETRY_DISABLED=1
"@

# حفظ ملفات الإعدادات
$mohammiEnv | Out-File -FilePath ".env.mohammi" -Encoding UTF8
$rubaieEnv | Out-File -FilePath ".env.rubaie" -Encoding UTF8

Write-Host "✅ تم إنشاء ملفات الإعدادات" -ForegroundColor Green

# دالة لتشغيل خادم
function Start-Server {
    param(
        [string]$Name,
        [int]$Port,
        [string]$Database,
        [string]$EnvFile
    )
    
    Write-Host "`n🚀 تشغيل $Name على المنفذ $Port..." -ForegroundColor Cyan
    
    # نسخ ملف الإعدادات
    Copy-Item $EnvFile ".env" -Force
    
    # تشغيل الخادم في نافذة منفصلة
    $processArgs = @{
        FilePath = "cmd"
        ArgumentList = "/c", "start", "powershell", "-NoExit", "-Command", "npm run dev"
        WindowStyle = "Normal"
    }
    
    Start-Process @processArgs
    
    Write-Host "✅ تم تشغيل $Name" -ForegroundColor Green
    Start-Sleep -Seconds 3
}

# تشغيل الخادمين
Write-Host "`n🌐 تشغيل الخوادم..." -ForegroundColor Yellow

# تشغيل خادم محمد
Start-Server -Name "نظام محمد" -Port 7443 -Database "mohammi" -EnvFile ".env.mohammi"

# تشغيل خادم الربعي
Start-Server -Name "نظام الربعي" -Port 8914 -Database "rubaie" -EnvFile ".env.rubaie"

Write-Host "`n🎉 تم تشغيل كلا النظامين!" -ForegroundColor Green
Write-Host "`n📋 ملخص الخوادم:" -ForegroundColor Cyan
Write-Host "   🔗 نظام محمد: http://localhost:7443 (قاعدة البيانات: mohammi)" -ForegroundColor White
Write-Host "   🔗 نظام الربعي: http://localhost:8914 (قاعدة البيانات: rubaie)" -ForegroundColor White

Write-Host "`n💡 ملاحظات:" -ForegroundColor Yellow
Write-Host "   - كل نظام يعمل في نافذة منفصلة" -ForegroundColor White
Write-Host "   - لإيقاف نظام: أغلق النافذة المخصصة له" -ForegroundColor White
Write-Host "   - لإعادة التشغيل: شغل هذا السكريپت مرة أخرى" -ForegroundColor White

Read-Host "`nاضغط Enter للخروج..."
