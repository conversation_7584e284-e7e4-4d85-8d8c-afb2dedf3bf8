import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع أنواع القضايا من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM issue_types ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching أنواع القضايا:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات أنواع القضايا',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة أنواع القضايا جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة أنواع القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error creating أنواع القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة أنواع القضايا' },
      { status: 500 }
    )
  }
}

// PUT - تحديث أنواع القضايا
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث أنواع القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error updating أنواع القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث أنواع القضايا' },
      { status: 500 }
    )
  }
}

// DELETE - حذف أنواع القضايا
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف أنواع القضايا مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM issue_types WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف أنواع القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error deleting أنواع القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف أنواع القضايا' },
      { status: 500 }
    )
  }
}