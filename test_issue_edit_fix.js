// اختبار إصلاح تعديل القضايا
async function testIssueEditFix() {
  console.log('🧪 اختبار إصلاح تعديل القضايا...\n');

  // 1. جلب قضية محددة للاختبار
  console.log('1️⃣ جلب قضية محددة للاختبار:');
  try {
    // جلب جميع القضايا أولاً
    const issuesResponse = await fetch('http://localhost:7443/api/issues');
    const issuesResult = await issuesResponse.json();
    
    if (issuesResult.success && issuesResult.data.length > 0) {
      // البحث عن القضية CASE-2024-005
      const targetIssue = issuesResult.data.find(issue => 
        issue.case_number === 'CASE-2024-005'
      );
      
      if (targetIssue) {
        console.log('   ✅ تم العثور على القضية CASE-2024-005');
        console.log('   📋 بيانات القضية:');
        console.log(`      - رقم القضية: ${targetIssue.case_number}`);
        console.log(`      - العنوان: ${targetIssue.title}`);
        console.log(`      - الموكل: ${targetIssue.client_name}`);
        console.log(`      - الهاتف: ${targetIssue.client_phone || 'فارغ'}`);
        console.log(`      - المحكمة: ${targetIssue.court_name || 'فارغ'}`);
        console.log(`      - نوع القضية: ${targetIssue.issue_type || 'فارغ'}`);
        console.log(`      - الحالة: ${targetIssue.status || 'فارغ'}`);
        console.log(`      - المبلغ: ${targetIssue.amount || 'فارغ'}`);
        console.log(`      - طريقة التعاقد: ${targetIssue.contract_method || 'فارغ'}`);
        
        // حفظ معرف القضية للاختبارات اللاحقة
        global.testIssueId = targetIssue.id;
        global.testIssueData = targetIssue;
      } else {
        console.log('   ⚠️ لم يتم العثور على القضية CASE-2024-005');
        // استخدام أول قضية متاحة
        if (issuesResult.data.length > 0) {
          global.testIssueId = issuesResult.data[0].id;
          global.testIssueData = issuesResult.data[0];
          console.log(`   📋 استخدام القضية: ${issuesResult.data[0].case_number}`);
        }
      }
    } else {
      console.log('   ❌ لا توجد قضايا للاختبار');
      return;
    }
  } catch (error) {
    console.log(`   ❌ خطأ في جلب القضايا: ${error.message}`);
    return;
  }

  console.log('');

  // 2. جلب القضية بشكل منفرد
  console.log('2️⃣ جلب القضية بشكل منفرد:');
  try {
    const response = await fetch(`http://localhost:7443/api/issues/${global.testIssueId}`);
    const result = await response.json();
    
    if (result.success) {
      console.log('   ✅ تم جلب القضية بنجاح');
      console.log('   📋 البيانات المُرجعة من API:');
      const issue = result.data;
      console.log(`      - رقم القضية: ${issue.case_number}`);
      console.log(`      - العنوان: ${issue.title}`);
      console.log(`      - الموكل: ${issue.client_name}`);
      console.log(`      - الهاتف: ${issue.client_phone || 'null/undefined'}`);
      console.log(`      - المحكمة: ${issue.court_name || 'null/undefined'}`);
      console.log(`      - نوع القضية: ${issue.issue_type || 'null/undefined'}`);
      console.log(`      - الحالة: ${issue.status || 'null/undefined'}`);
      console.log(`      - المبلغ: ${issue.amount || 'null/undefined'}`);
      console.log(`      - طريقة التعاقد: ${issue.contract_method || 'null/undefined'}`);
      
      // فحص البيانات الفارغة
      const emptyFields = [];
      if (!issue.client_phone || issue.client_phone.trim() === '') emptyFields.push('الهاتف');
      if (!issue.court_name || issue.court_name.trim() === '') emptyFields.push('المحكمة');
      if (!issue.issue_type || issue.issue_type.trim() === '') emptyFields.push('نوع القضية');
      if (!issue.status || issue.status.trim() === '') emptyFields.push('الحالة');
      if (!issue.contract_method || issue.contract_method.trim() === '') emptyFields.push('طريقة التعاقد');
      
      if (emptyFields.length > 0) {
        console.log(`   ⚠️ الحقول الفارغة: ${emptyFields.join(', ')}`);
      } else {
        console.log('   ✅ جميع الحقول مملوءة');
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ: ${error.message}`);
  }

  console.log('');

  // 3. محاكاة تحديث القضية
  console.log('3️⃣ محاكاة تحديث القضية:');
  try {
    const updateData = {
      case_number: global.testIssueData.case_number,
      title: global.testIssueData.title,
      description: global.testIssueData.description || 'وصف محدث',
      client_id: global.testIssueData.client_id,
      client_name: global.testIssueData.client_name,
      client_phone: global.testIssueData.client_phone || '777888999', // إضافة رقم هاتف
      court_name: global.testIssueData.court_name || 'المحكمة الجنائية', // إضافة محكمة
      issue_type: global.testIssueData.issue_type || 'جنائي', // إضافة نوع
      status: global.testIssueData.status || 'active', // إضافة حالة
      amount: global.testIssueData.amount || 100000,
      notes: global.testIssueData.notes || 'ملاحظات محدثة',
      contract_method: global.testIssueData.contract_method || 'بالعقد', // إضافة طريقة تعاقد
      contract_date: global.testIssueData.contract_date || new Date().toISOString().split('T')[0]
    };

    console.log('   📤 إرسال بيانات التحديث...');
    
    const response = await fetch(`http://localhost:7443/api/issues/${global.testIssueId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('   ✅ تم تحديث القضية بنجاح');
      console.log('   📋 البيانات بعد التحديث:');
      const updated = result.data;
      console.log(`      - الهاتف: ${updated.client_phone}`);
      console.log(`      - المحكمة: ${updated.court_name}`);
      console.log(`      - نوع القضية: ${updated.issue_type}`);
      console.log(`      - الحالة: ${updated.status}`);
      console.log(`      - طريقة التعاقد: ${updated.contract_method}`);
    } else {
      console.log(`   ❌ فشل التحديث: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في التحديث: ${error.message}`);
  }

  console.log('');

  // 4. التحقق النهائي
  console.log('4️⃣ التحقق النهائي من البيانات:');
  try {
    const response = await fetch(`http://localhost:7443/api/issues/${global.testIssueId}`);
    const result = await response.json();
    
    if (result.success) {
      const issue = result.data;
      console.log('   📋 البيانات النهائية:');
      console.log(`      - الهاتف: ${issue.client_phone || 'لا يزال فارغ'}`);
      console.log(`      - المحكمة: ${issue.court_name || 'لا يزال فارغ'}`);
      console.log(`      - نوع القضية: ${issue.issue_type || 'لا يزال فارغ'}`);
      console.log(`      - الحالة: ${issue.status || 'لا يزال فارغ'}`);
      console.log(`      - طريقة التعاقد: ${issue.contract_method || 'لا يزال فارغ'}`);
      
      // تقييم النتائج
      const filledFields = [];
      if (issue.client_phone && issue.client_phone.trim() !== '') filledFields.push('الهاتف');
      if (issue.court_name && issue.court_name.trim() !== '') filledFields.push('المحكمة');
      if (issue.issue_type && issue.issue_type.trim() !== '') filledFields.push('نوع القضية');
      if (issue.status && issue.status.trim() !== '') filledFields.push('الحالة');
      if (issue.contract_method && issue.contract_method.trim() !== '') filledFields.push('طريقة التعاقد');
      
      console.log(`   ✅ الحقول المملوءة: ${filledFields.join(', ')}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في التحقق النهائي: ${error.message}`);
  }

  console.log('\n✅ انتهى اختبار إصلاح تعديل القضايا');
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('   1. ✅ إضافة قيم افتراضية للحقول الفارغة في الواجهة');
  console.log('   2. ✅ إضافة تسجيل مفصل لتشخيص المشاكل');
  console.log('   3. ✅ إضافة key للنموذج لإجبار إعادة التحديث');
  console.log('   4. ✅ تحسين معالجة البيانات null/undefined');
}

// تشغيل الاختبار
testIssueEditFix().catch(console.error);
