/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/accounting/chart-of-accounts/route";
exports.ids = ["app/api/accounting/chart-of-accounts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&page=%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&page=%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_accounting_chart_of_accounts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/accounting/chart-of-accounts/route.ts */ \"(rsc)/./src/app/api/accounting/chart-of-accounts/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_accounting_chart_of_accounts_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_accounting_chart_of_accounts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/accounting/chart-of-accounts/route\",\n        pathname: \"/api/accounting/chart-of-accounts\",\n        filename: \"route\",\n        bundlePath: \"app/api/accounting/chart-of-accounts/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\accounting\\\\chart-of-accounts\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_accounting_chart_of_accounts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&page=%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/accounting/chart-of-accounts/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/accounting/chart-of-accounts/route.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع الحسابات\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const level = searchParams.get('level');\n        const includeLinked = searchParams.get('include_linked') === 'true';\n        const excludeLinkedTables = searchParams.get('exclude_linked_tables') === 'true';\n        const onlyTransactional = searchParams.get('only_transactional') === 'true';\n        let sql = `\n      SELECT\n        coa.id,\n        coa.account_code,\n        coa.account_name,\n        coa.account_name_en,\n        coa.level_1_code,\n        coa.level_2_code,\n        coa.level_3_code,\n        coa.level_4_code,\n        coa.account_level,\n        coa.parent_id,\n        coa.account_type,\n        coa.account_nature,\n        coa.is_active,\n        coa.allow_transactions,\n        coa.linked_table,\n        coa.auto_create_sub_accounts,\n        coa.opening_balance,\n        coa.current_balance,\n        coa.description,\n        coa.created_date,\n        coa.is_linked_record,\n        coa.original_table,\n        coa.linked_record_id\n      FROM chart_of_accounts coa\n      WHERE coa.is_active = true\n    `;\n        // إخفاء الجداول المربوطة مباشرة (العملاء، الموظفين، الموردين)\n        if (excludeLinkedTables) {\n            sql += ` AND (coa.is_linked_record IS NULL OR coa.is_linked_record = false)`;\n        }\n        const params = [];\n        let paramIndex = 1;\n        // تصفية حسب المستوى\n        if (level && level !== 'all') {\n            sql += ` AND coa.account_level = $${paramIndex}`;\n            params.push(parseInt(level));\n            paramIndex++;\n        }\n        // تصفية للحسابات التي تقبل معاملات فقط\n        if (onlyTransactional) {\n            sql += ` AND coa.allow_transactions = true`;\n        }\n        sql += ` ORDER BY coa.account_code`;\n        console.log('🔍 Executing SQL:', sql);\n        console.log('🔍 With params:', params);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(sql, params);\n        let accounts = result.rows;\n        // ملاحظة: تم إزالة عرض العملاء والموظفين والموردين من دليل الحسابات\n        // سيتم ربطهم يدوياً من خلال تعديل الحسابات\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            accounts,\n            total: accounts.length,\n            message: 'تم جلب الحسابات بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في جلب الحسابات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الحسابات',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة حساب جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { account_code, account_name, account_name_en, account_level, parent_id, account_type, account_nature = 'مدين', linked_table, auto_create_sub_accounts = false, allow_transactions = false, opening_balance = 0, description } = body;\n        // التحقق من صحة البيانات\n        if (!account_code || !account_name || !account_level || !account_type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات المطلوبة مفقودة'\n            }, {\n                status: 400\n            });\n        }\n        // منع تفعيل المعاملات على حسابات التحكم\n        if (allow_transactions && linked_table && linked_table !== 'none') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن تفعيل المعاملات على حسابات التحكم المرتبطة بجداول خارجية'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم تكرار رمز الحساب\n        const existingAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM chart_of_accounts WHERE account_code = $1', [\n            account_code\n        ]);\n        if (existingAccount.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز الحساب موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد أكواد المستويات\n        let level_1_code = null, level_2_code = null, level_3_code = null, level_4_code = null;\n        if (account_level === 1) {\n            level_1_code = account_code;\n        } else if (account_level === 2) {\n            level_1_code = account_code.substring(0, 2);\n            level_2_code = account_code;\n        } else if (account_level === 3) {\n            level_1_code = account_code.substring(0, 2);\n            level_2_code = account_code.substring(0, 4);\n            level_3_code = account_code;\n        } else if (account_level === 4) {\n            level_1_code = account_code.substring(0, 2);\n            level_2_code = account_code.substring(0, 4);\n            level_3_code = account_code.substring(0, 6);\n            level_4_code = account_code;\n        }\n        // استخدام القيمة المرسلة من النموذج أو افتراضياً حسب المستوى\n        const finalAllowTransactions = allow_transactions !== undefined ? allow_transactions : account_level === 4;\n        // إدراج الحساب الجديد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO chart_of_accounts (\n        account_code, account_name, account_name_en,\n        level_1_code, level_2_code, level_3_code, level_4_code,\n        account_level, parent_id, account_type, account_nature,\n        allow_transactions, linked_table, auto_create_sub_accounts,\n        opening_balance, current_balance, description\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)\n      RETURNING *\n    `, [\n            account_code,\n            account_name,\n            account_name_en,\n            level_1_code,\n            level_2_code,\n            level_3_code,\n            level_4_code,\n            account_level,\n            parent_id,\n            account_type,\n            account_nature,\n            finalAllowTransactions,\n            linked_table,\n            auto_create_sub_accounts,\n            opening_balance,\n            opening_balance,\n            description\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            account: result.rows[0],\n            message: 'تم إضافة الحساب بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة الحساب:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الحساب',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث حساب\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, account_name, account_name_en, account_type, account_nature, linked_table, auto_create_sub_accounts, description, is_active = true } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الحساب مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE chart_of_accounts\n      SET\n        account_name = $2,\n        account_name_en = $3,\n        account_type = $4,\n        account_nature = $5,\n        linked_table = $6,\n        auto_create_sub_accounts = $7,\n        description = $8,\n        is_active = $9,\n        updated_date = CURRENT_TIMESTAMP\n      WHERE id = $1\n      RETURNING *\n    `, [\n            id,\n            account_name,\n            account_name_en,\n            account_type,\n            account_nature,\n            linked_table,\n            auto_create_sub_accounts,\n            description,\n            is_active\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الحساب غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            account: result.rows[0],\n            message: 'تم تحديث الحساب بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث الحساب:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث الحساب',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hY2NvdW50aW5nL2NoYXJ0LW9mLWFjY291bnRzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ3ZCO0FBRWhDLDBCQUEwQjtBQUNuQixlQUFlRSxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBRUYsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO1FBQzVDLE1BQU1DLFFBQVFILGFBQWFJLEdBQUcsQ0FBQztRQUMvQixNQUFNQyxnQkFBZ0JMLGFBQWFJLEdBQUcsQ0FBQyxzQkFBc0I7UUFDN0QsTUFBTUUsc0JBQXNCTixhQUFhSSxHQUFHLENBQUMsNkJBQTZCO1FBQzFFLE1BQU1HLG9CQUFvQlAsYUFBYUksR0FBRyxDQUFDLDBCQUEwQjtRQUlyRSxJQUFJSSxNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQTJCWCxDQUFDO1FBRUQsOERBQThEO1FBQzlELElBQUlGLHFCQUFxQjtZQUN2QkUsT0FBTyxDQUFDLG1FQUFtRSxDQUFDO1FBQzlFO1FBRUEsTUFBTUMsU0FBZ0IsRUFBRTtRQUN4QixJQUFJQyxhQUFhO1FBRWpCLG9CQUFvQjtRQUNwQixJQUFJUCxTQUFTQSxVQUFVLE9BQU87WUFDNUJLLE9BQU8sQ0FBQywwQkFBMEIsRUFBRUUsWUFBWTtZQUNoREQsT0FBT0UsSUFBSSxDQUFDQyxTQUFTVDtZQUNyQk87UUFDRjtRQUVBLHVDQUF1QztRQUN2QyxJQUFJSCxtQkFBbUI7WUFDckJDLE9BQU8sQ0FBQyxrQ0FBa0MsQ0FBQztRQUM3QztRQUVBQSxPQUFPLENBQUMsMEJBQTBCLENBQUM7UUFFbkNLLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJOO1FBQ2pDSyxRQUFRQyxHQUFHLENBQUMsbUJBQW1CTDtRQUUvQixNQUFNTSxTQUFTLE1BQU1sQiw4Q0FBS0EsQ0FBQ1csS0FBS0M7UUFDaEMsSUFBSU8sV0FBV0QsT0FBT0UsSUFBSTtRQUkxQixvRUFBb0U7UUFDcEUsMkNBQTJDO1FBRzNDLE9BQU9yQixxREFBWUEsQ0FBQ3NCLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUSDtZQUNBSSxPQUFPSixTQUFTSyxNQUFNO1lBQ3RCQyxTQUFTO1FBQ1g7SUFFRixFQUFFLE9BQU9DLE9BQU87UUFDZFYsUUFBUVUsS0FBSyxDQUFDLHdCQUF3QkE7UUFDdEMsT0FBTzNCLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RJLE9BQU87WUFDUEMsU0FBU0QsaUJBQWlCRSxRQUFRRixNQUFNRCxPQUFPLEdBQUc7UUFDcEQsR0FBRztZQUFFSSxRQUFRO1FBQUk7SUFDbkI7QUFDRjtBQUVBLHlCQUF5QjtBQUNsQixlQUFlQyxLQUFLNUIsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU02QixPQUFPLE1BQU03QixRQUFRbUIsSUFBSTtRQUMvQixNQUFNLEVBQ0pXLFlBQVksRUFDWkMsWUFBWSxFQUNaQyxlQUFlLEVBQ2ZDLGFBQWEsRUFDYkMsU0FBUyxFQUNUQyxZQUFZLEVBQ1pDLGlCQUFpQixNQUFNLEVBQ3ZCQyxZQUFZLEVBQ1pDLDJCQUEyQixLQUFLLEVBQ2hDQyxxQkFBcUIsS0FBSyxFQUMxQkMsa0JBQWtCLENBQUMsRUFDbkJDLFdBQVcsRUFDWixHQUFHWjtRQUVKLHlCQUF5QjtRQUN6QixJQUFJLENBQUNDLGdCQUFnQixDQUFDQyxnQkFBZ0IsQ0FBQ0UsaUJBQWlCLENBQUNFLGNBQWM7WUFDckUsT0FBT3RDLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO2dCQUN2QkMsU0FBUztnQkFDVEksT0FBTztZQUNULEdBQUc7Z0JBQUVHLFFBQVE7WUFBSTtRQUNuQjtRQUVBLHdDQUF3QztRQUN4QyxJQUFJWSxzQkFBc0JGLGdCQUFnQkEsaUJBQWlCLFFBQVE7WUFDakUsT0FBT3hDLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO2dCQUN2QkMsU0FBUztnQkFDVEksT0FBTztZQUNULEdBQUc7Z0JBQUVHLFFBQVE7WUFBSTtRQUNuQjtRQUVBLGlDQUFpQztRQUNqQyxNQUFNZSxrQkFBa0IsTUFBTTVDLDhDQUFLQSxDQUNqQyw0REFDQTtZQUFDZ0M7U0FBYTtRQUdoQixJQUFJWSxnQkFBZ0J4QixJQUFJLENBQUNJLE1BQU0sR0FBRyxHQUFHO1lBQ25DLE9BQU96QixxREFBWUEsQ0FBQ3NCLElBQUksQ0FBQztnQkFDdkJDLFNBQVM7Z0JBQ1RJLE9BQU87WUFDVCxHQUFHO2dCQUFFRyxRQUFRO1lBQUk7UUFDbkI7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSWdCLGVBQWUsTUFBTUMsZUFBZSxNQUFNQyxlQUFlLE1BQU1DLGVBQWU7UUFFbEYsSUFBSWIsa0JBQWtCLEdBQUc7WUFDdkJVLGVBQWViO1FBQ2pCLE9BQU8sSUFBSUcsa0JBQWtCLEdBQUc7WUFDOUJVLGVBQWViLGFBQWFpQixTQUFTLENBQUMsR0FBRztZQUN6Q0gsZUFBZWQ7UUFDakIsT0FBTyxJQUFJRyxrQkFBa0IsR0FBRztZQUM5QlUsZUFBZWIsYUFBYWlCLFNBQVMsQ0FBQyxHQUFHO1lBQ3pDSCxlQUFlZCxhQUFhaUIsU0FBUyxDQUFDLEdBQUc7WUFDekNGLGVBQWVmO1FBQ2pCLE9BQU8sSUFBSUcsa0JBQWtCLEdBQUc7WUFDOUJVLGVBQWViLGFBQWFpQixTQUFTLENBQUMsR0FBRztZQUN6Q0gsZUFBZWQsYUFBYWlCLFNBQVMsQ0FBQyxHQUFHO1lBQ3pDRixlQUFlZixhQUFhaUIsU0FBUyxDQUFDLEdBQUc7WUFDekNELGVBQWVoQjtRQUNqQjtRQUVBLDZEQUE2RDtRQUM3RCxNQUFNa0IseUJBQXlCVCx1QkFBdUJVLFlBQVlWLHFCQUFzQk4sa0JBQWtCO1FBRTFHLHNCQUFzQjtRQUN0QixNQUFNakIsU0FBUyxNQUFNbEIsOENBQUtBLENBQUMsQ0FBQzs7Ozs7Ozs7O0lBUzVCLENBQUMsRUFBRTtZQUNEZ0M7WUFBY0M7WUFBY0M7WUFDNUJXO1lBQWNDO1lBQWNDO1lBQWNDO1lBQzFDYjtZQUFlQztZQUFXQztZQUFjQztZQUN4Q1k7WUFBd0JYO1lBQWNDO1lBQ3RDRTtZQUFpQkE7WUFBaUJDO1NBQ25DO1FBRUQsT0FBTzVDLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1Q4QixTQUFTbEMsT0FBT0UsSUFBSSxDQUFDLEVBQUU7WUFDdkJLLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT0MsT0FBTztRQUNkVixRQUFRVSxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxPQUFPM0IscURBQVlBLENBQUNzQixJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEksT0FBTztZQUNQQyxTQUFTRCxpQkFBaUJFLFFBQVFGLE1BQU1ELE9BQU8sR0FBRztRQUNwRCxHQUFHO1lBQUVJLFFBQVE7UUFBSTtJQUNuQjtBQUNGO0FBRUEsbUJBQW1CO0FBQ1osZUFBZXdCLElBQUluRCxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTTZCLE9BQU8sTUFBTTdCLFFBQVFtQixJQUFJO1FBQy9CLE1BQU0sRUFDSmlDLEVBQUUsRUFDRnJCLFlBQVksRUFDWkMsZUFBZSxFQUNmRyxZQUFZLEVBQ1pDLGNBQWMsRUFDZEMsWUFBWSxFQUNaQyx3QkFBd0IsRUFDeEJHLFdBQVcsRUFDWFksWUFBWSxJQUFJLEVBQ2pCLEdBQUd4QjtRQUVKLElBQUksQ0FBQ3VCLElBQUk7WUFDUCxPQUFPdkQscURBQVlBLENBQUNzQixJQUFJLENBQUM7Z0JBQ3ZCQyxTQUFTO2dCQUNUSSxPQUFPO1lBQ1QsR0FBRztnQkFBRUcsUUFBUTtZQUFJO1FBQ25CO1FBRUEsTUFBTVgsU0FBUyxNQUFNbEIsOENBQUtBLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7SUFjNUIsQ0FBQyxFQUFFO1lBQ0RzRDtZQUFJckI7WUFBY0M7WUFBaUJHO1lBQWNDO1lBQ2pEQztZQUFjQztZQUEwQkc7WUFBYVk7U0FDdEQ7UUFFRCxJQUFJckMsT0FBT0UsSUFBSSxDQUFDSSxNQUFNLEtBQUssR0FBRztZQUM1QixPQUFPekIscURBQVlBLENBQUNzQixJQUFJLENBQUM7Z0JBQ3ZCQyxTQUFTO2dCQUNUSSxPQUFPO1lBQ1QsR0FBRztnQkFBRUcsUUFBUTtZQUFJO1FBQ25CO1FBRUEsT0FBTzlCLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1Q4QixTQUFTbEMsT0FBT0UsSUFBSSxDQUFDLEVBQUU7WUFDdkJLLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT0MsT0FBTztRQUNkVixRQUFRVSxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxPQUFPM0IscURBQVlBLENBQUNzQixJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEksT0FBTztZQUNQQyxTQUFTRCxpQkFBaUJFLFFBQVFGLE1BQU1ELE9BQU8sR0FBRztRQUNwRCxHQUFHO1lBQUVJLFFBQVE7UUFBSTtJQUNuQjtBQUNGIiwic291cmNlcyI6WyJEOlxcbW9oYW1pbmV3XFxzcmNcXGFwcFxcYXBpXFxhY2NvdW50aW5nXFxjaGFydC1vZi1hY2NvdW50c1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgcXVlcnkgfSBmcm9tICdAL2xpYi9kYidcblxuLy8gR0VUIC0g2KzZhNioINis2YXZiti5INin2YTYrdiz2KfYqNin2KpcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybClcbiAgICBjb25zdCBsZXZlbCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2xldmVsJylcbiAgICBjb25zdCBpbmNsdWRlTGlua2VkID0gc2VhcmNoUGFyYW1zLmdldCgnaW5jbHVkZV9saW5rZWQnKSA9PT0gJ3RydWUnXG4gICAgY29uc3QgZXhjbHVkZUxpbmtlZFRhYmxlcyA9IHNlYXJjaFBhcmFtcy5nZXQoJ2V4Y2x1ZGVfbGlua2VkX3RhYmxlcycpID09PSAndHJ1ZSdcbiAgICBjb25zdCBvbmx5VHJhbnNhY3Rpb25hbCA9IHNlYXJjaFBhcmFtcy5nZXQoJ29ubHlfdHJhbnNhY3Rpb25hbCcpID09PSAndHJ1ZSdcblxuXG5cbiAgICBsZXQgc3FsID0gYFxuICAgICAgU0VMRUNUXG4gICAgICAgIGNvYS5pZCxcbiAgICAgICAgY29hLmFjY291bnRfY29kZSxcbiAgICAgICAgY29hLmFjY291bnRfbmFtZSxcbiAgICAgICAgY29hLmFjY291bnRfbmFtZV9lbixcbiAgICAgICAgY29hLmxldmVsXzFfY29kZSxcbiAgICAgICAgY29hLmxldmVsXzJfY29kZSxcbiAgICAgICAgY29hLmxldmVsXzNfY29kZSxcbiAgICAgICAgY29hLmxldmVsXzRfY29kZSxcbiAgICAgICAgY29hLmFjY291bnRfbGV2ZWwsXG4gICAgICAgIGNvYS5wYXJlbnRfaWQsXG4gICAgICAgIGNvYS5hY2NvdW50X3R5cGUsXG4gICAgICAgIGNvYS5hY2NvdW50X25hdHVyZSxcbiAgICAgICAgY29hLmlzX2FjdGl2ZSxcbiAgICAgICAgY29hLmFsbG93X3RyYW5zYWN0aW9ucyxcbiAgICAgICAgY29hLmxpbmtlZF90YWJsZSxcbiAgICAgICAgY29hLmF1dG9fY3JlYXRlX3N1Yl9hY2NvdW50cyxcbiAgICAgICAgY29hLm9wZW5pbmdfYmFsYW5jZSxcbiAgICAgICAgY29hLmN1cnJlbnRfYmFsYW5jZSxcbiAgICAgICAgY29hLmRlc2NyaXB0aW9uLFxuICAgICAgICBjb2EuY3JlYXRlZF9kYXRlLFxuICAgICAgICBjb2EuaXNfbGlua2VkX3JlY29yZCxcbiAgICAgICAgY29hLm9yaWdpbmFsX3RhYmxlLFxuICAgICAgICBjb2EubGlua2VkX3JlY29yZF9pZFxuICAgICAgRlJPTSBjaGFydF9vZl9hY2NvdW50cyBjb2FcbiAgICAgIFdIRVJFIGNvYS5pc19hY3RpdmUgPSB0cnVlXG4gICAgYFxuXG4gICAgLy8g2KXYrtmB2KfYoSDYp9mE2KzYr9in2YjZhCDYp9mE2YXYsdio2YjYt9ipINmF2KjYp9i02LHYqSAo2KfZhNi52YXZhNin2KHYjCDYp9mE2YXZiNi42YHZitmG2Iwg2KfZhNmF2YjYsdiv2YrZhilcbiAgICBpZiAoZXhjbHVkZUxpbmtlZFRhYmxlcykge1xuICAgICAgc3FsICs9IGAgQU5EIChjb2EuaXNfbGlua2VkX3JlY29yZCBJUyBOVUxMIE9SIGNvYS5pc19saW5rZWRfcmVjb3JkID0gZmFsc2UpYFxuICAgIH1cblxuICAgIGNvbnN0IHBhcmFtczogYW55W10gPSBbXVxuICAgIGxldCBwYXJhbUluZGV4ID0gMVxuXG4gICAgLy8g2KrYtdmB2YrYqSDYrdiz2Kgg2KfZhNmF2LPYqtmI2YlcbiAgICBpZiAobGV2ZWwgJiYgbGV2ZWwgIT09ICdhbGwnKSB7XG4gICAgICBzcWwgKz0gYCBBTkQgY29hLmFjY291bnRfbGV2ZWwgPSAkJHtwYXJhbUluZGV4fWBcbiAgICAgIHBhcmFtcy5wdXNoKHBhcnNlSW50KGxldmVsKSlcbiAgICAgIHBhcmFtSW5kZXgrK1xuICAgIH1cblxuICAgIC8vINiq2LXZgdmK2Kkg2YTZhNit2LPYp9io2KfYqiDYp9mE2KrZiiDYqtmC2KjZhCDZhdi52KfZhdmE2KfYqiDZgdmC2LdcbiAgICBpZiAob25seVRyYW5zYWN0aW9uYWwpIHtcbiAgICAgIHNxbCArPSBgIEFORCBjb2EuYWxsb3dfdHJhbnNhY3Rpb25zID0gdHJ1ZWBcbiAgICB9XG5cbiAgICBzcWwgKz0gYCBPUkRFUiBCWSBjb2EuYWNjb3VudF9jb2RlYFxuXG4gICAgY29uc29sZS5sb2coJ/CflI0gRXhlY3V0aW5nIFNRTDonLCBzcWwpXG4gICAgY29uc29sZS5sb2coJ/CflI0gV2l0aCBwYXJhbXM6JywgcGFyYW1zKVxuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoc3FsLCBwYXJhbXMpXG4gICAgbGV0IGFjY291bnRzID0gcmVzdWx0LnJvd3NcblxuICAgIFxuXG4gICAgLy8g2YXZhNin2K3YuNipOiDYqtmFINil2LLYp9mE2Kkg2LnYsdi2INin2YTYudmF2YTYp9ihINmI2KfZhNmF2YjYuNmB2YrZhiDZiNin2YTZhdmI2LHYr9mK2YYg2YXZhiDYr9mE2YrZhCDYp9mE2K3Ys9in2KjYp9iqXG4gICAgLy8g2LPZitiq2YUg2LHYqNi32YfZhSDZitiv2YjZitin2Ysg2YXZhiDYrtmE2KfZhCDYqti52K/ZitmEINin2YTYrdiz2KfYqNin2KpcbiAgICBcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgYWNjb3VudHMsXG4gICAgICB0b3RhbDogYWNjb3VudHMubGVuZ3RoLFxuICAgICAgbWVzc2FnZTogJ9iq2YUg2KzZhNioINin2YTYrdiz2KfYqNin2Kog2KjZhtis2KfYrSdcbiAgICB9KVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KzZhNioINin2YTYrdiz2KfYqNin2Ko6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6ICfZgdi02YQg2YHZiiDYrNmE2Kgg2KfZhNit2LPYp9io2KfYqicsXG4gICAgICBkZXRhaWxzOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfYrti32KMg2LrZitixINmF2LnYsdmI2YEnXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICB9XG59XG5cbi8vIFBPU1QgLSDYpdi22KfZgdipINit2LPYp9ioINis2K/ZitivXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuICAgIGNvbnN0IHtcbiAgICAgIGFjY291bnRfY29kZSxcbiAgICAgIGFjY291bnRfbmFtZSxcbiAgICAgIGFjY291bnRfbmFtZV9lbixcbiAgICAgIGFjY291bnRfbGV2ZWwsXG4gICAgICBwYXJlbnRfaWQsXG4gICAgICBhY2NvdW50X3R5cGUsXG4gICAgICBhY2NvdW50X25hdHVyZSA9ICfZhdiv2YrZhicsXG4gICAgICBsaW5rZWRfdGFibGUsXG4gICAgICBhdXRvX2NyZWF0ZV9zdWJfYWNjb3VudHMgPSBmYWxzZSxcbiAgICAgIGFsbG93X3RyYW5zYWN0aW9ucyA9IGZhbHNlLFxuICAgICAgb3BlbmluZ19iYWxhbmNlID0gMCxcbiAgICAgIGRlc2NyaXB0aW9uXG4gICAgfSA9IGJvZHlcblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINi12K3YqSDYp9mE2KjZitin2YbYp9iqXG4gICAgaWYgKCFhY2NvdW50X2NvZGUgfHwgIWFjY291bnRfbmFtZSB8fCAhYWNjb3VudF9sZXZlbCB8fCAhYWNjb3VudF90eXBlKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICfYp9mE2KjZitin2YbYp9iqINin2YTZhdi32YTZiNio2Kkg2YXZgdmC2YjYr9ipJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KVxuICAgIH1cblxuICAgIC8vINmF2YbYuSDYqtmB2LnZitmEINin2YTZhdi52KfZhdmE2KfYqiDYudmE2Ykg2K3Ys9in2KjYp9iqINin2YTYqtit2YPZhVxuICAgIGlmIChhbGxvd190cmFuc2FjdGlvbnMgJiYgbGlua2VkX3RhYmxlICYmIGxpbmtlZF90YWJsZSAhPT0gJ25vbmUnKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICfZhNinINmK2YXZg9mGINiq2YHYudmK2YQg2KfZhNmF2LnYp9mF2YTYp9iqINi52YTZiSDYrdiz2KfYqNin2Kog2KfZhNiq2K3Zg9mFINin2YTZhdix2KrYqNi32Kkg2KjYrNiv2KfZiNmEINiu2KfYsdis2YrYqSdcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSlcbiAgICB9XG5cbiAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYudiv2YUg2KrZg9ix2KfYsSDYsdmF2LIg2KfZhNit2LPYp9ioXG4gICAgY29uc3QgZXhpc3RpbmdBY2NvdW50ID0gYXdhaXQgcXVlcnkoXG4gICAgICAnU0VMRUNUIGlkIEZST00gY2hhcnRfb2ZfYWNjb3VudHMgV0hFUkUgYWNjb3VudF9jb2RlID0gJDEnLFxuICAgICAgW2FjY291bnRfY29kZV1cbiAgICApXG5cbiAgICBpZiAoZXhpc3RpbmdBY2NvdW50LnJvd3MubGVuZ3RoID4gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAn2LHZhdiyINin2YTYrdiz2KfYqCDZhdmI2KzZiNivINmF2LPYqNmC2KfZiydcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSlcbiAgICB9XG5cbiAgICAvLyDYqtit2K/ZitivINij2YPZiNin2K8g2KfZhNmF2LPYqtmI2YrYp9iqXG4gICAgbGV0IGxldmVsXzFfY29kZSA9IG51bGwsIGxldmVsXzJfY29kZSA9IG51bGwsIGxldmVsXzNfY29kZSA9IG51bGwsIGxldmVsXzRfY29kZSA9IG51bGxcblxuICAgIGlmIChhY2NvdW50X2xldmVsID09PSAxKSB7XG4gICAgICBsZXZlbF8xX2NvZGUgPSBhY2NvdW50X2NvZGVcbiAgICB9IGVsc2UgaWYgKGFjY291bnRfbGV2ZWwgPT09IDIpIHtcbiAgICAgIGxldmVsXzFfY29kZSA9IGFjY291bnRfY29kZS5zdWJzdHJpbmcoMCwgMilcbiAgICAgIGxldmVsXzJfY29kZSA9IGFjY291bnRfY29kZVxuICAgIH0gZWxzZSBpZiAoYWNjb3VudF9sZXZlbCA9PT0gMykge1xuICAgICAgbGV2ZWxfMV9jb2RlID0gYWNjb3VudF9jb2RlLnN1YnN0cmluZygwLCAyKVxuICAgICAgbGV2ZWxfMl9jb2RlID0gYWNjb3VudF9jb2RlLnN1YnN0cmluZygwLCA0KVxuICAgICAgbGV2ZWxfM19jb2RlID0gYWNjb3VudF9jb2RlXG4gICAgfSBlbHNlIGlmIChhY2NvdW50X2xldmVsID09PSA0KSB7XG4gICAgICBsZXZlbF8xX2NvZGUgPSBhY2NvdW50X2NvZGUuc3Vic3RyaW5nKDAsIDIpXG4gICAgICBsZXZlbF8yX2NvZGUgPSBhY2NvdW50X2NvZGUuc3Vic3RyaW5nKDAsIDQpXG4gICAgICBsZXZlbF8zX2NvZGUgPSBhY2NvdW50X2NvZGUuc3Vic3RyaW5nKDAsIDYpXG4gICAgICBsZXZlbF80X2NvZGUgPSBhY2NvdW50X2NvZGVcbiAgICB9XG5cbiAgICAvLyDYp9iz2KrYrtiv2KfZhSDYp9mE2YLZitmF2Kkg2KfZhNmF2LHYs9mE2Kkg2YXZhiDYp9mE2YbZhdmI2LDYrCDYo9mIINin2YHYqtix2KfYttmK2KfZiyDYrdiz2Kgg2KfZhNmF2LPYqtmI2YlcbiAgICBjb25zdCBmaW5hbEFsbG93VHJhbnNhY3Rpb25zID0gYWxsb3dfdHJhbnNhY3Rpb25zICE9PSB1bmRlZmluZWQgPyBhbGxvd190cmFuc2FjdGlvbnMgOiAoYWNjb3VudF9sZXZlbCA9PT0gNClcblxuICAgIC8vINil2K/Ysdin2Kwg2KfZhNit2LPYp9ioINin2YTYrNiv2YrYr1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5KGBcbiAgICAgIElOU0VSVCBJTlRPIGNoYXJ0X29mX2FjY291bnRzIChcbiAgICAgICAgYWNjb3VudF9jb2RlLCBhY2NvdW50X25hbWUsIGFjY291bnRfbmFtZV9lbixcbiAgICAgICAgbGV2ZWxfMV9jb2RlLCBsZXZlbF8yX2NvZGUsIGxldmVsXzNfY29kZSwgbGV2ZWxfNF9jb2RlLFxuICAgICAgICBhY2NvdW50X2xldmVsLCBwYXJlbnRfaWQsIGFjY291bnRfdHlwZSwgYWNjb3VudF9uYXR1cmUsXG4gICAgICAgIGFsbG93X3RyYW5zYWN0aW9ucywgbGlua2VkX3RhYmxlLCBhdXRvX2NyZWF0ZV9zdWJfYWNjb3VudHMsXG4gICAgICAgIG9wZW5pbmdfYmFsYW5jZSwgY3VycmVudF9iYWxhbmNlLCBkZXNjcmlwdGlvblxuICAgICAgKSBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSwgJDYsICQ3LCAkOCwgJDksICQxMCwgJDExLCAkMTIsICQxMywgJDE0LCAkMTUsICQxNiwgJDE3KVxuICAgICAgUkVUVVJOSU5HICpcbiAgICBgLCBbXG4gICAgICBhY2NvdW50X2NvZGUsIGFjY291bnRfbmFtZSwgYWNjb3VudF9uYW1lX2VuLFxuICAgICAgbGV2ZWxfMV9jb2RlLCBsZXZlbF8yX2NvZGUsIGxldmVsXzNfY29kZSwgbGV2ZWxfNF9jb2RlLFxuICAgICAgYWNjb3VudF9sZXZlbCwgcGFyZW50X2lkLCBhY2NvdW50X3R5cGUsIGFjY291bnRfbmF0dXJlLFxuICAgICAgZmluYWxBbGxvd1RyYW5zYWN0aW9ucywgbGlua2VkX3RhYmxlLCBhdXRvX2NyZWF0ZV9zdWJfYWNjb3VudHMsXG4gICAgICBvcGVuaW5nX2JhbGFuY2UsIG9wZW5pbmdfYmFsYW5jZSwgZGVzY3JpcHRpb25cbiAgICBdKVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBhY2NvdW50OiByZXN1bHQucm93c1swXSxcbiAgICAgIG1lc3NhZ2U6ICfYqtmFINil2LbYp9mB2Kkg2KfZhNit2LPYp9ioINio2YbYrNin2K0nXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINil2LbYp9mB2Kkg2KfZhNit2LPYp9ioOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiAn2YHYtNmEINmB2Yog2KXYttin2YHYqSDYp9mE2K3Ys9in2KgnLFxuICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K7Yt9ijINi62YrYsSDZhdi52LHZiNmBJ1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgfVxufVxuXG4vLyBQVVQgLSDYqtit2K/ZitirINit2LPYp9ioXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUFVUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpXG4gICAgY29uc3Qge1xuICAgICAgaWQsXG4gICAgICBhY2NvdW50X25hbWUsXG4gICAgICBhY2NvdW50X25hbWVfZW4sXG4gICAgICBhY2NvdW50X3R5cGUsXG4gICAgICBhY2NvdW50X25hdHVyZSxcbiAgICAgIGxpbmtlZF90YWJsZSxcbiAgICAgIGF1dG9fY3JlYXRlX3N1Yl9hY2NvdW50cyxcbiAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgaXNfYWN0aXZlID0gdHJ1ZVxuICAgIH0gPSBib2R5XG5cbiAgICBpZiAoIWlkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICfZhdi52LHZgSDYp9mE2K3Ys9in2Kgg2YXYt9mE2YjYqCdcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSlcbiAgICB9XG5cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBVUERBVEUgY2hhcnRfb2ZfYWNjb3VudHNcbiAgICAgIFNFVFxuICAgICAgICBhY2NvdW50X25hbWUgPSAkMixcbiAgICAgICAgYWNjb3VudF9uYW1lX2VuID0gJDMsXG4gICAgICAgIGFjY291bnRfdHlwZSA9ICQ0LFxuICAgICAgICBhY2NvdW50X25hdHVyZSA9ICQ1LFxuICAgICAgICBsaW5rZWRfdGFibGUgPSAkNixcbiAgICAgICAgYXV0b19jcmVhdGVfc3ViX2FjY291bnRzID0gJDcsXG4gICAgICAgIGRlc2NyaXB0aW9uID0gJDgsXG4gICAgICAgIGlzX2FjdGl2ZSA9ICQ5LFxuICAgICAgICB1cGRhdGVkX2RhdGUgPSBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgV0hFUkUgaWQgPSAkMVxuICAgICAgUkVUVVJOSU5HICpcbiAgICBgLCBbXG4gICAgICBpZCwgYWNjb3VudF9uYW1lLCBhY2NvdW50X25hbWVfZW4sIGFjY291bnRfdHlwZSwgYWNjb3VudF9uYXR1cmUsXG4gICAgICBsaW5rZWRfdGFibGUsIGF1dG9fY3JlYXRlX3N1Yl9hY2NvdW50cywgZGVzY3JpcHRpb24sIGlzX2FjdGl2ZVxuICAgIF0pXG5cbiAgICBpZiAocmVzdWx0LnJvd3MubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICfYp9mE2K3Ys9in2Kgg2LrZitixINmF2YjYrNmI2K8nXG4gICAgICB9LCB7IHN0YXR1czogNDA0IH0pXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBhY2NvdW50OiByZXN1bHQucm93c1swXSxcbiAgICAgIG1lc3NhZ2U6ICfYqtmFINiq2K3Yr9mK2Ksg2KfZhNit2LPYp9ioINio2YbYrNin2K0nXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2K3Yr9mK2Ksg2KfZhNit2LPYp9ioOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiAn2YHYtNmEINmB2Yog2KrYrdiv2YrYqyDYp9mE2K3Ys9in2KgnLFxuICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K7Yt9ijINi62YrYsSDZhdi52LHZiNmBJ1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInF1ZXJ5IiwiR0VUIiwicmVxdWVzdCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsImxldmVsIiwiZ2V0IiwiaW5jbHVkZUxpbmtlZCIsImV4Y2x1ZGVMaW5rZWRUYWJsZXMiLCJvbmx5VHJhbnNhY3Rpb25hbCIsInNxbCIsInBhcmFtcyIsInBhcmFtSW5kZXgiLCJwdXNoIiwicGFyc2VJbnQiLCJjb25zb2xlIiwibG9nIiwicmVzdWx0IiwiYWNjb3VudHMiLCJyb3dzIiwianNvbiIsInN1Y2Nlc3MiLCJ0b3RhbCIsImxlbmd0aCIsIm1lc3NhZ2UiLCJlcnJvciIsImRldGFpbHMiLCJFcnJvciIsInN0YXR1cyIsIlBPU1QiLCJib2R5IiwiYWNjb3VudF9jb2RlIiwiYWNjb3VudF9uYW1lIiwiYWNjb3VudF9uYW1lX2VuIiwiYWNjb3VudF9sZXZlbCIsInBhcmVudF9pZCIsImFjY291bnRfdHlwZSIsImFjY291bnRfbmF0dXJlIiwibGlua2VkX3RhYmxlIiwiYXV0b19jcmVhdGVfc3ViX2FjY291bnRzIiwiYWxsb3dfdHJhbnNhY3Rpb25zIiwib3BlbmluZ19iYWxhbmNlIiwiZGVzY3JpcHRpb24iLCJleGlzdGluZ0FjY291bnQiLCJsZXZlbF8xX2NvZGUiLCJsZXZlbF8yX2NvZGUiLCJsZXZlbF8zX2NvZGUiLCJsZXZlbF80X2NvZGUiLCJzdWJzdHJpbmciLCJmaW5hbEFsbG93VHJhbnNhY3Rpb25zIiwidW5kZWZpbmVkIiwiYWNjb3VudCIsIlBVVCIsImlkIiwiaXNfYWN0aXZlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/accounting/chart-of-accounts/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&page=%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fchart-of-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();