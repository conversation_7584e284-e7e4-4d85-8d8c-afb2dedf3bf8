(()=>{var e={};e.id=6259,e.ids=[6259],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>l});var a=s(64939),o=s(29021),c=s.n(o),n=s(33873),u=s.n(n),i=e([a]);a=(i.then?(await i)():i)[0];let _=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=c().readFileSync(e,"utf8");_=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!_?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(_&&_.routes[e]){let t=_.routes[e],s=_.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new a.Pool(d);async function l(e,t){let s=await p.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11630:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>i,routeModule:()=>l,serverHooks:()=>p,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>d});var a=s(96559),o=s(48088),c=s(37719),n=s(55411),u=e([n]);n=(u.then?(await u)():u)[0];let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/chart-of-accounts/route",pathname:"/api/accounting/chart-of-accounts",filename:"route",bundlePath:"app/api/accounting/chart-of-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\chart-of-accounts\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:_,workUnitAsyncStorage:d,serverHooks:p}=l;function i(){return(0,c.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55411:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{GET:()=>n,POST:()=>u,PUT:()=>i});var a=s(32190),o=s(5069),c=e([o]);async function n(e){try{let{searchParams:t}=new URL(e.url),s=t.get("level");t.get("include_linked");let r="true"===t.get("exclude_linked_tables"),c="true"===t.get("only_transactional"),n=`
      SELECT
        coa.id,
        coa.account_code,
        coa.account_name,
        coa.account_name_en,
        coa.level_1_code,
        coa.level_2_code,
        coa.level_3_code,
        coa.level_4_code,
        coa.account_level,
        coa.parent_id,
        coa.account_type,
        coa.account_nature,
        coa.is_active,
        coa.allow_transactions,
        coa.linked_table,
        coa.auto_create_sub_accounts,
        coa.opening_balance,
        coa.current_balance,
        coa.description,
        coa.created_date,
        coa.is_linked_record,
        coa.original_table,
        coa.linked_record_id
      FROM chart_of_accounts coa
      WHERE coa.is_active = true
    `;r&&(n+=" AND (coa.is_linked_record IS NULL OR coa.is_linked_record = false)");let u=[],i=1;s&&"all"!==s&&(n+=` AND coa.account_level = $${i}`,u.push(parseInt(s)),i++),c&&(n+=" AND coa.allow_transactions = true"),n+=" ORDER BY coa.account_code",console.log("\uD83D\uDD0D Executing SQL:",n),console.log("\uD83D\uDD0D With params:",u);let l=(await (0,o.P)(n,u)).rows;return a.NextResponse.json({success:!0,accounts:l,total:l.length,message:"تم جلب الحسابات بنجاح"})}catch(e){return console.error("خطأ في جلب الحسابات:",e),a.NextResponse.json({success:!1,error:"فشل في جلب الحسابات",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function u(e){try{let{account_code:t,account_name:s,account_name_en:r,account_level:c,parent_id:n,account_type:u,account_nature:i="مدين",linked_table:l,auto_create_sub_accounts:_=!1,allow_transactions:d=!1,opening_balance:p=0,description:g}=await e.json();if(!t||!s||!c||!u)return a.NextResponse.json({success:!1,error:"البيانات المطلوبة مفقودة"},{status:400});if(d&&l&&"none"!==l)return a.NextResponse.json({success:!1,error:"لا يمكن تفعيل المعاملات على حسابات التحكم المرتبطة بجداول خارجية"},{status:400});if((await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[t])).rows.length>0)return a.NextResponse.json({success:!1,error:"رمز الحساب موجود مسبقاً"},{status:400});let h=null,f=null,R=null,v=null;1===c?h=t:2===c?(h=t.substring(0,2),f=t):3===c?(h=t.substring(0,2),f=t.substring(0,4),R=t):4===c&&(h=t.substring(0,2),f=t.substring(0,4),R=t.substring(0,6),v=t);let w=await (0,o.P)(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_name_en,
        level_1_code, level_2_code, level_3_code, level_4_code,
        account_level, parent_id, account_type, account_nature,
        allow_transactions, linked_table, auto_create_sub_accounts,
        opening_balance, current_balance, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `,[t,s,r,h,f,R,v,c,n,u,i,void 0!==d?d:4===c,l,_,p,p,g]);return a.NextResponse.json({success:!0,account:w.rows[0],message:"تم إضافة الحساب بنجاح"})}catch(e){return console.error("خطأ في إضافة الحساب:",e),a.NextResponse.json({success:!1,error:"فشل في إضافة الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e){try{let{id:t,account_name:s,account_name_en:r,account_type:c,account_nature:n,linked_table:u,auto_create_sub_accounts:i,description:l,is_active:_=!0}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});let d=await (0,o.P)(`
      UPDATE chart_of_accounts
      SET
        account_name = $2,
        account_name_en = $3,
        account_type = $4,
        account_nature = $5,
        linked_table = $6,
        auto_create_sub_accounts = $7,
        description = $8,
        is_active = $9,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[t,s,r,c,n,u,i,l,_]);if(0===d.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return a.NextResponse.json({success:!0,account:d.rows[0],message:"تم تحديث الحساب بنجاح"})}catch(e){return console.error("خطأ في تحديث الحساب:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}o=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(11630));module.exports=r})();