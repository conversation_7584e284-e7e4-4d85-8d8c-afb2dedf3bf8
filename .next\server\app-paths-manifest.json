{"/api/issues/route": "app/api/issues/route.js", "/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/courts/route": "app/api/courts/route.js", "/api/users/route": "app/api/users/route.js", "/api/user-roles/route": "app/api/user-roles/route.js", "/api/user-roles/assignments/route": "app/api/user-roles/assignments/route.js", "/api/permissions/route": "app/api/permissions/route.js", "/api/user-permissions/route": "app/api/user-permissions/route.js", "/api/case-distribution/route": "app/api/case-distribution/route.js", "/home/<USER>": "app/home/<USER>", "/issues/page": "app/issues/page.js", "/percentages/page": "app/percentages/page.js", "/dashboard/page": "app/dashboard/page.js", "/case-distribution/page": "app/case-distribution/page.js", "/users/page": "app/users/page.js"}