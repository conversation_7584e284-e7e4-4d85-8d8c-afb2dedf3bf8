{"/api/legal-library/route": "app/api/legal-library/route.js", "/api/companies/route": "app/api/companies/route.js", "/api/announcements/route": "app/api/announcements/route.js", "/api/serviceslow/route": "app/api/serviceslow/route.js", "/api/company/route": "app/api/company/route.js", "/api/footer-links/route": "app/api/footer-links/route.js", "/page": "app/page.js", "/home/<USER>": "app/home/<USER>"}