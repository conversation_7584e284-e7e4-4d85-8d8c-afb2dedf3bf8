{"/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/clients/route": "app/api/clients/route.js", "/api/issues/route": "app/api/issues/route.js", "/api/courts/route": "app/api/courts/route.js", "/api/currencies/route": "app/api/currencies/route.js", "/api/issue-types/route": "app/api/issue-types/route.js", "/issues/page": "app/issues/page.js", "/users/page": "app/users/page.js", "/dashboard/page": "app/dashboard/page.js", "/login/page": "app/login/page.js"}