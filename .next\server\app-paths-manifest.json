{"/api/announcements/route": "app/api/announcements/route.js", "/api/companies/route": "app/api/companies/route.js", "/api/legal-library/route": "app/api/legal-library/route.js", "/api/company/route": "app/api/company/route.js", "/api/serviceslow/route": "app/api/serviceslow/route.js", "/api/footer-links/route": "app/api/footer-links/route.js", "/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/accounting/payment-vouchers/route": "app/api/accounting/payment-vouchers/route.js", "/api/accounting/chart-of-accounts/route": "app/api/accounting/chart-of-accounts/route.js", "/api/accounting/currencies/route": "app/api/accounting/currencies/route.js", "/api/accounting/payment-methods/route": "app/api/accounting/payment-methods/route.js", "/api/cost-centers/route": "app/api/cost-centers/route.js", "/api/issues/route": "app/api/issues/route.js", "/api/clients/route": "app/api/clients/route.js", "/api/services/route": "app/api/services/route.js", "/api/employees/route": "app/api/employees/route.js", "/home/<USER>": "app/home/<USER>", "/page": "app/page.js", "/login/page": "app/login/page.js", "/dashboard/page": "app/dashboard/page.js", "/accounting/payment-vouchers/page": "app/accounting/payment-vouchers/page.js"}