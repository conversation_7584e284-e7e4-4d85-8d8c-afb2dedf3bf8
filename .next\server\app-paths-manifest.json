{"/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/serviceslow/route": "app/api/serviceslow/route.js", "/api/company/route": "app/api/company/route.js", "/api/announcements/route": "app/api/announcements/route.js", "/api/legal-library/route": "app/api/legal-library/route.js", "/api/companies/route": "app/api/companies/route.js", "/api/settings/public-announcements/route": "app/api/settings/public-announcements/route.js", "/api/ai/local-models/route": "app/api/ai/local-models/route.js", "/api/footer-links/route": "app/api/footer-links/route.js", "/api/ai/settings/route": "app/api/ai/settings/route.js", "/home/<USER>": "app/home/<USER>", "/login/page": "app/login/page.js", "/website-admin/page": "app/website-admin/page.js", "/client-login/page": "app/client-login/page.js", "/admin/ai-settings/page": "app/admin/ai-settings/page.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/page": "app/dashboard/page.js"}