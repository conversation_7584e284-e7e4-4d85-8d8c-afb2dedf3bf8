{"/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/percentages/route": "app/api/percentages/route.js", "/api/services/route": "app/api/services/route.js", "/api/issues/route": "app/api/issues/route.js", "/api/issues/[id]/route": "app/api/issues/[id]/route.js", "/api/courts/route": "app/api/courts/route.js", "/api/clients/route": "app/api/clients/route.js", "/home/<USER>": "app/home/<USER>", "/issues/page": "app/issues/page.js", "/percentages/page": "app/percentages/page.js", "/users/page": "app/users/page.js", "/_not-found/page": "app/_not-found/page.js", "/case-distribution/page": "app/case-distribution/page.js"}