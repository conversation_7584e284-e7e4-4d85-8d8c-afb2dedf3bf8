import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الفروع من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM branches ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الفروع:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات الفروع',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الفروع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة الفروع بنجاح'
    })
  } catch (error) {
    console.error('Error creating الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الفروع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الفروع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الفروع بنجاح'
    })
  } catch (error) {
    console.error('Error updating الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الفروع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الفروع
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفروع مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM branches WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الفروع بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الفروع' },
      { status: 500 }
    )
  }
}