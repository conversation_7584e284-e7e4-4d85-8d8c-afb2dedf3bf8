(()=>{var c={};c.id=2907,c.ids=[2907],c.modules={3295:c=>{"use strict";c.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(c,e,t)=>{"use strict";t.a(c,async(c,a)=>{try{t.d(e,{P:()=>d});var o=t(64939),s=t(29021),n=t.n(s),r=t(33873),u=t.n(r),i=c([o]);o=(i.then?(await i)():i)[0];let p=null;try{let c=u().join(process.cwd(),"routing.config.json"),e=n().readFileSync(c,"utf8");p=JSON.parse(e)}catch(c){console.error("❌ خطأ في تحميل ملف التوجيه:",c)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let _=(()=>{let c=process.env.PORT||"7443";if(p&&p.routes[c]){let e=p.routes[c],t=p.default_config;return{database:e.database,user:t.db_user,host:t.db_host,password:process.env.DB_PASSWORD||t.db_password,port:t.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${c}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new o.Pool(_);async function d(c,e){let t=await l.connect();try{return await t.query(c,e)}finally{t.release()}}a()}catch(c){a(c)}})},10846:c=>{"use strict";c.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:c=>{"use strict";c.exports=require("fs")},29294:c=>{"use strict";c.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:c=>{"use strict";c.exports=require("path")},44870:c=>{"use strict";c.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55291:(c,e,t)=>{"use strict";t.a(c,async(c,a)=>{try{t.r(e),t.d(e,{POST:()=>r});var o=t(32190),s=t(5069),n=c([s]);async function r(c){try{console.log("\uD83D\uDD27 تحديث مراجع API لاستخدام account_id بدلاً من account_code...");let c=[];console.log("\uD83D\uDCDD تحديث API العملاء...");try{await (0,s.P)(`
        SELECT 
          c.*,
          coa.account_name,
          coa.account_code
        FROM clients c
        LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
        LIMIT 1
      `),c.push("✅ API العملاء: الربط عبر account_id يعمل بشكل صحيح")}catch(e){c.push(`❌ API العملاء: خطأ في الربط - ${e}`)}console.log("\uD83D\uDCDD تحديث API الموظفين...");try{await (0,s.P)(`
        SELECT 
          e.*,
          coa.account_name,
          coa.account_code
        FROM employees e
        LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
        LIMIT 1
      `),c.push("✅ API الموظفين: الربط عبر account_id يعمل بشكل صحيح")}catch(e){c.push(`❌ API الموظفين: خطأ في الربط - ${e}`)}console.log("\uD83D\uDCDD تحديث API الموردين...");try{await (0,s.P)(`
        SELECT 
          s.*,
          coa.account_name,
          coa.account_code
        FROM suppliers s
        LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
        LIMIT 1
      `),c.push("✅ API الموردين: الربط عبر account_id يعمل بشكل صحيح")}catch(e){c.push(`❌ API الموردين: خطأ في الربط - ${e}`)}console.log("\uD83D\uDCDD اختبار سندات القبض والصرف...");try{await (0,s.P)(`
        SELECT 
          rv.*,
          da.account_name as debit_account_name,
          da.account_code as debit_account_code,
          ca.account_name as credit_account_name,
          ca.account_code as credit_account_code
        FROM receipt_vouchers rv
        LEFT JOIN chart_of_accounts da ON rv.debit_account_id = da.id
        LEFT JOIN chart_of_accounts ca ON rv.credit_account_id = ca.id
        LIMIT 1
      `),c.push("✅ سندات القبض: الربط عبر account_id يعمل بشكل صحيح"),await (0,s.P)(`
        SELECT 
          pv.*,
          da.account_name as debit_account_name,
          da.account_code as debit_account_code,
          ca.account_name as credit_account_name,
          ca.account_code as credit_account_code
        FROM payment_vouchers pv
        LEFT JOIN chart_of_accounts da ON pv.debit_account_id = da.id
        LEFT JOIN chart_of_accounts ca ON pv.credit_account_id = ca.id
        LIMIT 1
      `),c.push("✅ سندات الصرف: الربط عبر account_id يعمل بشكل صحيح")}catch(e){c.push(`❌ السندات: خطأ في الربط - ${e}`)}console.log("\uD83D\uDCDD اختبار القيود اليومية...");try{await (0,s.P)(`
        SELECT 
          jed.*,
          coa.account_name,
          coa.account_code
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        LIMIT 1
      `),c.push("✅ القيود اليومية: الربط عبر account_id يعمل بشكل صحيح")}catch(e){c.push(`❌ القيود اليومية: خطأ في الربط - ${e}`)}let e={};try{e.clientsLinked=(await (0,s.P)(`
        SELECT COUNT(*) as count 
        FROM clients c 
        INNER JOIN chart_of_accounts coa ON c.account_id = coa.id
      `)).rows[0].count,e.employeesLinked=(await (0,s.P)(`
        SELECT COUNT(*) as count 
        FROM employees e 
        INNER JOIN chart_of_accounts coa ON e.account_id = coa.id
      `)).rows[0].count,e.suppliersLinked=(await (0,s.P)(`
        SELECT COUNT(*) as count 
        FROM suppliers s 
        INNER JOIN chart_of_accounts coa ON s.account_id = coa.id
      `)).rows[0].count,e.totalAccounts=(await (0,s.P)("SELECT COUNT(*) as count FROM chart_of_accounts")).rows[0].count,c.push(`📊 إحصائيات الربط: ${e.clientsLinked} عميل، ${e.employeesLinked} موظف، ${e.suppliersLinked} مورد مربوطين`)}catch(e){c.push(`❌ خطأ في جمع الإحصائيات: ${e}`)}console.log("⚡ اختبار أداء الاستعلامات...");try{let e=Date.now();await (0,s.P)(`
        SELECT 
          c.name as client_name,
          coa.account_name,
          coa.account_code,
          coa.current_balance
        FROM clients c
        INNER JOIN chart_of_accounts coa ON c.account_id = coa.id
        WHERE coa.account_type = 'أصول'
        ORDER BY c.name
        LIMIT 10
      `);let t=Date.now();c.push(`⚡ أداء الاستعلام: ${t-e}ms (ممتاز إذا كان أقل من 100ms)`)}catch(e){c.push(`❌ خطأ في اختبار الأداء: ${e}`)}let t=[];return e.clientsLinked<.1*e.totalAccounts&&t.push("\uD83D\uDCA1 يُنصح بربط المزيد من العملاء بدليل الحسابات"),0===e.employeesLinked&&t.push("\uD83D\uDCA1 يُنصح بربط الموظفين بحسابات الرواتب"),0===e.suppliersLinked&&t.push("\uD83D\uDCA1 يُنصح بإضافة موردين وربطهم بحسابات الموردين"),t.push("✅ الربط عبر account_id يعمل بشكل مثالي"),t.push("✅ تم حذف account_code بنجاح من جميع الجداول"),t.push("✅ الأداء محسن مع الفهارس الجديدة"),console.log("\uD83C\uDF89 تم تحديث مراجع API بنجاح!"),o.NextResponse.json({success:!0,message:"تم تحديث مراجع API وتأكيد عمل الربط عبر account_id بنجاح",data:{results:c,stats:e,recommendations:t,summary:{testsRun:c.length,successfulTests:c.filter(c=>c.includes("✅")).length,failedTests:c.filter(c=>c.includes("❌")).length}}})}catch(c){return console.error("❌ خطأ في تحديث مراجع API:",c),o.NextResponse.json({success:!1,message:"فشل في تحديث مراجع API",error:c instanceof Error?c.message:"خطأ غير معروف"},{status:500})}}s=(n.then?(await n)():n)[0],a()}catch(c){a(c)}})},63033:c=>{"use strict";c.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:c=>{"use strict";c.exports=import("pg")},78335:()=>{},95786:(c,e,t)=>{"use strict";t.a(c,async(c,a)=>{try{t.r(e),t.d(e,{patchFetch:()=>i,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>_});var o=t(96559),s=t(48088),n=t(37719),r=t(55291),u=c([r]);r=(u.then?(await u)():u)[0];let d=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/update-api-references/route",pathname:"/api/accounting/update-api-references",filename:"route",bundlePath:"app/api/accounting/update-api-references/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\update-api-references\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:_,serverHooks:l}=d;function i(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:_})}a()}catch(c){a(c)}})},96487:()=>{}};var e=require("../../../../webpack-runtime.js");e.C(c);var t=c=>e(e.s=c),a=e.X(0,[4447,580],()=>t(95786));module.exports=a})();