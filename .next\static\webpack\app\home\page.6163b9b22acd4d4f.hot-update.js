"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/simple-chat-widget */ \"(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match database\nconst companyData = {\n    id: 1,\n    name: 'مكتب المحاماة والاستشارات القانونية',\n    legal_name: 'شركة المحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'شارع الزبيري، مبنى رقم 15، الطابق الثالث',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/simple-logo.svg',\n    logo_image_url: '/images/simple-logo.svg',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyDataState, setCompanyDataState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // جلب بيانات الشركة من قاعدة البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchCompanyData = {\n                \"HomePage.useEffect.fetchCompanyData\": async ()=>{\n                    try {\n                        const response = await fetch('/api/company');\n                        const result = await response.json();\n                        if (result.success && result.data && result.data.length > 0) {\n                            // أخذ أول شركة من القائمة (الشركة الرئيسية)\n                            const company = result.data[0];\n                            setCompanyDataState({\n                                ...company,\n                                working_hours: company.working_hours || 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n                            });\n                        } else {\n                            // استخدام البيانات الافتراضية في حالة عدم وجود شركة\n                            setCompanyDataState(companyData);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب بيانات الشركة:', error);\n                        // استخدام البيانات الافتراضية في حالة الخطأ\n                        setCompanyDataState(companyData);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchCompanyData\"];\n            fetchCompanyData();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__.SimpleChatWidget, {\n                isOpen: isChatWidgetOpen,\n                onClose: ()=>setIsChatWidgetOpen(false),\n                onOpen: ()=>setIsChatWidgetOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"18l5owaLq2anWF6r71/MFBYG+h4=\");\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ })

});