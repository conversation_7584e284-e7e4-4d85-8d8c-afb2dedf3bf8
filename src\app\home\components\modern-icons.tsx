import React from 'react';

// أيقونات حديثة مخصصة بتقنيات SVG متقدمة
export const ModernIcons = {
  // أيقونة العدالة الحديثة
  Justice: ({ className = "w-8 h-8", color = "#3b82f6" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="justice-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#1e40af" />
        </linearGradient>
      </defs>
      <path
        d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
        fill="url(#justice-gradient)"
        className="animate-pulse"
      />
      <path
        d="M7 14L12 19L17 14"
        stroke="url(#justice-gradient)"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </svg>
  ),

  // أيقونة المحكمة الحديثة
  Court: ({ className = "w-8 h-8", color = "#059669" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="court-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#047857" />
        </linearGradient>
      </defs>
      <rect x="3" y="4" width="18" height="2" fill="url(#court-gradient)" rx="1" />
      <path
        d="M6 6V18H8V6M10 6V18H12V6M14 6V18H16V6M18 6V18H20V6"
        fill="url(#court-gradient)"
        opacity="0.8"
      />
      <rect x="2" y="18" width="20" height="2" fill="url(#court-gradient)" rx="1" />
      <circle cx="12" cy="2" r="1" fill="url(#court-gradient)" className="animate-bounce" />
    </svg>
  ),

  // أيقونة العقود الحديثة
  Contract: ({ className = "w-8 h-8", color = "#dc2626" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="contract-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#b91c1c" />
        </linearGradient>
      </defs>
      <path
        d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
        fill="url(#contract-gradient)"
        opacity="0.9"
      />
      <path d="M14 2V8H20" fill="none" stroke="#ffffff" strokeWidth="2" />
      <circle cx="9" cy="12" r="1" fill="#ffffff" />
      <circle cx="9" cy="15" r="1" fill="#ffffff" />
      <circle cx="9" cy="18" r="1" fill="#ffffff" />
      <line x1="12" y1="12" x2="16" y2="12" stroke="#ffffff" strokeWidth="2" />
      <line x1="12" y1="15" x2="16" y2="15" stroke="#ffffff" strokeWidth="2" />
      <line x1="12" y1="18" x2="14" y2="18" stroke="#ffffff" strokeWidth="2" />
    </svg>
  ),

  // أيقونة الاستشارة الحديثة
  Consultation: ({ className = "w-8 h-8", color = "#f59e0b" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="consultation-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#d97706" />
        </linearGradient>
      </defs>
      <circle cx="12" cy="8" r="4" fill="url(#consultation-gradient)" />
      <path
        d="M6 21V19C6 16.79 7.79 15 10 15H14C16.21 15 18 16.79 18 19V21"
        stroke="url(#consultation-gradient)"
        strokeWidth="2"
        fill="none"
      />
      <path
        d="M16 8C18 8 20 10 20 12C20 14 18 16 16 16"
        stroke="url(#consultation-gradient)"
        strokeWidth="2"
        fill="none"
        className="animate-pulse"
      />
      <path
        d="M8 8C6 8 4 10 4 12C4 14 6 16 8 16"
        stroke="url(#consultation-gradient)"
        strokeWidth="2"
        fill="none"
        className="animate-pulse"
      />
    </svg>
  ),

  // أيقونة الحماية القانونية
  Protection: ({ className = "w-8 h-8", color = "#8b5cf6" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="protection-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#7c3aed" />
        </linearGradient>
      </defs>
      <path
        d="M12 2L3 7L12 12L21 7L12 2Z"
        fill="url(#protection-gradient)"
        opacity="0.8"
      />
      <path
        d="M3 7V17L12 22L21 17V7"
        stroke="url(#protection-gradient)"
        strokeWidth="2"
        fill="none"
      />
      <path
        d="M12 12V22"
        stroke="url(#protection-gradient)"
        strokeWidth="2"
      />
      <circle cx="12" cy="15" r="2" fill="#ffffff" className="animate-pulse" />
    </svg>
  ),

  // أيقونة الأعمال التجارية
  Business: ({ className = "w-8 h-8", color = "#06b6d4" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="business-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#0891b2" />
        </linearGradient>
      </defs>
      <rect x="3" y="6" width="18" height="12" rx="2" fill="url(#business-gradient)" opacity="0.9" />
      <rect x="7" y="10" width="2" height="4" fill="#ffffff" />
      <rect x="11" y="8" width="2" height="6" fill="#ffffff" />
      <rect x="15" y="12" width="2" height="2" fill="#ffffff" />
      <path d="M3 6L12 2L21 6" stroke="url(#business-gradient)" strokeWidth="2" fill="none" />
      <circle cx="18" cy="8" r="1" fill="#ffffff" className="animate-bounce" />
    </svg>
  ),

  // أيقونة الميراث
  Inheritance: ({ className = "w-8 h-8", color = "#ec4899" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="inheritance-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#db2777" />
        </linearGradient>
      </defs>
      <circle cx="12" cy="6" r="3" fill="url(#inheritance-gradient)" />
      <path
        d="M6 18C6 15.79 7.79 14 10 14H14C16.21 14 18 15.79 18 18"
        stroke="url(#inheritance-gradient)"
        strokeWidth="2"
        fill="none"
      />
      <path
        d="M8 22L12 18L16 22"
        stroke="url(#inheritance-gradient)"
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle cx="8" cy="10" r="1" fill="url(#inheritance-gradient)" className="animate-pulse" />
      <circle cx="16" cy="10" r="1" fill="url(#inheritance-gradient)" className="animate-pulse" />
    </svg>
  ),

  // أيقونة التحكيم
  Arbitration: ({ className = "w-8 h-8", color = "#f97316" }) => (
    <svg className={className} viewBox="0 0 24 24" fill="none">
      <defs>
        <linearGradient id="arbitration-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={color} />
          <stop offset="100%" stopColor="#ea580c" />
        </linearGradient>
      </defs>
      <circle cx="12" cy="12" r="8" stroke="url(#arbitration-gradient)" strokeWidth="2" fill="none" />
      <path
        d="M8 12L11 15L16 9"
        stroke="url(#arbitration-gradient)"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
        className="animate-pulse"
      />
      <circle cx="12" cy="12" r="2" fill="url(#arbitration-gradient)" opacity="0.5" />
    </svg>
  )
};

// مكون الأيقونة الديناميكي
export const DynamicIcon = ({ 
  name, 
  className = "w-8 h-8", 
  color = "#3b82f6" 
}: { 
  name: string; 
  className?: string; 
  color?: string; 
}) => {
  const IconComponent = ModernIcons[name as keyof typeof ModernIcons];
  
  if (!IconComponent) {
    return <ModernIcons.Justice className={className} color={color} />;
  }
  
  return <IconComponent className={className} color={color} />;
};
