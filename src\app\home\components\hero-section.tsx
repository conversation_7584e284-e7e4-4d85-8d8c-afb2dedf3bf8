'use client';



type CompanyData = {
  name: string;
  description: string;
};

type Stats = {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
};

interface HeroSectionProps {
  companyData: CompanyData;
  stats: Stats;
  onContactClick?: () => void;
  onServicesClick: () => void;
}

export function HeroSection({
  companyData,
  stats,
  onServicesClick
}: HeroSectionProps) {


  return (
    <section id="home" className="relative min-h-screen flex items-center text-white overflow-hidden" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
      {/* خلفية مستوحاة من تفاهم */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-600/10 to-transparent"></div>
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-yellow-500/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-yellow-600/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-yellow-500/3 to-transparent rounded-full blur-3xl animate-pulse"></div>
      </div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content */}
          <div className="text-center lg:text-right space-y-8">
            {/* Badge - بتصميم تفاهم */}
            <div className="inline-flex items-center bg-gradient-to-r from-yellow-600/20 to-yellow-500/10 backdrop-blur-sm border border-yellow-600/30 text-yellow-400 text-sm font-semibold px-6 py-3 rounded-full tafahum-glow">
              <span className="w-3 h-3 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full mr-3 animate-pulse"></span>
              شركة رائدة في المجال القانوني
            </div>

            {/* Main Heading - بأسلوب تفاهم */}
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="block text-white mb-2">مرحباً بكم في</span>
                <span className="block font-extrabold" style={{
                  background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  {companyData.name}
                </span>
              </h1>

              <div className="mx-auto lg:mx-0 w-24 h-1 rounded-full my-6" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}></div>

              <p className="text-xl md:text-2xl text-gray-300 font-light leading-relaxed max-w-2xl mx-auto lg:mx-0">
                {companyData.description}
              </p>
            </div>

            {/* CTA Buttons - بتصميم تفاهم */}
            <div className="flex flex-col sm:flex-row justify-center lg:justify-start gap-6">
              <button
                className="text-lg px-10 py-4 shadow-lg hover:shadow-xl rounded-full font-bold transition-all duration-300 transform hover:-translate-y-1"
                style={{
                  background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)',
                  color: '#222222'
                }}
                onClick={() => {
                  // فتح نافذة الدردشة
                  const chatButton = document.querySelector('[title="المحادثات مع الذكاء الاصطناعي"]') as HTMLButtonElement;
                  if (chatButton) {
                    chatButton.click();
                  }
                }}
              >
                <span className="flex items-center">
                  احجز استشارة مجانية
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </span>
              </button>

              <button
                className="text-lg px-10 py-4 backdrop-blur-sm rounded-full font-bold border-2 transition-all duration-300 transform hover:-translate-y-1"
                style={{
                  borderColor: '#cca967',
                  color: '#cca967',
                  background: 'transparent'
                }}
                onClick={onServicesClick}
              >
                <span className="flex items-center">
                  استكشف خدماتنا
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </span>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-slate-300">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                استشارة مجانية
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                سرية تامة
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                متاح 24/7
              </div>
            </div>
          </div>

          {/* Right Side - Stats & Visual */}
          <div className="relative">
            {/* Statistics Grid - إحصائيات دائرية */}
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {/* إجمالي القضايا */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-blue-200/30 shadow-sm group-hover:border-blue-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-blue-50/10 group-hover:bg-blue-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-blue-400 mb-1 group-hover:text-blue-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
                    </svg>
                    <div className="text-lg font-bold text-blue-400 group-hover:text-blue-300 transition-colors duration-300">{stats.issues}</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">إجمالي القضايا</div>
              </div>

              {/* الموكلين النشطين */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-green-200/30 shadow-sm group-hover:border-green-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-green-50/10 group-hover:bg-green-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-green-400 mb-1 group-hover:text-green-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <div className="text-lg font-bold text-green-400 group-hover:text-green-300 transition-colors duration-300">{stats.clients}</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">الموكلين النشطين</div>
              </div>

              {/* نسبة النجاح */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-yellow-200/30 shadow-sm group-hover:border-yellow-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-yellow-50/10 group-hover:bg-yellow-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-yellow-400 mb-1 group-hover:text-yellow-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <div className="text-lg font-bold text-yellow-400 group-hover:text-yellow-300 transition-colors duration-300">{stats.successRate}%</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">نسبة النجاح</div>
              </div>

              {/* سنوات الخبرة */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-purple-200/30 shadow-sm group-hover:border-purple-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-purple-50/10 group-hover:bg-purple-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-purple-400 mb-1 group-hover:text-purple-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <div className="text-lg font-bold text-purple-400 group-hover:text-purple-300 transition-colors duration-300">{stats.experienceYears}+</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">سنة خبرة</div>
              </div>

              {/* المحامين */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-indigo-200/30 shadow-sm group-hover:border-indigo-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-indigo-50/10 group-hover:bg-indigo-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-indigo-400 mb-1 group-hover:text-indigo-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <div className="text-lg font-bold text-indigo-400 group-hover:text-indigo-300 transition-colors duration-300">{stats.employees}</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">محامٍ متخصص</div>
              </div>

              {/* المحاكم */}
              <div className="text-center group cursor-pointer">
                <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                  <div className="absolute inset-0 rounded-full border-2 border-red-200/30 shadow-sm group-hover:border-red-300/50 group-hover:shadow-md transition-all duration-300"></div>
                  <div className="absolute inset-1 rounded-full bg-red-50/10 group-hover:bg-red-100/20 transition-colors duration-300"></div>
                  <div className="relative flex flex-col items-center justify-center">
                    <svg className="h-6 w-6 text-red-400 mb-1 group-hover:text-red-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <div className="text-lg font-bold text-red-400 group-hover:text-red-300 transition-colors duration-300">{stats.courts}+</div>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-300 group-hover:text-white transition-colors duration-300">محكمة</div>
              </div>
            </div>


          </div>
        </div>


      </div>
    </section>
  );
}
