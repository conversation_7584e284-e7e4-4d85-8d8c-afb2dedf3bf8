(()=>{var e={};e.id=4357,e.ids=[4357],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>b,gC:()=>h,l6:()=>d,yv:()=>p});var s=t(60687),i=t(43210),r=t(58106),n=t(78272),l=t(3589),c=t(13964),o=t(4780);let d=r.bL;r.YJ;let p=r.WT,m=i.forwardRef(({className:e,children:a,...t},i)=>(0,s.jsxs)(r.l9,{ref:i,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-700"})})]}));m.displayName=r.l9.displayName;let u=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=r.PP.displayName;let x=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=r.wn.displayName;let h=i.forwardRef(({className:e,children:a,position:t="popper",...i},n)=>(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...i,children:[(0,s.jsx)(u,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(x,{})]})}));h.displayName=r.UC.displayName,i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=r.JU.displayName;let b=i.forwardRef(({className:e,children:a,...t},i)=>(0,s.jsxs)(r.q7,{ref:i,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})}),(0,s.jsx)(r.p4,{children:a})]}));b.displayName=r.q7.displayName,i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=r.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19728:(e,a,t)=>{Promise.resolve().then(t.bind(t,86691))},20189:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>v});var s=t(60687),i=t(43210),r=t(98254),n=t(44493),l=t(29523),c=t(89667),o=t(80013),d=t(34729),p=t(15079),m=t(63503),u=t(96834),x=t(23576),h=t(96474),b=t(99270),y=t(13861),f=t(63143),g=t(88233),_=t(71444);function v(){let[e,a]=(0,i.useState)([]),[t,v]=(0,i.useState)([]),[j,w]=(0,i.useState)([]),[N,k]=(0,i.useState)([]),[C,D]=(0,i.useState)([]),[$,S]=(0,i.useState)([]),[A,P]=(0,i.useState)([]),[q,L]=(0,i.useState)([]),[R,z]=(0,i.useState)([]),[E,J]=(0,i.useState)(!0),[F,M]=(0,i.useState)(!1),[G,I]=(0,i.useState)(null),[T,V]=(0,i.useState)(""),[B,U]=(0,i.useState)({entry_date:new Date().toISOString().split("T")[0],beneficiary_id:"",beneficiary_name:"",beneficiary_type:"",debit_account_id:"",credit_account_id:"",amount:"",currency_id:"1",payment_method_id:"1",cost_center_id:"",description:"",reference_number:"",case_id:"",service_id:""}),O=e=>{if(!e)return;let a=t.find(a=>a.id.toString()===e);if(!a)return;let s="",i="external";a.is_linked_record&&a.original_table?"clients"===a.original_table?(i="client",s=a.account_name.replace("حساب العميل: ","").replace("حساب العميل:","").trim()):"employees"===a.original_table?(i="employee",s=a.account_name.replace("حساب الموظف: ","").replace("حساب الموظف:","").trim()):"suppliers"===a.original_table&&(i="supplier",s=a.account_name.replace("حساب المورد: ","").replace("حساب المورد:","").trim()):(s=a.account_name,a.account_name.includes("عميل")||a.account_name.includes("العملاء")?i="client":a.account_name.includes("موظف")||a.account_name.includes("الموظفين")?i="employee":(a.account_name.includes("مورد")||a.account_name.includes("الموردين"))&&(i="supplier")),U(e=>({...e,beneficiary_name:s,beneficiary_type:i}))},Z=async()=>{try{J(!0);let e=await fetch("/api/accounting/payment-vouchers");if(e.ok){let t=await e.json();a(t.vouchers||[])}let t=await fetch("/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true");if(t.ok){let e=(await t.json()).accounts.sort((e,a)=>{if(e.is_linked_record&&!a.is_linked_record)return 1;if(!e.is_linked_record&&a.is_linked_record)return -1;if(e.is_linked_record&&a.is_linked_record){if("clients"===e.original_table&&"employees"===a.original_table)return -1;if("employees"===e.original_table&&"clients"===a.original_table)return 1}return e.account_name.localeCompare(a.account_name,"ar")});v(e)}let s=await fetch("/api/accounting/currencies");if(s.ok){let e=await s.json();w(e.currencies||[])}let i=await fetch("/api/accounting/payment-methods");if(i.ok){let e=await i.json();k(e.methods||[])}let r=await fetch("/api/cost-centers");if(r.ok){let e=await r.json();D(e.centers||[])}let n=await fetch("/api/issues");if(n.ok){let e=await n.json();console.log("Cases data:",e),S(e.data||e.issues||[])}let l=await fetch("/api/services");if(l.ok){let e=await l.json();console.log("Services data:",e),P(e.data||e.services||[])}let c=await fetch("/api/clients");if(c.ok){let e=await c.json();L(e.clients||[])}let o=await fetch("/api/employees");if(o.ok){let e=await o.json();z(e.employees||[])}}catch(e){console.error("خطأ في جلب البيانات:",e)}finally{J(!1)}},H=async e=>{e.preventDefault();try{let e=G?`/api/accounting/payment-vouchers/${G.id}`:"/api/accounting/payment-vouchers",a=G?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify({...B,amount:parseFloat(B.amount),beneficiary_id:B.beneficiary_id?parseInt(B.beneficiary_id.split("_")[1]):null,beneficiary_type:B.beneficiary_type,beneficiary_name:B.beneficiary_name,debit_account_id:B.debit_account_id?parseInt(B.debit_account_id):null,credit_account_id:parseInt(B.credit_account_id),currency_id:parseInt(B.currency_id),payment_method_id:B.payment_method_id?parseInt(B.payment_method_id):null,cost_center_id:B.cost_center_id?parseInt(B.cost_center_id):null,case_id:B.case_id?parseInt(B.case_id):null})});if(t.ok)await Z(),M(!1),I(null),Y();else{let e=await t.json();alert(`خطأ: ${e.error}`)}}catch(e){console.error("خطأ في حفظ السند:",e),alert("حدث خطأ أثناء حفظ السند")}},Y=()=>{U({entry_date:new Date().toISOString().split("T")[0],beneficiary_id:"",beneficiary_name:"",beneficiary_type:"",debit_account_id:"",credit_account_id:"",amount:"",currency_id:"1",payment_method_id:"1",cost_center_id:"",description:"",reference_number:"",case_id:"",service_id:""})},W=e=>{I(e),U({entry_date:e.entry_date,beneficiary_id:e.beneficiary_type&&e.beneficiary_id?`${e.beneficiary_type}_${e.beneficiary_id}`:"",beneficiary_name:e.beneficiary_name||"",beneficiary_type:e.beneficiary_type||"",debit_account_id:e.debit_account_id?.toString()||"",credit_account_id:e.credit_account_id?.toString()||"",amount:e.amount.toString(),currency_id:e.currency_id.toString(),payment_method_id:e.payment_method_id?.toString()||"",cost_center_id:e.cost_center_id?.toString()||"",description:e.description,reference_number:e.reference_number||"",case_id:e.case_id?.toString()||"",service_id:e.service_id?.toString()||""}),M(!0)},K=e=>{let a=window.open("","_blank","width=800,height=600");a&&(a.document.write(`
        <html dir="rtl">
          <head>
            <title>سند صرف رقم ${e.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
              .label { font-weight: bold; text-align: right; }
              .amount { font-size: 18px; font-weight: bold; color: #d32f2f; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند صرف</h1>
              <h2>رقم: ${e.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span>${new Date(e.entry_date).toLocaleDateString("en-GB")}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="amount">${(e.amount||e.total_debit||0).toLocaleString()} ${e.currency_code||"ر.ي"}</span>
              </div>
              <div class="row">
                <span class="label">المستفيد:</span>
                <span>${e.beneficiary_name||e.payee_name||"غير محدد"}</span>
                ${e.beneficiary_type||e.payee_type?`<br><small style="color: #666;">(${"client"===(e.beneficiary_type||e.payee_type)?"عميل":"employee"===(e.beneficiary_type||e.payee_type)?"موظف":"supplier"===(e.beneficiary_type||e.payee_type)?"مورد":"خارجي"})</small>`:""}
              </div>
              <div class="row">
                <span class="label">الحساب الدائن:</span>
                <span>${e.credit_account_name}</span>
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span>${e.description}</span>
              </div>
              <div class="row">
                <span class="label">طريقة الدفع:</span>
                <span>${e.payment_method_name||"نقد"}</span>
              </div>
              ${e.reference_number?`
              <div class="row">
                <span class="label">رقم المرجع:</span>
                <span>${e.reference_number}</span>
              </div>
              `:""}
            </div>
          </body>
        </html>
      `),a.document.close())},X=async e=>{if(confirm("هل أنت متأكد من حذف هذا السند؟"))try{let a=await fetch(`/api/accounting/payment-vouchers/${e}`,{method:"DELETE"});if(a.ok)await Z(),alert("تم حذف السند بنجاح");else{let e=await a.json();alert(`خطأ في حذف السند: ${e.error}`)}}catch(e){console.error("خطأ في حذف السند:",e),alert("حدث خطأ أثناء حذف السند")}},Q=e=>{let a=window.open("","_blank","width=800,height=600");a&&(a.document.write(`
        <html dir="rtl">
          <head>
            <title>طباعة سند صرف رقم ${e.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 15px 0; text-align: right; }
              .label { font-weight: bold; width: 150px; text-align: right; }
              .value { flex: 1; border-bottom: 1px dotted #333; padding-bottom: 5px; text-align: right; }
              .amount { font-size: 20px; font-weight: bold; color: #d32f2f; text-align: center; }
              .signature { margin-top: 50px; display: flex; justify-content: space-between; }
              .signature div { text-align: center; width: 200px; }
              .signature-line { border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; }
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند صرف</h1>
              <h2>رقم: ${e.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span class="value">${new Date(e.entry_date).toLocaleDateString("en-GB")}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="value amount">${(e.amount||e.total_debit||0).toLocaleString()} ${e.currency_code||"ر.ي"}</span>
              </div>
              <div class="row">
                <span class="label">صرف إلى:</span>
                <span class="value">${e.beneficiary_name||e.payee_name||e.debit_account_name||"غير محدد"}</span>
                ${e.beneficiary_type||e.payee_type?`<br><small style="color: #666;">(${"client"===(e.beneficiary_type||e.payee_type)?"عميل":"employee"===(e.beneficiary_type||e.payee_type)?"موظف":"supplier"===(e.beneficiary_type||e.payee_type)?"مورد":"خارجي"})</small>`:""}
              </div>
              <div class="row">
                <span class="label">من حساب:</span>
                <span class="value">${e.credit_account_name}</span>
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span class="value">${e.description}</span>
              </div>
              <div class="row">
                <span class="label">طريقة الدفع:</span>
                <span class="value">${e.payment_method_name||"نقد"}</span>
              </div>
              ${e.reference_number?`
              <div class="row">
                <span class="label">رقم المرجع:</span>
                <span class="value">${e.reference_number}</span>
              </div>
              `:""}
            </div>
            <div class="signature">
              <div>
                <div class="signature-line">المحاسب</div>
              </div>
              <div>
                <div class="signature-line">المستلم</div>
              </div>
              <div>
                <div class="signature-line">المدير المالي</div>
              </div>
            </div>
            <script>
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                }
              }
            </script>
          </body>
        </html>
      `),a.document.close())},ee=e.filter(e=>e.entry_number.toLowerCase().includes(T.toLowerCase())||(e.beneficiary_name||e.payee_name||e.debit_account_name||"").toLowerCase().includes(T.toLowerCase())||e.description.toLowerCase().includes(T.toLowerCase())),ea=e=>{let a={draft:{label:"مسودة",variant:"secondary"},approved:{label:"معتمد",variant:"default"},cancelled:{label:"ملغي",variant:"destructive"}},t=a[e]||a.draft;return(0,s.jsx)(u.E,{variant:t.variant,children:t.label})};return(0,s.jsx)(r.A,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"space-y-6 p-6 bg-white min-h-screen",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-red-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"سندات الصرف"}),(0,s.jsx)("p",{className:"text-gray-600",children:"إدارة سندات الصرف والمدفوعات"})]})]}),(0,s.jsxs)(l.$,{onClick:()=>M(!0),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 ml-2"}),"سند صرف جديد"]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,s.jsx)(n.ZB,{children:"سندات الصرف"}),(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(c.p,{placeholder:"البحث في سندات الصرف...",value:T,onChange:e=>V(e.target.value),className:"pr-10"})]})]}),(0,s.jsx)(n.Wu,{children:E?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"جاري تحميل السندات..."})]}):0===ee.length?(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"لا توجد سندات صرف"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"رقم السند"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"التاريخ"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"المبلغ"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"الدافع"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"المستفيد"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"القضية"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"البيان"}),(0,s.jsx)("th",{className:"text-center p-3 font-semibold text-black",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{children:ee.map((e,a)=>(0,s.jsxs)("tr",{className:`border-b hover:bg-gray-50 ${a%2==0?"bg-white":"bg-gray-25"}`,children:[(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"font-mono text-black font-medium",children:e.entry_number}),ea(e.status)]})}),(0,s.jsx)("td",{className:"p-3 text-black",children:new Date(e.entry_date).toLocaleDateString("en-GB")}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("span",{className:"font-bold text-black",children:[(e.amount||e.total_debit||0).toLocaleString()," ",e.currency_code||"ر.ي"]})}),(0,s.jsx)("td",{className:"p-3 text-black",children:(0,s.jsx)("span",{className:"font-medium",children:e.credit_account_name||"غير محدد"})}),(0,s.jsxs)("td",{className:"p-3 text-black",children:[(0,s.jsx)("span",{className:"font-medium",children:e.beneficiary_name||e.payee_name||"غير محدد"}),(e.beneficiary_type||e.payee_type)&&(0,s.jsxs)("span",{className:"text-xs text-black block",children:["(","client"===(e.beneficiary_type||e.payee_type)?"عميل":"employee"===(e.beneficiary_type||e.payee_type)?"موظف":"supplier"===(e.beneficiary_type||e.payee_type)?"مورد":"خارجي",")"]})]}),(0,s.jsx)("td",{className:"p-3 text-black",children:e.case_number?(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium text-black",children:e.case_number}),(0,s.jsx)("span",{className:"text-xs text-black",children:e.case_title})]}):(0,s.jsx)("span",{className:"text-black",children:"بدون قضية"})}),(0,s.jsx)("td",{className:"p-3 text-black max-w-xs truncate",title:e.description,children:e.description}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>K(e),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",title:"مشاهدة",children:(0,s.jsx)(y.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>W(e),className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",title:"تعديل",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>X(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"حذف",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>Q(e),className:"text-gray-600 hover:text-gray-700 hover:bg-gray-50",title:"طباعة",children:(0,s.jsx)(_.A,{className:"h-4 w-4"})})]})})]},`voucher-${e.id}-${a}`))})]})})})]}),(0,s.jsx)(m.lG,{open:F,onOpenChange:M,children:(0,s.jsxs)(m.Cf,{className:"max-w-[95vw] max-h-[95vh] w-[95vw] overflow-y-auto",children:[(0,s.jsxs)(m.c7,{className:"bg-gradient-to-r from-red-50 to-red-100 p-4 border-b",children:[(0,s.jsxs)(m.L3,{className:"text-2xl font-bold text-red-800 flex items-center",children:[(0,s.jsx)(x.A,{className:"ml-2 h-6 w-6"}),G?"تعديل سند الصرف":"سند صرف جديد"]}),(0,s.jsx)(m.rr,{className:"text-red-600 mt-2",children:G?"تعديل بيانات سند الصرف المحدد":"إنشاء سند صرف جديد مع تفاصيل المبلغ والحساب"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,s.jsxs)("form",{onSubmit:H,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"entry_date",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCC5 تاريخ السند"}),(0,s.jsx)(c.p,{id:"entry_date",type:"date",value:B.entry_date,onChange:e=>U({...B,entry_date:e.target.value}),required:!0,className:"bg-gray-50 border-gray-300 focus:border-green-500 focus:bg-white transition-colors"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"amount",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCB0 المبلغ"}),(0,s.jsx)(c.p,{id:"amount",type:"number",step:"0.01",value:B.amount,onChange:e=>U({...B,amount:e.target.value}),required:!0,placeholder:"0.00",className:"bg-gray-50 border-gray-300 focus:border-red-500 focus:bg-white transition-colors text-right font-medium"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"currency_id",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCB1 العملة"}),(0,s.jsxs)(p.l6,{value:B.currency_id,onValueChange:e=>U({...B,currency_id:e}),children:[(0,s.jsx)(p.bq,{className:"bg-gray-50 border-gray-300 focus:border-orange-500 focus:bg-white transition-colors",children:(0,s.jsx)(p.yv,{placeholder:"اختر العملة"})}),(0,s.jsx)(p.gC,{children:j.map(e=>(0,s.jsxs)(p.eb,{value:e.id.toString(),children:[(0,s.jsx)("span",{className:"font-medium",children:e.currency_code})," - ",e.currency_name]},e.id))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"payment_method_id",className:"text-sm font-semibold text-indigo-700 bg-indigo-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCB3 طريقة الدفع"}),(0,s.jsxs)(p.l6,{value:B.payment_method_id,onValueChange:e=>U({...B,payment_method_id:e}),children:[(0,s.jsx)(p.bq,{className:"bg-gray-50 border-gray-300 focus:border-indigo-500 focus:bg-white transition-colors",children:(0,s.jsx)(p.yv,{placeholder:"اختر طريقة الدفع"})}),(0,s.jsx)(p.gC,{children:N.map(e=>(0,s.jsx)(p.eb,{value:e.id.toString(),children:e.method_name},e.id))})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"cost_center_id",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83C\uDFE2 مركز التكلفة"}),(0,s.jsxs)(p.l6,{value:B.cost_center_id,onValueChange:e=>U({...B,cost_center_id:e}),children:[(0,s.jsx)(p.bq,{className:"bg-gray-50 border-gray-300 focus:border-orange-500 focus:bg-white transition-colors",children:(0,s.jsx)(p.yv,{placeholder:"\uD83D\uDD0D اختر مركز التكلفة..."})}),(0,s.jsxs)(p.gC,{className:"max-h-60",children:[(0,s.jsx)(p.eb,{value:"0",children:"بدون مركز تكلفة"}),C.map(e=>(0,s.jsx)(p.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.center_name})},e.id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"case_id",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"⚖️ القضية"}),(0,s.jsxs)(p.l6,{value:B.case_id,onValueChange:e=>U({...B,case_id:e}),children:[(0,s.jsx)(p.bq,{className:"bg-gray-50 border-gray-300 focus:border-purple-500 focus:bg-white transition-colors",children:(0,s.jsx)(p.yv,{placeholder:"\uD83D\uDD0D اختر القضية..."})}),(0,s.jsxs)(p.gC,{className:"max-h-60",children:[(0,s.jsx)(p.eb,{value:"0",children:"بدون قضية"}),$.map(e=>(0,s.jsx)(p.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.case_title})},e.id))]})]})]})]}),B.beneficiary_name&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsxs)("div",{className:"text-blue-600",children:["client"===B.beneficiary_type&&"\uD83D\uDC64","employee"===B.beneficiary_type&&"\uD83D\uDC68‍\uD83D\uDCBC","supplier"===B.beneficiary_type&&"\uD83C\uDFEA","external"===B.beneficiary_type&&"\uD83C\uDF10"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-blue-600 font-medium",children:"المستفيد: "}),(0,s.jsx)("span",{className:"font-semibold text-blue-800",children:B.beneficiary_name}),(0,s.jsxs)("span",{className:"text-xs text-blue-500 mr-2",children:["(","client"===B.beneficiary_type?"عميل":"employee"===B.beneficiary_type?"موظف":"supplier"===B.beneficiary_type?"مورد":"خارجي",")"]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-black",children:"\uD83C\uDFE6 الحساب المدين (المستفيد)"}),(0,s.jsxs)("select",{value:B.debit_account_id,onChange:e=>{U({...B,debit_account_id:e.target.value}),O(e.target.value)},className:"w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"اختر الحساب المدين (المستفيد)..."}),t.filter(e=>e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-black",children:"\uD83C\uDFE6 الحساب الدائن (المدفوع منه)"}),(0,s.jsxs)("select",{value:B.credit_account_id,onChange:e=>U({...B,credit_account_id:e.target.value}),className:"w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"اختر الحساب الدائن..."}),t.filter(e=>"أصول"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")"]},e.id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"description",className:"text-sm font-semibold text-black bg-gray-100 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCDD البيان"}),(0,s.jsx)(d.T,{id:"description",value:B.description,onChange:e=>U({...B,description:e.target.value}),required:!0,placeholder:"اكتب وصف العملية...",className:"bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors min-h-[80px]"})]}),(0,s.jsxs)(m.Es,{className:"flex justify-between items-center pt-6 border-t",children:[(0,s.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>M(!1),className:"px-6",children:"❌ إلغاء"}),G&&(0,s.jsx)(l.$,{type:"button",variant:"secondary",onClick:()=>{window.print()},className:"px-6",children:"\uD83D\uDDA8️ طباعة"})]}),(0,s.jsx)(l.$,{type:"submit",className:"px-8 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800",children:G?"✅ تحديث السند":"\uD83D\uDCBE حفظ السند"})]})]})})]})})]})})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,t)=>{"use strict";t.d(a,{T:()=>n});var s=t(60687),i=t(43210),r=t(4780);let n=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...a}));n.displayName="Textarea"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>p,Es:()=>u,L3:()=>x,c7:()=>m,lG:()=>c,rr:()=>h});var s=t(60687),i=t(43210),r=t(37908),n=t(11860),l=t(4780);let c=r.bL;r.l9;let o=r.ZL;r.bm;let d=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));d.displayName=r.hJ.displayName;let p=i.forwardRef(({className:e,children:a,...t},i)=>(0,s.jsxs)(o,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{ref:i,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=r.UC.displayName;let m=({className:e,...a})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});m.displayName="DialogHeader";let u=({className:e,...a})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});u.displayName="DialogFooter";let x=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));x.displayName=r.hE.displayName;let h=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));h.displayName=r.VY.displayName},71444:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},77872:(e,a,t)=>{Promise.resolve().then(t.bind(t,20189))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var s=t(60687),i=t(43210),r=t(4780);let n=i.forwardRef(({className:e,...a},t)=>(0,s.jsx)("label",{ref:t,className:(0,r.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...a}));n.displayName="Label"},80762:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o});var s=t(65239),i=t(48088),r=t(88170),n=t.n(r),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(a,c);let o={children:["",{children:["accounting",{children:["payment-vouchers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86691)),"D:\\mohaminew\\src\\app\\accounting\\payment-vouchers\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\mohaminew\\src\\app\\accounting\\payment-vouchers\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/accounting/payment-vouchers/page",pathname:"/accounting/payment-vouchers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},86691:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\payment-vouchers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\payment-vouchers\\page.tsx","default")},88233:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[4447,8409,4036,8106,2614,7932],()=>t(80762));module.exports=s})();