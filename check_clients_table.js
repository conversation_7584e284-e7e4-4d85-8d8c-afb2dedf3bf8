// التحقق من جدول العملاء (الموكلين) في قاعدة البيانات rubaie
const { Pool } = require('pg');

async function checkClientsTable() {
  console.log('🔍 التحقق من جدول العملاء (الموكلين) في قاعدة البيانات rubaie...\n');

  // الاتصال بقاعدة البيانات الأصلية (mohammi)
  const sourcePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'mohammi',
    password: 'yemen123',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات الهدف (rubaie)
  const targetPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    console.log('📋 الخطوة 1: التحقق من وجود جدول العملاء في mohammi...');
    
    // التحقق من وجود الجدول في قاعدة البيانات الأصلية
    const sourceTableCheck = await sourcePool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'clients'
    `);
    
    if (sourceTableCheck.rows.length === 0) {
      console.log('❌ جدول العملاء غير موجود في قاعدة البيانات الأصلية mohammi');
      return;
    }
    
    console.log('✅ جدول العملاء موجود في mohammi');
    
    // عد العملاء في قاعدة البيانات الأصلية
    const sourceCount = await sourcePool.query('SELECT COUNT(*) as count FROM clients');
    console.log(`📊 عدد العملاء في mohammi: ${sourceCount.rows[0].count}`);
    
    console.log('\n📋 الخطوة 2: التحقق من وجود جدول العملاء في rubaie...');
    
    // التحقق من وجود الجدول في قاعدة البيانات الهدف
    const targetTableCheck = await targetPool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'clients'
    `);
    
    if (targetTableCheck.rows.length === 0) {
      console.log('❌ جدول العملاء غير موجود في قاعدة البيانات rubaie');
      console.log('🔧 سيتم إنشاؤه ونسخ البيانات...');
      
      await copyClientsTable(sourcePool, targetPool);
      return;
    }
    
    console.log('✅ جدول العملاء موجود في rubaie');
    
    // عد العملاء في قاعدة البيانات الهدف
    const targetCount = await targetPool.query('SELECT COUNT(*) as count FROM clients');
    console.log(`📊 عدد العملاء في rubaie: ${targetCount.rows[0].count}`);
    
    // مقارنة العدد
    const sourceTotal = parseInt(sourceCount.rows[0].count);
    const targetTotal = parseInt(targetCount.rows[0].count);
    
    if (sourceTotal === targetTotal) {
      console.log('✅ عدد العملاء متطابق في كلا قاعدتي البيانات');
      
      // عرض عينة من العملاء
      const sampleClients = await targetPool.query('SELECT id, name, phone, email FROM clients LIMIT 5');
      console.log('\n📋 عينة من العملاء في rubaie:');
      sampleClients.rows.forEach(client => {
        console.log(`   ${client.id}. ${client.name} - ${client.phone || 'لا يوجد هاتف'} - ${client.email || 'لا يوجد إيميل'}`);
      });
      
    } else {
      console.log(`⚠️ عدد العملاء غير متطابق!`);
      console.log(`   mohammi: ${sourceTotal} عميل`);
      console.log(`   rubaie: ${targetTotal} عميل`);
      console.log('🔧 سيتم إعادة نسخ البيانات...');
      
      await copyClientsTable(sourcePool, targetPool);
    }

  } catch (error) {
    console.error('❌ خطأ في التحقق من جدول العملاء:', error);
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }
}

// دالة نسخ جدول العملاء
async function copyClientsTable(sourcePool, targetPool) {
  console.log('\n🔄 بدء نسخ جدول العملاء...');
  
  try {
    // جلب هيكل الجدول من المصدر
    console.log('📋 جلب هيكل جدول العملاء...');
    
    const structureResult = await sourcePool.query(`
      SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'clients' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log(`   ✅ ${structureResult.rows.length} عمود`);
    
    // حذف الجدول إذا كان موجوداً
    console.log('🗑️ حذف جدول العملاء إذا كان موجوداً...');
    await targetPool.query('DROP TABLE IF EXISTS clients CASCADE');
    
    // إنشاء الجدول الجديد
    console.log('🔨 إنشاء جدول العملاء...');
    
    await targetPool.query(`
      CREATE TABLE clients (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        email VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        national_id VARCHAR(50),
        passport_number VARCHAR(50),
        date_of_birth DATE,
        gender VARCHAR(20),
        marital_status VARCHAR(50),
        occupation VARCHAR(100),
        notes TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('   ✅ تم إنشاء جدول العملاء');
    
    // جلب البيانات من المصدر
    console.log('📊 جلب بيانات العملاء من mohammi...');
    
    const clientsData = await sourcePool.query('SELECT * FROM clients');
    const clients = clientsData.rows;
    
    console.log(`   📈 ${clients.length} عميل`);
    
    if (clients.length === 0) {
      console.log('⚠️ لا توجد بيانات عملاء للنسخ');
      return;
    }
    
    // نسخ البيانات
    console.log('📥 نسخ بيانات العملاء...');
    
    for (let i = 0; i < clients.length; i++) {
      const client = clients[i];
      
      try {
        const columns = Object.keys(client).filter(key => key !== 'id');
        const values = columns.map(col => client[col]);
        const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
        
        const insertQuery = `
          INSERT INTO clients (${columns.join(', ')}) 
          VALUES (${placeholders})
        `;
        
        await targetPool.query(insertQuery, values);
        
        if ((i + 1) % 5 === 0 || i === clients.length - 1) {
          console.log(`   📝 تم نسخ ${i + 1}/${clients.length} عميل`);
        }
        
      } catch (insertError) {
        console.log(`   ❌ خطأ في نسخ العميل ${client.name}:`, insertError.message);
      }
    }
    
    // إعادة تعيين sequence
    console.log('🔄 إعادة تعيين sequence...');
    await targetPool.query(`SELECT setval('clients_id_seq', COALESCE(MAX(id), 1)) FROM clients`);
    
    // التحقق من النتائج
    const finalCount = await targetPool.query('SELECT COUNT(*) as count FROM clients');
    console.log(`✅ تم نسخ ${finalCount.rows[0].count} عميل بنجاح`);
    
  } catch (error) {
    console.error('❌ خطأ في نسخ جدول العملاء:', error);
  }
}

// تشغيل التحقق
checkClientsTable().catch(console.error);
