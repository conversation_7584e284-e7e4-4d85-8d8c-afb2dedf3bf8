(()=>{var e={};e.id=9285,e.ids=[9285],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14618:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=s(65239),r=s(48088),c=s(88170),l=s.n(c),n=s(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(t,i);let d={children:["",{children:["accounting",{children:["opening-balances",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68435)),"D:\\mohaminew\\src\\app\\accounting\\opening-balances\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\opening-balances\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/accounting/opening-balances/page",pathname:"/accounting/opening-balances",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29437:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(60687),r=s(43210),c=s(98254),l=s(44493),n=s(29523),i=s(89667),d=s(96834),o=s(70352),x=s(59402),h=s(96474),m=s(93613),u=s(99270),p=s(23928),b=s(88233),g=s(11860),j=s(5336),v=s(8819);function N(){let[e,t]=(0,r.useState)([]),[s,N]=(0,r.useState)([]),[y,f]=(0,r.useState)(!0),[_,w]=(0,r.useState)(""),[k,A]=(0,r.useState)(null),[C,$]=(0,r.useState)(!1),[P,F]=(0,r.useState)([{id:"1",account_id:null,account_code:"",account_name:"",debit_balance:"",credit_balance:""}]),[M,S]=(0,r.useState)({}),E=async()=>{try{f(!0);let e=await fetch("/api/accounting/opening-balances"),s=await e.json(),a=await fetch("/api/accounting/chart-of-accounts?include_linked=true&only_transactional=true"),r=await a.json();s.success&&t(s.data||[]),r.success?(console.log("تم جلب الحسابات:",r.accounts?.length||0),N(r.accounts||[])):(console.error("خطأ في جلب الحسابات:",r.error),A({type:"error",text:"فشل في جلب دليل الحسابات"}))}catch(e){console.error("خطأ في جلب البيانات:",e),A({type:"error",text:"حدث خطأ في جلب البيانات"})}finally{f(!1)}},L=e.filter(e=>e.account_name.toLowerCase().includes(_.toLowerCase())||e.account_code.toLowerCase().includes(_.toLowerCase())),D=e=>{if(P.length>1){F(P.filter(t=>t.id!==e));let t={...M};delete t[e],S(t)}},q=(e,t,s)=>{F(P.map(a=>a.id===e?{...a,[t]:s}:a))},z=(e,t)=>{let s=t.id;t.is_linked_record&&(s="clients"===t.original_table?`client_${t.external_id}`:`employee_${t.external_id}`),q(e,"account_id",s),q(e,"account_code",t.account_code),q(e,"account_name",t.account_name),S(t=>({...t,[e]:""}))},W=(e,t)=>{S(s=>({...s,[e]:t}))},O=e=>{let t=M[e]||"";return t.trim()?s.filter(e=>e.account_code.toLowerCase().includes(t.toLowerCase())||e.account_name.toLowerCase().includes(t.toLowerCase())||e.account_type.toLowerCase().includes(t.toLowerCase())):s},{totalDebit:T,totalCredit:R}={totalDebit:P.reduce((e,t)=>e+(parseFloat(t.debit_balance)||0),0),totalCredit:P.reduce((e,t)=>e+(parseFloat(t.credit_balance)||0),0)},G=.01>Math.abs(T-R),V=async()=>{try{if(!G)return void A({type:"error",text:`يجب أن يكون إجمالي المدين (${T.toFixed(2)}) مساوياً لإجمالي الدائن (${R.toFixed(2)})`});let e=P.filter(e=>e.account_id&&(parseFloat(e.debit_balance)>0||parseFloat(e.credit_balance)>0));if(0===e.length)return void A({type:"error",text:"يجب إضافة حساب واحد على الأقل برصيد"});let t=e.map(e=>({account_id:e.account_id,debit_balance:parseFloat(e.debit_balance)||0,credit_balance:parseFloat(e.credit_balance)||0,balance_date:new Date().toISOString().split("T")[0]})),s=await fetch("/api/accounting/opening-balances/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({balances:t})}),a=await s.json();a.success?(A({type:"success",text:`تم حفظ ${e.length} رصيد افتتاحي بنجاح`}),$(!1),F([{id:"1",account_id:null,account_code:"",account_name:"",debit_balance:"",credit_balance:""}]),S({}),await E()):A({type:"error",text:a.error||"فشل في حفظ الأرصدة"})}catch(e){console.error("خطأ في الحفظ:",e),A({type:"error",text:"حدث خطأ أثناء حفظ الأرصدة"})}},Z=async e=>{if(confirm("هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟"))try{let t=await fetch(`/api/accounting/opening-balances?id=${e}`,{method:"DELETE"}),s=await t.json();s.success?(A({type:"success",text:"تم حذف الرصيد الافتتاحي بنجاح"}),await E()):A({type:"error",text:s.error||"فشل في حذف الرصيد"})}catch(e){console.error("خطأ في الحذف:",e),A({type:"error",text:"حدث خطأ أثناء حذف الرصيد"})}};return y?(0,a.jsx)(c.O,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 animate-spin mx-auto mb-2"}),(0,a.jsx)("span",{children:"جاري التحميل..."})]})})}):(0,a.jsx)(c.O,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"الأرصدة الافتتاحية"}),(0,a.jsx)("p",{className:"text-gray-600",children:"إدارة الأرصدة الافتتاحية للحسابات"})]})]}),(0,a.jsxs)(n.$,{onClick:()=>$(!0),className:"bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 ml-2"}),"إضافة أرصدة افتتاحية"]})]}),k&&(0,a.jsxs)(o.Fc,{className:"success"===k.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(o.TN,{className:"success"===k.type?"text-green-800":"text-red-800",children:k.text})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:L.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي الأرصدة"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:L.reduce((e,t)=>e+t.debit_balance,0).toFixed(2)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي المدين"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:L.reduce((e,t)=>e+t.credit_balance,0).toFixed(2)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي الدائن"})]})})]}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(i.p,{placeholder:"البحث في الأرصدة الافتتاحية...",value:_,onChange:e=>w(e.target.value),className:"pr-10"})]})})}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 ml-2"}),"الأرصدة الافتتاحية (",L.length,")"]})}),(0,a.jsx)(l.Wu,{children:0===L.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا توجد أرصدة افتتاحية"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:'انقر على "إضافة أرصدة افتتاحية" لبدء الإضافة'})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-right p-3",children:"رقم الحساب"}),(0,a.jsx)("th",{className:"text-right p-3",children:"اسم الحساب"}),(0,a.jsx)("th",{className:"text-right p-3",children:"الرصيد المدين"}),(0,a.jsx)("th",{className:"text-right p-3",children:"الرصيد الدائن"}),(0,a.jsx)("th",{className:"text-right p-3",children:"تاريخ الرصيد"}),(0,a.jsx)("th",{className:"text-center p-3",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{children:L.map(e=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"p-3 font-mono text-blue-600",children:e.account_code}),(0,a.jsx)("td",{className:"p-3",children:e.account_name}),(0,a.jsx)("td",{className:"p-3 text-green-600 font-medium",children:e.debit_balance>0?e.debit_balance.toFixed(2):"-"}),(0,a.jsx)("td",{className:"p-3 text-red-600 font-medium",children:e.credit_balance>0?e.credit_balance.toFixed(2):"-"}),(0,a.jsx)("td",{className:"p-3 text-gray-600",children:e.balance_date}),(0,a.jsx)("td",{className:"p-3 text-center",children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>Z(e.id),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})})]},e.id))})]})})})]}),C&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"إضافة أرصدة افتتاحية"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>$(!1),children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(n.$,{onClick:()=>{F([...P,{id:Date.now().toString(),account_id:null,account_code:"",account_name:"",debit_balance:"",credit_balance:""}])},variant:"outline",className:"border-green-600 text-green-600 hover:bg-green-50",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 ml-2"}),"إضافة صف جديد"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"إجمالي المدين: "}),(0,a.jsx)("span",{className:"font-bold text-green-600",children:T.toFixed(2)})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"إجمالي الدائن: "}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:R.toFixed(2)})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[G?(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(m.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:`text-sm ml-1 ${G?"text-green-600":"text-red-600"}`,children:G?"متوازن":"غير متوازن"})]})]})]}),(0,a.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-right p-3 border-b",children:"اسم الحساب"}),(0,a.jsx)("th",{className:"text-right p-3 border-b",children:"الرصيد المدين"}),(0,a.jsx)("th",{className:"text-right p-3 border-b",children:"الرصيد الدائن"}),(0,a.jsx)("th",{className:"text-center p-3 border-b",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{children:P.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("td",{className:"p-3",style:{minWidth:"300px"},children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(i.p,{placeholder:"ابحث عن الحساب...",value:M[e.id]||"",onChange:t=>W(e.id,t.target.value),className:"pr-10"})]}),e.account_id&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)("span",{className:"font-mono text-sm text-blue-600 font-medium",children:e.account_code}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.account_name}),(0,a.jsx)(d.E,{variant:"outline",className:`text-xs ${e.account_code?.startsWith("C")?"border-green-500 text-green-700 bg-green-50":e.account_code?.startsWith("E")?"border-purple-500 text-purple-700 bg-purple-50":"border-blue-500 text-blue-700 bg-blue-50"}`,children:e.account_code?.startsWith("C")?"عميل":e.account_code?.startsWith("E")?"موظف":"حساب مالي"})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{q(e.id,"account_id",null),q(e.id,"account_code",""),q(e.id,"account_name","")},className:"h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(g.A,{className:"h-3 w-3"})})]}),M[e.id]&&!e.account_id&&(0,a.jsxs)("div",{className:"max-h-60 overflow-y-auto border rounded bg-white shadow-lg z-50",children:[O(e.id).slice(0,15).map(t=>(0,a.jsxs)("div",{onClick:()=>z(e.id,t),className:"p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)("span",{className:"font-mono text-sm text-blue-600 font-medium",children:t.account_code}),(0,a.jsx)("span",{className:"text-sm font-medium",children:t.account_name})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1 space-x-reverse",children:(0,a.jsx)(d.E,{variant:"outline",className:`text-xs ${t.is_linked_record?"clients"===t.original_table?"border-green-500 text-green-700 bg-green-50":"border-purple-500 text-purple-700 bg-purple-50":"border-blue-500 text-blue-700 bg-blue-50"}`,children:t.is_linked_record?"clients"===t.original_table?"عميل":"موظف":t.account_type})})]}),t.description&&(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1 mr-16",children:t.description})]},t.id)),0===O(e.id).length&&(0,a.jsxs)("div",{className:"p-4 text-center text-gray-500 text-sm",children:[(0,a.jsx)("div",{className:"mb-2",children:"لا توجد نتائج للبحث"}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:"جرب البحث باسم الحساب أو رقمه"})]}),O(e.id).length>15&&(0,a.jsxs)("div",{className:"p-2 text-center text-gray-400 text-xs border-t bg-gray-50",children:["يوجد ",O(e.id).length-15," نتيجة إضافية..."]})]})]})}),(0,a.jsx)("td",{className:"p-3",children:(0,a.jsx)(i.p,{type:"number",step:"0.01",placeholder:"0.00",value:e.debit_balance,onChange:t=>q(e.id,"debit_balance",t.target.value),className:"text-center"})}),(0,a.jsx)("td",{className:"p-3",children:(0,a.jsx)(i.p,{type:"number",step:"0.01",placeholder:"0.00",value:e.credit_balance,onChange:t=>q(e.id,"credit_balance",t.target.value),className:"text-center"})}),(0,a.jsx)("td",{className:"p-3 text-center",children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id),disabled:1===P.length,className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})})]},e.id))})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 space-x-reverse pt-4 border-t",children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>$(!1),children:"إلغاء"}),(0,a.jsxs)(n.$,{onClick:V,disabled:!G,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 ml-2"}),"حفظ الأرصدة"]})]}),!G&&(0,a.jsxs)(o.Fc,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(o.TN,{className:"text-red-800",children:"يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن لحفظ الأرصدة"})]})]})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\opening-balances\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\opening-balances\\page.tsx","default")},70352:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>x});var a=s(60687),r=s(43210),c=s(49384);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=c.$;var i=s(4780);let d=((e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:r,defaultVariants:c}=t,i=Object.keys(r).map(e=>{let t=null==s?void 0:s[e],a=null==c?void 0:c[e];if(null===t)return null;let n=l(t)||l(a);return r[e][n]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return n(e,i,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...c,...d}[t]):({...c,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)})("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(d({variant:t}),e),...s}));o.displayName="Alert",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let x=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));x.displayName="AlertDescription"},77136:(e,t,s)=>{Promise.resolve().then(s.bind(s,29437))},79551:e=>{"use strict";e.exports=require("url")},83760:(e,t,s)=>{Promise.resolve().then(s.bind(s,68435))},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,8409,7932],()=>s(14618));module.exports=a})();