(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>d});var o=s(64939),a=s(29021),n=s.n(a),c=s(33873),i=s.n(c),u=e([o]);o=(u.then?(await u)():u)[0];let p=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],s=p.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new o.Pool(l);async function d(e,t){let s=await E.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22390:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>d,GET:()=>c,POST:()=>i,PUT:()=>u});var o=s(32190),a=s(5069),n=e([a]);async function c(){try{let e=await (0,a.P)(`
      SELECT 
        ob.id,
        ob.account_id,
        ob.debit_balance,
        ob.credit_balance,
        ob.balance_date,
        ob.created_date,
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_code
          WHEN ob.account_id LIKE 'client_%' THEN 'C' || LPAD(SUBSTRING(ob.account_id FROM 8)::text, 6, '0')
          WHEN ob.account_id LIKE 'employee_%' THEN 'E' || LPAD(SUBSTRING(ob.account_id FROM 10)::text, 6, '0')
          ELSE ob.account_id
        END as account_code,
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_name
          WHEN ob.account_id LIKE 'client_%' THEN c.name
          WHEN ob.account_id LIKE 'employee_%' THEN e.name
          ELSE 'حساب غير معروف'
        END as account_name
      FROM opening_balances ob
      LEFT JOIN chart_of_accounts coa ON ob.account_id ~ '^[0-9]+$' AND ob.account_id::integer = coa.id
      LEFT JOIN clients c ON ob.account_id LIKE 'client_%' AND SUBSTRING(ob.account_id FROM 8) ~ '^[0-9]+$' AND SUBSTRING(ob.account_id FROM 8)::integer = c.id
      LEFT JOIN employees e ON ob.account_id LIKE 'employee_%' AND SUBSTRING(ob.account_id FROM 10) ~ '^[0-9]+$' AND SUBSTRING(ob.account_id FROM 10)::integer = e.id
      ORDER BY 
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_code
          WHEN ob.account_id LIKE 'client_%' THEN 'C' || LPAD(SUBSTRING(ob.account_id FROM 8)::text, 6, '0')
          WHEN ob.account_id LIKE 'employee_%' THEN 'E' || LPAD(SUBSTRING(ob.account_id FROM 10)::text, 6, '0')
          ELSE ob.account_id
        END
    `);return o.NextResponse.json({success:!0,data:e.rows,total:e.rows.length,message:"تم جلب الأرصدة الافتتاحية بنجاح"})}catch(e){return console.error("خطأ في جلب الأرصدة الافتتاحية:",e),o.NextResponse.json({success:!1,error:"فشل في جلب الأرصدة الافتتاحية",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e){try{let{account_id:t,debit_balance:s=0,credit_balance:r=0,balance_date:n}=await e.json();if(!t||!n)return o.NextResponse.json({success:!1,error:"الحساب وتاريخ الرصيد مطلوبان"},{status:400});if((await (0,a.P)("SELECT id FROM opening_balances WHERE account_id = $1",[t])).rows.length>0)return o.NextResponse.json({success:!1,error:"يوجد رصيد افتتاحي لهذا الحساب مسبقاً"},{status:400});let c=parseFloat(s)||0,i=parseFloat(r)||0;if(c<=0&&i<=0)return o.NextResponse.json({success:!1,error:"يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر"},{status:400});if(c>0&&i>0)return o.NextResponse.json({success:!1,error:"لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت"},{status:400});let u=await (0,a.P)(`
      INSERT INTO opening_balances (account_id, debit_balance, credit_balance, balance_date)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `,[parseInt(t),parseFloat(s)||0,parseFloat(r)||0,n]);return o.NextResponse.json({success:!0,data:u.rows[0],message:"تم إضافة الرصيد الافتتاحي بنجاح"})}catch(e){return console.error("خطأ في إضافة الرصيد الافتتاحي:",e),o.NextResponse.json({success:!1,error:"فشل في إضافة الرصيد الافتتاحي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function u(e){try{let{id:t,account_id:s,debit_balance:r=0,credit_balance:n=0,balance_date:c}=await e.json();if(!t||!s||!c)return o.NextResponse.json({success:!1,error:"المعرف والحساب وتاريخ الرصيد مطلوبان"},{status:400});let i=parseFloat(r)||0,u=parseFloat(n)||0;if(i<=0&&u<=0)return o.NextResponse.json({success:!1,error:"يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر"},{status:400});if(i>0&&u>0)return o.NextResponse.json({success:!1,error:"لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت"},{status:400});let d=await (0,a.P)(`
      UPDATE opening_balances
      SET account_id = $1, debit_balance = $2, credit_balance = $3, balance_date = $4
      WHERE id = $5
      RETURNING *
    `,[parseInt(s),parseFloat(r)||0,parseFloat(n)||0,c,parseInt(t)]);if(0===d.rows.length)return o.NextResponse.json({success:!1,error:"الرصيد الافتتاحي غير موجود"},{status:404});return o.NextResponse.json({success:!0,data:d.rows[0],message:"تم تحديث الرصيد الافتتاحي بنجاح"})}catch(e){return console.error("خطأ في تحديث الرصيد الافتتاحي:",e),o.NextResponse.json({success:!1,error:"فشل في تحديث الرصيد الافتتاحي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function d(e){try{let{searchParams:t}=new URL(e.url),s=t.get("id");if(!s)return o.NextResponse.json({success:!1,error:"معرف الرصيد الافتتاحي مطلوب"},{status:400});let r=await (0,a.P)("DELETE FROM opening_balances WHERE id = $1 RETURNING *",[s]);if(0===r.rows.length)return o.NextResponse.json({success:!1,error:"الرصيد الافتتاحي غير موجود"},{status:404});return o.NextResponse.json({success:!0,message:"تم حذف الرصيد الافتتاحي بنجاح"})}catch(e){return console.error("خطأ في حذف الرصيد الافتتاحي:",e),o.NextResponse.json({success:!1,error:"فشل في حذف الرصيد الافتتاحي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}a=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43928:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>E,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var o=s(96559),a=s(48088),n=s(37719),c=s(22390),i=e([c]);c=(i.then?(await i)():i)[0];let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/opening-balances/route",pathname:"/api/accounting/opening-balances",filename:"route",bundlePath:"app/api/accounting/opening-balances/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\opening-balances\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:E}=d;function u(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}r()}catch(e){r(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(43928));module.exports=r})();