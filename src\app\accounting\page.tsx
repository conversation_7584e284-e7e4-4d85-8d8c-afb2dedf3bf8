'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Calculator, 
  BookOpen, 
  Receipt, 
  FileText, 
  BarChart3, 
  TrendingUp,
  DollarSign,
  Users,
  Building,
  ArrowRight,
  Link2,
  Settings
} from 'lucide-react'

interface AccountingSummary {
  totalPaymentVouchers: number
  totalReceiptVouchers: number
  totalJournalEntries: number
  totalAccounts: number
  monthlyPayments: number
  monthlyReceipts: number
  currentBalance: number
}

export default function AccountingPage() {
  const [summary, setSummary] = useState<AccountingSummary>({
    totalPaymentVouchers: 0,
    totalReceiptVouchers: 0,
    totalJournalEntries: 0,
    totalAccounts: 0,
    monthlyPayments: 0,
    monthlyReceipts: 0,
    currentBalance: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSummary()
  }, [])

  const fetchSummary = async () => {
    try {
      setLoading(true)
      // TODO: إنشاء API للملخص المحاسبي
      // const response = await fetch('/api/accounting/summary')
      // if (response.ok) {
      //   const data = await response.json()
      //   setSummary(data.summary)
      // }
      
      // بيانات تجريبية
      setSummary({
        totalPaymentVouchers: 45,
        totalReceiptVouchers: 32,
        totalJournalEntries: 18,
        totalAccounts: 156,
        monthlyPayments: 125000,
        monthlyReceipts: 180000,
        currentBalance: 55000
      })
    } catch (error) {
      console.error('خطأ في جلب الملخص المحاسبي:', error)
    } finally {
      setLoading(false)
    }
  }

  const accountingModules = [
    {
      title: 'ربط الحسابات',
      description: 'إدارة الحسابات الأساسية للنظام المحاسبي مع الربط التلقائي (الإيرادات، المصروفات، رأس المال، العملاء، الموظفين، الصندوق)',
      icon: Link2,
      href: '/accounting/account-linking',
      color: 'bg-indigo-500',
      stats: 'إعداد النظام',
      features: ['7 حسابات أساسية', 'ربط تلقائي', 'مراقبة الحالة']
    },
    {
      title: 'ربط الحسابات الرئيسية',
      description: 'تحديد الحسابات الهامة من دليل الحسابات (الإيرادات، المصروفات، رأس المال، العملاء، الموظفين، الصندوق)',
      icon: Settings,
      href: '/accounting/main-accounts',
      color: 'bg-slate-500',
      stats: 'إعداد متقدم',
      features: ['10 حسابات رئيسية', 'بحث قابل للتصفية', 'حفظ مباشر']
    },
    {
      title: 'الحسابات الأساسية',
      description: 'ربط الحسابات الأساسية للنظام (الإيرادات، المصروفات، الموظفين، العملاء، الصندوق) بدليل الحسابات',
      icon: Settings,
      href: '/accounting/default-accounts',
      color: 'bg-emerald-500',
      stats: '10 حسابات أساسية',
      features: ['ربط تلقائي', 'قوائم منسدلة', 'حفظ متعدد']
    },
    {
      title: 'ربط الحسابات التلقائي',
      description: 'إعداد قواعد الربط التلقائي للعملاء والموظفين الجدد مع الحسابات',
      icon: Link2,
      href: '/accounting/link-accounts',
      color: 'bg-teal-500',
      stats: 'إعدادات متقدمة',
      features: ['ربط تلقائي', 'قواعد ذكية', 'إنشاء حسابات']
    },
    {
      title: 'دليل الحسابات',
      description: 'إدارة الهيكل المحاسبي (4 مستويات) مع ربط العملاء والموظفين',
      icon: BookOpen,
      href: '/accounting/chart-of-accounts',
      color: 'bg-blue-500',
      stats: `${summary.totalAccounts} حساب`,
      features: ['4 مستويات هرمية', 'ربط العملاء والموظفين', 'حسابات تحكم']
    },
    {
      title: 'سندات الصرف',
      description: 'إدارة سندات الصرف والمدفوعات مع ربط القضايا',
      icon: Receipt,
      href: '/accounting/payment-vouchers',
      color: 'bg-red-500',
      stats: `${summary.totalPaymentVouchers} سند`,
      features: ['ربط بالقضايا', 'طرق دفع متنوعة', 'مراكز تكلفة']
    },
    {
      title: 'سندات القبض',
      description: 'إدارة سندات القبض والمقبوضات من العملاء',
      icon: FileText,
      href: '/accounting/receipt-vouchers',
      color: 'bg-green-500',
      stats: `${summary.totalReceiptVouchers} سند`,
      features: ['تتبع المقبوضات', 'ربط بالعملاء', 'عملات متعددة']
    },
    {
      title: 'القيود اليومية',
      description: 'إدارة القيود المحاسبية المركبة والمعقدة',
      icon: Calculator,
      href: '/accounting/journal-entries',
      color: 'bg-purple-500',
      stats: `${summary.totalJournalEntries} قيد`,
      features: ['قيود متوازنة', 'تفاصيل متعددة', 'تحقق تلقائي']
    },
    {
      title: 'التقارير المحاسبية',
      description: 'تقارير شاملة: دفتر الأستاذ، ميزان المراجعة، القوائم المالية',
      icon: BarChart3,
      href: '/accounting/reports',
      color: 'bg-orange-500',
      stats: 'تقارير متقدمة',
      features: ['دفتر الأستاذ', 'ميزان المراجعة', 'القوائم المالية']
    }
  ]

  const quickStats = [
    {
      title: 'إجمالي المدفوعات الشهرية',
      value: summary.monthlyPayments,
      icon: TrendingUp,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      format: 'currency'
    },
    {
      title: 'إجمالي المقبوضات الشهرية',
      value: summary.monthlyReceipts,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      format: 'currency'
    },
    {
      title: 'الرصيد الحالي',
      value: summary.currentBalance,
      icon: Building,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      format: 'currency'
    },
    {
      title: 'إجمالي الحسابات',
      value: summary.totalAccounts,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      format: 'number'
    }
  ]

  const formatValue = (value: number, format: string) => {
    if (format === 'currency') {
      return `${value.toLocaleString()} ر.ي`
    }
    return value.toLocaleString()
  }

  return (
    <MainLayout>
      <div className="space-y-8">
        {/* العنوان الرئيسي */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3 space-x-reverse">
            <Calculator className="h-12 w-12 text-blue-600" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">النظام المحاسبي</h1>
              <p className="text-xl text-gray-600">نظام محاسبي متكامل لشركات المحاماة</p>
            </div>
          </div>
          
          <div className="flex justify-center space-x-4 space-x-reverse">
            <Badge variant="outline" className="text-sm">
              نظام 4 مستويات
            </Badge>
            <Badge variant="outline" className="text-sm">
              ربط بالقضايا
            </Badge>
            <Badge variant="outline" className="text-sm">
              عملات متعددة
            </Badge>
            <Badge variant="outline" className="text-sm">
              مراكز تكلفة
            </Badge>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? (
                        <div className="h-8 w-20 bg-gray-200 rounded animate-pulse"></div>
                      ) : (
                        formatValue(stat.value, stat.format)
                      )}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* الوحدات المحاسبية */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">الوحدات المحاسبية</h2>
            <p className="text-gray-600">اختر الوحدة المحاسبية التي تريد العمل عليها</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {accountingModules.map((module, index) => (
              <Link key={index} href={module.href}>
                <Card className="h-full hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer group">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className={`p-3 rounded-lg ${module.color} text-white`}>
                          <module.icon className="h-6 w-6" />
                        </div>
                        <div>
                          <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                            {module.title}
                          </CardTitle>
                          <Badge variant="secondary" className="mt-1">
                            {module.stats}
                          </Badge>
                        </div>
                      </div>
                      <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all" />
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <p className="text-gray-600 mb-4">{module.description}</p>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">الميزات الرئيسية:</p>
                      <div className="flex flex-wrap gap-2">
                        {module.features.map((feature, featureIndex) => (
                          <Badge key={featureIndex} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* معلومات النظام */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <h3 className="text-xl font-bold text-gray-900">مميزات النظام المحاسبي الجديد</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div className="text-center space-y-2">
                  <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto">
                    <BookOpen className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium">هيكل محاسبي متقدم</h4>
                  <p className="text-sm text-gray-600">نظام 4 مستويات مع ربط تلقائي بالعملاء والموظفين</p>
                </div>
                
                <div className="text-center space-y-2">
                  <div className="bg-green-100 p-3 rounded-full w-fit mx-auto">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-medium">تكامل مع النظام القانوني</h4>
                  <p className="text-sm text-gray-600">ربط السندات والقيود بالقضايا والعملاء</p>
                </div>
                
                <div className="text-center space-y-2">
                  <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto">
                    <BarChart3 className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-medium">تقارير شاملة</h4>
                  <p className="text-sm text-gray-600">تقارير مالية متقدمة وتحليلات تفصيلية</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
