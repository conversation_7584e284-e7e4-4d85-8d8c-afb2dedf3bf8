// فحص وإصلاح علاقة أنواع القضايا
const { Pool } = require('pg');

async function fixIssueTypesRelation() {
  console.log('🔧 فحص وإصلاح علاقة أنواع القضايا...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 فحص قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص وجود جدول أنواع القضايا
      console.log('\n   📊 فحص جدول أنواع القضايا:');
      
      const issueTypesTableExists = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'issue_types'
        )
      `);

      if (!issueTypesTableExists.rows[0].exists) {
        console.log('      ➕ إنشاء جدول أنواع القضايا...');
        await pool.query(`
          CREATE TABLE issue_types (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            category VARCHAR(100),
            color VARCHAR(7), -- لون للتمييز في الواجهة
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('      ✅ تم إنشاء جدول أنواع القضايا');
      } else {
        console.log('      ✅ جدول أنواع القضايا موجود');
      }

      // 2. فحص العمود issue_type_id في جدول القضايا
      console.log('\n   🔍 فحص العمود issue_type_id:');
      
      const issueTypeIdExists = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'issue_type_id'
      `);

      if (issueTypeIdExists.rows.length === 0) {
        console.log('      ➕ إضافة العمود issue_type_id...');
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN issue_type_id INTEGER
        `);
        console.log('      ✅ تم إضافة العمود issue_type_id');
      } else {
        const column = issueTypeIdExists.rows[0];
        console.log(`      ✅ العمود موجود: ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      }

      // 3. فحص العلاقة الحالية
      console.log('\n   🔗 فحص العلاقة الحالية:');
      
      const currentRelation = await pool.query(`
        SELECT 
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        AND kcu.column_name = 'issue_type_id'
      `);

      if (currentRelation.rows.length > 0) {
        const relation = currentRelation.rows[0];
        console.log(`      ✅ العلاقة موجودة: issues.${relation.column_name} -> ${relation.foreign_table_name}.${relation.foreign_column_name}`);
        
        // التحقق من صحة العلاقة
        if (relation.foreign_table_name === 'issue_types' && relation.foreign_column_name === 'id') {
          console.log('      ✅ العلاقة صحيحة');
        } else {
          console.log('      ⚠️ العلاقة خاطئة - سيتم إصلاحها');
          // حذف العلاقة الخاطئة
          await pool.query(`ALTER TABLE issues DROP CONSTRAINT IF EXISTS ${relation.constraint_name}`);
          console.log('      🗑️ تم حذف العلاقة الخاطئة');
        }
      } else {
        console.log('      ⚠️ العلاقة غير موجودة');
      }

      // 4. إدراج أنواع القضايا الأساسية
      console.log('\n   📋 إدراج أنواع القضايا الأساسية:');
      
      const defaultIssueTypes = [
        { name: 'قضية جنائية', category: 'جنائي', description: 'قضايا جنائية عامة', color: '#dc2626' },
        { name: 'قضية مدنية', category: 'مدني', description: 'قضايا مدنية عامة', color: '#2563eb' },
        { name: 'قضية تجارية', category: 'تجاري', description: 'قضايا تجارية ومالية', color: '#059669' },
        { name: 'قضية أحوال شخصية', category: 'أحوال شخصية', description: 'قضايا الزواج والطلاق والميراث', color: '#7c3aed' },
        { name: 'قضية عمالية', category: 'عمالي', description: 'قضايا العمل والموظفين', color: '#ea580c' },
        { name: 'قضية إدارية', category: 'إداري', description: 'قضايا إدارية وحكومية', color: '#0891b2' },
        { name: 'قضية عقارية', category: 'عقاري', description: 'قضايا العقارات والأراضي', color: '#65a30d' },
        { name: 'قضية تأمين', category: 'تأمين', description: 'قضايا التأمين والتعويضات', color: '#be185d' },
        { name: 'قضية ضريبية', category: 'ضريبي', description: 'قضايا الضرائب والرسوم', color: '#9333ea' },
        { name: 'قضية دستورية', category: 'دستوري', description: 'قضايا دستورية وحقوق أساسية', color: '#1f2937' }
      ];

      for (const issueType of defaultIssueTypes) {
        try {
          await pool.query(`
            INSERT INTO issue_types (name, category, description, color)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (name) DO UPDATE SET
              category = EXCLUDED.category,
              description = EXCLUDED.description,
              color = EXCLUDED.color,
              updated_at = CURRENT_TIMESTAMP
          `, [issueType.name, issueType.category, issueType.description, issueType.color]);
          
          console.log(`      ✅ تم إدراج/تحديث: ${issueType.name}`);
        } catch (error) {
          console.log(`      ❌ خطأ في إدراج نوع ${issueType.name}: ${error.message}`);
        }
      }

      // 5. إنشاء العلاقة الصحيحة
      console.log('\n   🔗 إنشاء العلاقة الصحيحة:');
      
      try {
        await pool.query(`
          ALTER TABLE issues 
          ADD CONSTRAINT fk_issues_issue_type_id 
          FOREIGN KEY (issue_type_id) REFERENCES issue_types(id)
          ON DELETE SET NULL
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء العلاقة: issues.issue_type_id -> issue_types.id');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('      ✅ العلاقة موجودة مسبقاً');
        } else {
          console.log(`      ⚠️ خطأ في إنشاء العلاقة: ${error.message}`);
        }
      }

      // 6. ربط القضايا الموجودة بأنواعها
      console.log('\n   🔄 ربط القضايا الموجودة بأنواعها:');
      
      // الحصول على القضايا التي لها issue_type كنص
      const issuesWithTextType = await pool.query(`
        SELECT id, case_number, issue_type, issue_type_id
        FROM issues 
        WHERE issue_type IS NOT NULL 
        AND issue_type != ''
        AND (issue_type_id IS NULL OR issue_type_id = 0)
      `);

      console.log(`      📊 عدد القضايا المطلوب ربطها: ${issuesWithTextType.rows.length}`);

      for (const issue of issuesWithTextType.rows) {
        try {
          // البحث عن نوع القضية المطابق
          let issueType = await pool.query(`
            SELECT id FROM issue_types 
            WHERE name ILIKE $1 OR category ILIKE $1
          `, [`%${issue.issue_type}%`]);

          let typeId = null;

          if (issueType.rows.length > 0) {
            typeId = issueType.rows[0].id;
          } else {
            // إنشاء نوع جديد إذا لم يوجد
            const newType = await pool.query(`
              INSERT INTO issue_types (name, category, description)
              VALUES ($1, $1, $2)
              ON CONFLICT (name) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
              RETURNING id
            `, [issue.issue_type, `نوع قضية: ${issue.issue_type}`]);
            
            typeId = newType.rows[0].id;
            console.log(`      ➕ تم إنشاء نوع جديد: "${issue.issue_type}" (ID: ${typeId})`);
          }

          // ربط القضية بالنوع
          await pool.query(`
            UPDATE issues 
            SET issue_type_id = $1
            WHERE id = $2
          `, [typeId, issue.id]);

          console.log(`      🔗 تم ربط القضية ${issue.case_number} بالنوع ${typeId}`);

        } catch (error) {
          console.log(`      ❌ خطأ في ربط القضية ${issue.case_number}: ${error.message}`);
        }
      }

      // 7. عرض الإحصائيات النهائية
      console.log('\n   📊 الإحصائيات النهائية:');
      
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issue_types) as total_types,
          (SELECT COUNT(*) FROM issues WHERE issue_type_id IS NOT NULL) as issues_with_type,
          (SELECT COUNT(*) FROM issues WHERE issue_type_id IS NULL) as issues_without_type,
          (SELECT COUNT(*) FROM issues) as total_issues
      `);

      const finalStats = stats.rows[0];
      console.log(`      - إجمالي أنواع القضايا: ${finalStats.total_types}`);
      console.log(`      - قضايا مرتبطة بأنواع: ${finalStats.issues_with_type}`);
      console.log(`      - قضايا بدون أنواع: ${finalStats.issues_without_type}`);
      console.log(`      - إجمالي القضايا: ${finalStats.total_issues}`);

      // 8. عرض أمثلة على العلاقة
      console.log('\n   📋 أمثلة على العلاقة:');
      
      const examples = await pool.query(`
        SELECT 
          it.name as type_name,
          it.category,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ') as case_numbers
        FROM issue_types it
        LEFT JOIN issues i ON it.id = i.issue_type_id
        GROUP BY it.id, it.name, it.category
        HAVING COUNT(i.id) > 0
        ORDER BY issue_count DESC
        LIMIT 5
      `);

      examples.rows.forEach(example => {
        console.log(`      📋 ${example.type_name} (${example.category}): ${example.issue_count} قضية`);
        if (example.case_numbers) {
          console.log(`         القضايا: ${example.case_numbers}`);
        }
      });

      // 9. اختبار العلاقة
      console.log('\n   🧪 اختبار العلاقة:');
      
      const relationTest = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          it.name as issue_type_name,
          it.category as issue_type_category
        FROM issues i
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        WHERE i.issue_type_id IS NOT NULL
        LIMIT 3
      `);

      relationTest.rows.forEach(test => {
        console.log(`      ✅ ${test.case_number}: ${test.title}`);
        console.log(`         النوع: ${test.issue_type_name} (${test.issue_type_category})`);
      });

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح علاقة أنواع القضايا');
  
  console.log('\n📋 ملخص العلاقة المصححة:');
  console.log('1. ✅ issue_types.id -> issues.issue_type_id (One-to-Many)');
  console.log('2. ✅ نوع واحد يمكن أن يكون له عدة قضايا');
  console.log('3. ✅ قضية واحدة لها نوع واحد فقط');
  console.log('4. ✅ ON DELETE SET NULL - إذا حُذف النوع، القضايا تبقى بدون نوع');
  console.log('5. ✅ ON UPDATE CASCADE - إذا تغير معرف النوع، يتحدث في القضايا');
}

// تشغيل الإصلاح
fixIssueTypesRelation().catch(console.error);
