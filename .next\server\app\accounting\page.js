(()=>{var e={};e.id=9450,e.ids=[9450],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16372:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),c=s(98254),l=s(44493),o=s(96834),d=s(84821),u=s(84027),x=s(82080),m=s(23576),p=s(10022),h=s(59402),g=s(53411),f=s(25541),j=s(23928),v=s(79410),b=s(41312),N=s(70334);function y(){let[e,t]=(0,a.useState)({totalPaymentVouchers:0,totalReceiptVouchers:0,totalJournalEntries:0,totalAccounts:0,monthlyPayments:0,monthlyReceipts:0,currentBalance:0}),[s,n]=(0,a.useState)(!0),y=[{title:"ربط الحسابات",description:"إدارة الحسابات الأساسية للنظام المحاسبي مع الربط التلقائي (الإيرادات، المصروفات، رأس المال، العملاء، الموظفين، الصندوق)",icon:d.A,href:"/accounting/account-linking",color:"bg-indigo-500",stats:"إعداد النظام",features:["7 حسابات أساسية","ربط تلقائي","مراقبة الحالة"]},{title:"ربط الحسابات الرئيسية",description:"تحديد الحسابات الهامة من دليل الحسابات (الإيرادات، المصروفات، رأس المال، العملاء، الموظفين، الصندوق)",icon:u.A,href:"/accounting/main-accounts",color:"bg-slate-500",stats:"إعداد متقدم",features:["10 حسابات رئيسية","بحث قابل للتصفية","حفظ مباشر"]},{title:"الحسابات الأساسية",description:"ربط الحسابات الأساسية للنظام (الإيرادات، المصروفات، الموظفين، العملاء، الصندوق) بدليل الحسابات",icon:u.A,href:"/accounting/default-accounts",color:"bg-emerald-500",stats:"10 حسابات أساسية",features:["ربط تلقائي","قوائم منسدلة","حفظ متعدد"]},{title:"ربط الحسابات التلقائي",description:"إعداد قواعد الربط التلقائي للعملاء والموظفين الجدد مع الحسابات",icon:d.A,href:"/accounting/link-accounts",color:"bg-teal-500",stats:"إعدادات متقدمة",features:["ربط تلقائي","قواعد ذكية","إنشاء حسابات"]},{title:"دليل الحسابات",description:"إدارة الهيكل المحاسبي (4 مستويات) مع ربط العملاء والموظفين",icon:x.A,href:"/accounting/chart-of-accounts",color:"bg-blue-500",stats:`${e.totalAccounts} حساب`,features:["4 مستويات هرمية","ربط العملاء والموظفين","حسابات تحكم"]},{title:"سندات الصرف",description:"إدارة سندات الصرف والمدفوعات مع ربط القضايا",icon:m.A,href:"/accounting/payment-vouchers",color:"bg-red-500",stats:`${e.totalPaymentVouchers} سند`,features:["ربط بالقضايا","طرق دفع متنوعة","مراكز تكلفة"]},{title:"سندات القبض",description:"إدارة سندات القبض والمقبوضات من العملاء",icon:p.A,href:"/accounting/receipt-vouchers",color:"bg-green-500",stats:`${e.totalReceiptVouchers} سند`,features:["تتبع المقبوضات","ربط بالعملاء","عملات متعددة"]},{title:"القيود اليومية",description:"إدارة القيود المحاسبية المركبة والمعقدة",icon:h.A,href:"/accounting/journal-entries",color:"bg-purple-500",stats:`${e.totalJournalEntries} قيد`,features:["قيود متوازنة","تفاصيل متعددة","تحقق تلقائي"]},{title:"التقارير المحاسبية",description:"تقارير شاملة: دفتر الأستاذ، ميزان المراجعة، القوائم المالية",icon:g.A,href:"/accounting/reports",color:"bg-orange-500",stats:"تقارير متقدمة",features:["دفتر الأستاذ","ميزان المراجعة","القوائم المالية"]}],w=[{title:"إجمالي المدفوعات الشهرية",value:e.monthlyPayments,icon:f.A,color:"text-red-600",bgColor:"bg-red-50",format:"currency"},{title:"إجمالي المقبوضات الشهرية",value:e.monthlyReceipts,icon:j.A,color:"text-green-600",bgColor:"bg-green-50",format:"currency"},{title:"الرصيد الحالي",value:e.currentBalance,icon:v.A,color:"text-blue-600",bgColor:"bg-blue-50",format:"currency"},{title:"إجمالي الحسابات",value:e.totalAccounts,icon:b.A,color:"text-purple-600",bgColor:"bg-purple-50",format:"number"}],A=(e,t)=>"currency"===t?`${e.toLocaleString()} ر.ي`:e.toLocaleString();return(0,r.jsx)(c.O,{children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3 space-x-reverse",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"النظام المحاسبي"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"نظام محاسبي متكامل لشركات المحاماة"})]})]}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4 space-x-reverse",children:[(0,r.jsx)(o.E,{variant:"outline",className:"text-sm",children:"نظام 4 مستويات"}),(0,r.jsx)(o.E,{variant:"outline",className:"text-sm",children:"ربط بالقضايا"}),(0,r.jsx)(o.E,{variant:"outline",className:"text-sm",children:"عملات متعددة"}),(0,r.jsx)(o.E,{variant:"outline",className:"text-sm",children:"مراكز تكلفة"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:w.map((e,t)=>(0,r.jsx)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s?(0,r.jsx)("div",{className:"h-8 w-20 bg-gray-200 rounded animate-pulse"}):A(e.value,e.format)})]}),(0,r.jsx)("div",{className:`p-3 rounded-full ${e.bgColor}`,children:(0,r.jsx)(e.icon,{className:`h-6 w-6 ${e.color}`})})]})})},t))}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"الوحدات المحاسبية"}),(0,r.jsx)("p",{className:"text-gray-600",children:"اختر الوحدة المحاسبية التي تريد العمل عليها"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:y.map((e,t)=>(0,r.jsx)(i(),{href:e.href,children:(0,r.jsxs)(l.Zp,{className:"h-full hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer group",children:[(0,r.jsx)(l.aR,{className:"pb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:`p-3 rounded-lg ${e.color} text-white`,children:(0,r.jsx)(e.icon,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.ZB,{className:"text-lg group-hover:text-blue-600 transition-colors",children:e.title}),(0,r.jsx)(o.E,{variant:"secondary",className:"mt-1",children:e.stats})]})]}),(0,r.jsx)(N.A,{className:"h-5 w-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all"})]})}),(0,r.jsxs)(l.Wu,{className:"pt-0",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"الميزات الرئيسية:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.features.map((e,t)=>(0,r.jsx)(o.E,{variant:"outline",className:"text-xs",children:e},t))})]})]})]})},t))})]}),(0,r.jsx)(l.Zp,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"مميزات النظام المحاسبي الجديد"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-full w-fit mx-auto",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsx)("h4",{className:"font-medium",children:"هيكل محاسبي متقدم"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"نظام 4 مستويات مع ربط تلقائي بالعملاء والموظفين"})]}),(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-full w-fit mx-auto",children:(0,r.jsx)(b.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("h4",{className:"font-medium",children:"تكامل مع النظام القانوني"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"ربط السندات والقيود بالقضايا والعملاء"})]}),(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-full w-fit mx-auto",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-purple-600"})}),(0,r.jsx)("h4",{className:"font-medium",children:"تقارير شاملة"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"تقارير مالية متقدمة وتحليلات تفصيلية"})]})]})]})})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73642:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81100:(e,t,s)=>{Promise.resolve().then(s.bind(s,73642))},87154:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),c=s(30893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o={children:["",{children:["accounting",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73642)),"D:\\mohaminew\\src\\app\\accounting\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\mohaminew\\src\\app\\accounting\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/accounting/page",pathname:"/accounting",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},87180:(e,t,s)=>{Promise.resolve().then(s.bind(s,16372))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8409,7932],()=>s(87154));module.exports=r})();