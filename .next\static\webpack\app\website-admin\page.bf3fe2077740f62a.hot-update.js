"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/website-admin/page",{

/***/ "(app-pages-browser)/./src/components/LegalLibraryManager.tsx":
/*!************************************************!*\
  !*** ./src/components/LegalLibraryManager.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LegalLibraryManager: () => (/* binding */ LegalLibraryManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,RefreshCw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ LegalLibraryManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction LegalLibraryManager() {\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPath, setNewPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [syncing, setSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحميل البيانات الأولية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LegalLibraryManager.useEffect\": ()=>{\n            loadLibraryData();\n        }\n    }[\"LegalLibraryManager.useEffect\"], []);\n    // تحميل بيانات المكتبة\n    const loadLibraryData = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/legal-library/files?action=list');\n            const result = await response.json();\n            if (result.success) {\n                setFiles(result.data.files || []);\n                setSettings(result.data.settings || {});\n                setCurrentPath(result.data.path || '');\n                setNewPath(result.data.path || '');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحميل البيانات');\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل بيانات المكتبة:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تحديث مسار المكتبة\n    const updateLibraryPath = async ()=>{\n        if (!newPath.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('يجب إدخال مسار صحيح');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/system-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    setting_key: 'legal_library_path',\n                    setting_value: newPath.trim()\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setCurrentPath(newPath.trim());\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('تم تحديث مسار المكتبة بنجاح');\n                // إعادة تحميل البيانات\n                await loadLibraryData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحديث المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في تحديث المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // مزامنة الملفات\n    const syncFiles = async ()=>{\n        setSyncing(true);\n        try {\n            const response = await fetch('/api/legal-library/files', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'sync'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(result.message || 'تم مزامنة الملفات بنجاح');\n                // إعادة تحميل البيانات\n                await loadLibraryData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في المزامنة');\n            }\n        } catch (error) {\n            console.error('خطأ في مزامنة الملفات:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setSyncing(false);\n        }\n    };\n    // تنسيق حجم الملف\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // تنسيق التاريخ\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('ar-SA', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"إدارة ملفات المكتبة القانونية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"الإعدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: loadLibraryData,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                \"إعدادات المكتبة القانونية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"library-path\",\n                                        children: \"مسار مجلد المكتبة القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"library-path\",\n                                                value: newPath,\n                                                onChange: (e)=>setNewPath(e.target.value),\n                                                placeholder: \"/home/<USER>/Downloads/legal-system/laws\",\n                                                className: \"flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: updateLibraryPath,\n                                                disabled: loading || newPath === currentPath,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"حفظ\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            \"المسار الحالي: \",\n                                            currentPath\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            settings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"الامتدادات المسموحة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: settings.legal_library_allowed_extensions || '.pdf,.doc,.docx,.txt'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"الحد الأقصى لحجم الملف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    settings.legal_library_max_file_size || 50,\n                                                    \" ميجابايت\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: \"مجلد المكتبة القانونية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: currentPath\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                        variant: \"secondary\",\n                                        children: [\n                                            files.length,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: syncFiles,\n                                        disabled: syncing,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sync, {\n                                                className: \"h-4 w-4 mr-2 \".concat(syncing ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            syncing ? 'جاري المزامنة...' : 'مزامنة الملفات'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملفات الموجودة (\",\n                                files.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin mx-auto text-blue-600 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"جاري تحميل الملفات...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this) : files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"لا توجد ملفات في المجلد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mt-1\",\n                                    children: [\n                                        \"أضف ملفات إلى المجلد: \",\n                                        currentPath\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: formatFileSize(file.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: file.extension\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"آخر تعديل: \",\n                                                                        formatDate(file.modified)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                variant: \"outline\",\n                                                children: file.extension\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_RefreshCw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"تعليمات:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mt-2 space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"أضف الملفات مباشرة إلى المجلد المحدد في الإعدادات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: 'اضغط على \"مزامنة الملفات\" لإضافة الملفات الجديدة إلى قاعدة البيانات'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"يمكن تغيير مسار المجلد من الإعدادات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"الامتدادات المدعومة: PDF, DOC, DOCX, TXT\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(LegalLibraryManager, \"Z6uXTeOoQALdTP/ygHPtwv3fch8=\");\n_c = LegalLibraryManager;\nvar _c;\n$RefreshReg$(_c, \"LegalLibraryManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LegalLibraryManager.tsx\n"));

/***/ })

});