"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx":
/*!********************************************************!*\
  !*** ./src/app/home/<USER>/simple-chat-widget.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleChatWidget: () => (/* binding */ SimpleChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ SimpleChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleChatWidget(param) {\n    let { isOpen, onClose, onOpen } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guestId, setGuestId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // إنشاء معرف زائر فريد وجلب بيانات الشركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            let guestIdentifier = localStorage.getItem('guestId');\n            if (!guestIdentifier) {\n                guestIdentifier = \"guest_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                localStorage.setItem('guestId', guestIdentifier);\n            }\n            setGuestId(guestIdentifier);\n            // جلب بيانات الشركة\n            fetchCompanyData();\n        }\n    }[\"SimpleChatWidget.useEffect\"], []);\n    // جلب بيانات الشركة\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/companies');\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && result.data.length > 0) {\n                    const company = result.data[0];\n                    setCompanyData(company);\n                    // إنشاء الرسائل الترحيبية\n                    const welcomeMessages = [\n                        {\n                            id: '1',\n                            content: \"مرحباً بك في \".concat(company.name, \"! \\uD83C\\uDFDB️\"),\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        },\n                        {\n                            id: '2',\n                            content: 'يمكنني مساعدتك في الاستشارات القانونية. كيف يمكنني مساعدتك؟',\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        }\n                    ];\n                    setMessages(welcomeMessages);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching company data:', error);\n            // رسائل افتراضية\n            const defaultMessages = [\n                {\n                    id: '1',\n                    content: 'مرحباً! كيف يمكنني مساعدتك؟ 🏛️',\n                    sender: 'assistant',\n                    timestamp: new Date(),\n                    status: 'delivered'\n                }\n            ];\n            setMessages(defaultMessages);\n        }\n    };\n    // Auto-scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat is opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!message.trim()) return;\n        console.log('📤 Sending message:', message);\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            sender: 'user',\n            timestamp: new Date(),\n            status: 'delivered'\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setIsTyping(true);\n        try {\n            // إرسال للذكاء الاصطناعي\n            const response = await sendToAI(message);\n            // Add AI response\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message,\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'delivered'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: 'أعتذر، حدث خطأ. يرجى المحاولة مرة أخرى.',\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'error'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            // إعادة التركيز على مربع النص بعد الإرسال\n            setTimeout(()=>{\n                if (inputRef.current) {\n                    inputRef.current.focus();\n                }\n            }, 100);\n        }\n    };\n    const sendToAI = async (userMessage)=>{\n        try {\n            console.log('🤖 Sending message to AI:', userMessage);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage,\n                    model: 'groq-llama-8b',\n                    conversationId: \"guest_\".concat(guestId, \"_\").concat(Date.now()),\n                    context: []\n                })\n            });\n            if (!response.ok) {\n                throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');\n            }\n            const result = await response.json();\n            console.log('🤖 AI Response:', result);\n            if (result.success && result.response) {\n                return {\n                    message: result.response\n                };\n            } else {\n                throw new Error(result.error || 'خطأ في الاستجابة');\n            }\n        } catch (error) {\n            console.error('AI Error:', error);\n            // رد احتياطي\n            const fallbackResponses = [\n                \"أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: \".concat((companyData === null || companyData === void 0 ? void 0 : companyData.phone) || '+967-1-123456'),\n                'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',\n                'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.'\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return {\n                message: randomResponse\n            };\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onOpen,\n                className: \"h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700 flex items-center justify-center\",\n                title: \"المحادثات مع الذكاء الاصطناعي\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-7 w-7 text-white\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-24 left-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b bg-blue-600 text-white rounded-t-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"المحادثات المباشرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-2 w-2 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"AI\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-100 mr-2\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 rounded-full hover:bg-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.sender === 'user' ? 'justify-end' : 'justify-start'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-sm px-4 py-3 rounded-lg \".concat(msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 opacity-70\",\n                                        children: msg.timestamp.toLocaleTimeString('ar-SA', {\n                                            hour: '2-digit',\n                                            minute: '2-digit'\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 text-gray-800 px-4 py-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"المساعد الذكي يكتب...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendMessage,\n                    className: \"flex space-x-2 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ref: inputRef,\n                            type: \"text\",\n                            value: message,\n                            onChange: (e)=>setMessage(e.target.value),\n                            placeholder: \"اكتب رسالتك...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                            disabled: isTyping,\n                            dir: \"rtl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n                            disabled: !message.trim() || isTyping,\n                            children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatWidget, \"+wvRQPGNvkoLiajW7IFDjiYLFZs=\");\n_c = SimpleChatWidget;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\n"));

/***/ })

});