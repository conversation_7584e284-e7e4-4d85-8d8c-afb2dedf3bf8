'use client'

import { useState } from 'react'
import { IssueSelect } from '@/components/ui/issue-select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestIssueSelectPage() {
  const [selectedIssueId, setSelectedIssueId] = useState('')
  const [selectedIssueData, setSelectedIssueData] = useState<any>(null)
  const [excludeDistributed, setExcludeDistributed] = useState(false)

  const handleIssueChange = (issueId: string, issueData: any) => {
    console.log('Test Page: Issue changed', { issueId, issueData })
    setSelectedIssueId(issueId)
    setSelectedIssueData(issueData)
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">اختبار مكون IssueSelect</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* اختبار القضايا العادية */}
        <Card>
          <CardHeader>
            <CardTitle>القضايا العادية (جميع القضايا)</CardTitle>
          </CardHeader>
          <CardContent>
            <IssueSelect
              value=""
              onChange={(id, data) => console.log('Normal Issues:', { id, data })}
              label="جميع القضايا"
              placeholder="اختر من جميع القضايا"
              excludeDistributed={false}
            />
          </CardContent>
        </Card>

        {/* اختبار القضايا غير الموزعة */}
        <Card>
          <CardHeader>
            <CardTitle>القضايا غير الموزعة فقط</CardTitle>
          </CardHeader>
          <CardContent>
            <IssueSelect
              value=""
              onChange={(id, data) => console.log('Undistributed Issues:', { id, data })}
              label="القضايا غير الموزعة"
              placeholder="اختر من القضايا غير الموزعة"
              excludeDistributed={true}
            />
          </CardContent>
        </Card>
      </div>

      {/* اختبار تفاعلي */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>اختبار تفاعلي</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={() => setExcludeDistributed(false)}
              variant={!excludeDistributed ? "default" : "outline"}
            >
              جميع القضايا
            </Button>
            <Button 
              onClick={() => setExcludeDistributed(true)}
              variant={excludeDistributed ? "default" : "outline"}
            >
              القضايا غير الموزعة فقط
            </Button>
          </div>

          <IssueSelect
            value={selectedIssueId}
            onChange={handleIssueChange}
            label={excludeDistributed ? "القضايا غير الموزعة" : "جميع القضايا"}
            placeholder="اختر القضية"
            excludeDistributed={excludeDistributed}
          />

          {selectedIssueData && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-bold">القضية المختارة:</h3>
              <p><strong>رقم القضية:</strong> {selectedIssueData.case_number}</p>
              <p><strong>العنوان:</strong> {selectedIssueData.title}</p>
              <p><strong>العميل:</strong> {selectedIssueData.client_name}</p>
              <p><strong>المحكمة:</strong> {selectedIssueData.court_name}</p>
              <p><strong>الحالة:</strong> {selectedIssueData.status}</p>
              <p><strong>المبلغ:</strong> {parseFloat(selectedIssueData.amount || 0).toLocaleString()} ريال</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* معلومات التشخيص */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>معلومات التشخيص</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>الوضع الحالي:</strong> {excludeDistributed ? 'القضايا غير الموزعة فقط' : 'جميع القضايا'}</p>
            <p><strong>القضية المختارة:</strong> {selectedIssueId || 'لا يوجد'}</p>
            <p><strong>تحقق من Console للمزيد من التفاصيل</strong></p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}