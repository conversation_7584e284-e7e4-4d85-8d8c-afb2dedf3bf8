// اختبار الاتصال بـ OpenAI API
require('dotenv').config({ path: '.env.local' });

async function testOpenAIConnection() {
  console.log('🔄 اختبار الاتصال بـ OpenAI API...\n');

  // التحقق من وجود API Key
  const apiKey = process.env.OPENAI_API_KEY;

  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY غير موجود في ملف .env.local');
    console.log('📝 يرجى إضافة المفتاح كما يلي:');
    console.log('   OPENAI_API_KEY=sk-your-actual-api-key-here\n');
    return;
  }

  if (apiKey === 'your_openai_api_key_here') {
    console.log('⚠️ يرجى استبدال "your_openai_api_key_here" بالمفتاح الحقيقي');
    console.log('🔗 احصل على المفتاح من: https://platform.openai.com/api-keys\n');
    return;
  }

  console.log('✅ تم العثور على OPENAI_API_KEY');
  console.log(`🔑 المفتاح يبدأ بـ: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}\n`);

  try {
    // اختبار الاتصال بـ OpenAI API
    console.log('🌐 اختبار الاتصال بـ OpenAI API...');

    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ نجح الاتصال بـ OpenAI API!');

      // البحث عن نماذج GPT-4
      const gpt4Models = data.data.filter(model =>
        model.id.includes('gpt-4') && !model.id.includes('vision')
      );

      console.log(`\n📊 النماذج المتاحة (${gpt4Models.length} من نماذج GPT-4):`);
      gpt4Models.slice(0, 5).forEach(model => {
        console.log(`   • ${model.id}`);
      });

      if (gpt4Models.length > 5) {
        console.log(`   ... و ${gpt4Models.length - 5} نموذج آخر`);
      }

    } else {
      const errorData = await response.text();
      console.log('❌ فشل الاتصال بـ OpenAI API');
      console.log(`📄 رمز الخطأ: ${response.status}`);
      console.log(`📝 رسالة الخطأ: ${errorData}\n`);

      if (response.status === 401) {
        console.log('🔑 المفتاح غير صحيح أو منتهي الصلاحية');
        console.log('📋 تأكد من:');
        console.log('   1. نسخ المفتاح بالكامل من OpenAI');
        console.log('   2. عدم وجود مسافات إضافية');
        console.log('   3. أن المفتاح لم ينته أو يُحذف');
      } else if (response.status === 429) {
        console.log('⏰ تم تجاوز حد الاستخدام');
        console.log('💰 تحقق من رصيد حسابك في OpenAI');
      }

      return;
    }

  } catch (error) {
    console.log('❌ خطأ في الشبكة:', error.message);
    console.log('🌐 تأكد من اتصالك بالإنترنت\n');
    return;
  }

  // اختبار إرسال رسالة تجريبية
  console.log('\n💬 اختبار إرسال رسالة تجريبية...');

  try {
    const testResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'أنت مساعد قانوني ذكي متخصص في القانون اليمني.'
          },
          {
            role: 'user',
            content: 'مرحباً، هل يمكنك مساعدتي؟'
          }
        ],
        max_tokens: 100,
        temperature: 0.7
      })
    });

    if (testResponse.ok) {
      const testData = await testResponse.json();
      const aiMessage = testData.choices[0].message.content;

      console.log('✅ نجح اختبار الرسالة!');
      console.log('🤖 رد GPT-4:');
      console.log(`   "${aiMessage}"\n`);

      console.log('🎉 النظام جاهز للعمل مع GPT-4!');
      console.log('📊 معلومات الاستخدام:');
      console.log(`   • النموذج: ${testData.model}`);
      console.log(`   • Tokens المستخدمة: ${testData.usage.total_tokens}`);
      console.log(`   • التكلفة التقريبية: $${(testData.usage.total_tokens * 0.00003).toFixed(4)}`);

    } else {
      const errorData = await testResponse.text();
      console.log('❌ فشل اختبار الرسالة');
      console.log(`📄 رمز الخطأ: ${testResponse.status}`);
      console.log(`📝 رسالة الخطأ: ${errorData}`);

      if (testResponse.status === 403) {
        console.log('🚫 ليس لديك صلاحية لاستخدام GPT-4');
        console.log('💳 قد تحتاج لإضافة طريقة دفع أو ترقية حسابك');
      }
    }

  } catch (error) {
    console.log('❌ خطأ في اختبار الرسالة:', error.message);
  }

  console.log('\n' + '='.repeat(50));
  console.log('📋 ملخص الاختبار:');
  console.log('✅ API Key موجود ومُعرَّف');
  console.log('✅ الاتصال بـ OpenAI API يعمل');
  console.log('✅ GPT-4 متاح للاستخدام');
  console.log('🚀 النظام جاهز للعمل!');
  console.log('='.repeat(50));
}

// تشغيل الاختبار
testOpenAIConnection().catch(console.error);
