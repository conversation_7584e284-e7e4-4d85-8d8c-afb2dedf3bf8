(()=>{var t={};t.id=6784,t.ids=[6784],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.d(e,{P:()=>d});var s=a(64939),r=a(29021),n=a.n(r),o=a(33873),i=a.n(o),u=t([s]);s=(u.then?(await u)():u)[0];let _=null;try{let t=i().join(process.cwd(),"routing.config.json"),e=n().readFileSync(t,"utf8");_=JSON.parse(e)}catch(t){console.error("❌ خطأ في تحميل ملف التوجيه:",t)}if(!process.env.DB_PASSWORD&&!_?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let t=process.env.PORT||"7443";if(_&&_.routes[t]){let e=_.routes[t],a=_.default_config;return{database:e.database,user:a.db_user,host:a.db_host,password:process.env.DB_PASSWORD||a.db_password,port:a.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${t}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new s.Pool(l);async function d(t,e){let a=await p.connect();try{return await a.query(t,e)}finally{a.release()}}c()}catch(t){c(t)}})},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},37458:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.r(e),a.d(e,{GET:()=>i,POST:()=>d,PUT:()=>u});var s=a(32190),r=a(5069),n=a(97933),o=t([r,n]);async function i(t){try{let t=await (0,r.P)(`
      SELECT
        ma.id,
        ma.account_name,
        ma.account_code,
        ma.chart_account_id,
        ma.description,
        coa.account_code as chart_account_code,
        coa.account_name as chart_account_name
      FROM main_accounts ma
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY ma.account_name
    `),e=await (0,r.P)(`
      SELECT
        id,
        account_code,
        account_name,
        account_type,
        account_level
      FROM chart_of_accounts
      WHERE is_active = true
      ORDER BY account_code
    `);return s.NextResponse.json({success:!0,mainAccounts:t.rows,chartAccounts:e.rows})}catch(t){return console.error("خطأ في جلب الحسابات الرئيسية:",t),s.NextResponse.json({success:!1,error:"خطأ في جلب الحسابات الرئيسية"},{status:500})}}async function u(t){try{let{id:e,chart_account_id:a}=await t.json();if(!e||!a)return s.NextResponse.json({success:!1,error:"معرف الحساب ومعرف الحساب الأب مطلوبان"},{status:400});let c=await (0,r.P)(`
      UPDATE main_accounts 
      SET 
        chart_account_id = $1,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `,[a,e]);if(0===c.rows.length)return s.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return s.NextResponse.json({success:!0,message:"تم تحديث ربط الحساب بنجاح",account:c.rows[0]})}catch(t){return console.error("خطأ في تحديث ربط الحساب:",t),s.NextResponse.json({success:!1,error:"خطأ في تحديث ربط الحساب"},{status:500})}}async function d(t){try{let e=await t.json();if(e.mainAccounts){let{mainAccounts:t}=e;if(!t||!Array.isArray(t))return s.NextResponse.json({success:!1,error:"بيانات الحسابات مطلوبة"},{status:400});let a=[];for(let e of t)if(await (0,r.P)(`
          UPDATE main_accounts
          SET chart_account_id = $1, account_code = $2, updated_at = CURRENT_TIMESTAMP
          WHERE id = $3
        `,[e.chart_account_id,e.account_code,e.id]),e.chart_account_id){console.log(`🔗 تطبيق الربط التلقائي للحساب ${e.id}`);let t=await (0,n.s)(e.id,e.chart_account_id);a.push({account_id:e.id,linking_result:t})}return s.NextResponse.json({success:!0,message:"تم تحديث ربط الحسابات بنجاح",linking_results:a})}{let{account_name:t,description:a,chart_account_id:c}=e;if(!t?.trim())return s.NextResponse.json({success:!1,error:"اسم الحساب مطلوب"},{status:400});if((await (0,r.P)("SELECT id FROM main_accounts WHERE LOWER(account_name) = LOWER($1)",[t.trim()])).rows.length>0)return s.NextResponse.json({success:!1,error:"يوجد حساب رئيسي بهذا الاسم مسبقاً"},{status:400});let o=await (0,r.P)(`
        INSERT INTO main_accounts (
          account_name,
          description,
          chart_account_id,
          is_required,
          created_date
        ) VALUES ($1, $2, $3, false, CURRENT_DATE)
        RETURNING *
      `,[t.trim(),a?.trim()||null,c||null]),i=null;return c&&(console.log(`🔗 تطبيق الربط التلقائي للحساب الجديد ${o.rows[0].id}`),i=await (0,n.s)(o.rows[0].id,c)),s.NextResponse.json({success:!0,message:"تم إضافة الحساب الرئيسي بنجاح",data:o.rows[0],linking_result:i})}}catch(t){return console.error("Error in POST main accounts:",t),s.NextResponse.json({success:!1,error:"خطأ في معالجة الطلب"},{status:500})}}[r,n]=o.then?(await o)():o,c()}catch(t){c(t)}})},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},62446:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.r(e),a.d(e,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>l});var s=a(96559),r=a(48088),n=a(37719),o=a(37458),i=t([o]);o=(i.then?(await i)():i)[0];let d=new s.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/accounting/main-accounts/route",pathname:"/api/accounting/main-accounts",filename:"route",bundlePath:"app/api/accounting/main-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\main-accounts\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:_,workUnitAsyncStorage:l,serverHooks:p}=d;function u(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:l})}c()}catch(t){c(t)}})},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:t=>{"use strict";t.exports=import("pg")},78335:()=>{},96487:()=>{},97933:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.d(e,{s:()=>n});var s=a(5069),r=t([s]);async function n(t,e){try{let a={success:!1,message:"",details:{clients_updated:0,employees_updated:0,suppliers_updated:0}},c=await (0,s.P)(`
      SELECT 
        ma.account_name as main_account_name,
        coa.account_code,
        coa.account_name as chart_account_name,
        coa.id as chart_account_id
      FROM main_accounts ma
      JOIN chart_of_accounts coa ON coa.id = $1
      WHERE ma.id = $2
    `,[e,t]);if(0===c.rows.length)return{success:!1,message:"لم يتم العثور على بيانات الحساب",details:{clients_updated:0,employees_updated:0,suppliers_updated:0}};let r=c.rows[0];switch(console.log(`🔗 تطبيق ربط الحساب: ${r.main_account_name} -> ${r.account_code}`),r.main_account_name){case"حسابات العملاء":a.details.clients_updated=await o(r.chart_account_id,r.account_code);break;case"حسابات الموظفين":a.details.employees_updated=await i(r.chart_account_id,r.account_code);break;case"حسابات الموردين":a.details.suppliers_updated=await u(r.chart_account_id,r.account_code)}let n=a.details.clients_updated+a.details.employees_updated+a.details.suppliers_updated;return a.success=!0,a.message=`تم تطبيق الربط بنجاح. تم تحديث ${n} سجل`,a}catch(t){return console.error("خطأ في تطبيق ربط الحسابات:",t),{success:!1,message:"حدث خطأ في تطبيق ربط الحسابات",details:{clients_updated:0,employees_updated:0,suppliers_updated:0}}}}async function o(t,e){try{let a=await (0,s.P)(`
      SELECT id, name 
      FROM clients 
      WHERE status = 'active'
      ORDER BY id
    `),c=0;for(let r of a.rows)try{let a,n=`${e}${String(r.id).padStart(3,"0")}`,o=await (0,s.P)(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `,[n]);a=0===o.rows.length?(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'أصول', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `,[n,`حساب العميل: ${r.name}`,t])).rows[0].id:o.rows[0].id,await (0,s.P)(`
          UPDATE clients 
          SET account_id = $1
          WHERE id = $2
        `,[a,r.id]),c++}catch(t){console.error(`خطأ في ربط العميل ${r.name}:`,t)}return c}catch(t){return console.error("خطأ في ربط العملاء:",t),0}}async function i(t,e){try{let a=await (0,s.P)(`
      SELECT id, name 
      FROM employees 
      WHERE status = 'active'
      ORDER BY id
    `),c=0;for(let r of a.rows)try{let a,n=`${e}${String(r.id).padStart(3,"0")}`,o=await (0,s.P)(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `,[n]);a=0===o.rows.length?(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'مصروفات', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `,[n,`حساب الموظف: ${r.name}`,t])).rows[0].id:o.rows[0].id,await (0,s.P)(`
          UPDATE employees 
          SET account_id = $1
          WHERE id = $2
        `,[a,r.id]),c++}catch(t){console.error(`خطأ في ربط الموظف ${r.name}:`,t)}return c}catch(t){return console.error("خطأ في ربط الموظفين:",t),0}}async function u(t,e){try{let a=await (0,s.P)(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `);if(0===a.rows.length)return console.log("⚠️ جدول الموردين غير موجود"),0;let c=await (0,s.P)(`
      SELECT id, name 
      FROM suppliers 
      WHERE status = 'active'
      ORDER BY id
    `),r=0;for(let a of c.rows)try{let c,n=`${e}${String(a.id).padStart(3,"0")}`,o=await (0,s.P)(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `,[n]);c=0===o.rows.length?(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'خصوم', 'دائن', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `,[n,`حساب المورد: ${a.name}`,t])).rows[0].id:o.rows[0].id,await (0,s.P)(`
          UPDATE suppliers 
          SET account_id = $1
          WHERE id = $2
        `,[c,a.id]),r++}catch(t){console.error(`خطأ في ربط المورد ${a.name}:`,t)}return r}catch(t){return console.error("خطأ في ربط الموردين:",t),0}}s=(r.then?(await r)():r)[0],c()}catch(t){c(t)}})}};var e=require("../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),c=e.X(0,[4447,580],()=>a(62446));module.exports=c})();