'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  User,
  Lock,
  Smartphone,
  UserCheck,
  Building2,
  Eye,
  EyeOff,
  LogIn
} from 'lucide-react'

export default function LoginPage() {
  const router = useRouter()
  const { user, login } = useAuth()
  const [loginType, setLoginType] = useState<'user' | 'client'>('user')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    deviceId: ''
  })

  // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للصفحة المناسبة
  useEffect(() => {
    if (user) {
      if (user.type === 'client') {
        router.push('/client-portal')
      } else {
        router.push('/dashboard')
      }
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const endpoint = loginType === 'user' ? '/api/auth/users' : '/api/client-portal/auth/simple'

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username.trim(),
          password: formData.password.trim(),
          deviceId: loginType === 'user' ? (formData.deviceId || 'default-device') : undefined
        }),
      })

      const result = await response.json()

      if (result.success) {
        if (loginType === 'user') {
          // استخدام hook المصادقة لحفظ بيانات المستخدم
          const userData = {
            id: result.user.id,
            username: result.user.username,
            type: loginType as 'user',
            name: result.user.name,
            role: result.user.role,
            role_display_name: result.user.role_display_name,
            permissions: result.user.permissions,
            user_type: result.user.user_type,
            token: result.token
          }
          login(userData)
        } else {
          // استخدام hook المصادقة لحفظ بيانات العميل
          const clientData = {
            id: result.data.client.client_id,
            username: result.data.client.username,
            type: loginType as 'client',
            name: result.data.client.client_name,
            email: result.data.client.email,
            token: result.data.token,
            sessionToken: result.data.sessionToken
          }
          localStorage.setItem('clientToken', result.data.token)
          login(clientData)
        }
      } else {
        alert(result.error || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      console.error('Login error:', error)
      alert('حدث خطأ في الاتصال')
    } finally {
      setIsLoading(false)
    }
  }

  // توليد معرف جهاز عشوائي
  const generateDeviceId = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = 'gtx'
    for (let i = 0; i < 6; i++) {
      result += Math.floor(Math.random() * 10)
    }
    result += '_'
    for (let i = 0; i < 17; i++) {
      result += Math.floor(Math.random() * 10)
    }
    setFormData({...formData, deviceId: result})
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً بك في نظام الإدارة المتخصص
          </h1>
          <p className="text-gray-600">قم بتسجيل الدخول للمتابعة</p>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader className="space-y-4">
            {/* أزرار نوع المستخدم */}
            <div className="flex rounded-lg bg-gray-100 p-1">
              <Button
                type="button"
                variant={loginType === 'user' ? 'default' : 'ghost'}
                className={`flex-1 ${
                  loginType === 'user'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setLoginType('user')}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                دخول مستخدم
              </Button>
              <Button
                type="button"
                variant={loginType === 'client' ? 'default' : 'ghost'}
                className={`flex-1 ${
                  loginType === 'client'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setLoginType('client')}
              >
                <Building2 className="h-4 w-4 mr-2" />
                دخول عميل
              </Button>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* اسم المستخدم */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-right block">
                  اسم المستخدم *
                </Label>
                <div className="relative">
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    className="pl-10 text-right"
                    placeholder="أدخل اسم المستخدم"
                    required
                  />
                  <User className="h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                </div>
              </div>

              {/* كلمة المرور */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-right block">
                  كلمة المرور *
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="pl-10 pr-10 text-right"
                    placeholder="••••••••"
                    required
                  />
                  <Lock className="h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              {/* معرف الجهاز (للمستخدمين فقط - اختياري) */}
              {loginType === 'user' && (
                <div className="space-y-2">
                  <Label htmlFor="deviceId" className="text-right block">
                    معرف الجهاز (اختياري)
                  </Label>
                  <div className="relative">
                    <Input
                      id="deviceId"
                      type="text"
                      value={formData.deviceId}
                      onChange={(e) => setFormData({...formData, deviceId: e.target.value})}
                      className="pl-10 text-right font-mono text-sm"
                      placeholder="اتركه فارغاً أو اضغط لتوليد معرف"
                    />
                    <Smartphone className="h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateDeviceId}
                    className="w-full text-sm"
                  >
                    توليد معرف جهاز جديد
                  </Button>
                </div>
              )}

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 text-lg font-semibold"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <>
                    <LogIn className="h-5 w-5 mr-2" />
                    {loginType === 'user' ? 'تسجيل دخول المستخدم' : 'تسجيل دخول العميل'}
                  </>
                )}
              </Button>
            </form>

            {/* معلومات إضافية */}
            <div className="mt-6 text-center">
              <div className="text-sm text-gray-500 mb-4">
                {loginType === 'user' ? (
                  <p>للمستخدمين: يتطلب اسم المستخدم وكلمة المرور (معرف الجهاز اختياري)</p>
                ) : (
                  <p>للعملاء: يتطلب اسم المستخدم وكلمة المرور فقط</p>
                )}
              </div>
              
              {/* بيانات تجريبية */}
              <div className="bg-blue-50 p-4 rounded-lg text-right">
                <h4 className="font-medium text-blue-900 mb-2">بيانات تجريبية:</h4>
                {loginType === 'user' ? (
                  <div className="text-sm text-blue-700 space-y-1 mb-3">
                    <p><strong>المستخدم:</strong> admin</p>
                    <p><strong>كلمة المرور:</strong> admin123</p>
                  </div>
                ) : (
                  <div className="text-sm text-blue-700 space-y-1 mb-3">
                    <p><strong>العميل:</strong> demo_client</p>
                    <p><strong>كلمة المرور:</strong> password123</p>
                  </div>
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (loginType === 'user') {
                      setFormData({...formData, username: 'admin', password: 'admin123'})
                    } else {
                      setFormData({...formData, username: 'demo_client', password: 'password123'})
                    }
                  }}
                  className="text-xs"
                >
                  ملء البيانات التجريبية
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* تذييل */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            © 2024 نظام الإدارة القانونية المتخصص
          </p>
        </div>
      </div>
    </div>
  )
}
