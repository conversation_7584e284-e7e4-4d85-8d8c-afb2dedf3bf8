"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_home_components_footer_tsx",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/footer.tsx":
/*!********************************************!*\
  !*** ./src/app/home/<USER>/footer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Footer(param) {\n    let { companyData } = param;\n    _s();\n    const currentYear = new Date().getFullYear();\n    const [footerLinks, setFooterLinks] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            fetchFooterLinks();\n        }\n    }[\"Footer.useEffect\"], []);\n    const fetchFooterLinks = async ()=>{\n        try {\n            const response = await fetch('/api/footer-links');\n            const data = await response.json();\n            if (data.success) {\n                setFooterLinks(data.data.filter((link)=>link.is_active));\n            }\n        } catch (error) {\n            console.error('Error fetching footer links:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تجميع الروابط حسب الفئة\n    const groupedLinks = footerLinks.reduce((acc, link)=>{\n        if (!acc[link.category]) {\n            acc[link.category] = [];\n        }\n        acc[link.category].push(link);\n        return acc;\n    }, {});\n    // ترتيب الروابط داخل كل فئة\n    Object.keys(groupedLinks).forEach((category)=>{\n        groupedLinks[category].sort((a, b)=>a.sort_order - b.sort_order);\n    });\n    const socialLinks = [\n        {\n            name: 'فيسبوك',\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'تويتر',\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'لينكد إن',\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'إنستغرام',\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'يوتيوب',\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: '#'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"pt-20 pb-8\",\n        style: {\n            background: 'linear-gradient(135deg, #222222 0%, #171717 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-900 mr-3 shadow-lg\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                className: \"w-6 h-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                        points: \"14 2 14 8 20 8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold\",\n                                            style: {\n                                                color: '#cca967'\n                                            },\n                                            children: companyData.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: companyData.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 space-x-reverse\",\n                                    children: socialLinks.map((social, index)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"w-10 h-10 rounded-full flex items-center justify-center text-gray-300 hover:text-gray-900 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #444444 0%, #333333 100%)'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #444444 0%, #333333 100%)';\n                                            },\n                                            \"aria-label\": social.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        Object.entries(groupedLinks).map((param)=>{\n                            let [category, links] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        style: {\n                                            color: '#cca967'\n                                        },\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.url || '#',\n                                                    className: \"tafahum-footer-widget text-gray-300 hover:text-yellow-400 transition-colors flex items-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-1 h-1 bg-yellow-600 rounded-full ml-2 group-hover:bg-yellow-400 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        link.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.id, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    style: {\n                        borderColor: 'rgba(204, 169, 103, 0.2)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-300 text-sm mb-4 md:mb-0\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: '#cca967'\n                                    },\n                                    children: companyData.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 29\n                                }, this),\n                                \". جميع الحقوق محفوظة.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"سياسة الخصوصية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"الشروط والأحكام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/sitemap\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"خريطة الموقع\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"OMKwvCdPwTk+bl/Ia3n/ULsx9Ik=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaG9tZS9jb21wb25lbnRzL2Zvb3Rlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUE2QjtBQUNlO0FBQytEO0FBMEJwRyxTQUFTUSxPQUFPLEtBQTRCO1FBQTVCLEVBQUVDLFdBQVcsRUFBZSxHQUE1Qjs7SUFDckIsTUFBTUMsY0FBYyxJQUFJQyxPQUFPQyxXQUFXO0lBQzFDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHYiwrQ0FBUUEsQ0FBZSxFQUFFO0lBQy9ELE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUV2Q0MsZ0RBQVNBOzRCQUFDO1lBQ1JlO1FBQ0Y7MkJBQUcsRUFBRTtJQUVMLE1BQU1BLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLE9BQU8sTUFBTUYsU0FBU0csSUFBSTtZQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCUixlQUFlTSxLQUFLQSxJQUFJLENBQUNHLE1BQU0sQ0FBQyxDQUFDQyxPQUFxQkEsS0FBS0MsU0FBUztZQUN0RTtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUNoRCxTQUFVO1lBQ1JWLFdBQVc7UUFDYjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1ZLGVBQWVmLFlBQVlnQixNQUFNLENBQUMsQ0FBQ0MsS0FBS047UUFDNUMsSUFBSSxDQUFDTSxHQUFHLENBQUNOLEtBQUtPLFFBQVEsQ0FBQyxFQUFFO1lBQ3ZCRCxHQUFHLENBQUNOLEtBQUtPLFFBQVEsQ0FBQyxHQUFHLEVBQUU7UUFDekI7UUFDQUQsR0FBRyxDQUFDTixLQUFLTyxRQUFRLENBQUMsQ0FBQ0MsSUFBSSxDQUFDUjtRQUN4QixPQUFPTTtJQUNULEdBQUcsQ0FBQztJQUVKLDRCQUE0QjtJQUM1QkcsT0FBT0MsSUFBSSxDQUFDTixjQUFjTyxPQUFPLENBQUNKLENBQUFBO1FBQ2hDSCxZQUFZLENBQUNHLFNBQVMsQ0FBQ0ssSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUVFLFVBQVUsR0FBR0QsRUFBRUMsVUFBVTtJQUNuRTtJQUVBLE1BQU1DLGNBQWM7UUFDbEI7WUFBRUMsTUFBTTtZQUFVQyxNQUFNdkMsdUhBQVFBO1lBQUV3QyxNQUFNO1FBQUk7UUFDNUM7WUFBRUYsTUFBTTtZQUFTQyxNQUFNdEMsdUhBQU9BO1lBQUV1QyxNQUFNO1FBQUk7UUFDMUM7WUFBRUYsTUFBTTtZQUFZQyxNQUFNckMsdUhBQVFBO1lBQUVzQyxNQUFNO1FBQUk7UUFDOUM7WUFBRUYsTUFBTTtZQUFZQyxNQUFNcEMsdUhBQVNBO1lBQUVxQyxNQUFNO1FBQUk7UUFDL0M7WUFBRUYsTUFBTTtZQUFVQyxNQUFNbkMsdUhBQU9BO1lBQUVvQyxNQUFNO1FBQUk7S0FDNUM7SUFFRCxxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtRQUFhQyxPQUFPO1lBQUVDLFlBQVk7UUFBb0Q7a0JBQ3RHLDRFQUFDQztZQUFJSCxXQUFVOzs4QkFDYiw4REFBQ0c7b0JBQUlILFdBQVU7O3NDQUViLDhEQUFDRzs0QkFBSUgsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7NENBQXFGQyxPQUFPO2dEQUN6R0MsWUFBWTs0Q0FDZDtzREFDRSw0RUFBQ0U7Z0RBQ0NDLE9BQU07Z0RBQ05DLE9BQU07Z0RBQ05DLFFBQU87Z0RBQ1BDLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xDLFFBQU87Z0RBQ1BDLGFBQVk7Z0RBQ1pDLGVBQWM7Z0RBQ2RDLGdCQUFlO2dEQUNmYixXQUFVOztrRUFFViw4REFBQ2M7d0RBQUtDLEdBQUU7Ozs7OztrRUFDUiw4REFBQ0M7d0RBQVNDLFFBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUdyQiw4REFBQ0M7NENBQUdsQixXQUFVOzRDQUFvQkMsT0FBTztnREFBRWtCLE9BQU87NENBQVU7c0RBQUl2RCxZQUFZZ0MsSUFBSTs7Ozs7Ozs7Ozs7OzhDQUVsRiw4REFBQ3dCO29DQUFFcEIsV0FBVTs4Q0FDVnBDLFlBQVl5RCxXQUFXOzs7Ozs7OENBRTFCLDhEQUFDbEI7b0NBQUlILFdBQVU7OENBQ1pMLFlBQVkyQixHQUFHLENBQUMsQ0FBQ0MsUUFBUUM7d0NBQ3hCLE1BQU1DLE9BQU9GLE9BQU8xQixJQUFJO3dDQUN4QixxQkFDRSw4REFBQ0w7NENBRUNNLE1BQU15QixPQUFPekIsSUFBSTs0Q0FDakI0QixRQUFPOzRDQUNQQyxLQUFJOzRDQUNKM0IsV0FBVTs0Q0FDVkMsT0FBTztnREFDTEMsWUFBWTs0Q0FDZDs0Q0FDQTBCLGNBQWMsQ0FBQ0M7Z0RBQ2JBLEVBQUVDLGFBQWEsQ0FBQzdCLEtBQUssQ0FBQ0MsVUFBVSxHQUFHOzRDQUNyQzs0Q0FDQTZCLGNBQWMsQ0FBQ0Y7Z0RBQ2JBLEVBQUVDLGFBQWEsQ0FBQzdCLEtBQUssQ0FBQ0MsVUFBVSxHQUFHOzRDQUNyQzs0Q0FDQThCLGNBQVlULE9BQU8zQixJQUFJO3NEQUV2Qiw0RUFBQzZCO2dEQUFLekIsV0FBVTs7Ozs7OzJDQWhCWHdCOzs7OztvQ0FtQlg7Ozs7Ozs7Ozs7Ozt3QkFLSHBDLE9BQU82QyxPQUFPLENBQUNsRCxjQUFjdUMsR0FBRyxDQUFDO2dDQUFDLENBQUNwQyxVQUFVZ0QsTUFBTTtpREFDbEQsOERBQUMvQjs7a0RBQ0MsOERBQUNnQzt3Q0FBR25DLFdBQVU7d0NBQTZCQyxPQUFPOzRDQUFFa0IsT0FBTzt3Q0FBVTtrREFDbEVqQzs7Ozs7O2tEQUVILDhEQUFDa0Q7d0NBQUdwQyxXQUFVO2tEQUNYa0MsTUFBTVosR0FBRyxDQUFDLENBQUMzQyxxQkFDViw4REFBQzBEOzBEQUNDLDRFQUFDbEYsa0RBQUlBO29EQUNIMkMsTUFBTW5CLEtBQUsyRCxHQUFHLElBQUk7b0RBQ2xCdEMsV0FBVTs7c0VBRVYsOERBQUN1Qzs0REFBS3ZDLFdBQVU7Ozs7Ozt3REFDZnJCLEtBQUs2RCxLQUFLOzs7Ozs7OytDQU5ON0QsS0FBSzhELEVBQUU7Ozs7Ozs7Ozs7OytCQU5adkQ7Ozs7Ozs7Ozs7Ozs4QkFzQmQsOERBQUNpQjtvQkFBSUgsV0FBVTtvQkFBdUVDLE9BQU87d0JBQUV5QyxhQUFhO29CQUEyQjs7c0NBQ3JJLDhEQUFDdkM7NEJBQUlILFdBQVU7O2dDQUFxQztnQ0FDL0NuQztnQ0FBWTs4Q0FBQyw4REFBQzBFO29DQUFLdEMsT0FBTzt3Q0FBRWtCLE9BQU87b0NBQVU7OENBQUl2RCxZQUFZZ0MsSUFBSTs7Ozs7O2dDQUFROzs7Ozs7O3NDQUU5RSw4REFBQ087NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDN0Msa0RBQUlBO29DQUFDMkMsTUFBSztvQ0FBV0UsV0FBVTs4Q0FBMkQ7Ozs7Ozs4Q0FHM0YsOERBQUN1QztvQ0FBS3ZDLFdBQVU7OENBQWdCOzs7Ozs7OENBQ2hDLDhEQUFDN0Msa0RBQUlBO29DQUFDMkMsTUFBSztvQ0FBU0UsV0FBVTs4Q0FBMkQ7Ozs7Ozs4Q0FHekYsOERBQUN1QztvQ0FBS3ZDLFdBQVU7OENBQWdCOzs7Ozs7OENBQ2hDLDhEQUFDN0Msa0RBQUlBO29DQUFDMkMsTUFBSztvQ0FBV0UsV0FBVTs4Q0FBMkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXZHO0dBdEpnQnJDO0tBQUFBIiwic291cmNlcyI6WyJEOlxcbW9oYW1pbmV3XFxzcmNcXGFwcFxcaG9tZVxcY29tcG9uZW50c1xcZm9vdGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEZhY2Vib29rLCBUd2l0dGVyLCBMaW5rZWRpbiwgSW5zdGFncmFtLCBZb3V0dWJlLCBNYXBQaW4sIFBob25lLCBNYWlsLCBDbG9jayB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbnR5cGUgQ29tcGFueURhdGEgPSB7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgbG9nb191cmw/OiBzdHJpbmc7XG4gIGFkZHJlc3M6IHN0cmluZztcbiAgY2l0eT86IHN0cmluZztcbiAgcGhvbmU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgd29ya2luZ19ob3Vycz86IHN0cmluZztcbn07XG5cbmludGVyZmFjZSBGb290ZXJMaW5rIHtcbiAgaWQ6IG51bWJlcjtcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgdXJsOiBzdHJpbmc7XG4gIHNvcnRfb3JkZXI6IG51bWJlcjtcbiAgaXNfYWN0aXZlOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgRm9vdGVyUHJvcHMge1xuICBjb21wYW55RGF0YTogQ29tcGFueURhdGE7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBGb290ZXIoeyBjb21wYW55RGF0YSB9OiBGb290ZXJQcm9wcykge1xuICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcbiAgY29uc3QgW2Zvb3RlckxpbmtzLCBzZXRGb290ZXJMaW5rc10gPSB1c2VTdGF0ZTxGb290ZXJMaW5rW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEZvb3RlckxpbmtzKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBmZXRjaEZvb3RlckxpbmtzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Zvb3Rlci1saW5rcycpO1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRGb290ZXJMaW5rcyhkYXRhLmRhdGEuZmlsdGVyKChsaW5rOiBGb290ZXJMaW5rKSA9PiBsaW5rLmlzX2FjdGl2ZSkpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBmb290ZXIgbGlua3M6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g2KrYrNmF2YrYuSDYp9mE2LHZiNin2KjYtyDYrdiz2Kgg2KfZhNmB2KbYqVxuICBjb25zdCBncm91cGVkTGlua3MgPSBmb290ZXJMaW5rcy5yZWR1Y2UoKGFjYywgbGluaykgPT4ge1xuICAgIGlmICghYWNjW2xpbmsuY2F0ZWdvcnldKSB7XG4gICAgICBhY2NbbGluay5jYXRlZ29yeV0gPSBbXTtcbiAgICB9XG4gICAgYWNjW2xpbmsuY2F0ZWdvcnldLnB1c2gobGluayk7XG4gICAgcmV0dXJuIGFjYztcbiAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgRm9vdGVyTGlua1tdPik7XG5cbiAgLy8g2KrYsdiq2YrYqCDYp9mE2LHZiNin2KjYtyDYr9in2K7ZhCDZg9mEINmB2KbYqVxuICBPYmplY3Qua2V5cyhncm91cGVkTGlua3MpLmZvckVhY2goY2F0ZWdvcnkgPT4ge1xuICAgIGdyb3VwZWRMaW5rc1tjYXRlZ29yeV0uc29ydCgoYSwgYikgPT4gYS5zb3J0X29yZGVyIC0gYi5zb3J0X29yZGVyKTtcbiAgfSk7XG5cbiAgY29uc3Qgc29jaWFsTGlua3MgPSBbXG4gICAgeyBuYW1lOiAn2YHZitiz2KjZiNmDJywgaWNvbjogRmFjZWJvb2ssIGhyZWY6ICcjJyB9LFxuICAgIHsgbmFtZTogJ9iq2YjZitiq2LEnLCBpY29uOiBUd2l0dGVyLCBocmVmOiAnIycgfSxcbiAgICB7IG5hbWU6ICfZhNmK2YbZg9ivINil2YYnLCBpY29uOiBMaW5rZWRpbiwgaHJlZjogJyMnIH0sXG4gICAgeyBuYW1lOiAn2KXZhtiz2KrYutix2KfZhScsIGljb246IEluc3RhZ3JhbSwgaHJlZjogJyMnIH0sXG4gICAgeyBuYW1lOiAn2YrZiNiq2YrZiNioJywgaWNvbjogWW91dHViZSwgaHJlZjogJyMnIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cInB0LTIwIHBiLThcIiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzIyMjIyMiAwJSwgIzE3MTcxNyAxMDAlKScgfX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC0xMCBtYi0xMlwiPlxuICAgICAgICAgIHsvKiBDb21wYW55IEluZm8gLSDYqNiq2LXZhdmK2YUg2KrZgdin2YfZhSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtZ3JheS05MDAgbXItMyBzaGFkb3ctbGdcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjY2NhOTY3IDAlLCAjZDRiODc2IDEwMCUpJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMjRcIlxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMjRcIlxuICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTZcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIHBvaW50cz1cIjE0IDIgMTQgOCAyMCA4XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiIHN0eWxlPXt7IGNvbG9yOiAnI2NjYTk2NycgfX0+e2NvbXBhbnlEYXRhLm5hbWV9PC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi02IGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7Y29tcGFueURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00IHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICB7c29jaWFsTGlua3MubWFwKChzb2NpYWwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IHNvY2lhbC5pY29uO1xuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICBocmVmPXtzb2NpYWwuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTAgaC0xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQ0NDQ0NCAwJSwgIzMzMzMzMyAxMDAlKSdcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNjY2E5NjcgMCUsICNkNGI4NzYgMTAwJSknO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQ0NDQ0NCAwJSwgIzMzMzMzMyAxMDAlKSc7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9e3NvY2lhbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZvb3RlciBMaW5rcyAqL31cbiAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoZ3JvdXBlZExpbmtzKS5tYXAoKFtjYXRlZ29yeSwgbGlua3NdKSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17Y2F0ZWdvcnl9PlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTZcIiBzdHlsZT17eyBjb2xvcjogJyNjY2E5NjcnIH19PlxuICAgICAgICAgICAgICAgIHtjYXRlZ29yeX1cbiAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIHtsaW5rcy5tYXAoKGxpbmspID0+IChcbiAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xpbmsuaWR9PlxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsudXJsIHx8ICcjJ31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0YWZhaHVtLWZvb3Rlci13aWRnZXQgdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXllbGxvdy00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0xIGgtMSBiZy15ZWxsb3ctNjAwIHJvdW5kZWQtZnVsbCBtbC0yIGdyb3VwLWhvdmVyOmJnLXllbGxvdy00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAge2xpbmsudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENvcHlyaWdodCBhbmQgQm90dG9tIExpbmtzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IHB0LTggZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCIgc3R5bGU9e3sgYm9yZGVyQ29sb3I6ICdyZ2JhKDIwNCwgMTY5LCAxMDMsIDAuMiknIH19PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtIG1iLTQgbWQ6bWItMFwiPlxuICAgICAgICAgICAgwqkge2N1cnJlbnRZZWFyfSA8c3BhbiBzdHlsZT17eyBjb2xvcjogJyNjY2E5NjcnIH19Pntjb21wYW55RGF0YS5uYW1lfTwvc3Bhbj4uINis2YXZiti5INin2YTYrdmC2YjZgiDZhdit2YHZiNi42KkuXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcml2YWN5XCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAg2LPZitin2LPYqSDYp9mE2K7YtdmI2LXZitipXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+fDwvc3Bhbj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICDYp9mE2LTYsdmI2Lcg2YjYp9mE2KPYrdmD2KfZhVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPnw8L3NwYW4+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL3NpdGVtYXBcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICDYrtix2YrYt9ipINin2YTZhdmI2YLYuVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkZhY2Vib29rIiwiVHdpdHRlciIsIkxpbmtlZGluIiwiSW5zdGFncmFtIiwiWW91dHViZSIsIkZvb3RlciIsImNvbXBhbnlEYXRhIiwiY3VycmVudFllYXIiLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJmb290ZXJMaW5rcyIsInNldEZvb3RlckxpbmtzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJmZXRjaEZvb3RlckxpbmtzIiwicmVzcG9uc2UiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImZpbHRlciIsImxpbmsiLCJpc19hY3RpdmUiLCJlcnJvciIsImNvbnNvbGUiLCJncm91cGVkTGlua3MiLCJyZWR1Y2UiLCJhY2MiLCJjYXRlZ29yeSIsInB1c2giLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsInNvcnQiLCJhIiwiYiIsInNvcnRfb3JkZXIiLCJzb2NpYWxMaW5rcyIsIm5hbWUiLCJpY29uIiwiaHJlZiIsImZvb3RlciIsImNsYXNzTmFtZSIsInN0eWxlIiwiYmFja2dyb3VuZCIsImRpdiIsInN2ZyIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwicGF0aCIsImQiLCJwb2x5bGluZSIsInBvaW50cyIsImgzIiwiY29sb3IiLCJwIiwiZGVzY3JpcHRpb24iLCJtYXAiLCJzb2NpYWwiLCJpbmRleCIsIkljb24iLCJ0YXJnZXQiLCJyZWwiLCJvbk1vdXNlRW50ZXIiLCJlIiwiY3VycmVudFRhcmdldCIsIm9uTW91c2VMZWF2ZSIsImFyaWEtbGFiZWwiLCJlbnRyaWVzIiwibGlua3MiLCJoNCIsInVsIiwibGkiLCJ1cmwiLCJzcGFuIiwidGl0bGUiLCJpZCIsImJvcmRlckNvbG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/footer.tsx\n"));

/***/ })

});