"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/website-admin/page",{

/***/ "(app-pages-browser)/./src/app/website-admin/page.tsx":
/*!****************************************!*\
  !*** ./src/app/website-admin/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WebsiteAdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/megaphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,Award,BookOpen,Building,CheckCircle,Edit,ExternalLink,Eye,EyeOff,FileText,Gavel,Globe,Link,Mail,MapPin,Megaphone,MessageCircle,Phone,Plus,Save,Scale,Settings,Shield,Star,Trash2,TrendingUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// خريطة الأيقونات المتاحة\nconst availableIcons = [\n    {\n        name: 'Scale',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: 'ميزان العدالة'\n    },\n    {\n        name: 'Users',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        label: 'المستخدمين'\n    },\n    {\n        name: 'FileText',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        label: 'المستندات'\n    },\n    {\n        name: 'Shield',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        label: 'الحماية'\n    },\n    {\n        name: 'Building',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        label: 'المباني'\n    },\n    {\n        name: 'Gavel',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        label: 'المطرقة'\n    },\n    {\n        name: 'BookOpen',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        label: 'الكتاب'\n    },\n    {\n        name: 'Award',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        label: 'الجائزة'\n    },\n    {\n        name: 'Phone',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        label: 'الهاتف'\n    },\n    {\n        name: 'Mail',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        label: 'البريد'\n    },\n    {\n        name: 'MapPin',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        label: 'الموقع'\n    },\n    {\n        name: 'Star',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        label: 'النجمة'\n    },\n    {\n        name: 'TrendingUp',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n        label: 'النمو'\n    },\n    {\n        name: 'CheckCircle',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        label: 'التحقق'\n    },\n    {\n        name: 'MessageCircle',\n        icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        label: 'الرسائل'\n    }\n];\n// الألوان المتاحة\nconst availableColors = [\n    '#2563eb',\n    '#dc2626',\n    '#059669',\n    '#7c3aed',\n    '#ea580c',\n    '#0891b2',\n    '#be123c',\n    '#9333ea',\n    '#c2410c',\n    '#0369a1',\n    '#15803d',\n    '#a21caf'\n];\nfunction WebsiteAdminPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('company');\n    // Services state\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAddingNew, setIsAddingNew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        slug: '',\n        description: '',\n        content: '',\n        icon_name: 'Scale',\n        icon_color: '#2563eb',\n        image_url: '',\n        is_active: true,\n        sort_order: 0,\n        meta_title: '',\n        meta_description: ''\n    });\n    // Announcements state\n    const [announcements, setAnnouncements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newAnnouncement, setNewAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        content: '',\n        type: 'public_1'\n    });\n    // Company data state\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: 1,\n        name: '',\n        legal_name: '',\n        description: '',\n        address: '',\n        city: '',\n        country: '',\n        phone: '',\n        email: '',\n        website: '',\n        logo_url: '',\n        logo_image_url: '',\n        established_date: '',\n        registration_number: '',\n        legal_form: '',\n        capital: 0,\n        tax_number: '',\n        is_active: true,\n        working_hours: '',\n        latitude: undefined,\n        longitude: undefined\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WebsiteAdminPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"WebsiteAdminPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            // جلب الخدمات من جدول serviceslow\n            const servicesResponse = await fetch('/api/serviceslow');\n            const servicesResult = await servicesResponse.json();\n            if (servicesResult.success) {\n                setServices(servicesResult.data);\n            }\n            // جلب الإعلانات\n            const announcementsRes = await fetch('/api/settings/public-announcements');\n            const announcementsData = await announcementsRes.json();\n            if (announcementsData.success) {\n                setAnnouncements(announcementsData.data);\n            }\n            // جلب بيانات الشركة\n            const companyResponse = await fetch('/api/company');\n            const companyResult = await companyResponse.json();\n            if (companyResult.success && companyResult.data && companyResult.data.length > 0) {\n                const company = companyResult.data[0];\n                setCompanyData({\n                    ...company,\n                    latitude: company.latitude ? Number(company.latitude) : undefined,\n                    longitude: company.longitude ? Number(company.longitude) : undefined\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // دوال إدارة الخدمات\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            title: service.title,\n            slug: service.slug,\n            description: service.description || '',\n            content: service.content || '',\n            icon_name: service.icon_name,\n            icon_color: service.icon_color,\n            image_url: service.image_url || '',\n            is_active: service.is_active,\n            sort_order: service.sort_order,\n            meta_title: service.meta_title || '',\n            meta_description: service.meta_description || ''\n        });\n        setIsAddingNew(false);\n    };\n    const handleAddNewService = ()=>{\n        setIsAddingNew(true);\n        setEditingService(null);\n        setFormData({\n            title: '',\n            slug: '',\n            description: '',\n            content: '',\n            icon_name: 'Scale',\n            icon_color: '#2563eb',\n            image_url: '',\n            is_active: true,\n            sort_order: services.length + 1,\n            meta_title: '',\n            meta_description: ''\n        });\n    };\n    const handleCancelService = ()=>{\n        setEditingService(null);\n        setIsAddingNew(false);\n        setFormData({\n            title: '',\n            slug: '',\n            description: '',\n            content: '',\n            icon_name: 'Scale',\n            icon_color: '#2563eb',\n            image_url: '',\n            is_active: true,\n            sort_order: 0,\n            meta_title: '',\n            meta_description: ''\n        });\n    };\n    const generateSlug = (title)=>{\n        return title.toLowerCase().replace(/[أ-ي]/g, (match)=>{\n            const arabicToEnglish = {\n                'أ': 'a',\n                'ب': 'b',\n                'ت': 't',\n                'ث': 'th',\n                'ج': 'j',\n                'ح': 'h',\n                'خ': 'kh',\n                'د': 'd',\n                'ذ': 'dh',\n                'ر': 'r',\n                'ز': 'z',\n                'س': 's',\n                'ش': 'sh',\n                'ص': 's',\n                'ض': 'd',\n                'ط': 't',\n                'ظ': 'z',\n                'ع': 'a',\n                'غ': 'gh',\n                'ف': 'f',\n                'ق': 'q',\n                'ك': 'k',\n                'ل': 'l',\n                'م': 'm',\n                'ن': 'n',\n                'ه': 'h',\n                'و': 'w',\n                'ي': 'y'\n            };\n            return arabicToEnglish[match] || match;\n        }).replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    const handleTitleChange = (title)=>{\n        setFormData((prev)=>({\n                ...prev,\n                title,\n                slug: generateSlug(title),\n                meta_title: title\n            }));\n    };\n    const handleSaveService = async ()=>{\n        try {\n            const url = isAddingNew ? '/api/serviceslow' : \"/api/serviceslow/\".concat(editingService === null || editingService === void 0 ? void 0 : editingService.id);\n            const method = isAddingNew ? 'POST' : 'PUT';\n            const body = formData;\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(body)\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchData();\n                handleCancelService();\n                setMessage('تم حفظ الخدمة بنجاح');\n                setTimeout(()=>setMessage(''), 3000);\n            } else {\n                alert(result.error || 'حدث خطأ');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('حدث خطأ في الحفظ');\n        }\n    };\n    const handleDeleteService = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه الخدمة؟')) return;\n        try {\n            const response = await fetch(\"/api/serviceslow/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchData();\n                setMessage('تم حذف الخدمة بنجاح');\n                setTimeout(()=>setMessage(''), 3000);\n            } else {\n                alert(result.error || 'حدث خطأ في الحذف');\n            }\n        } catch (error) {\n            console.error('Error deleting service:', error);\n            alert('حدث خطأ في الحذف');\n        }\n    };\n    const handleToggleServiceActive = async (service)=>{\n        try {\n            const response = await fetch(\"/api/serviceslow/\".concat(service.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...service,\n                    is_active: !service.is_active\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchData();\n            }\n        } catch (error) {\n            console.error('Error toggling service:', error);\n        }\n    };\n    const moveService = async (service, direction)=>{\n        const currentIndex = services.findIndex((s)=>s.id === service.id);\n        const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;\n        if (targetIndex < 0 || targetIndex >= services.length) return;\n        const targetService = services[targetIndex];\n        try {\n            await fetch(\"/api/serviceslow/\".concat(service.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...service,\n                    sort_order: targetService.sort_order\n                })\n            });\n            await fetch(\"/api/serviceslow/\".concat(targetService.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...targetService,\n                    sort_order: service.sort_order\n                })\n            });\n            await fetchData();\n        } catch (error) {\n            console.error('Error moving service:', error);\n        }\n    };\n    const getIconComponent = (iconName)=>{\n        const iconData = availableIcons.find((icon)=>icon.name === iconName);\n        return iconData ? iconData.icon : _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n    };\n    const handleSaveAnnouncement = async ()=>{\n        if (!newAnnouncement.title || !newAnnouncement.content) {\n            setMessage('يرجى ملء جميع حقول الإعلان');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/public-announcements', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newAnnouncement)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMessage('تم إضافة الإعلان بنجاح');\n                setNewAnnouncement({\n                    title: '',\n                    content: '',\n                    type: 'public_1'\n                });\n                fetchData() // إعادة تحميل البيانات\n                ;\n            } else {\n                setMessage('فشل في إضافة الإعلان: ' + result.error);\n            }\n        } catch (error) {\n            setMessage('حدث خطأ أثناء إضافة الإعلان');\n        } finally{\n            setLoading(false);\n            setTimeout(()=>setMessage(''), 3000);\n        }\n    };\n    const handleDeleteAnnouncement = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا الإعلان؟')) return;\n        try {\n            const response = await fetch(\"/api/settings/public-announcements/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMessage('تم حذف الإعلان بنجاح');\n                fetchData();\n            } else {\n                setMessage('فشل في حذف الإعلان');\n            }\n        } catch (error) {\n            setMessage('حدث خطأ أثناء حذف الإعلان');\n        } finally{\n            setTimeout(()=>setMessage(''), 3000);\n        }\n    };\n    // دالة حفظ بيانات الشركة\n    const handleSaveCompany = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/company', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(companyData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMessage('تم حفظ بيانات الشركة بنجاح');\n                fetchData();\n            } else {\n                setMessage('فشل في حفظ بيانات الشركة: ' + result.error);\n            }\n        } catch (error) {\n            setMessage('حدث خطأ أثناء حفظ بيانات الشركة');\n        } finally{\n            setLoading(false);\n            setTimeout(()=>setMessage(''), 3000);\n        }\n    };\n    const tabs = [\n        {\n            id: 'company',\n            label: 'بيانات الشركة',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'services',\n            label: 'إدارة الخدمات',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n        },\n        {\n            id: 'announcements',\n            label: 'الإعلانات العامة',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"]\n        },\n        {\n            id: 'footer-links',\n            label: 'روابط التذييل',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"]\n        },\n        {\n            id: 'library',\n            label: 'المكتبة القانونية',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n        },\n        {\n            id: 'preview',\n            label: 'معاينة الموقع',\n            icon: _barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_4__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    className: \"h-8 w-8 ml-3 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                \"لوحة تحكم الموقع الرئيسي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: ()=>window.open('/company', '_blank'),\n                                    variant: \"outline\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"بيانات الشركة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: ()=>window.open('/home', '_blank'),\n                                    variant: \"outline\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"معاينة الموقع\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 rounded-lg \".concat(message.includes('فشل') || message.includes('خطأ') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'),\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8 space-x-reverse\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm flex items-center \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                        className: \"h-4 w-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'services' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        (editingService || isAddingNew) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"shadow-xl border-0 bg-white rounded-2xl overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"bg-gradient-to-r from-purple-50 to-blue-50 border-b border-purple-100 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"flex items-center text-xl font-bold text-gray-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-6 w-6 mr-3 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 21\n                                            }, this),\n                                            isAddingNew ? 'إضافة خدمة جديدة' : 'تعديل الخدمة'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                        children: \"المعلومات الأساسية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"title\",\n                                                                children: \"عنوان الخدمة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"title\",\n                                                                value: formData.title,\n                                                                onChange: (e)=>handleTitleChange(e.target.value),\n                                                                placeholder: \"أدخل عنوان الخدمة\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-purple-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"slug\",\n                                                                children: \"الرابط (Slug) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"slug\",\n                                                                value: formData.slug,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            slug: e.target.value\n                                                                        })),\n                                                                placeholder: \"service-url\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-purple-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"سيكون الرابط: /serviceslow/\",\n                                                                    formData.slug\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"الوصف المختصر *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                                id: \"description\",\n                                                                value: formData.description,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            description: e.target.value\n                                                                        })),\n                                                                placeholder: \"وصف مختصر للخدمة\",\n                                                                rows: 3,\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-purple-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"content\",\n                                                                children: \"المحتوى التفصيلي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                                id: \"content\",\n                                                                value: formData.content,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            content: e.target.value\n                                                                        })),\n                                                                placeholder: \"المحتوى التفصيلي للخدمة (يمكن استخدام HTML)\",\n                                                                rows: 8,\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-purple-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"يمكنك استخدام HTML لتنسيق المحتوى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                        children: \"التصميم والإعدادات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                children: \"الأيقونة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-5 gap-2\",\n                                                                children: availableIcons.map((iconData)=>{\n                                                                    const IconComponent = iconData.icon;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    icon_name: iconData.name\n                                                                                })),\n                                                                        className: \"p-3 rounded-lg border-2 transition-all \".concat(formData.icon_name === iconData.name ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'),\n                                                                        title: iconData.label,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                            className: \"h-5 w-5 mx-auto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, iconData.name, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                children: \"لون الأيقونة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-6 gap-2\",\n                                                                children: availableColors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    icon_color: color\n                                                                                })),\n                                                                        className: \"w-10 h-10 rounded-lg border-2 transition-all \".concat(formData.icon_color === color ? 'border-gray-800 scale-110' : 'border-gray-300 hover:scale-105'),\n                                                                        style: {\n                                                                            backgroundColor: color\n                                                                        }\n                                                                    }, color, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        type: \"color\",\n                                                                        value: formData.icon_color,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    icon_color: e.target.value\n                                                                                })),\n                                                                        className: \"w-12 h-8 border-0 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"أو اختر لون مخصص\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                children: \"معاينة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-2 border-gray-200 rounded-xl bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 rounded-lg flex items-center justify-center mr-4\",\n                                                                                style: {\n                                                                                    backgroundColor: \"\".concat(formData.icon_color, \"20\")\n                                                                                },\n                                                                                children: (()=>{\n                                                                                    const IconComponent = getIconComponent(formData.icon_name);\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                        className: \"h-6 w-6\",\n                                                                                        style: {\n                                                                                            color: formData.icon_color\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                        lineNumber: 701,\n                                                                                        columnNumber: 40\n                                                                                    }, this);\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                lineNumber: 695,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: formData.title || 'عنوان الخدمة'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm\",\n                                                                        children: formData.description || 'وصف الخدمة'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"is_active\",\n                                                                        children: \"نشط\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                        id: \"is_active\",\n                                                                        checked: formData.is_active,\n                                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    is_active: checked\n                                                                                }))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"sort_order\",\n                                                                        children: \"ترتيب العرض\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"sort_order\",\n                                                                        type: \"number\",\n                                                                        value: formData.sort_order,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    sort_order: parseInt(e.target.value) || 0\n                                                                                })),\n                                                                        className: \"border-2 border-gray-200 rounded-xl focus:border-purple-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                onClick: handleCancelService,\n                                                                className: \"px-6 py-3 rounded-xl border-2 border-gray-300 hover:border-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"إلغاء\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                onClick: handleSaveService,\n                                                                className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"حفظ\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"shadow-xl border-0 bg-white rounded-2xl overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-blue-100 pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"flex items-center text-xl font-bold text-gray-900\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"الخدمات المتاحة (\",\n                                                    services.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/serviceslow\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"bg-white hover:bg-gray-50 text-gray-700 border-gray-300 px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"معاينة جميع الخدمات\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        onClick: handleAddNewService,\n                                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"إضافة خدمة جديدة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-0\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-4\",\n                                                children: \"جاري التحميل...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 19\n                                    }, this) : services.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"لا توجد خدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"ابدأ بإضافة خدمة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                onClick: handleAddNewService,\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"إضافة خدمة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"divide-y divide-gray-200\",\n                                        children: services.map((service, index)=>{\n                                            const IconComponent = getIconComponent(service.icon_name);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/serviceslow/\".concat(service.slug),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"mr-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200 hover:shadow-md\",\n                                                                        style: {\n                                                                            backgroundColor: \"\".concat(service.icon_color, \"20\")\n                                                                        },\n                                                                        title: \"عرض صفحة \".concat(service.title),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                            className: \"h-6 w-6\",\n                                                                            style: {\n                                                                                color: service.icon_color\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                                    href: \"/serviceslow/\".concat(service.slug),\n                                                                                    target: \"_blank\",\n                                                                                    rel: \"noopener noreferrer\",\n                                                                                    className: \"mr-3\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer transition-colors duration-200\",\n                                                                                        children: service.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                        lineNumber: 840,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                    lineNumber: 834,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                                    variant: service.is_active ? \"default\" : \"secondary\",\n                                                                                    children: service.is_active ? 'نشط' : 'غير نشط'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 text-sm mb-2\",\n                                                                            children: service.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"الرابط:\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                                    href: \"/serviceslow/\".concat(service.slug),\n                                                                                    target: \"_blank\",\n                                                                                    rel: \"noopener noreferrer\",\n                                                                                    className: \"text-blue-600 hover:text-blue-800 underline mx-1\",\n                                                                                    children: [\n                                                                                        \"/serviceslow/\",\n                                                                                        service.slug\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                                    lineNumber: 851,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"| الترتيب: \",\n                                                                                service.sort_order\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/serviceslow/\".concat(service.slug),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"p-2 text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200\",\n                                                                        title: \"عرض صفحة الخدمة\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 877,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 871,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>moveService(service, 'up'),\n                                                                    disabled: index === 0,\n                                                                    className: \"p-2\",\n                                                                    title: \"نقل للأعلى\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>moveService(service, 'down'),\n                                                                    disabled: index === services.length - 1,\n                                                                    className: \"p-2\",\n                                                                    title: \"نقل للأسفل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 900,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleToggleServiceActive(service),\n                                                                    className: \"p-2\",\n                                                                    title: service.is_active ? 'إلغاء التفعيل' : 'تفعيل',\n                                                                    children: service.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 911,\n                                                                        columnNumber: 54\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 911,\n                                                                        columnNumber: 87\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleEditService(service),\n                                                                    className: \"p-2\",\n                                                                    title: \"تعديل الخدمة\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleDeleteService(service.id),\n                                                                    className: \"p-2 text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                    title: \"حذف الخدمة\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, service.id, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'company' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"shadow-xl border-0 bg-white rounded-2xl overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-blue-100 pb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center text-xl font-bold text-gray-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 mr-3 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إدارة بيانات الشركة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة المعلومات الأساسية للشركة التي تظهر في الموقع الرئيسي\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 950,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"المعلومات الأساسية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"اسم الشركة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"name\",\n                                                                value: companyData.name,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                placeholder: \"اسم الشركة\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"legal_name\",\n                                                                children: \"الاسم القانوني *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"legal_name\",\n                                                                value: companyData.legal_name,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        legal_name: e.target.value\n                                                                    }),\n                                                                placeholder: \"الاسم القانوني للشركة\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"legal_form\",\n                                                                children: \"الشكل القانوني\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"legal_form\",\n                                                                value: companyData.legal_form,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        legal_form: e.target.value\n                                                                    }),\n                                                                placeholder: \"مثل: شركة محدودة المسؤولية\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"registration_number\",\n                                                                children: \"رقم التسجيل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"registration_number\",\n                                                                value: companyData.registration_number,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        registration_number: e.target.value\n                                                                    }),\n                                                                placeholder: \"رقم التسجيل التجاري\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"tax_number\",\n                                                                children: \"الرقم الضريبي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1008,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"tax_number\",\n                                                                value: companyData.tax_number,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        tax_number: e.target.value\n                                                                    }),\n                                                                placeholder: \"الرقم الضريبي\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"established_date\",\n                                                                children: \"تاريخ التأسيس\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1018,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"established_date\",\n                                                                type: \"date\",\n                                                                value: companyData.established_date,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        established_date: e.target.value\n                                                                    }),\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1019,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"معلومات الاتصال\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"phone\",\n                                                                children: \"رقم الهاتف *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"phone\",\n                                                                value: companyData.phone,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        phone: e.target.value\n                                                                    }),\n                                                                placeholder: \"+966 50 000 0000\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"email\",\n                                                                children: \"البريد الإلكتروني *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"email\",\n                                                                type: \"email\",\n                                                                value: companyData.email,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        email: e.target.value\n                                                                    }),\n                                                                placeholder: \"<EMAIL>\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"website\",\n                                                                children: \"الموقع الإلكتروني\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"website\",\n                                                                value: companyData.website,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        website: e.target.value\n                                                                    }),\n                                                                placeholder: \"www.company.com\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"working_hours\",\n                                                                children: \"ساعات العمل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"working_hours\",\n                                                                value: companyData.working_hours || '',\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        working_hours: e.target.value\n                                                                    }),\n                                                                placeholder: \"الأحد - الخميس: 8 صباحاً - 6 مساءً\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1069,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"العنوان والموقع\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"city\",\n                                                                children: \"المدينة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"city\",\n                                                                value: companyData.city,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        city: e.target.value\n                                                                    }),\n                                                                placeholder: \"الرياض\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"country\",\n                                                                children: \"الدولة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1097,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"country\",\n                                                                value: companyData.country,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        country: e.target.value\n                                                                    }),\n                                                                placeholder: \"المملكة العربية السعودية\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1098,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"address\",\n                                                        children: \"العنوان الكامل *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"address\",\n                                                        value: companyData.address,\n                                                        onChange: (e)=>setCompanyData({\n                                                                ...companyData,\n                                                                address: e.target.value\n                                                            }),\n                                                        placeholder: \"شارع الملك فهد، الرياض، المملكة العربية السعودية\",\n                                                        className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"latitude\",\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 1122,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"خط العرض (Latitude)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1121,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"latitude\",\n                                                                type: \"number\",\n                                                                step: \"any\",\n                                                                value: companyData.latitude || '',\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        latitude: e.target.value ? parseFloat(e.target.value) : undefined\n                                                                    }),\n                                                                placeholder: \"24.7136\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1125,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"مثال: 24.7136 (للرياض)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"longitude\",\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 1140,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"خط الطول (Longitude)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"longitude\",\n                                                                type: \"number\",\n                                                                step: \"any\",\n                                                                value: companyData.longitude || '',\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        longitude: e.target.value ? parseFloat(e.target.value) : undefined\n                                                                    }),\n                                                                placeholder: \"46.6753\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"مثال: 46.6753 (للرياض)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 19\n                                            }, this),\n                                            companyData.latitude && companyData.longitude && !isNaN(Number(companyData.latitude)) && !isNaN(Number(companyData.longitude)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-600 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-800 font-medium\",\n                                                                    children: [\n                                                                        \"الموقع محفوظ: \",\n                                                                        Number(companyData.latitude).toFixed(6),\n                                                                        \", \",\n                                                                        Number(companyData.longitude).toFixed(6)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>window.open(\"https://www.google.com/maps?q=\".concat(companyData.latitude, \",\").concat(companyData.longitude), '_blank'),\n                                                            className: \"text-green-700 border-green-300 hover:bg-green-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1175,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"عرض في الخرائط\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1168,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1161,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"الشعار والوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"logo_image_url\",\n                                                                children: \"رابط شعار الشركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"logo_image_url\",\n                                                                value: companyData.logo_image_url,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        logo_image_url: e.target.value\n                                                                    }),\n                                                                placeholder: \"/images/logo.png\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            companyData.logo_image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                                        children: \"معاينة الشعار:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 1200,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: companyData.logo_image_url,\n                                                                        alt: \"شعار الشركة\",\n                                                                        className: \"w-20 h-20 object-contain border border-gray-200 rounded-lg\",\n                                                                        onError: (e)=>{\n                                                                            e.currentTarget.style.display = 'none';\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                        lineNumber: 1201,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"وصف الشركة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1213,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                                id: \"description\",\n                                                                value: companyData.description,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        description: e.target.value\n                                                                    }),\n                                                                placeholder: \"نقدم خدمات قانونية متميزة بأعلى معايير الجودة والاحترافية\",\n                                                                rows: 4,\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1214,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"معلومات إضافية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1228,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"capital\",\n                                                                children: \"رأس المال\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"capital\",\n                                                                type: \"number\",\n                                                                value: companyData.capital,\n                                                                onChange: (e)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        capital: parseInt(e.target.value) || 0\n                                                                    }),\n                                                                placeholder: \"2000000\",\n                                                                className: \"border-2 border-gray-200 rounded-xl focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1232,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"is_active\",\n                                                                children: \"الشركة نشطة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                id: \"is_active\",\n                                                                checked: companyData.is_active,\n                                                                onCheckedChange: (checked)=>setCompanyData({\n                                                                        ...companyData,\n                                                                        is_active: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1245,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1227,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end pt-6 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: handleSaveCompany,\n                                            disabled: loading,\n                                            className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? 'جاري الحفظ...' : 'حفظ بيانات الشركة'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 959,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 949,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'announcements' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"إضافة إعلان جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                    htmlFor: \"announcement_type\",\n                                                    children: \"نوع الإعلان\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"announcement_type\",\n                                                    value: newAnnouncement.type,\n                                                    onChange: (e)=>setNewAnnouncement({\n                                                            ...newAnnouncement,\n                                                            type: e.target.value\n                                                        }),\n                                                    className: \"w-full p-2 border border-gray-300 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"public_1\",\n                                                            children: \"إعلان رقم 1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"public_2\",\n                                                            children: \"إعلان رقم 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1288,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                    htmlFor: \"announcement_title\",\n                                                    children: \"عنوان الإعلان\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"announcement_title\",\n                                                    value: newAnnouncement.title,\n                                                    onChange: (e)=>setNewAnnouncement({\n                                                            ...newAnnouncement,\n                                                            title: e.target.value\n                                                        }),\n                                                    placeholder: \"عنوان الإعلان\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                    htmlFor: \"announcement_content\",\n                                                    children: \"محتوى الإعلان\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                    id: \"announcement_content\",\n                                                    value: newAnnouncement.content,\n                                                    onChange: (e)=>setNewAnnouncement({\n                                                            ...newAnnouncement,\n                                                            content: e.target.value\n                                                        }),\n                                                    placeholder: \"محتوى الإعلان\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 1300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: handleSaveAnnouncement,\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"إضافة الإعلان\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                            lineNumber: 1310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1274,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"الإعلانات الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: announcements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-center py-8\",\n                                        children: \"لا توجد إعلانات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1324,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: announcements.map((announcement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 space-x-reverse mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: announcement.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 1332,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                            variant: announcement.type === 'public_1' ? 'default' : 'secondary',\n                                                                            children: announcement.type === 'public_1' ? 'إعلان رقم 1' : 'إعلان رقم 2'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 1333,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                            variant: announcement.is_active ? 'default' : 'secondary',\n                                                                            children: announcement.is_active ? 'نشط' : 'غير نشط'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                            lineNumber: 1336,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1331,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: announcement.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400 mt-2\",\n                                                                    children: [\n                                                                        \"تاريخ الإنشاء: \",\n                                                                        new Date(announcement.created_date).toLocaleDateString('ar-SA')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1330,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            onClick: ()=>handleDeleteAnnouncement(announcement.id),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-red-600 hover:text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                                lineNumber: 1351,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                    lineNumber: 1329,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, announcement.id, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1328,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1326,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                    lineNumber: 1322,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 1272,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'library' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: \"إدارة المكتبة القانونية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1367,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-4\",\n                                        children: \"إدارة ملفات المكتبة القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"يمكن إضافة الملفات مباشرة إلى مجلد: /home/<USER>/Downloads/legal-system/laws\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1373,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1370,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1369,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 1365,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'footer-links' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"h-6 w-6 mr-3 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"إدارة روابط التذييل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1384,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"h-12 w-12 text-blue-600 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"إدارة الروابط التي تظهر في أسفل الموقع الرئيسي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>window.open('/settings/footer-links', '_blank'),\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-5 w-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"إدارة روابط التذييل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1390,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 1383,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: \"معاينة الموقع\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1411,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1410,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"h-12 w-12 text-blue-600 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1415,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"معاينة الموقع الرئيسي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>window.open('/home', '_blank'),\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_Award_BookOpen_Building_CheckCircle_Edit_ExternalLink_Eye_EyeOff_FileText_Gavel_Globe_Link_Mail_MapPin_Megaphone_MessageCircle_Phone_Plus_Save_Scale_Settings_Shield_Star_Trash2_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"h-5 w-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"فتح الموقع في نافذة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                        lineNumber: 1417,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                            lineNumber: 1413,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n                    lineNumber: 1409,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n            lineNumber: 505,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\website-admin\\\\page.tsx\",\n        lineNumber: 504,\n        columnNumber: 5\n    }, this);\n}\n_s(WebsiteAdminPage, \"aZLaJ+nsyLkpb5nCfzvfYYasX5w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = WebsiteAdminPage;\nvar _c;\n$RefreshReg$(_c, \"WebsiteAdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/website-admin/page.tsx\n"));

/***/ })

});