// اختبار الاتصال بـ Hugging Face API (مجاني)
require('dotenv').config({ path: '.env.local' });

async function testHuggingFaceConnection() {
  console.log('🔄 اختبار الاتصال بـ Hugging Face API (مجاني)...\n');
  
  // التحقق من وجود API Key
  const apiKey = process.env.HUGGINGFACE_API_KEY;
  
  if (!apiKey) {
    console.log('❌ HUGGINGFACE_API_KEY غير موجود في ملف .env.local');
    console.log('📝 يرجى إضافة المفتاح كما يلي:');
    console.log('   HUGGINGFACE_API_KEY=hf_your-actual-api-key-here\n');
    console.log('🔗 احصل على مفتاح مجاني من: https://huggingface.co/settings/tokens');
    return;
  }
  
  if (apiKey === 'your_huggingface_api_key_here') {
    console.log('⚠️ يرجى استبدال "your_huggingface_api_key_here" بالمفتاح الحقيقي');
    console.log('🔗 احصل على المفتاح من: https://huggingface.co/settings/tokens\n');
    return;
  }
  
  console.log('✅ تم العثور على HUGGINGFACE_API_KEY');
  console.log(`🔑 المفتاح يبدأ بـ: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}\n`);
  
  try {
    // اختبار الاتصال بـ Hugging Face API
    console.log('🌐 اختبار الاتصال بـ Hugging Face API...');
    
    const endpoint = 'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-72B-Instruct';
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        inputs: "مرحباً، هل يمكنك مساعدتي في استشارة قانونية؟",
        parameters: {
          max_new_tokens: 100,
          temperature: 0.7,
          return_full_text: false
        }
      })
    });
    
    console.log(`📡 رمز الاستجابة: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ نجح الاتصال بـ Hugging Face API!');
      
      if (Array.isArray(data) && data.length > 0) {
        const aiMessage = data[0].generated_text || data[0].text || JSON.stringify(data[0]);
        
        console.log('🤖 رد Qwen 2.5:');
        console.log(`   "${aiMessage}"\n`);
        
        console.log('🎉 النظام جاهز للعمل مع Qwen 2.5 مجاناً!');
        console.log('📊 معلومات الاستخدام:');
        console.log(`   • النموذج: Qwen 2.5 72B`);
        console.log(`   • التكلفة: مجاني تماماً! 🆓`);
        console.log(`   • ممتاز للغة العربية 🇸🇦`);
        
      } else {
        console.log('📄 استجابة غير متوقعة:', JSON.stringify(data, null, 2));
      }
      
    } else {
      const errorData = await response.text();
      console.log('❌ فشل الاتصال بـ Hugging Face API');
      console.log(`📄 رمز الخطأ: ${response.status}`);
      console.log(`📝 رسالة الخطأ: ${errorData}\n`);
      
      if (response.status === 401) {
        console.log('🔑 المفتاح غير صحيح أو منتهي الصلاحية');
        console.log('📋 تأكد من:');
        console.log('   1. نسخ المفتاح بالكامل من Hugging Face');
        console.log('   2. عدم وجود مسافات إضافية');
        console.log('   3. أن المفتاح لم ينته أو يُحذف');
      } else if (response.status === 503) {
        console.log('⏰ النموذج قيد التحميل أو مشغول');
        console.log('⏳ انتظر دقيقة وحاول مرة أخرى');
        console.log('💡 هذا طبيعي مع النماذج المجانية');
      } else if (response.status === 429) {
        console.log('⏰ تم تجاوز حد الاستخدام المجاني');
        console.log('⏳ انتظر قليلاً ثم حاول مرة أخرى');
      }
      
      return;
    }
    
  } catch (error) {
    console.log('❌ خطأ في الشبكة:', error.message);
    console.log('🌐 تأكد من اتصالك بالإنترنت\n');
    return;
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 ملخص الاختبار:');
  console.log('✅ Hugging Face API Key موجود ومُعرَّف');
  console.log('✅ الاتصال بـ Hugging Face API يعمل');
  console.log('✅ Qwen 2.5 72B متاح للاستخدام');
  console.log('🆓 مجاني تماماً - بدون تكلفة!');
  console.log('🇸🇦 ممتاز للغة العربية!');
  console.log('🚀 النظام جاهز للعمل!');
  console.log('='.repeat(50));
}

// تشغيل الاختبار
testHuggingFaceConnection().catch(console.error);
