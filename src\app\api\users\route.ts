import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المستخدمين من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM users ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching المستخدمين:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات المستخدمين',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة المستخدمين جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستخدمين بنجاح'
    })
  } catch (error) {
    console.error('Error creating المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المستخدمين' },
      { status: 500 }
    )
  }
}

// PUT - تحديث المستخدمين
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث المستخدمين بنجاح'
    })
  } catch (error) {
    console.error('Error updating المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المستخدمين' },
      { status: 500 }
    )
  }
}

// DELETE - حذف المستخدمين
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدمين مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM users WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدمين بنجاح'
    })
  } catch (error) {
    console.error('Error deleting المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المستخدمين' },
      { status: 500 }
    )
  }
}