(()=>{var e={};e.id=7014,e.ids=[7014],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>l});var n=s(64939),a=s(29021),o=s.n(a),c=s(33873),u=s.n(c),i=e([n]);n=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let _=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],s=d.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new n.Pool(_);async function l(e,t){let s=await p.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27430:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>l,GET:()=>u,POST:()=>c,PUT:()=>i});var n=s(32190),a=s(5069),o=e([a]);async function c(e){try{let{table_name:t,record_id:s,record_name:r}=await e.json();if(!t||!s)return n.NextResponse.json({success:!1,error:"اسم الجدول ومعرف السجل مطلوبان"},{status:400});let o=await (0,a.P)(`
      SELECT 
        als.*,
        coa.account_code,
        coa.account_name
      FROM account_linking_settings als
      JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      WHERE als.table_name = $1 AND als.is_enabled = true AND als.auto_create_on_insert = true
    `,[t]);if(0===o.rows.length)return n.NextResponse.json({success:!1,error:"لا توجد إعدادات ربط مفعلة لهذا الجدول"});let c=o.rows[0];if((await (0,a.P)("SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3",[c.default_main_account_id,t,s])).rows.length>0)return n.NextResponse.json({success:!1,error:"هذا السجل مربوط بحساب مسبقاً"});let u=`${c.account_code}-${s.toString().padStart(4,"0")}`,i=`${c.table_display_name}: `;if(r)i+=r;else try{let e=await (0,a.P)(`SELECT ${c.name_field} as name FROM ${t} WHERE ${c.id_field} = $1`,[s]);e.rows.length>0?i+=e.rows[0].name:i+=`سجل رقم ${s}`}catch(e){i+=`سجل رقم ${s}`}let l=await (0,a.P)(`
      INSERT INTO account_sub_links
      (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `,[c.default_main_account_id,t,s,u,i,"النظام"]);return n.NextResponse.json({success:!0,message:"تم ربط الحساب تلقائياً بنجاح",data:{link:l.rows[0],main_account:{id:c.default_main_account_id,name:c.account_name,code:c.account_code}}})}catch(e){return console.error("Error auto-linking account:",e),n.NextResponse.json({success:!1,error:"فشل في الربط التلقائي للحساب"},{status:500})}}async function u(e){try{let{searchParams:t}=new URL(e.url),s=t.get("table_name");if(!s)return n.NextResponse.json({success:!1,error:"اسم الجدول مطلوب"},{status:400});let r=await (0,a.P)(`
      SELECT 
        als.*,
        coa.account_code,
        coa.account_name
      FROM account_linking_settings als
      LEFT JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      WHERE als.table_name = $1
    `,[s]),o=r.rows.length>0&&r.rows[0].is_enabled&&r.rows[0].auto_create_on_insert;return n.NextResponse.json({success:!0,data:{is_enabled:o,settings:r.rows.length>0?r.rows[0]:null}})}catch(e){return console.error("Error checking auto-link settings:",e),n.NextResponse.json({success:!1,error:"فشل في التحقق من إعدادات الربط"},{status:500})}}async function i(e){try{let{link_id:t,sub_account_name:s,notes:r}=await e.json();if(!t)return n.NextResponse.json({success:!1,error:"معرف الرابط مطلوب"},{status:400});let o=await (0,a.P)(`
      UPDATE account_sub_links 
      SET 
        sub_account_name = COALESCE($1, sub_account_name),
        notes = COALESCE($2, notes)
      WHERE id = $3
      RETURNING *
    `,[s,r,t]);if(0===o.rows.length)return n.NextResponse.json({success:!1,error:"الرابط غير موجود"},{status:404});return n.NextResponse.json({success:!0,message:"تم تحديث الرابط بنجاح",data:o.rows[0]})}catch(e){return console.error("Error updating account link:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث الرابط"},{status:500})}}async function l(e){try{let{link_id:t}=await e.json();if(!t)return n.NextResponse.json({success:!1,error:"معرف الرابط مطلوب"},{status:400});let s=await (0,a.P)("DELETE FROM account_sub_links WHERE id = $1 RETURNING *",[t]);if(0===s.rows.length)return n.NextResponse.json({success:!1,error:"الرابط غير موجود"},{status:404});return n.NextResponse.json({success:!0,message:"تم حذف الرابط بنجاح"})}catch(e){return console.error("Error deleting account link:",e),n.NextResponse.json({success:!1,error:"فشل في حذف الرابط"},{status:500})}}a=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},72260:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>i,routeModule:()=>l,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>_});var n=s(96559),a=s(48088),o=s(37719),c=s(27430),u=e([c]);c=(u.then?(await u)():u)[0];let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auto-link-account/route",pathname:"/api/auto-link-account",filename:"route",bundlePath:"app/api/auto-link-account/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\auto-link-account\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:_,serverHooks:p}=l;function i(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:_})}r()}catch(e){r(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(72260));module.exports=r})();