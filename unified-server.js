// خادم موحد مع توجيه المنافذ إلى قواعد البيانات المختلفة
// Unified Server with Port-based Database Routing

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// إعدادات المنافذ وقواعد البيانات
const PORT_CONFIG = {
  7443: {
    database: 'mohammi',
    name: 'نظام إدارة المحاماة - محمد',
    env: {
      DATABASE_URL: 'postgresql://postgres:yemen123@localhost:5432/mohammi',
      DB_NAME: 'mohammi',
      PORT: '7443',
      APP_NAME: 'نظام إدارة المحاماة - محمد'
    }
  },
  8914: {
    database: 'rubaie',
    name: 'نظام إدارة المحاماة - الربعي',
    env: {
      DATABASE_URL: 'postgresql://postgres:yemen123@localhost:5432/rubaie',
      DB_NAME: 'rubaie',
      PORT: '8914',
      APP_NAME: 'نظام إدارة المحاماة - الربعي'
    }
  }
};

// دالة لتعيين متغيرات البيئة حسب المنفذ
function setEnvironmentForPort(port) {
  const config = PORT_CONFIG[port];
  if (!config) {
    throw new Error(`لا يوجد إعداد للمنفذ ${port}`);
  }

  // تعيين متغيرات البيئة
  Object.keys(config.env).forEach(key => {
    process.env[key] = config.env[key];
  });

  console.log(`🔧 تم تعيين البيئة للمنفذ ${port}:`);
  console.log(`   📊 قاعدة البيانات: ${config.database}`);
  console.log(`   🏢 النظام: ${config.name}`);
  console.log(`   🌐 الرابط: http://localhost:${port}`);
}

// دالة لإنشاء خادم لمنفذ محدد
async function createServerForPort(port) {
  try {
    // تعيين البيئة للمنفذ
    setEnvironmentForPort(port);

    // إنشاء تطبيق Next.js
    const app = next({ 
      dev: process.env.NODE_ENV !== 'production',
      port: port
    });
    
    const handle = app.getRequestHandler();
    await app.prepare();

    // إنشاء الخادم
    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    });

    // بدء الاستماع
    server.listen(port, (err) => {
      if (err) {
        console.error(`❌ خطأ في تشغيل الخادم على المنفذ ${port}:`, err);
        throw err;
      }
      
      const config = PORT_CONFIG[port];
      console.log(`\n🚀 تم تشغيل ${config.name} بنجاح!`);
      console.log(`   🌐 الرابط: http://localhost:${port}`);
      console.log(`   🗄️ قاعدة البيانات: ${config.database}`);
      console.log(`   ⏰ الوقت: ${new Date().toLocaleString('ar-SA')}`);
    });

    return server;
  } catch (error) {
    console.error(`❌ فشل في إنشاء خادم للمنفذ ${port}:`, error);
    throw error;
  }
}

// دالة لتشغيل جميع الخوادم
async function startAllServers() {
  console.log('🌟 بدء تشغيل النظام الموحد لإدارة المحاماة');
  console.log('=' .repeat(50));

  const ports = Object.keys(PORT_CONFIG).map(Number);
  const servers = [];

  for (const port of ports) {
    try {
      console.log(`\n🔄 تشغيل الخادم على المنفذ ${port}...`);
      const server = await createServerForPort(port);
      servers.push({ port, server });
    } catch (error) {
      console.error(`❌ فشل في تشغيل الخادم على المنفذ ${port}:`, error);
    }
  }

  if (servers.length > 0) {
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 تم تشغيل جميع الخوادم بنجاح!');
    console.log('\n📋 ملخص الخوادم النشطة:');
    
    servers.forEach(({ port }) => {
      const config = PORT_CONFIG[port];
      console.log(`   🔗 ${config.name}`);
      console.log(`      🌐 http://localhost:${port}`);
      console.log(`      🗄️ ${config.database}`);
    });

    console.log('\n⏹️ للإيقاف: اضغط Ctrl+C');
    
    // معالجة إيقاف النظام
    process.on('SIGINT', () => {
      console.log('\n🛑 إيقاف جميع الخوادم...');
      servers.forEach(({ port, server }) => {
        server.close(() => {
          console.log(`✅ تم إيقاف الخادم على المنفذ ${port}`);
        });
      });
      process.exit(0);
    });
  } else {
    console.log('❌ فشل في تشغيل أي خادم');
    process.exit(1);
  }
}

// دالة لتشغيل خادم واحد
async function startSingleServer(port) {
  if (!PORT_CONFIG[port]) {
    console.error(`❌ المنفذ ${port} غير مدعوم`);
    console.log('المنافذ المدعومة:', Object.keys(PORT_CONFIG).join(', '));
    process.exit(1);
  }

  console.log(`🚀 تشغيل خادم واحد على المنفذ ${port}`);
  
  try {
    await createServerForPort(port);
    
    // معالجة إيقاف النظام
    process.on('SIGINT', () => {
      console.log(`\n🛑 إيقاف الخادم على المنفذ ${port}...`);
      process.exit(0);
    });
  } catch (error) {
    console.error(`❌ فشل في تشغيل الخادم:`, error);
    process.exit(1);
  }
}

// تشغيل النظام
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // تشغيل جميع الخوادم
    startAllServers().catch(error => {
      console.error('❌ خطأ في تشغيل النظام:', error);
      process.exit(1);
    });
  } else if (args[0] === '--port' && args[1]) {
    // تشغيل خادم واحد
    const port = parseInt(args[1]);
    startSingleServer(port).catch(error => {
      console.error('❌ خطأ في تشغيل الخادم:', error);
      process.exit(1);
    });
  } else {
    console.log('الاستخدام:');
    console.log('  node unified-server.js                 # تشغيل جميع الخوادم');
    console.log('  node unified-server.js --port 7443     # تشغيل خادم واحد');
    process.exit(1);
  }
}

module.exports = { createServerForPort, startAllServers, startSingleServer };
