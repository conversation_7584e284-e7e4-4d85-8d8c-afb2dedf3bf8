/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cost-centers/route";
exports.ids = ["app/api/cost-centers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcost-centers%2Froute&page=%2Fapi%2Fcost-centers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcost-centers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcost-centers%2Froute&page=%2Fapi%2Fcost-centers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcost-centers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_cost_centers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cost-centers/route.ts */ \"(rsc)/./src/app/api/cost-centers/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_cost_centers_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_cost_centers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cost-centers/route\",\n        pathname: \"/api/cost-centers\",\n        filename: \"route\",\n        bundlePath: \"app/api/cost-centers/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\cost-centers\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_cost_centers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjb3N0LWNlbnRlcnMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNvc3QtY2VudGVycyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNvc3QtY2VudGVycyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbW9oYW1pbmV3JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbW9oYW1pbmV3JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY29zdC1jZW50ZXJzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jb3N0LWNlbnRlcnMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb3N0LWNlbnRlcnNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2Nvc3QtY2VudGVycy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXG1vaGFtaW5ld1xcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjb3N0LWNlbnRlcnNcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcost-centers%2Froute&page=%2Fapi%2Fcost-centers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcost-centers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cost-centers/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/cost-centers/route.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع مراكز التكلفة\nasync function GET() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT \n        cc.id,\n        cc.center_code,\n        cc.center_name,\n        cc.parent_id,\n        cc.center_level,\n        cc.is_active,\n        cc.description,\n        cc.created_date,\n        cc.updated_date,\n        parent.center_name as parent_name\n      FROM cost_centers cc\n      LEFT JOIN cost_centers parent ON cc.parent_id = parent.id\n      ORDER BY cc.center_level, cc.center_code\n    `);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            centers: result.rows\n        });\n    } catch (error) {\n        console.error('خطأ في جلب مراكز التكلفة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب مراكز التكلفة'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مركز تكلفة جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { center_code, center_name, parent_id, description } = body;\n        // التحقق من البيانات المطلوبة\n        if (!center_code || !center_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المركز واسم المركز مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم تكرار رمز المركز\n        const existingCenter = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM cost_centers WHERE center_code = $1', [\n            center_code\n        ]);\n        if (existingCenter.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المركز موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد مستوى المركز\n        let center_level = 1;\n        if (parent_id && parent_id !== '0') {\n            const parentResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT center_level FROM cost_centers WHERE id = $1', [\n                parent_id\n            ]);\n            if (parentResult.rows.length > 0) {\n                center_level = parentResult.rows[0].center_level + 1;\n            }\n        }\n        // إدراج مركز التكلفة الجديد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO cost_centers (\n        center_code, center_name, parent_id, center_level, description, is_active\n      )\n      VALUES ($1, $2, $3, $4, $5, true)\n      RETURNING *\n    `, [\n            center_code,\n            center_name,\n            parent_id && parent_id !== '0' ? parseInt(parent_id) : null,\n            center_level,\n            description || null\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            center: result.rows[0],\n            message: 'تم إنشاء مركز التكلفة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إنشاء مركز التكلفة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء مركز التكلفة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مركز تكلفة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, center_code, center_name, parent_id, description, is_active } = body;\n        // التحقق من البيانات المطلوبة\n        if (!id || !center_code || !center_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المركز ورمز المركز واسم المركز مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم تكرار رمز المركز (باستثناء المركز الحالي)\n        const existingCenter = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM cost_centers WHERE center_code = $1 AND id != $2', [\n            center_code,\n            id\n        ]);\n        if (existingCenter.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المركز موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد مستوى المركز\n        let center_level = 1;\n        if (parent_id && parent_id !== '0') {\n            const parentResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT center_level FROM cost_centers WHERE id = $1', [\n                parent_id\n            ]);\n            if (parentResult.rows.length > 0) {\n                center_level = parentResult.rows[0].center_level + 1;\n            }\n        }\n        // تحديث مركز التكلفة\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE cost_centers \n      SET \n        center_code = $1,\n        center_name = $2,\n        parent_id = $3,\n        center_level = $4,\n        description = $5,\n        is_active = $6,\n        updated_date = CURRENT_TIMESTAMP\n      WHERE id = $7\n      RETURNING *\n    `, [\n            center_code,\n            center_name,\n            parent_id && parent_id !== '0' ? parseInt(parent_id) : null,\n            center_level,\n            description || null,\n            is_active !== undefined ? is_active : true,\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مركز التكلفة غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            center: result.rows[0],\n            message: 'تم تحديث مركز التكلفة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث مركز التكلفة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث مركز التكلفة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مركز تكلفة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف مركز التكلفة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود مراكز فرعية\n        const subCenters = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT COUNT(*) as count FROM cost_centers WHERE parent_id = $1', [\n            id\n        ]);\n        if (parseInt(subCenters.rows[0].count) > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف مركز التكلفة لأنه يحتوي على مراكز فرعية'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود معاملات مرتبطة\n        const transactions = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT COUNT(*) as count FROM journal_entry_details WHERE cost_center_id = $1', [\n            id\n        ]);\n        if (parseInt(transactions.rows[0].count) > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف مركز التكلفة لأنه مرتبط بمعاملات مالية'\n            }, {\n                status: 400\n            });\n        }\n        // حذف مركز التكلفة\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM cost_centers WHERE id = $1 RETURNING *', [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مركز التكلفة غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف مركز التكلفة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف مركز التكلفة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف مركز التكلفة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cost-centers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcost-centers%2Froute&page=%2Fapi%2Fcost-centers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcost-centers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();