# سكريبت تحديث قاعدة البيانات rubaie من mohammi
# PowerShell Script

Write-Host "🔄 بدء تحديث قاعدة البيانات rubaie..." -ForegroundColor Green

# متغيرات الاتصال
$sourceDb = "mohammi"
$targetDb = "rubaie"
$pgUser = "postgres"
$pgHost = "localhost"
$pgPort = "5432"

# التحقق من وجود PostgreSQL
Write-Host "🔍 التحقق من PostgreSQL..." -ForegroundColor Yellow
try {
    $pgVersion = psql --version
    Write-Host "✅ PostgreSQL متوفر: $pgVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL غير متوفر. يرجى التأكد من تثبيته وإضافته إلى PATH" -ForegroundColor Red
    exit 1
}

# التحقق من وجود قاعدة البيانات المصدر
Write-Host "🔍 التحقق من قاعدة البيانات المصدر ($sourceDb)..." -ForegroundColor Yellow
$sourceExists = psql -U $pgUser -h $pgHost -p $pgPort -lqt | Select-String $sourceDb
if (-not $sourceExists) {
    Write-Host "❌ قاعدة البيانات المصدر '$sourceDb' غير موجودة" -ForegroundColor Red
    exit 1
}
Write-Host "✅ قاعدة البيانات المصدر موجودة" -ForegroundColor Green

# التحقق من وجود قاعدة البيانات الهدف
Write-Host "🔍 التحقق من قاعدة البيانات الهدف ($targetDb)..." -ForegroundColor Yellow
$targetExists = psql -U $pgUser -h $pgHost -p $pgPort -lqt | Select-String $targetDb
if (-not $targetExists) {
    Write-Host "⚠️ قاعدة البيانات الهدف '$targetDb' غير موجودة. سيتم إنشاؤها..." -ForegroundColor Yellow
    createdb -U $pgUser -h $pgHost -p $pgPort $targetDb
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم إنشاء قاعدة البيانات '$targetDb'" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في إنشاء قاعدة البيانات '$targetDb'" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ قاعدة البيانات الهدف موجودة" -ForegroundColor Green
}

# تطبيق هيكل قاعدة البيانات المحدث
Write-Host "🔧 تطبيق هيكل قاعدة البيانات المحدث..." -ForegroundColor Yellow
if (Test-Path "update_database_structure.sql") {
    psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -f "update_database_structure.sql"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم تطبيق هيكل قاعدة البيانات بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في تطبيق هيكل قاعدة البيانات" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ ملف update_database_structure.sql غير موجود" -ForegroundColor Red
    exit 1
}

# نسخ بيانات العملاء من قاعدة البيانات المصدر
Write-Host "📋 نسخ بيانات العملاء..." -ForegroundColor Yellow
$copyClientsQuery = @"
INSERT INTO clients (name, phone, email, id_number, address, notes, created_date, updated_date)
SELECT name, phone, email, id_number, address, notes, created_date, updated_date
FROM dblink('host=localhost port=5432 dbname=$sourceDb user=$pgUser', 
    'SELECT name, phone, email, id_number, address, notes, created_date, updated_date FROM clients')
AS source_clients(name VARCHAR(200), phone VARCHAR(20), email VARCHAR(100), 
    id_number VARCHAR(50), address TEXT, notes TEXT, 
    created_date TIMESTAMP, updated_date TIMESTAMP)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    id_number = EXCLUDED.id_number,
    address = EXCLUDED.address,
    notes = EXCLUDED.notes,
    updated_date = EXCLUDED.updated_date;
"@

# تمكين dblink إذا لم يكن مفعلاً
psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -c "CREATE EXTENSION IF NOT EXISTS dblink;"

# تنفيذ نسخ البيانات
Write-Host "📊 نسخ البيانات من قاعدة البيانات المصدر..." -ForegroundColor Yellow

# نسخ العملاء
Write-Host "   👥 نسخ العملاء..." -ForegroundColor Cyan
psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -c "
TRUNCATE TABLE clients RESTART IDENTITY CASCADE;
INSERT INTO clients (name, phone, email, id_number, address, notes)
SELECT name, phone, email, id_number, address, notes
FROM dblink('host=localhost port=5432 dbname=$sourceDb user=$pgUser', 
    'SELECT name, phone, email, id_number, address, notes FROM clients')
AS source_clients(name VARCHAR(200), phone VARCHAR(20), email VARCHAR(100), 
    id_number VARCHAR(50), address TEXT, notes TEXT);
"

# نسخ القضايا
Write-Host "   ⚖️ نسخ القضايا..." -ForegroundColor Cyan
psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -c "
TRUNCATE TABLE issues RESTART IDENTITY CASCADE;
INSERT INTO issues (case_number, title, description, client_id, issue_type_id, court_id, 
    status, case_amount, currency_id, notes, contract_method, contract_date, start_date, created_date)
SELECT case_number, title, description, client_id, issue_type_id, court_id, 
    status, case_amount, currency_id, notes, contract_method, contract_date, start_date, created_date
FROM dblink('host=localhost port=5432 dbname=$sourceDb user=$pgUser', 
    'SELECT case_number, title, description, client_id, issue_type_id, court_id, 
     status, case_amount, currency_id, notes, contract_method, contract_date, start_date, created_date 
     FROM issues')
AS source_issues(case_number VARCHAR(50), title VARCHAR(500), description TEXT, 
    client_id INTEGER, issue_type_id INTEGER, court_id INTEGER, 
    status VARCHAR(50), case_amount DECIMAL(15,2), currency_id INTEGER, 
    notes TEXT, contract_method VARCHAR(100), contract_date DATE, start_date DATE, created_date TIMESTAMP);
"

# التحقق من النتائج
Write-Host "📊 التحقق من النتائج..." -ForegroundColor Yellow

$clientCount = psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -t -c "SELECT COUNT(*) FROM clients;"
$issueCount = psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -t -c "SELECT COUNT(*) FROM issues;"
$courtCount = psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -t -c "SELECT COUNT(*) FROM courts;"

Write-Host "✅ تم التحديث بنجاح!" -ForegroundColor Green
Write-Host "📊 إحصائيات قاعدة البيانات المحدثة:" -ForegroundColor Cyan
Write-Host "   👥 العملاء: $($clientCount.Trim())" -ForegroundColor White
Write-Host "   ⚖️ القضايا: $($issueCount.Trim())" -ForegroundColor White
Write-Host "   🏛️ المحاكم: $($courtCount.Trim())" -ForegroundColor White

Write-Host "`n🎉 تم تحديث قاعدة البيانات rubaie بنجاح من mohammi!" -ForegroundColor Green
Write-Host "🚀 يمكنك الآن تشغيل النسخة الثانية على المنفذ 8914" -ForegroundColor Yellow
