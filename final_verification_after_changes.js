// التحقق النهائي من جميع العلاقات بعد التعديل
const { Pool } = require('pg');

async function finalVerificationAfterChanges() {
  console.log('🔍 التحقق النهائي من جميع العلاقات بعد التعديل...\n');

  // قواعد البيانات المطلوب فحصها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 فحص قاعدة البيانات: ${dbName}`);
    console.log('='.repeat(40));
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. عرض جميع العلاقات النهائية
      console.log('\n   🔗 جميع العلاقات النهائية:');
      
      const allRelations = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule,
          rc.update_rule
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        ORDER BY kcu.column_name
      `);

      allRelations.rows.forEach((rel, index) => {
        console.log(`      ${index + 1}. ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
        console.log(`         (ON DELETE ${rel.delete_rule}, ON UPDATE ${rel.update_rule})`);
      });

      // 2. التأكد من عدم وجود جدول issue_courts
      console.log('\n   🗑️ التأكد من حذف جدول issue_courts:');
      
      const issueCourtTableExists = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'issue_courts'
        )
      `);

      if (issueCourtTableExists.rows[0].exists) {
        console.log('      ❌ جدول issue_courts لا يزال موجود!');
      } else {
        console.log('      ✅ تم حذف جدول issue_courts بنجاح');
      }

      // 3. التأكد من وجود العمود court_id في جدول القضايا
      console.log('\n   📋 التأكد من وجود العمود court_id في جدول القضايا:');
      
      const courtIdColumn = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'court_id'
      `);

      if (courtIdColumn.rows.length > 0) {
        const column = courtIdColumn.rows[0];
        console.log(`      ✅ العمود موجود: ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      } else {
        console.log('      ❌ العمود court_id غير موجود في جدول القضايا!');
      }

      // 4. التأكد من عدم وجود العمود issue_id في جدول المحاكم
      console.log('\n   🏛️ التأكد من حذف العمود issue_id من جدول المحاكم:');
      
      const issueIdInCourts = await pool.query(`
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_name = 'courts' AND column_name = 'issue_id'
      `);

      if (issueIdInCourts.rows.length > 0) {
        console.log('      ❌ العمود issue_id لا يزال موجود في جدول المحاكم!');
      } else {
        console.log('      ✅ تم حذف العمود issue_id من جدول المحاكم بنجاح');
      }

      // 5. اختبار العلاقة الجديدة: المحاكم -> القضايا
      console.log('\n   🏛️ اختبار علاقة المحاكم -> القضايا (One-to-Many):');
      
      const courtsIssuesTest = await pool.query(`
        SELECT 
          c.id as court_id,
          c.name as court_name,
          c.type as court_type,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ' ORDER BY i.case_number) as case_numbers
        FROM courts c
        LEFT JOIN issues i ON c.id = i.court_id
        GROUP BY c.id, c.name, c.type
        ORDER BY issue_count DESC, c.name
      `);

      console.log('      📊 المحاكم وقضاياها:');
      courtsIssuesTest.rows.forEach(court => {
        console.log(`         - ${court.court_name} (${court.court_type || 'غير محدد'}): ${court.issue_count} قضية`);
        if (court.case_numbers && court.issue_count > 0) {
          console.log(`           القضايا: ${court.case_numbers}`);
        }
      });

      // 6. اختبار العلاقة: العملاء -> القضايا
      console.log('\n   👥 اختبار علاقة العملاء -> القضايا (One-to-Many):');
      
      const clientsIssuesTest = await pool.query(`
        SELECT 
          c.id as client_id,
          c.name as client_name,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ' ORDER BY i.case_number) as case_numbers
        FROM clients c
        LEFT JOIN issues i ON c.id = i.client_id
        GROUP BY c.id, c.name
        HAVING COUNT(i.id) > 0
        ORDER BY issue_count DESC, c.name
        LIMIT 5
      `);

      console.log('      📊 العملاء وقضاياهم:');
      clientsIssuesTest.rows.forEach(client => {
        console.log(`         - ${client.client_name}: ${client.issue_count} قضية (${client.case_numbers})`);
      });

      // 7. اختبار العلاقة: أنواع القضايا -> القضايا
      console.log('\n   📋 اختبار علاقة أنواع القضايا -> القضايا (One-to-Many):');
      
      const issueTypesTest = await pool.query(`
        SELECT 
          it.id as type_id,
          it.name as type_name,
          it.category,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ' ORDER BY i.case_number) as case_numbers
        FROM issue_types it
        LEFT JOIN issues i ON it.id = i.issue_type_id
        GROUP BY it.id, it.name, it.category
        HAVING COUNT(i.id) > 0
        ORDER BY issue_count DESC, it.name
      `);

      console.log('      📊 أنواع القضايا وقضاياها:');
      issueTypesTest.rows.forEach(type => {
        console.log(`         - ${type.type_name} (${type.category || 'غير محدد'}): ${type.issue_count} قضية`);
        if (type.case_numbers) {
          console.log(`           القضايا: ${type.case_numbers}`);
        }
      });

      // 8. الاستعلام الشامل النهائي
      console.log('\n   🧪 الاستعلام الشامل النهائي:');
      
      const comprehensiveQuery = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          cl.name as client_name,
          cl.phone as client_phone,
          it.name as issue_type_name,
          it.category as issue_type_category,
          c.name as court_name,
          c.type as court_type,
          i.status,
          i.amount,
          i.created_at
        FROM issues i
        LEFT JOIN clients cl ON i.client_id = cl.id
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts c ON i.court_id = c.id
        ORDER BY i.case_number
      `);

      console.log('      ✅ جميع القضايا مع علاقاتها:');
      comprehensiveQuery.rows.forEach(row => {
        console.log(`         📄 ${row.case_number}: ${row.title || 'بدون عنوان'}`);
        console.log(`            👤 العميل: ${row.client_name || 'غير محدد'} (${row.client_phone || 'بدون هاتف'})`);
        console.log(`            📋 النوع: ${row.issue_type_name || 'غير محدد'} (${row.issue_type_category || 'غير محدد'})`);
        console.log(`            🏛️ المحكمة: ${row.court_name || 'غير محدد'} (${row.court_type || 'غير محدد'})`);
        console.log(`            📊 الحالة: ${row.status || 'غير محدد'} | المبلغ: ${row.amount || 0}`);
        console.log('');
      });

      // 9. فحص سلامة البيانات النهائي
      console.log('\n   🔍 فحص سلامة البيانات النهائي:');
      
      const finalIntegrityCheck = await pool.query(`
        SELECT 
          'قضايا بدون عملاء' as check_type,
          COUNT(*) as count
        FROM issues 
        WHERE client_id IS NULL
        UNION ALL
        SELECT 
          'قضايا بدون أنواع' as check_type,
          COUNT(*) as count
        FROM issues 
        WHERE issue_type_id IS NULL
        UNION ALL
        SELECT 
          'قضايا بدون محاكم' as check_type,
          COUNT(*) as count
        FROM issues 
        WHERE court_id IS NULL
        UNION ALL
        SELECT 
          'مراجع عملاء مكسورة' as check_type,
          COUNT(*) as count
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        WHERE i.client_id IS NOT NULL AND c.id IS NULL
        UNION ALL
        SELECT 
          'مراجع أنواع مكسورة' as check_type,
          COUNT(*) as count
        FROM issues i
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        WHERE i.issue_type_id IS NOT NULL AND it.id IS NULL
        UNION ALL
        SELECT 
          'مراجع محاكم مكسورة' as check_type,
          COUNT(*) as count
        FROM issues i
        LEFT JOIN courts c ON i.court_id = c.id
        WHERE i.court_id IS NOT NULL AND c.id IS NULL
      `);

      finalIntegrityCheck.rows.forEach(check => {
        const status = check.count === '0' ? '✅' : '⚠️';
        console.log(`      ${status} ${check.check_type}: ${check.count}`);
      });

      // 10. إحصائيات نهائية
      console.log('\n   📊 إحصائيات نهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM clients) as total_clients,
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM courts) as total_courts,
          (SELECT COUNT(*) FROM issue_types) as total_issue_types,
          (SELECT COUNT(*) FROM issues WHERE client_id IS NOT NULL) as issues_with_clients,
          (SELECT COUNT(*) FROM issues WHERE issue_type_id IS NOT NULL) as issues_with_types,
          (SELECT COUNT(*) FROM issues WHERE court_id IS NOT NULL) as issues_with_courts
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي العملاء: ${stats.total_clients}`);
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - إجمالي المحاكم: ${stats.total_courts}`);
      console.log(`      - إجمالي أنواع القضايا: ${stats.total_issue_types}`);
      console.log(`      - قضايا مرتبطة بعملاء: ${stats.issues_with_clients}`);
      console.log(`      - قضايا مرتبطة بأنواع: ${stats.issues_with_types}`);
      console.log(`      - قضايا مرتبطة بمحاكم: ${stats.issues_with_courts}`);

    } catch (error) {
      console.error(`   ❌ خطأ في فحص قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من التحقق النهائي');
  
  console.log('\n🎯 ملخص العلاقات النهائية المؤكدة:');
  console.log('1. ✅ clients.id -> issues.client_id (One-to-Many)');
  console.log('   📝 عميل واحد يمكن أن يكون له عدة قضايا');
  console.log('');
  console.log('2. ✅ issue_types.id -> issues.issue_type_id (One-to-Many)');
  console.log('   📝 نوع واحد يمكن أن يكون له عدة قضايا');
  console.log('');
  console.log('3. ✅ courts.id -> issues.court_id (One-to-Many)');
  console.log('   📝 محكمة واحدة يمكن أن تكون لها عدة قضايا');
  console.log('   📝 قضية واحدة لها محكمة واحدة فقط');
  console.log('');
  console.log('4. 🗑️ تم حذف جدول issue_courts (Many-to-Many)');
  console.log('5. 🗑️ تم حذف العمود issue_id من جدول المحاكم');
  console.log('6. ➕ تم إضافة العمود court_id إلى جدول القضايا');
  console.log('7. 🛡️ جميع المفاتيح الخارجية تعمل بشكل صحيح');
  console.log('8. 📊 البيانات متسقة وسليمة');
}

// تشغيل التحقق
finalVerificationAfterChanges().catch(console.error);
