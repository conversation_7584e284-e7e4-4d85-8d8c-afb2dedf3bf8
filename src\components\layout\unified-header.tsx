'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Bell, Search, User, LogOut, Menu, X, LogIn } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SmartSearch } from './smart-search'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'

interface CompanyData {
  id: number;
  name: string;
  legal_name: string;
  logo_url: string;
  logo_image_url: string;
}

export function UnifiedHeader() {
  const router = useRouter()
  const { user, logout } = useAuth()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)

  // جلب بيانات الشركة
  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        const response = await fetch('/api/company')
        const result = await response.json()
        if (result.success && result.data && result.data.length > 0) {
          setCompanyData(result.data[0])
        } else {
          // البيانات الافتراضية - محدثة لتتطابق مع قاعدة البيانات الفعلية
          setCompanyData({
            id: 1,
            name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
            legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',
            logo_url: '/images/company-logo.png',
            logo_image_url: '/images/logo.png'
          })
        }
      } catch (error) {
        console.error('خطأ في جلب بيانات الشركة:', error)
        setCompanyData({
          id: 1,
          name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
          legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
          logo_url: '/images/simple-logo.svg',
          logo_image_url: '/images/simple-logo.svg'
        })
      }
    }

    fetchCompanyData()
  }, [])

  // Add scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleLogout = () => {
    logout()
  }

  const navLinks = [
    { href: '/dashboard', label: 'لوحة التحكم' },
    { href: '/clients', label: 'الموكلين' },
    { href: '/issues', label: 'القضايا' },
    { href: '/employees', label: 'الموظفين' },
    { href: '/reports', label: 'التقارير' },
  ]

  return (
    <header
      className="w-full z-50 transition-all duration-500"
      style={{
        background: 'linear-gradient(135deg, #333333 0%, #**********%)',
        borderBottom: '1px solid rgba(234, 179, 8, 0.2)',
        padding: '1.5rem 0'
      }}
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo - بحجم أكبر ليتناسب مع ارتفاع شريط العنوان */}
          <Link href="/dashboard" className="flex items-center group">
            {companyData?.logo_image_url ? (
              <div className="w-40 h-40 rounded-xl overflow-hidden bg-gradient-to-br from-yellow-500 to-yellow-600 shadow-lg border border-yellow-600/30">
                <img
                  src={companyData.logo_image_url}
                  alt={companyData.name}
                  className="w-full h-full object-contain p-4"
                />
              </div>
            ) : (
              <div className="w-40 h-40 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center text-gray-900 shadow-lg">
                <svg className="w-24 h-24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
            )}
            <div className="mr-4 text-right">
              <h1 className="text-2xl font-bold text-white transition-colors duration-300">
                {companyData?.name || 'مؤسسة الجرافي للمحاماة والاستشارات القانونية'}
              </h1>
              <p className="text-lg text-yellow-300 transition-colors duration-300">
                {companyData?.legal_name || 'مؤسسة الجرافي للمحاماة والاستشارات القانونية'}
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="relative font-medium text-white transition-all duration-300 hover:scale-105 hover:text-yellow-300 after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-yellow-500 after:to-yellow-600 after:transition-all after:duration-300 hover:after:w-full"
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Search and Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <SmartSearch />

            <Button
              variant="outline"
              onClick={() => window.open('/home', '_blank')}
              className="text-white border-yellow-600/30 hover:bg-yellow-600/10 hover:border-yellow-500"
            >
              الموقع الرئيسي
            </Button>

            <Button
              variant="outline"
              onClick={() => router.push('/client-login')}
              className="text-white border-yellow-600/30 hover:bg-yellow-600/10 hover:border-yellow-500"
            >
              بوابة العملاء
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/10"
            >
              <Bell className="h-5 w-5" />
            </Button>

            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="text-right">
                <div className="text-sm font-medium text-white">
                  {user?.name || 'المستخدم'}
                </div>
                <div className="text-xs text-yellow-300">
                  {user?.role_display_name || user?.username || 'غير محدد'}
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/10"
              >
                <User className="h-5 w-5" />
              </Button>

              {user && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleLogout}
                  className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                  title="تسجيل الخروج"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              )}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center space-x-3 space-x-reverse">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-white hover:bg-white/10"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden mt-6 pb-6">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-gray-100 p-6">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="px-4 py-3 rounded-xl hover:bg-blue-50 hover:text-blue-600 font-medium text-right transition-all duration-300 text-gray-700"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
                <div className="pt-4 border-t border-yellow-600/20 space-y-3">
                  <Button
                    variant="outline"
                    className="w-full font-medium"
                    onClick={() => {
                      window.open('/home', '_blank')
                      setIsMobileMenuOpen(false)
                    }}
                  >
                    الموقع الرئيسي
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full font-medium"
                    onClick={() => {
                      router.push('/client-login')
                      setIsMobileMenuOpen(false)
                    }}
                  >
                    بوابة العملاء
                  </Button>
                  {user && (
                    <Button
                      variant="outline"
                      className="w-full font-medium text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => {
                        handleLogout()
                        setIsMobileMenuOpen(false)
                      }}
                    >
                      <LogOut className="w-4 h-4 ml-2" />
                      تسجيل الخروج
                    </Button>
                  )}
                </div>
              </nav>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
