/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/home/<USER>
  \*********************************************************************************************************************************************************************************************************************************************************************/
/* تصميم موحد للصفحة الرئيسية */
.tafahum-page {
  background: linear-gradient(135deg, #333333 0%, #171717 100%);
  color: white;
  min-height: 100vh;
}

/* تصميم الهيدر */
.tafahum-header {
  background: linear-gradient(135deg, #333333 0%, #171717 100%) !important;
  border-bottom: 1px solid rgba(234, 179, 8, 0.2);
}

/* تصميم الأزرار */
.tafahum-btn {
  background: linear-gradient(to right, #eab308, #f59e0b);
  color: #1f2937;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tafahum-btn:hover {
  background: linear-gradient(to right, #f59e0b, #d97706);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
}

.tafahum-btn-outline {
  background: transparent;
  color: #eab308;
  border: 2px solid #eab308;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tafahum-btn-outline:hover {
  background: #eab308;
  color: #1f2937;
  transform: translateY(-2px);
}

/* تصميم روابط التنقل */
.tafahum-nav-link {
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.tafahum-nav-link:hover {
  color: #fbbf24;
  transform: scale(1.05);
}

.tafahum-nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #eab308, #f59e0b);
  transition: width 0.3s ease;
}

.tafahum-nav-link:hover::after {
  width: 100%;
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, #333333 0%, #171717 100%);
  color: white;
  overflow: hidden;
}

.hero-pattern {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.4' fill-rule='evenodd'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/svg%3E");
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-badge {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Stats */
.stat-item {
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

/* Image Frame */
.image-frame {
  position: relative;
  z-index: 1;
}

.image-frame::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 1rem;
  z-index: -1;
  transition: all 0.3s ease;
}

.image-frame:hover::before {
  top: -15px;
  right: -15px;
}

/* Experience Badge */
.experience-badge {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.experience-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Wave Divider */
.wave-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.wave-divider svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 150px;
}

.wave-divider .shape-fill {
  fill: #FFFFFF;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .hero-content {
    text-align: center;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .hero {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
  
  .hero h1 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .experience-badge {
    position: static !important;
    margin-top: 2rem;
    margin-left: auto;
    margin-right: auto;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/vibrant-theme.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* تصميم حيوي واحترافي للصفحة الرئيسية */

:root {
  /* الألوان الأساسية الحيوية */
  --vibrant-primary: #1e40af;      /* أزرق ملكي */
  --vibrant-primary-light: #3b82f6; /* أزرق فاتح */
  --vibrant-primary-dark: #1e3a8a;  /* أزرق داكن */
  
  --vibrant-secondary: #059669;     /* أخضر زمردي */
  --vibrant-secondary-light: #10b981; /* أخضر فاتح */
  --vibrant-secondary-dark: #047857; /* أخضر داكن */
  
  --vibrant-accent: #dc2626;        /* أحمر حيوي */
  --vibrant-accent-light: #ef4444;  /* أحمر فاتح */
  --vibrant-accent-dark: #b91c1c;   /* أحمر داكن */
  
  --vibrant-gold: #f59e0b;          /* ذهبي حيوي */
  --vibrant-gold-light: #fbbf24;    /* ذهبي فاتح */
  --vibrant-gold-dark: #d97706;     /* ذهبي داكن */
  
  /* ألوان النص الحيوية */
  --vibrant-text-primary: #111827;  /* نص أساسي داكن */
  --vibrant-text-secondary: #374151; /* نص ثانوي */
  --vibrant-text-muted: #6b7280;    /* نص خفيف */
  --vibrant-text-white: #ffffff;    /* نص أبيض */
  
  /* ألوان الخلفية الحيوية */
  --vibrant-bg-primary: #ffffff;    /* خلفية بيضاء */
  --vibrant-bg-secondary: #f9fafb;  /* خلفية رمادية فاتحة */
  --vibrant-bg-dark: #1f2937;       /* خلفية داكنة */
  --vibrant-bg-darker: #111827;     /* خلفية أكثر قتامة */
  
  /* التدرجات الحيوية */
  --vibrant-gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  --vibrant-gradient-secondary: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --vibrant-gradient-accent: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  --vibrant-gradient-gold: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  --vibrant-gradient-hero: linear-gradient(135deg, #1e40af 0%, #059669 50%, #f59e0b 100%);
  
  /* الظلال الحيوية */
  --vibrant-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --vibrant-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* الظلال الملونة */
  --vibrant-shadow-primary: 0 10px 25px -5px rgba(30, 64, 175, 0.3);
  --vibrant-shadow-secondary: 0 10px 25px -5px rgba(5, 150, 105, 0.3);
  --vibrant-shadow-accent: 0 10px 25px -5px rgba(220, 38, 38, 0.3);
  --vibrant-shadow-gold: 0 10px 25px -5px rgba(245, 158, 11, 0.3);
  
  /* الحواف المدورة */
  --vibrant-radius-sm: 0.375rem;
  --vibrant-radius-md: 0.5rem;
  --vibrant-radius-lg: 0.75rem;
  --vibrant-radius-xl: 1rem;
  --vibrant-radius-2xl: 1.5rem;
  --vibrant-radius-full: 9999px;
}

/* إعادة تعيين الألوان الباهتة */
* {
  color: inherit;
}

body {
  background: var(--vibrant-bg-primary) !important;
  color: var(--vibrant-text-primary) !important;
}

/* البطاقات الحيوية */
.vibrant-card {
  background: var(--vibrant-bg-primary);
  border-radius: var(--vibrant-radius-xl);
  padding: 2rem;
  box-shadow: var(--vibrant-shadow-lg);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vibrant-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--vibrant-shadow-2xl);
  border-color: var(--vibrant-primary-light);
}

/* الأزرار الحيوية */
.vibrant-btn-primary {
  background: var(--vibrant-gradient-primary);
  color: var(--vibrant-text-white);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-primary);
  cursor: pointer;
}

.vibrant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

.vibrant-btn-secondary {
  background: var(--vibrant-gradient-secondary);
  color: var(--vibrant-text-white);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-secondary);
  cursor: pointer;
}

.vibrant-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #047857 0%, #059669 100%);
}

.vibrant-btn-gold {
  background: var(--vibrant-gradient-gold);
  color: var(--vibrant-text-primary);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-gold);
  cursor: pointer;
}

.vibrant-btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

/* النصوص الحيوية */
.vibrant-text-primary {
  color: var(--vibrant-text-primary) !important;
}

.vibrant-text-secondary {
  color: var(--vibrant-text-secondary) !important;
}

.vibrant-text-muted {
  color: var(--vibrant-text-muted) !important;
}

.vibrant-text-white {
  color: var(--vibrant-text-white) !important;
}

/* العناوين الحيوية */
.vibrant-heading {
  background: var(--vibrant-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.vibrant-heading-secondary {
  background: var(--vibrant-gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.vibrant-heading-gold {
  background: var(--vibrant-gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

/* الأيقونات الحيوية */
.vibrant-icon-primary {
  color: var(--vibrant-primary);
}

.vibrant-icon-secondary {
  color: var(--vibrant-secondary);
}

.vibrant-icon-accent {
  color: var(--vibrant-accent);
}

.vibrant-icon-gold {
  color: var(--vibrant-gold);
}

/* خلفيات الأقسام */
.vibrant-section-primary {
  background: var(--vibrant-gradient-primary);
  color: var(--vibrant-text-white);
}

.vibrant-section-secondary {
  background: var(--vibrant-gradient-secondary);
  color: var(--vibrant-text-white);
}

.vibrant-section-light {
  background: var(--vibrant-bg-secondary);
  color: var(--vibrant-text-primary);
}

.vibrant-section-white {
  background: var(--vibrant-bg-primary);
  color: var(--vibrant-text-primary);
}

/* تأثيرات الحركة */
.vibrant-animate-float {
  animation: vibrant-float 6s ease-in-out infinite;
}

@keyframes vibrant-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.vibrant-animate-pulse {
  animation: vibrant-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes vibrant-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.vibrant-animate-bounce {
  animation: vibrant-bounce 1s infinite;
}

@keyframes vibrant-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .vibrant-card {
    padding: 1.5rem;
  }
  
  .vibrant-btn-primary,
  .vibrant-btn-secondary,
  .vibrant-btn-gold {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* إزالة الألوان الباهتة من جميع العناصر */
.text-gray-500,
.text-gray-400,
.text-gray-600 {
  color: var(--vibrant-text-secondary) !important;
}

.text-gray-300 {
  color: var(--vibrant-text-muted) !important;
}

.bg-gray-100,
.bg-gray-50 {
  background: var(--vibrant-bg-secondary) !important;
}

.bg-gray-900,
.bg-gray-800 {
  background: var(--vibrant-bg-dark) !important;
}

