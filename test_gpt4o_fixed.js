// اختبار GPT-4o بعد الإصلاح
require('dotenv').config({ path: '.env.local' });

async function testGPT4oFixed() {
  console.log('🔄 اختبار GPT-4o بعد الإصلاح...\n');
  
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY غير متوفر');
    return;
  }
  
  // أسئلة للاختبار
  const testQuestions = [
    'ما هي حقوق العامل في قانون العمل اليمني؟',
    'كيف أطلق زوجتي حسب القانون اليمني؟',
    'ما هي إجراءات رفع دعوى في المحكمة؟',
    'أحتاج استشارة قانونية',
    'مساعدة'
  ];
  
  console.log(`📋 سيتم اختبار ${testQuestions.length} أسئلة مختلفة...\n`);
  
  for (let i = 0; i < testQuestions.length; i++) {
    const question = testQuestions[i];
    
    console.log(`${'='.repeat(60)}`);
    console.log(`🧪 اختبار ${i + 1}: "${question}"`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.

قدم استشارات قانونية مفيدة ومباشرة. أجب على جميع الأسئلة بوضوح ودون تعقيد.

تخصصك يشمل:
- قانون العمل اليمني
- قانون الأحوال الشخصية  
- القانون المدني والتجاري
- الإجراءات القضائية
- أحكام الميراث

أجب بشكل مهني ومختصر (50-150 كلمة).`
            },
            {
              role: 'user',
              content: `استشارة قانونية: ${question}`
            }
          ],
          max_tokens: 300,
          temperature: 0.7
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        console.log('✅ نجح الاختبار!');
        console.log('🤖 رد GPT-4o:');
        console.log(`"${aiResponse}"`);
        
        // تحليل الرد
        const wordCount = aiResponse.split(' ').length;
        const isRepeatedResponse = aiResponse.includes('يرجى توضيح موضوع استشارتك');
        const hasLegalContent = aiResponse.includes('قانون') || aiResponse.includes('حقوق') || aiResponse.includes('إجراءات');
        
        console.log('\n📊 تحليل الرد:');
        console.log(`   • عدد الكلمات: ${wordCount}`);
        console.log(`   • رد متكرر: ${isRepeatedResponse ? '❌ نعم' : '✅ لا'}`);
        console.log(`   • يحتوي على محتوى قانوني: ${hasLegalContent ? '✅ نعم' : '❌ لا'}`);
        
        if (!isRepeatedResponse && hasLegalContent) {
          console.log('   • التقييم: ✅ ممتاز - رد متنوع ومفيد');
        } else if (isRepeatedResponse) {
          console.log('   • التقييم: ❌ مشكلة - نفس الرد المتكرر');
        } else {
          console.log('   • التقييم: ⚠️ متوسط - يحتاج تحسين');
        }
        
      } else {
        const errorData = await response.text();
        console.log('❌ فشل الاختبار');
        console.log(`📄 رمز الخطأ: ${response.status}`);
        console.log(`📝 رسالة الخطأ: ${errorData.substring(0, 200)}`);
        
        if (response.status === 429) {
          console.log('💰 تجاوز حد الاستخدام - تحقق من الرصيد');
        } else if (response.status === 401) {
          console.log('🔑 مشكلة في API Key');
        }
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
    }
    
    console.log('\n');
    
    // انتظار قصير بين الاختبارات
    if (i < testQuestions.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log(`${'='.repeat(60)}`);
  console.log('📋 ملخص الاختبار:');
  console.log('🎯 إذا كانت جميع الردود متنوعة ومفيدة:');
  console.log('   ✅ GPT-4o يعمل بشكل مثالي');
  console.log('   ✅ تم حل مشكلة الردود المتكررة');
  console.log('🎯 إذا كانت الردود متكررة:');
  console.log('   ❌ ما زالت هناك مشكلة في الـ prompt');
  console.log('   🔧 يحتاج تعديل إضافي');
  console.log(`${'='.repeat(60)}`);
}

// تشغيل الاختبار
testGPT4oFixed().catch(console.error);
