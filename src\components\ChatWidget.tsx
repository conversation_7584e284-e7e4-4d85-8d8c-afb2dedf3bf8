'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  MessageCircle, 
  Send, 
  X, 
  Minimize2, 
  Phone, 
  Mail,
  Bot,
  User,
  Loader2
} from 'lucide-react'

interface Message {
  id: string
  type: 'user' | 'bot'
  content: string
  timestamp: Date
  responseType?: string
}

interface ChatWidgetProps {
  className?: string
}

export function ChatWidget({ className = '' }: ChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`)
  const [companyInfo, setCompanyInfo] = useState<any>(null)
  const [welcomeMessage, setWelcomeMessage] = useState('')
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // تحميل رسالة الترحيب عند فتح المحادثة
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      loadWelcomeMessage()
    }
  }, [isOpen])

  // التمرير التلقائي للرسائل الجديدة
  useEffect(() => {
    scrollToBottom()
  }, [messages, isTyping])

  // تركيز على حقل الإدخال عند فتح المحادثة
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen, isMinimized])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const loadWelcomeMessage = async () => {
    try {
      const response = await fetch('/api/chat')
      const result = await response.json()

      if (result.success) {
        setWelcomeMessage(result.data.welcomeMessage)
        setCompanyInfo(result.data.companyInfo)
        
        // إضافة رسالة الترحيب
        const welcomeMsg: Message = {
          id: `welcome_${Date.now()}`,
          type: 'bot',
          content: result.data.welcomeMessage,
          timestamp: new Date(),
          responseType: 'greeting'
        }
        setMessages([welcomeMsg])
      }
    } catch (error) {
      console.error('خطأ في تحميل رسالة الترحيب:', error)
      // رسالة ترحيب افتراضية
      const defaultWelcome: Message = {
        id: `welcome_${Date.now()}`,
        type: 'bot',
        content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',
        timestamp: new Date(),
        responseType: 'greeting'
      }
      setMessages([defaultWelcome])
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)
    setIsTyping(true)

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: sessionId
        })
      })

      const result = await response.json()

      if (result.success) {
        // تأخير قصير لمحاكاة الكتابة
        setTimeout(() => {
          const botMessage: Message = {
            id: `bot_${Date.now()}`,
            type: 'bot',
            content: result.data.message,
            timestamp: new Date(),
            responseType: result.data.type
          }

          setMessages(prev => [...prev, botMessage])
          setIsTyping(false)
          
          // تحديث معلومات الشركة إذا كانت متوفرة
          if (result.data.companyInfo) {
            setCompanyInfo(result.data.companyInfo)
          }
        }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية
      } else {
        throw new Error(result.error || 'خطأ في الإرسال')
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      
      setTimeout(() => {
        const errorMessage: Message = {
          id: `error_${Date.now()}`,
          type: 'bot',
          content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',
          timestamp: new Date(),
          responseType: 'error'
        }
        setMessages(prev => [...prev, errorMessage])
        setIsTyping(false)
      }, 500)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatMessage = (content: string) => {
    // تحويل النص إلى HTML مع دعم التنسيق البسيط
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // نص عريض
      .replace(/\n/g, '<br>') // أسطر جديدة
      .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class="text-blue-600">$&</span>') // أيقونات ملونة
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  if (!isOpen) {
    return (
      <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
        <Button
          onClick={() => setIsOpen(true)}
          className="h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
          size="lg"
        >
          <MessageCircle className="h-6 w-6 text-white" />
        </Button>
      </div>
    )
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <Card className={`w-80 h-96 shadow-2xl border-0 transition-all duration-300 ${isMinimized ? 'h-14' : 'h-96'}`}>
        {/* رأس المحادثة */}
        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="h-8 w-8 bg-white/20 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium">المساعد الذكي</CardTitle>
                <p className="text-xs text-blue-100">متصل الآن</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-1 space-x-reverse">
              <Button
                onClick={() => setIsMinimized(!isMinimized)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-white hover:bg-white/20"
              >
                <Minimize2 className="h-3 w-3" />
              </Button>
              
              <Button
                onClick={() => setIsOpen(false)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-white hover:bg-white/20"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* محتوى المحادثة */}
        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-80">
            {/* منطقة الرسائل */}
            <div className="flex-1 overflow-y-auto p-3 space-y-3">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-2 space-x-reverse max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                    {/* أيقونة المرسل */}
                    <div className={`h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.type === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="h-3 w-3" />
                      ) : (
                        <Bot className="h-3 w-3" />
                      )}
                    </div>
                    
                    {/* محتوى الرسالة */}
                    <div className={`rounded-lg p-2 ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div 
                        className="text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: formatMessage(message.content) }}
                      />
                      <div className={`text-xs mt-1 ${
                        message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* مؤشر الكتابة */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center">
                      <Bot className="h-3 w-3 text-gray-600" />
                    </div>
                    <div className="bg-gray-100 rounded-lg p-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* منطقة الإدخال */}
            <div className="border-t p-3">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Input
                  ref={inputRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك هنا..."
                  disabled={isLoading}
                  className="flex-1 text-sm"
                />
                
                <Button
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {/* معلومات التواصل السريع */}
              {companyInfo && (
                <div className="flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500">
                  {companyInfo.phone && (
                    <a 
                      href={`tel:${companyInfo.phone}`}
                      className="flex items-center space-x-1 space-x-reverse hover:text-blue-600"
                    >
                      <Phone className="h-3 w-3" />
                      <span>اتصال</span>
                    </a>
                  )}
                  
                  {companyInfo.email && (
                    <a 
                      href={`mailto:${companyInfo.email}`}
                      className="flex items-center space-x-1 space-x-reverse hover:text-blue-600"
                    >
                      <Mail className="h-3 w-3" />
                      <span>إيميل</span>
                    </a>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
