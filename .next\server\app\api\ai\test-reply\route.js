(()=>{var e={};e.id=8984,e.ids=[8984],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{P:()=>c});var a=t(64939),o=t(29021),n=t.n(o),i=t(33873),p=t.n(i),u=e([a]);a=(u.then?(await u)():u)[0];let d=null;try{let e=p().join(process.cwd(),"routing.config.json"),r=n().readFileSync(e,"utf8");d=JSON.parse(r)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let r=d.routes[e],t=d.default_config;return{database:r.database,user:t.db_user,host:t.db_host,password:process.env.DB_PASSWORD||t.db_password,port:t.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),y=new a.Pool(l);async function c(e,r){let t=await y.connect();try{return await t.query(e,r)}finally{t.release()}}s()}catch(e){s(e)}})},6280:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>u,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a=t(96559),o=t(48088),n=t(37719),i=t(84956),p=e([i]);i=(p.then?(await p)():p)[0];let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/ai/test-reply/route",pathname:"/api/ai/test-reply",filename:"route",bundlePath:"app/api/ai/test-reply/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\ai\\test-reply\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:y}=c;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},84956:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{POST:()=>i});var a=t(32190),o=t(5069),n=e([o]);async function i(e){try{let{conversationId:r,messageText:t,senderType:s}=await e.json();if(console.log("\uD83E\uDD16 Test Auto-Reply triggered:",{conversationId:r,messageText:t,senderType:s}),"user"===s)return a.NextResponse.json({success:!0,shouldReply:!1,reason:"message_from_admin"});let n=`مرحباً! هذا رد تجريبي.

رسالتك: "${t}"

يمكنني مساعدتك في:
• الاستفسارات القانونية العامة
• معلومات عن خدمات المكتب
• توجيهك للمحامي المناسب

للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.`;try{await (0,o.P)(`
        INSERT INTO messages
        (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
        VALUES ($1, 'ai', 0, $2, 'text', CURRENT_TIMESTAMP)
      `,[r,n])}catch(e){console.error("❌ Error saving AI message:",e)}return a.NextResponse.json({success:!0,shouldReply:!0,response:n,model:"test-model",delay:0})}catch(e){return console.error("❌ Error in test auto-reply:",e),a.NextResponse.json({success:!1,error:"فشل في الرد التجريبي"},{status:500})}}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(6280));module.exports=s})();