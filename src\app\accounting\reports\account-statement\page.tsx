'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FileText, Search, Download, Calendar, TrendingUp, TrendingDown } from 'lucide-react'

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
}

interface Transaction {
  journal_entry_id: number
  entry_number: string
  entry_date: string
  transaction_type: string
  full_description: string
  debit_amount: number
  credit_amount: number
  running_balance: number
  currency_code: string
  currency_symbol: string
  payment_method_name: string
  reference_number: string
}

interface AccountStatement {
  account: {
    id: number
    code: string
    name: string
    type: string
    nature: string
    opening_balance: number
  }
  period: {
    date_from: string
    date_to: string
  }
  transactions: Transaction[]
  summary: {
    total_debit: number
    total_credit: number
    final_balance: number
    balance_type: string
    transactions_count: number
  }
}

export default function AccountStatementPage() {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [statement, setStatement] = useState<AccountStatement | null>(null)
  const [loading, setLoading] = useState(false)
  const [searchLoading, setSearchLoading] = useState(false)

  // معايير البحث
  const [filters, setFilters] = useState({
    account_id: '',
    date_from: '',
    date_to: new Date().toISOString().split('T')[0]
  })

  useEffect(() => {
    fetchAccounts()
  }, [])

  const fetchAccounts = async () => {
    try {
      const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      if (response.ok) {
        const data = await response.json()
        setAccounts(data.accounts || [])
      }
    } catch (error) {
      console.error('خطأ في جلب الحسابات:', error)
    }
  }

  const handleSearch = async () => {
    if (!filters.account_id) {
      alert('يرجى اختيار الحساب')
      return
    }

    try {
      setSearchLoading(true)
      const params = new URLSearchParams()
      params.append('account_id', filters.account_id)
      if (filters.date_from) params.append('date_from', filters.date_from)
      if (filters.date_to) params.append('date_to', filters.date_to)

      const response = await fetch(`/api/accounting/reports/account-statement?${params}`)
      if (response.ok) {
        const data = await response.json()
        setStatement(data.data)
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في جلب كشف الحساب:', error)
      alert('حدث خطأ أثناء جلب كشف الحساب')
    } finally {
      setSearchLoading(false)
    }
  }

  const formatCurrency = (amount: number | null | undefined, symbol: string = 'ر.ي') => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return `0 ${symbol}`
    }
    return `${amount.toLocaleString()} ${symbol}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const getBalanceColor = (balance: number | null | undefined, nature: string) => {
    if (balance === null || balance === undefined || balance === 0) return 'text-gray-600'

    if (nature === 'مدين') {
      return balance >= 0 ? 'text-green-600' : 'text-red-600'
    } else {
      return balance >= 0 ? 'text-green-600' : 'text-red-600'
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <FileText className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">كشف حساب</h1>
            <p className="text-gray-600">عرض تفصيلي لحركة الحساب خلال فترة محددة</p>
          </div>
        </div>

        {/* معايير البحث */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Search className="h-5 w-5" />
              <span>معايير البحث</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="account_id">الحساب</Label>
                <Select value={filters.account_id} onValueChange={(value) => setFilters({...filters, account_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحساب" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id.toString()}>
                        {account.account_code} - {account.account_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="date_from">من تاريخ</Label>
                <Input
                  id="date_from"
                  type="date"
                  value={filters.date_from}
                  onChange={(e) => setFilters({...filters, date_from: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="date_to">إلى تاريخ</Label>
                <Input
                  id="date_to"
                  type="date"
                  value={filters.date_to}
                  onChange={(e) => setFilters({...filters, date_to: e.target.value})}
                />
              </div>

              <div className="flex items-end">
                <Button onClick={handleSearch} disabled={searchLoading} className="w-full">
                  {searchLoading ? 'جاري البحث...' : 'عرض الكشف'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* نتائج كشف الحساب */}
        {statement && (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">
                    كشف حساب: {statement.account.code} - {statement.account.name}
                  </CardTitle>
                  <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mt-2">
                    <span>نوع الحساب: {statement.account.type}</span>
                    <span>طبيعة الحساب: {statement.account.nature}</span>
                    {statement.period.date_from && (
                      <span>الفترة: {formatDate(statement.period.date_from)} - {formatDate(statement.period.date_to)}</span>
                    )}
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 ml-2" />
                  تصدير PDF
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* الإحصائيات */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-600">إجمالي المدين</span>
                  </div>
                  <p className="text-xl font-bold text-blue-900 mt-1">
                    {formatCurrency(statement.summary.total_debit)}
                  </p>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <TrendingDown className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-green-600">إجمالي الدائن</span>
                  </div>
                  <p className="text-xl font-bold text-green-900 mt-1">
                    {formatCurrency(statement.summary.total_credit)}
                  </p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Calendar className="h-5 w-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-600">الرصيد النهائي</span>
                  </div>
                  <p className={`text-xl font-bold mt-1 ${getBalanceColor(statement.summary.final_balance, statement.account.nature)}`}>
                    {formatCurrency(statement.summary.final_balance)} {statement.summary.balance_type}
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <FileText className="h-5 w-5 text-gray-600" />
                    <span className="text-sm font-medium text-gray-600">عدد المعاملات</span>
                  </div>
                  <p className="text-xl font-bold text-gray-900 mt-1">
                    {statement.summary.transactions_count}
                  </p>
                </div>
              </div>

              {/* جدول المعاملات */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">التاريخ</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">المدين</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">الدائن</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">العملة</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">طريقة الدفع</th>
                      <th className="border border-gray-300 px-6 py-2 text-right font-medium w-1/3">البيان</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">الرصيد</th>
                    </tr>
                  </thead>
                  <tbody>
                    {statement.transactions.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                          لا توجد معاملات في الفترة المحددة
                        </td>
                      </tr>
                    ) : (
                      statement.transactions.map((transaction, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {formatDate(transaction.entry_date)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm text-right">
                            {transaction.debit_amount > 0 ? formatCurrency(transaction.debit_amount, transaction.currency_symbol) : '-'}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm text-right">
                            {transaction.credit_amount > 0 ? formatCurrency(transaction.credit_amount, transaction.currency_symbol) : '-'}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {transaction.currency_code}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {transaction.payment_method_name || '-'}
                          </td>
                          <td className="border border-gray-300 px-6 py-2 text-sm">
                            <div>
                              <Badge variant="outline" className="mb-1">
                                {transaction.entry_number}
                              </Badge>
                              <p className="text-gray-900">{transaction.full_description}</p>
                              {transaction.reference_number && (
                                <p className="text-xs text-gray-500 mt-1">مرجع: {transaction.reference_number}</p>
                              )}
                            </div>
                          </td>
                          <td className={`border border-gray-300 px-4 py-2 text-sm text-right font-medium ${getBalanceColor(transaction.running_balance, statement.account.nature)}`}>
                            {formatCurrency(Math.abs(transaction.running_balance), transaction.currency_symbol)}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
