# 🚀 التشغيل السريع - النظام القانوني

## ⚡ التشغيل في 3 خطوات

### الخطوة 1: تثبيت Node.js
- تحميل من: https://nodejs.org/
- اختر النسخة LTS (الموصى بها)
- تثبيت مع الإعدادات الافتراضية

### الخطوة 2: تشغيل النظام
```cmd
# انقر نقراً مزدوجاً على أحد هذه الملفات:
start-production-fixed.bat    # للإنتاج (موصى به)
start-with-npx.bat           # بديل سريع
start-development.bat        # للتطوير
```

### الخطوة 3: فتح النظام
- انتظر حتى تظهر رسالة "Ready on http://localhost:3000"
- افتح المتصفح واذهب إلى: http://localhost:3000/home

## 🔧 إذا ظهرت مشكلة "next is not recognized"

### الحل السريع:
```cmd
# افتح Command Prompt في مجلد النظام واكتب:
npm install
npx next build
npx next start -p 3000
```

## 📋 إعداد قاعدة البيانات

### بعد تشغيل النظام:
```cmd
# في Command Prompt جديد:
node setup-production-db.js
node update-production-db.js
node create-admin-user.js
```

## 🌐 الوصول للنظام

### الصفحات الرئيسية:
- **الصفحة الرئيسية**: http://localhost:3000/home
- **تسجيل الدخول**: http://localhost:3000/login
- **لوحة التحكم**: http://localhost:3000/dashboard

### بيانات الدخول الافتراضية:
- **المستخدم**: admin
- **كلمة المرور**: (التي تم إنشاؤها في create-admin-user.js)

## ❓ مساعدة إضافية

راجع ملف `TROUBLESHOOTING_GUIDE.md` للحلول التفصيلية.

---
**ملاحظة**: تأكد من تشغيل PostgreSQL قبل بدء النظام.
