'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Upload,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  FolderOpen,
  Calendar,
  User,
  Tag,
  Plus,
  Share2,
  Archive,
  Image,
  File,
  Video,
  Music,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Star,
  Lock,
  Unlock,
  X
} from 'lucide-react'

interface Document {
  id: number
  title: string
  description: string
  file_name: string
  file_path: string
  file_size: number
  file_type: string
  category: string
  subcategory: string
  tags: string[]
  client_name: string
  case_title: string
  case_number: string
  uploaded_by_name: string
  created_date: string
  access_level: string
  is_confidential: boolean
}

interface DocumentStats {
  totalCount: number
  categories: { category: string; count: number }[]
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [stats, setStats] = useState<DocumentStats>({ totalCount: 0, categories: [] })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showUploadModal, setShowUploadModal] = useState(false)

  // جلب الوثائق
  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (selectedCategory) params.append('category', selectedCategory)

      const response = await fetch(`/api/documents?${params}`)
      const data = await response.json()

      if (data.success) {
        setDocuments(data.data.documents)
        setStats({
          totalCount: data.data.totalCount,
          categories: data.data.categories
        })
      }
    } catch (error) {
      console.error('خطأ في جلب الوثائق:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDocuments()
  }, [currentPage, searchTerm, selectedCategory])

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // الحصول على لون الفئة
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'contract': 'bg-blue-100 text-blue-800',
      'evidence': 'bg-green-100 text-green-800',
      'correspondence': 'bg-yellow-100 text-yellow-800',
      'template': 'bg-purple-100 text-purple-800',
      'guide': 'bg-gray-100 text-gray-800',
      'general': 'bg-orange-100 text-orange-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على أيقونة نوع الملف
  const getFileIcon = (fileType: string) => {
    if (fileType?.includes('pdf')) return '📄'
    if (fileType?.includes('word')) return '📝'
    if (fileType?.includes('image')) return '🖼️'
    if (fileType?.includes('excel')) return '📊'
    return '📁'
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الوثائق</h1>
            <p className="text-gray-600 mt-1">
              إجمالي الوثائق: {stats.totalCount}
            </p>
          </div>
          <Button 
            onClick={() => setShowUploadModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Upload className="h-4 w-4 mr-2" />
            رفع وثيقة جديدة
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.totalCount}</div>
                  <div className="text-sm text-gray-600">إجمالي الوثائق</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {stats.categories.slice(0, 3).map((cat, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <FolderOpen className="h-8 w-8 text-green-600" />
                  <div className="mr-4">
                    <div className="text-2xl font-bold">{cat.count}</div>
                    <div className="text-sm text-gray-600">{cat.category}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الوثائق..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="md:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع الفئات</option>
                  {stats.categories.map((cat) => (
                    <option key={cat.category} value={cat.category}>
                      {cat.category} ({cat.count})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الوثائق */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              الوثائق
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : documents.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد وثائق</p>
              </div>
            ) : (
              <div className="space-y-4">
                {documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <span className="text-2xl">{getFileIcon(doc.file_type)}</span>
                          <div>
                            <h3 className="font-semibold text-lg">{doc.title}</h3>
                            <p className="text-sm text-gray-600">{doc.file_name}</p>
                          </div>
                        </div>

                        {doc.description && (
                          <p className="text-gray-700 mb-3">{doc.description}</p>
                        )}

                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge className={getCategoryColor(doc.category)}>
                            {doc.category}
                          </Badge>
                          {doc.subcategory && (
                            <Badge variant="outline">{doc.subcategory}</Badge>
                          )}
                          {doc.is_confidential && (
                            <Badge className="bg-red-100 text-red-800">سري</Badge>
                          )}
                          {doc.tags?.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                          {doc.case_number && (
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 mr-1" />
                              {doc.case_number} - {doc.case_title}
                            </div>
                          )}
                          {doc.client_name && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              {doc.client_name}
                            </div>
                          )}
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(doc.created_date)}
                          </div>
                          <div>
                            الحجم: {formatFileSize(doc.file_size)}
                          </div>
                          <div>
                            رفع بواسطة: {doc.uploaded_by_name}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة رفع الوثائق */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">رفع وثيقة جديدة</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">عنوان الوثيقة</label>
                  <Input placeholder="أدخل عنوان الوثيقة" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">الوصف</label>
                  <textarea 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="وصف الوثيقة"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">اختر الفئة</option>
                    <option value="contract">عقد</option>
                    <option value="evidence">دليل</option>
                    <option value="correspondence">مراسلات</option>
                    <option value="template">نموذج</option>
                    <option value="guide">دليل</option>
                    <option value="general">عام</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">الملف</label>
                  <input 
                    type="file" 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls"
                  />
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                  <Upload className="h-4 w-4 mr-2" />
                  رفع الوثيقة
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowUploadModal(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}