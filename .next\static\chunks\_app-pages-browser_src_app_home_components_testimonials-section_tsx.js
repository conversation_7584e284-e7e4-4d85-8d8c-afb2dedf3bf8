"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_app_home_components_testimonials-section_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Star)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n            key: \"r04s7s\"\n        }\n    ]\n];\nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"star\", __iconNode);\n //# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx":
/*!**********************************************************!*\
  !*** ./src/app/home/<USER>/testimonials-section.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestimonialsSection: () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ TestimonialsSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestimonialsSection() {\n    _s();\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const testimonials = [\n        {\n            id: 1,\n            name: 'أحمد السيد',\n            role: 'مدير عام',\n            company: 'شركة التقنية المتطورة',\n            avatar: '',\n            content: 'أشكر فريق العمل على الاحترافية العالية في التعامل مع قضيتنا. لقد قدموا لنا استشارات قانونية متميزة ساعدتنا في تجنب العديد من المشاكل القانونية.',\n            rating: 5,\n            date: '15 مارس 2023'\n        },\n        {\n            id: 2,\n            name: 'سارة محمد',\n            role: 'رئيسة قسم الشؤون القانونية',\n            company: 'مجموعة الأعمال المتحدة',\n            avatar: '',\n            content: 'تعاملنا مع المكتب في عدة قضايا معقدة وكان أداؤهم ممتازاً. ننصح بهم بشدة لكل من يبحث عن استشارات قانونية احترافية.',\n            rating: 5,\n            date: '2 أبريل 2023'\n        },\n        {\n            id: 3,\n            name: 'خالد عبدالله',\n            role: 'مالك',\n            company: 'مطاعم الذواقة',\n            avatar: '',\n            content: 'المحامي محمد من أفضل من تعاملت معهم في مجال المحاماة. يمتلك خبرة واسعة وأسلوباً مهنياً راقياً في التعامل مع القضايا.',\n            rating: 4,\n            date: '22 أبريل 2023'\n        },\n        {\n            id: 4,\n            name: 'نورة سليمان',\n            role: 'مديرة الموارد البشرية',\n            company: 'شركة المستقبل',\n            avatar: '',\n            content: 'فريق عمل محترف ويتمتع بأعلى معايير النزاهة والكفاءة. ساعدونا في حل نزاع عمل معقد بكل كفاءة واحترافية.',\n            rating: 5,\n            date: '5 مايو 2023'\n        },\n        {\n            id: 5,\n            name: 'عمر أحمد',\n            role: 'مدير مالي',\n            company: 'شركة الاستثمارات العقارية',\n            avatar: '',\n            content: 'نحن نتعامل مع المكتب منذ أكثر من 3 سنوات في جميع استشاراتنا القانونية. نثق بهم تماماً وننصح الجميع بالتعامل معهم.',\n            rating: 5,\n            date: '18 مايو 2023'\n        }\n    ];\n    const visibleTestimonials = testimonials.slice(0, 3); // Show 3 testimonials at a time\n    const goToPrev = ()=>{\n        setActiveIndex((prev)=>prev === 0 ? testimonials.length - 1 : prev - 1);\n        resetAutoPlay();\n    };\n    const goToNext = ()=>{\n        setActiveIndex((prev)=>prev === testimonials.length - 1 ? 0 : prev + 1);\n        resetAutoPlay();\n    };\n    const goToSlide = (index)=>{\n        setActiveIndex(index);\n        resetAutoPlay();\n    };\n    const resetAutoPlay = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        intervalRef.current = setInterval(goToNext, 8000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            intervalRef.current = setInterval(goToNext, 8000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>{\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                }\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        activeIndex\n    ]);\n    const renderStars = (rating)=>{\n        return Array(5).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 \".concat(i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300')\n            }, i, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #171717 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-sm font-semibold px-6 py-3 rounded-full mb-6 border\",\n                            style: {\n                                background: 'linear-gradient(to right, rgba(204, 169, 103, 0.2), rgba(204, 169, 103, 0.1))',\n                                color: '#cca967',\n                                borderColor: 'rgba(204, 169, 103, 0.3)'\n                            },\n                            children: \"آراء العملاء\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"ماذا يقول عملاؤنا\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 rounded-full mx-auto my-8\",\n                            style: {\n                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 max-w-2xl mx-auto\",\n                            children: \"آراء وتقييمات عملائنا الكرام الذين استفادوا من خدماتنا القانونية المتميزة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center absolute left-0 right-0 top-1/2 -translate-y-1/2 z-10 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                    },\n                                    onClick: goToPrev,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"السابق\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                    },\n                                    onClick: goToNext,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"التالي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: sliderRef,\n                            className: \"relative overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex transition-transform duration-500 ease-in-out\",\n                                style: {\n                                    transform: \"translateX(-\".concat(activeIndex * (100 / 3), \"%)\"),\n                                    width: \"\".concat(testimonials.length * (100 / 3), \"%\")\n                                },\n                                children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/3 px-4 transition-all duration-300 \".concat(activeIndex === index ? 'scale-105' : 'scale-95 opacity-70'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full p-8 rounded-xl border relative\",\n                                            style: {\n                                                background: 'rgba(34, 34, 34, 0.3)',\n                                                borderColor: 'rgba(204, 169, 103, 0.1)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-6 text-6xl font-serif\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    },\n                                                    children: '\"'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg text-white font-medium leading-relaxed mb-6 pr-8 italic\",\n                                                    children: testimonial.content\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 pr-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full flex items-center justify-center text-gray-900 font-bold\",\n                                                            style: {\n                                                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                                            },\n                                                            children: testimonial.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-semibold\",\n                                                                    children: testimonial.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: [\n                                                                        testimonial.role,\n                                                                        \" - \",\n                                                                        testimonial.company\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mt-6 pr-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: renderStars(testimonial.rating)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: testimonial.date\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, testimonial.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 space-x-2 space-x-reverse\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>goToSlide(index),\n                                    className: \"w-3 h-3 rounded-full transition-colors \".concat(index === activeIndex ? 'bg-blue-600 w-8' : 'bg-gray-300'),\n                                    \"aria-label\": \"Go to slide \".concat(index + 1)\n                                }, index, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialsSection, \"+LEI9L4Juiu68Qv83pT4gyAh+LY=\");\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\n"));

/***/ })

}]);