"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_app_home_components_testimonials-section_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Star)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n            key: \"r04s7s\"\n        }\n    ]\n];\nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"star\", __iconNode);\n //# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx":
/*!**********************************************************!*\
  !*** ./src/app/home/<USER>/testimonials-section.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestimonialsSection: () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ TestimonialsSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestimonialsSection() {\n    _s();\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const testimonials = [\n        {\n            id: 1,\n            name: 'أحمد السيد',\n            role: 'مدير عام',\n            company: 'شركة التقنية المتطورة',\n            avatar: '',\n            content: 'أشكر فريق العمل على الاحترافية العالية في التعامل مع قضيتنا. لقد قدموا لنا استشارات قانونية متميزة ساعدتنا في تجنب العديد من المشاكل القانونية.',\n            rating: 5,\n            date: '15 مارس 2023'\n        },\n        {\n            id: 2,\n            name: 'سارة محمد',\n            role: 'رئيسة قسم الشؤون القانونية',\n            company: 'مجموعة الأعمال المتحدة',\n            avatar: '',\n            content: 'تعاملنا مع المكتب في عدة قضايا معقدة وكان أداؤهم ممتازاً. ننصح بهم بشدة لكل من يبحث عن استشارات قانونية احترافية.',\n            rating: 5,\n            date: '2 أبريل 2023'\n        },\n        {\n            id: 3,\n            name: 'خالد عبدالله',\n            role: 'مالك',\n            company: 'مطاعم الذواقة',\n            avatar: '',\n            content: 'المحامي محمد من أفضل من تعاملت معهم في مجال المحاماة. يمتلك خبرة واسعة وأسلوباً مهنياً راقياً في التعامل مع القضايا.',\n            rating: 4,\n            date: '22 أبريل 2023'\n        },\n        {\n            id: 4,\n            name: 'نورة سليمان',\n            role: 'مديرة الموارد البشرية',\n            company: 'شركة المستقبل',\n            avatar: '',\n            content: 'فريق عمل محترف ويتمتع بأعلى معايير النزاهة والكفاءة. ساعدونا في حل نزاع عمل معقد بكل كفاءة واحترافية.',\n            rating: 5,\n            date: '5 مايو 2023'\n        },\n        {\n            id: 5,\n            name: 'عمر أحمد',\n            role: 'مدير مالي',\n            company: 'شركة الاستثمارات العقارية',\n            avatar: '',\n            content: 'نحن نتعامل مع المكتب منذ أكثر من 3 سنوات في جميع استشاراتنا القانونية. نثق بهم تماماً وننصح الجميع بالتعامل معهم.',\n            rating: 5,\n            date: '18 مايو 2023'\n        }\n    ];\n    const visibleTestimonials = testimonials.slice(0, 3); // Show 3 testimonials at a time\n    const goToPrev = ()=>{\n        setActiveIndex((prev)=>prev === 0 ? testimonials.length - 1 : prev - 1);\n        resetAutoPlay();\n    };\n    const goToNext = ()=>{\n        setActiveIndex((prev)=>prev === testimonials.length - 1 ? 0 : prev + 1);\n        resetAutoPlay();\n    };\n    const goToSlide = (index)=>{\n        setActiveIndex(index);\n        resetAutoPlay();\n    };\n    const resetAutoPlay = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        intervalRef.current = setInterval(goToNext, 8000);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            intervalRef.current = setInterval(goToNext, 8000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>{\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                }\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        activeIndex\n    ]);\n    const renderStars = (rating)=>{\n        return Array(5).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 \".concat(i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300')\n            }, i, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #171717 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block text-sm font-semibold px-6 py-3 rounded-full mb-6 border\",\n                            style: {\n                                background: 'linear-gradient(to right, rgba(204, 169, 103, 0.2), rgba(204, 169, 103, 0.1))',\n                                color: '#cca967',\n                                borderColor: 'rgba(204, 169, 103, 0.3)'\n                            },\n                            children: \"آراء العملاء\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"ماذا يقول عملاؤنا\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 rounded-full mx-auto my-8\",\n                            style: {\n                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 max-w-2xl mx-auto\",\n                            children: \"آراء وتقييمات عملائنا الكرام الذين استفادوا من خدماتنا القانونية المتميزة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center absolute left-0 right-0 top-1/2 -translate-y-1/2 z-10 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                    },\n                                    onClick: goToPrev,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"السابق\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                    },\n                                    onClick: goToNext,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"التالي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: sliderRef,\n                            className: \"relative overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex transition-transform duration-500 ease-in-out\",\n                                style: {\n                                    transform: \"translateX(-\".concat(activeIndex * (100 / 3), \"%)\"),\n                                    width: \"\".concat(testimonials.length * (100 / 3), \"%\")\n                                },\n                                children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/3 px-4 transition-all duration-300 \".concat(activeIndex === index ? 'scale-105' : 'scale-95 opacity-70'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full vibrant-card relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-6 text-6xl font-serif vibrant-icon-primary\",\n                                                    children: '\"'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg vibrant-text-primary font-medium leading-relaxed mb-6 pr-8 italic\",\n                                                    children: testimonial.content\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 pr-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full flex items-center justify-center text-gray-900 font-bold\",\n                                                            style: {\n                                                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                                            },\n                                                            children: testimonial.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-semibold\",\n                                                                    children: testimonial.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: [\n                                                                        testimonial.role,\n                                                                        \" - \",\n                                                                        testimonial.company\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mt-6 pr-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: renderStars(testimonial.rating)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: testimonial.date\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, testimonial.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 space-x-2 space-x-reverse\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>goToSlide(index),\n                                    className: \"w-3 h-3 rounded-full transition-colors \".concat(index === activeIndex ? 'bg-blue-600 w-8' : 'bg-gray-300'),\n                                    \"aria-label\": \"Go to slide \".concat(index + 1)\n                                }, index, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\testimonials-section.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialsSection, \"+LEI9L4Juiu68Qv83pT4gyAh+LY=\");\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\n"));

/***/ })

}]);