// إصلاح نظام العملة بطريقة مبسطة
const { Pool } = require('pg');

async function fixCurrencySimple() {
  console.log('🔧 إصلاح نظام العملة بطريقة مبسطة...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. إضافة عمود amount_yer عادي (غير محسوب)
      console.log('\n   💱 إضافة عمود amount_yer:');
      
      const amountYerExists = await pool.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'amount_yer'
      `);

      if (amountYerExists.rows.length === 0) {
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN amount_yer DECIMAL(15,2) DEFAULT 0
        `);
        console.log('      ✅ تم إضافة عمود amount_yer');
      } else {
        console.log('      ✅ عمود amount_yer موجود');
      }

      // 2. إنشاء trigger لتحديث amount_yer
      console.log('\n   🔄 إنشاء trigger لتحديث amount_yer:');
      
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_amount_yer()
        RETURNS TRIGGER AS $$
        DECLARE
          exchange_rate DECIMAL;
        BEGIN
          -- إذا كانت العملة هي الريال اليمني (ID = 1)
          IF NEW.currency_id = 1 OR NEW.currency_id IS NULL THEN
            NEW.amount_yer := NEW.case_amount;
          ELSE
            -- الحصول على سعر الصرف
            SELECT c.exchange_rate INTO exchange_rate
            FROM currencies c
            WHERE c.id = NEW.currency_id AND c.is_active = TRUE;
            
            IF exchange_rate IS NOT NULL THEN
              NEW.amount_yer := NEW.case_amount * exchange_rate;
            ELSE
              NEW.amount_yer := NEW.case_amount;
            END IF;
          END IF;
          
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_update_amount_yer ON issues;
        CREATE TRIGGER trigger_update_amount_yer
          BEFORE INSERT OR UPDATE OF case_amount, currency_id ON issues
          FOR EACH ROW
          EXECUTE FUNCTION update_amount_yer();
      `);
      console.log('      ✅ تم إنشاء trigger لتحديث amount_yer');

      // 3. تحديث جميع القضايا الموجودة
      console.log('\n   🔄 تحديث جميع القضايا الموجودة:');
      
      const updateResult = await pool.query(`
        UPDATE issues 
        SET currency_id = COALESCE(currency_id, 1),
            amount_yer = CASE 
              WHEN COALESCE(currency_id, 1) = 1 THEN case_amount
              ELSE case_amount * COALESCE((SELECT exchange_rate FROM currencies WHERE id = COALESCE(currency_id, 1)), 1)
            END
        RETURNING case_number, case_amount, currency_id, amount_yer
      `);
      
      console.log(`      ✅ تم تحديث ${updateResult.rows.length} قضية`);
      
      // عرض عينة من النتائج
      if (updateResult.rows.length > 0) {
        console.log('      📋 عينة من النتائج:');
        updateResult.rows.slice(0, 3).forEach(row => {
          console.log(`         ${row.case_number}: ${row.case_amount} -> ${row.amount_yer} ر.ي`);
        });
      }

      // 4. فحص النتائج النهائية
      console.log('\n   📊 فحص النتائج النهائية:');
      
      const finalCheck = await pool.query(`
        SELECT 
          i.case_number,
          i.case_amount,
          i.currency_id,
          c.currency_code,
          c.symbol,
          i.amount_yer,
          i.status
        FROM issues i
        LEFT JOIN currencies c ON i.currency_id = c.id
        ORDER BY i.case_number
      `);

      console.log('      📋 جميع القضايا:');
      finalCheck.rows.forEach(row => {
        console.log(`         ${row.case_number}:`);
        console.log(`            - المبلغ: ${row.case_amount} ${row.symbol || 'ر.ي'}`);
        console.log(`            - بالريال: ${row.amount_yer} ر.ي`);
        console.log(`            - الحالة: ${row.status}`);
      });

      // 5. إحصائيات
      console.log('\n   📈 إحصائيات:');
      
      const stats = await pool.query(`
        SELECT 
          COUNT(*) as total_issues,
          COUNT(CASE WHEN currency_id = 1 THEN 1 END) as yer_issues,
          COUNT(CASE WHEN currency_id != 1 THEN 1 END) as foreign_issues,
          SUM(amount_yer) as total_amount_yer
        FROM issues
      `);

      const statistics = stats.rows[0];
      console.log(`      - إجمالي القضايا: ${statistics.total_issues}`);
      console.log(`      - قضايا بالريال: ${statistics.yer_issues}`);
      console.log(`      - قضايا بعملات أخرى: ${statistics.foreign_issues}`);
      console.log(`      - إجمالي المبالغ بالريال: ${statistics.total_amount_yer} ر.ي`);

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(40) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح نظام العملة');
}

// تشغيل الإصلاح
fixCurrencySimple().catch(console.error);
