import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

// مسار مجلد القوانين
const LAWS_DIR = path.join(process.cwd(), 'laws')

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('id')
    const fileName = searchParams.get('file')

    let filePath: string

    if (fileName) {
      // التحميل بالاسم
      filePath = path.join(LAWS_DIR, fileName)
    } else if (fileId) {
      // التحميل بالمعرف (فك التشفير)
      filePath = Buffer.from(fileId, 'base64').toString('utf-8')
    } else {
      return NextResponse.json(
        { success: false, error: 'معرف الملف أو اسم الملف مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود الملف
    try {
      await fs.access(filePath)
    } catch {
      return NextResponse.json(
        { success: false, error: 'الملف غير موجود' },
        { status: 404 }
      )
    }

    // قراءة الملف
    const fileBuffer = await fs.readFile(filePath)
    const fileName = path.basename(filePath)
    const fileExtension = path.extname(filePath)

    // تحديد نوع المحتوى
    let contentType = 'application/octet-stream'
    const ext = fileExtension.toLowerCase()

    switch (ext) {
      case '.txt':
        contentType = 'text/plain; charset=utf-8'
        break
      case '.pdf':
        contentType = 'application/pdf'
        break
      case '.doc':
        contentType = 'application/msword'
        break
      case '.docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        break
    }

    // إرجاع الملف
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(fileName)}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    })
  } catch (error) {
    console.error('Error downloading file:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحميل الملف' },
      { status: 500 }
    )
  }
}