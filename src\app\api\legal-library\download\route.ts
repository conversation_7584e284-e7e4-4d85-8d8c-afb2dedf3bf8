import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('id')

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'معرف الملف مطلوب' },
        { status: 400 }
      )
    }
    
    // فك تشفير مسار الملف
    const filePath = Buffer.from(fileId, 'base64').toString('utf-8')
    
    // التحقق من وجود الملف
    try {
      await fs.access(filePath)
    } catch {
      return NextResponse.json(
        { success: false, error: 'الملف غير موجود' },
        { status: 404 }
      )
    }
    
    // قراءة الملف
    const fileBuffer = await fs.readFile(filePath)
    const fileName = path.basename(filePath)
    const fileExtension = path.extname(filePath)
    
    // تحديد نوع المحتوى
    let contentType = 'application/octet-stream'
    if (fileExtension === '.pdf') {
      contentType = 'application/pdf'
    } else if (fileExtension === '.txt') {
      contentType = 'text/plain; charset=utf-8'
    }
    
    // إرجاع الملف
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(fileName)}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    })
  } catch (error) {
    console.error('Error downloading file:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحميل الملف' },
      { status: 500 }
    )
  }
}