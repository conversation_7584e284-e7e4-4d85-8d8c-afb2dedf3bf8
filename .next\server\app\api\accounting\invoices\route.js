(()=>{var e={};e.id=9580,e.ids=[9580],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>p});var n=r(64939),a=r(29021),o=r.n(a),i=r(33873),c=r.n(i),u=e([n]);n=(u.then?(await u)():u)[0];let d=null;try{let e=c().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new n.Pool(l);async function p(e,t){let r=await E.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53896:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n=r(96559),a=r(48088),o=r(37719),i=r(57712),c=e([i]);i=(c.then?(await c)():c)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/invoices/route",pathname:"/api/accounting/invoices",filename:"route",bundlePath:"app/api/accounting/invoices/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\invoices\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:E}=p;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},57712:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>p,GET:()=>i,POST:()=>c,PUT:()=>u});var n=r(32190),a=r(5069),o=e([a]);async function i(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),o=(r-1)*s,i=await (0,a.P)(`
      SELECT 
        ar.*,
        c.name as client_name,
        e.name as entity_name
      FROM ar
      LEFT JOIN entity e ON ar.entity_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      ORDER BY ar.transdate DESC, ar.id DESC
      LIMIT $1 OFFSET $2
    `,[s,o]),c=await (0,a.P)("SELECT COUNT(*) as total FROM ar"),u=parseInt(c.rows[0].total);return n.NextResponse.json({success:!0,data:i.rows,pagination:{page:r,limit:s,total:u,pages:Math.ceil(u/s)}})}catch(e){return console.error("Error fetching invoices:",e),n.NextResponse.json({success:!1,error:"فشل في جلب الفواتير"},{status:500})}}async function c(e){try{let{invnumber:t,transdate:r,entity_id:s,taxincluded:o=!1,amount:i,netamount:c,duedate:u,invoice:p=!0,notes:d,curr:l="SAR",employee_id:E,department_id:w,items:R=[]}=await e.json();if(!t||!r||!i)return n.NextResponse.json({success:!1,error:"رقم الفاتورة والتاريخ والمبلغ مطلوبة"},{status:400});if((await (0,a.P)("SELECT id FROM ar WHERE invnumber = $1",[t])).rows.length>0)return n.NextResponse.json({success:!1,error:"رقم الفاتورة موجود مسبقاً"},{status:400});await (0,a.P)("BEGIN");try{let e=await (0,a.P)(`
        INSERT INTO ar (
          invnumber, transdate, entity_id, taxincluded, amount, 
          netamount, duedate, invoice, notes, curr, employee_id, 
          department_id, approved
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *
      `,[t,r,s,o,i,c||i,u,p,d,l,E,w,!1]),$=e.rows[0].id;for(let e of R)e.description&&e.qty&&e.sellprice&&await (0,a.P)(`
            INSERT INTO invoice (
              trans_id, parts_id, description, qty, sellprice, 
              fxsellprice, discount, unit, notes
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `,[$,e.parts_id,e.description,e.qty,e.sellprice,e.sellprice,e.discount||0,e.unit,e.notes]);return await (0,a.P)("COMMIT"),n.NextResponse.json({success:!0,message:"تم إنشاء الفاتورة بنجاح",data:e.rows[0]})}catch(e){throw await (0,a.P)("ROLLBACK"),e}}catch(e){return console.error("Error creating invoice:",e),n.NextResponse.json({success:!1,error:"فشل في إنشاء الفاتورة"},{status:500})}}async function u(e){try{let{id:t,invnumber:r,transdate:s,entity_id:o,taxincluded:i,amount:c,netamount:u,paid:p,datepaid:d,duedate:l,notes:E,approved:w,on_hold:R}=await e.json();if(!t)return n.NextResponse.json({success:!1,error:"معرف الفاتورة مطلوب"},{status:400});let $=await (0,a.P)(`
      UPDATE ar 
      SET 
        invnumber = COALESCE($2, invnumber),
        transdate = COALESCE($3, transdate),
        entity_id = COALESCE($4, entity_id),
        taxincluded = COALESCE($5, taxincluded),
        amount = COALESCE($6, amount),
        netamount = COALESCE($7, netamount),
        paid = COALESCE($8, paid),
        datepaid = $9,
        duedate = COALESCE($10, duedate),
        notes = COALESCE($11, notes),
        approved = COALESCE($12, approved),
        on_hold = COALESCE($13, on_hold)
      WHERE id = $1
      RETURNING *
    `,[t,r,s,o,i,c,u,p,d,l,E,w,R]);if(0===$.rows.length)return n.NextResponse.json({success:!1,error:"الفاتورة غير موجودة"},{status:404});return n.NextResponse.json({success:!0,message:"تم تحديث الفاتورة بنجاح",data:$.rows[0]})}catch(e){return console.error("Error updating invoice:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث الفاتورة"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return n.NextResponse.json({success:!1,error:"معرف الفاتورة مطلوب"},{status:400});let s=await (0,a.P)("SELECT approved, paid, amount FROM ar WHERE id = $1",[r]);if(0===s.rows.length)return n.NextResponse.json({success:!1,error:"الفاتورة غير موجودة"},{status:404});let o=s.rows[0];if(o.approved)return n.NextResponse.json({success:!1,error:"لا يمكن حذف فاتورة معتمدة"},{status:400});if(o.paid>0)return n.NextResponse.json({success:!1,error:"لا يمكن حذف فاتورة مدفوعة جزئياً أو كلياً"},{status:400});await (0,a.P)("BEGIN");try{return await (0,a.P)("DELETE FROM invoice WHERE trans_id = $1",[r]),await (0,a.P)("DELETE FROM ar WHERE id = $1",[r]),await (0,a.P)("COMMIT"),n.NextResponse.json({success:!0,message:"تم حذف الفاتورة بنجاح"})}catch(e){throw await (0,a.P)("ROLLBACK"),e}}catch(e){return console.error("Error deleting invoice:",e),n.NextResponse.json({success:!1,error:"فشل في حذف الفاتورة"},{status:500})}}a=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(53896));module.exports=s})();