"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                console.log('🔄 تحميل رسالة الترحيب...');\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            console.log('📡 جاري الاتصال بـ /api/chat...');\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            console.log('📨 استجابة API:', result);\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                console.log('✅ تم إضافة رسالة الترحيب:', welcomeMsg);\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            console.log('🔄 استخدام رسالة ترحيب افتراضية');\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n            style: {\n                zIndex: 9999\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse flex items-center justify-center\",\n                style: {\n                    backgroundColor: '#3b82f6',\n                    border: 'none',\n                    cursor: 'pointer',\n                    zIndex: 9999\n                },\n                onMouseEnter: (e)=>{\n                    e.target.style.backgroundColor = '#2563eb';\n                    e.target.style.transform = 'scale(1.05)';\n                },\n                onMouseLeave: (e)=>{\n                    e.target.style.backgroundColor = '#3b82f6';\n                    e.target.style.transform = 'scale(1)';\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-6 w-6\",\n                    style: {\n                        color: 'white',\n                        strokeWidth: 2,\n                        fill: 'none'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n        style: {\n            zIndex: 9999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 shadow-2xl border transition-all duration-300 \".concat(isMinimized ? 'h-auto' : 'h-96', \" bg-white overflow-hidden\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 \".concat(isMinimized ? 'rounded-t-lg' : 'rounded-t-lg'),\n                    style: {\n                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                        color: 'white',\n                        borderTopLeftRadius: '0.5rem',\n                        borderTopRightRadius: '0.5rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-full flex items-center justify-center\",\n                                        style: {\n                                            backgroundColor: 'rgba(255, 255, 255, 0.2)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium\",\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0\n                                                },\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs\",\n                                                style: {\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    margin: 0\n                                                },\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        className: \"h-6 w-6 p-0 rounded hover:bg-white/20 transition-colors flex items-center justify-center\",\n                                        style: {\n                                            color: 'white',\n                                            background: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer'\n                                        },\n                                        title: isMinimized ? 'توسيع' : 'تصغير',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-3 w-3\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"h-6 w-6 p-0 rounded hover:bg-white/20 transition-colors flex items-center justify-center\",\n                                        style: {\n                                            color: 'white',\n                                            background: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer'\n                                        },\n                                        title: \"إغلاق\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-3 max-w-xs\",\n                                                    style: {\n                                                        backgroundColor: message.type === 'user' ? '#3b82f6' : '#f3f4f6',\n                                                        color: message.type === 'user' ? 'white' : '#374151'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            style: {\n                                                                color: message.type === 'user' ? 'white' : '#374151',\n                                                                lineHeight: '1.5'\n                                                            },\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1\",\n                                                            style: {\n                                                                color: message.type === 'user' ? 'rgba(255, 255, 255, 0.7)' : '#6b7280'\n                                                            },\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 rounded-full flex items-center justify-center\",\n                                                style: {\n                                                    backgroundColor: '#e5e7eb'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3\",\n                                                    style: {\n                                                        color: '#6b7280'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg p-3\",\n                                                style: {\n                                                    backgroundColor: '#f3f4f6'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full animate-bounce\",\n                                                            style: {\n                                                                backgroundColor: '#9ca3af'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full animate-bounce\",\n                                                            style: {\n                                                                backgroundColor: '#9ca3af',\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full animate-bounce\",\n                                                            style: {\n                                                                backgroundColor: '#9ca3af',\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t bg-gray-50 p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500\",\n                                            style: {\n                                                backgroundColor: 'white',\n                                                color: '#374151'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            className: \"px-3 py-2 rounded-md text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            style: {\n                                                backgroundColor: !inputMessage.trim() || isLoading ? '#9ca3af' : '#3b82f6',\n                                                border: 'none',\n                                                cursor: !inputMessage.trim() || isLoading ? 'not-allowed' : 'pointer'\n                                            },\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse transition-colors\",\n                                            style: {\n                                                color: '#6b7280',\n                                                textDecoration: 'none'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#3b82f6',\n                                            onMouseLeave: (e)=>e.target.style.color = '#6b7280',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse transition-colors\",\n                                            style: {\n                                                color: '#6b7280',\n                                                textDecoration: 'none'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#3b82f6',\n                                            onMouseLeave: (e)=>e.target.style.color = '#6b7280',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});