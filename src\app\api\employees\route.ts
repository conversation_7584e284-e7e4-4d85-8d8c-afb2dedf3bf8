import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الموظفين من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM employees ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الموظفين:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات الموظفين',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الموظفين جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة الموظفين بنجاح'
    })
  } catch (error) {
    console.error('Error creating الموظفين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموظفين' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الموظفين
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الموظفين بنجاح'
    })
  } catch (error) {
    console.error('Error updating الموظفين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الموظفين' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الموظفين
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموظفين مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM employees WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموظفين بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الموظفين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموظفين' },
      { status: 500 }
    )
  }
}