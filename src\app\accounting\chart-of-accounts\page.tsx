'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BookOpen, Plus, Edit, Trash2, Search, ChevronRight, ChevronDown, Users, Building, TreePine, Link2, UserCheck, Briefcase } from 'lucide-react'

interface Account {
  id: number | string
  account_code: string
  account_name: string
  account_name_en?: string
  account_level: number
  account_type: string
  account_nature: string
  parent_id?: number
  allow_transactions: boolean
  linked_table?: string
  auto_create_sub_accounts: boolean
  opening_balance: number
  current_balance: number
  is_active: boolean
  description?: string
  is_linked_record?: boolean
  original_table?: string
  external_id?: number
  children?: Account[]
}

export default function ChartOfAccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set())
  const [showTreeView, setShowTreeView] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingAccount, setEditingAccount] = useState<Account | null>(null)
  const [parentAccount, setParentAccount] = useState<Account | null>(null)
  const [error, setError] = useState<string | null>(null)

  // حالات الربط
  const [linkingResults, setLinkingResults] = useState<any>(null)

  // بيانات النموذج
  const [formData, setFormData] = useState({
    account_code: '',
    account_name: '',
    account_name_en: '',
    account_type: '',
    account_nature: 'مدين',
    linked_table: 'none',
    auto_create_sub_accounts: false,
    allow_transactions: false,
    description: ''
  })

  // جلب الحسابات من API
  useEffect(() => {
    fetchAccounts()
  }, [])

  const fetchAccounts = async () => {
    try {
      setLoading(true)
      setError(null)

      // جلب الحسابات بدون عرض الجداول المربوطة مباشرة
      const response = await fetch('/api/accounting/chart-of-accounts?exclude_linked_tables=true')
      if (response.ok) {
        const data = await response.json()
        console.log('البيانات المستلمة:', data) // للتشخيص

        if (data.success && Array.isArray(data.accounts)) {
          const hierarchicalAccounts = buildAccountHierarchy(data.accounts)
          setAccounts(hierarchicalAccounts)
          setFilteredAccounts(hierarchicalAccounts)
          setError(null)
        } else {
          console.error('البيانات غير صحيحة:', data)
          setError('البيانات المستلمة غير صحيحة')
          setAccounts([])
          setFilteredAccounts([])
        }
      } else {
        const errorText = `فشل في جلب البيانات: ${response.status}`
        console.error(errorText)
        setError(errorText)
        setAccounts([])
        setFilteredAccounts([])
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      console.error('خطأ في جلب الحسابات:', error)
      setError(`خطأ في الاتصال: ${errorMessage}`)
      setAccounts([])
      setFilteredAccounts([])
    } finally {
      setLoading(false)
    }
  }

  // حفظ الحساب
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingAccount
        ? `/api/accounting/chart-of-accounts/${editingAccount.id}`
        : '/api/accounting/chart-of-accounts'

      const method = editingAccount ? 'PUT' : 'POST'

      const requestData = {
        ...formData,
        linked_table: formData.linked_table === 'none' ? null : formData.linked_table,
        parent_id: parentAccount?.id || null,
        account_level: parentAccount ? parentAccount.account_level + 1 : 1,
        auto_create_sub_accounts: formData.auto_create_sub_accounts,
        allow_transactions: formData.allow_transactions
      }

      console.log('إرسال البيانات:', requestData)

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (response.ok) {
        const result = await response.json()
        console.log('نجح الحفظ:', result)
        await fetchAccounts()
        setShowAddDialog(false)
        setEditingAccount(null)
        setParentAccount(null)
        resetForm()
      } else {
        const errorData = await response.json()
        console.error('خطأ في الحفظ:', errorData)
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error)
      alert('حدث خطأ أثناء حفظ الحساب')
    }
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      account_code: '',
      account_name: '',
      account_name_en: '',
      account_type: '',
      account_nature: 'مدين',
      linked_table: 'none',
      auto_create_sub_accounts: false,
      allow_transactions: false,
      description: ''
    })
  }

  // تعديل حساب
  const handleEdit = (account: Account) => {
    setEditingAccount(account)
    setFormData({
      account_code: account.account_code,
      account_name: account.account_name,
      account_name_en: account.account_name_en || '',
      account_type: account.account_type,
      account_nature: account.account_nature,
      linked_table: account.linked_table || 'none',
      auto_create_sub_accounts: account.auto_create_sub_accounts || false,
      allow_transactions: account.allow_transactions || false,
      description: account.description || ''
    })
    // إعادة تعيين نتائج الربط
    setLinkingResults(null)
    setError(null)
    setShowAddDialog(true)
  }



  // دالة تطبيق الربط من النموذج
  const handleApplyLinkFromForm = async (account: Account, linkType: string) => {
    try {
      setLoading(true)
      setError(null)
      setLinkingResults(null)

      const response = await fetch('/api/accounting/chart-of-accounts/link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_id: account.id,
          link_type: linkType
        }),
      })

      const result = await response.json()

      if (result.success) {
        setLinkingResults(result.details)
        // إعادة جلب البيانات
        await fetchAccounts()
      } else {
        setError(result.error || 'فشل في تطبيق الربط')
      }
    } catch (error) {
      console.error('Error applying link from form:', error)
      setError('حدث خطأ في تطبيق الربط')
    } finally {
      setLoading(false)
    }
  }

  // إضافة حساب فرعي
  const handleAddSubAccount = (parent: Account) => {
    if (parent.account_level >= 4) {
      alert('لا يمكن إضافة حسابات فرعية للمستوى الرابع')
      return
    }

    setParentAccount(parent)
    setEditingAccount(null)
    resetForm()

    // تعيين القيم الافتراضية بناءً على الحساب الأب
    setFormData(prev => ({
      ...prev,
      account_type: parent.account_type,
      account_nature: parent.account_nature,
      // الحسابات من المستوى 4 تقبل معاملات افتراضياً
      allow_transactions: parent.account_level >= 3
    }))

    setShowAddDialog(true)
  }

  // حذف حساب
  const handleDelete = async (account: Account) => {
    if (!confirm(`هل أنت متأكد من حذف الحساب "${account.account_name}"؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء.`)) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/chart-of-accounts/${account.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchAccounts()
        alert('تم حذف الحساب بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error)
      alert('حدث خطأ أثناء حذف الحساب')
    }
  }

  // بناء الهيكل الهرمي للحسابات
  const buildAccountHierarchy = (flatAccounts: Account[]): Account[] => {
    // التحقق من صحة البيانات
    if (!Array.isArray(flatAccounts) || flatAccounts.length === 0) {
      console.log('لا توجد حسابات لبناء الهيكل الهرمي')
      return []
    }

    const accountMap = new Map<number, Account>()
    const rootAccounts: Account[] = []

    try {
      // إنشاء خريطة للحسابات
      flatAccounts.forEach(account => {
        if (account && account.id) {
          accountMap.set(account.id, { ...account, children: [] })
        }
      })

      // بناء الهيكل الهرمي
      flatAccounts.forEach(account => {
        if (account && account.id) {
          const accountWithChildren = accountMap.get(account.id)
          if (accountWithChildren) {
            if (account.parent_id && accountMap.has(account.parent_id)) {
              const parent = accountMap.get(account.parent_id)
              if (parent && parent.children) {
                parent.children.push(accountWithChildren)
              }
            } else {
              rootAccounts.push(accountWithChildren)
            }
          }
        }
      })

      console.log('تم بناء الهيكل الهرمي بنجاح:', rootAccounts.length, 'حساب جذر')
      return rootAccounts
    } catch (error) {
      console.error('خطأ في بناء الهيكل الهرمي:', error)
      return []
    }
  }

  // تصفية الحسابات
  useEffect(() => {
    try {
      // التحقق من وجود البيانات
      if (!Array.isArray(accounts)) {
        setFilteredAccounts([])
        return
      }

      let filtered = [...accounts]

      if (searchTerm && searchTerm.trim()) {
        filtered = filterAccountsBySearch(filtered, searchTerm)
      }

      if (selectedLevel !== 'all') {
        filtered = filterAccountsByLevel(filtered, parseInt(selectedLevel))
      }

      setFilteredAccounts(filtered || [])
    } catch (error) {
      console.error('خطأ في تصفية الحسابات:', error)
      setFilteredAccounts([])
    }
  }, [accounts, searchTerm, selectedLevel])

  const filterAccountsBySearch = (accountList: Account[], term: string): Account[] => {
    if (!Array.isArray(accountList) || !term) {
      return accountList || []
    }

    const result: Account[] = []

    accountList.forEach(account => {
      if (!account || !account.account_name || !account.account_code) {
        return
      }

      const matchesSearch =
        account.account_name.toLowerCase().includes(term.toLowerCase()) ||
        account.account_code.includes(term) ||
        (account.account_name_en && account.account_name_en.toLowerCase().includes(term.toLowerCase()))

      const filteredChildren = (account.children && Array.isArray(account.children))
        ? filterAccountsBySearch(account.children, term)
        : []

      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...account,
          children: filteredChildren
        })
      }
    })

    return result
  }

  const filterAccountsByLevel = (accountList: Account[], level: number): Account[] => {
    if (!Array.isArray(accountList) || isNaN(level)) {
      return accountList || []
    }

    const result: Account[] = []

    accountList.forEach(account => {
      if (!account || typeof account.account_level !== 'number') {
        return
      }

      if (account.account_level === level) {
        result.push(account)
      } else if (account.children && Array.isArray(account.children)) {
        const filteredChildren = filterAccountsByLevel(account.children, level)
        if (filteredChildren.length > 0) {
          result.push({
            ...account,
            children: filteredChildren
          })
        }
      }
    })

    return result
  }

  // دالة عرض شجرة الحسابات
  const handleShowTreeView = () => {
    setShowTreeView(!showTreeView)
    if (!showTreeView) {
      // توسيع جميع العقد عند عرض الشجرة
      const allIds = new Set<number>()
      const collectIds = (accountList: Account[]) => {
        accountList.forEach(account => {
          if (typeof account.id === 'number') {
            allIds.add(account.id)
          }
          if (account.children) {
            collectIds(account.children)
          }
        })
      }
      collectIds(filteredAccounts)
      setExpandedNodes(allIds)
    } else {
      // طي جميع العقد عند إخفاء الشجرة
      setExpandedNodes(new Set())
    }
  }

  // تبديل حالة التوسع
  const toggleExpanded = (accountId: number) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId)
    } else {
      newExpanded.add(accountId)
    }
    setExpandedNodes(newExpanded)
  }

  // عرض الحساب الواحد
  const renderAccount = (account: Account, depth: number = 0) => {
    // التحقق من صحة البيانات
    if (!account || !account.id) {
      console.error('حساب غير صحيح:', account)
      return null
    }

    const hasChildren = account.children && Array.isArray(account.children) && account.children.length > 0
    const isExpanded = expandedNodes.has(account.id)
    const indentStyle = { marginLeft: `${depth * 24}px` }

    // في وضع الشجرة، نعرض المزيد من التفاصيل
    const isTreeMode = showTreeView

    return (
      <div key={account.id} className={`border-b border-gray-100 ${isTreeMode ? 'border-l-2 border-l-blue-200' : ''}`}>
        <div className={`flex items-center justify-between p-3 hover:bg-gray-50 ${isTreeMode ? 'bg-gradient-to-r from-blue-50 to-transparent' : ''}`} style={indentStyle}>
          <div className="flex items-center space-x-3 space-x-reverse">
            {hasChildren && (
              <button
                onClick={() => toggleExpanded(account.id)}
                className="p-1 hover:bg-gray-200 rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>
            )}
            {!hasChildren && <div className="w-6" />}

            <div className="flex flex-col">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="font-mono text-sm text-blue-600">{account.account_code}</span>
                <span className="font-medium">{account.account_name}</span>
                {account.linked_table && (
                  <Badge variant="outline" className="text-xs">
                    {account.linked_table === 'clients' ? (
                      <><Users className="h-3 w-3 ml-1" /> عملاء</>
                    ) : account.linked_table === 'employees' ? (
                      <><Building className="h-3 w-3 ml-1" /> موظفين</>
                    ) : (
                      account.linked_table
                    )}
                  </Badge>
                )}
                {account.is_linked_record && (
                  <Badge variant="secondary" className="text-xs">
                    {account.original_table === 'clients' ? (
                      <><Users className="h-3 w-3 ml-1" /> عميل</>
                    ) : (
                      <><Building className="h-3 w-3 ml-1" /> موظف</>
                    )}
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                <span>المستوى {account.account_level}</span>
                <span>{account.account_type}</span>
                <span>{account.account_nature}</span>
                {account.allow_transactions && (
                  <Badge variant="secondary" className="text-xs">يقبل معاملات</Badge>
                )}
                {isTreeMode && account.description && (
                  <span className="text-gray-400 italic max-w-xs truncate" title={account.description}>
                    {account.description}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            {account.current_balance !== undefined && account.current_balance !== 0 && (
              <span className={`text-sm font-medium ${
                account.current_balance > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {account.current_balance.toLocaleString()} ر.ي
              </span>
            )}

            <div className="flex space-x-1 space-x-reverse">
              {!account.is_linked_record && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEdit(account)}
                    title="تعديل الحساب وإعداد الربط"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(account)}
                    title="حذف الحساب"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </>
              )}
              {account.account_level < 4 && !account.is_linked_record && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAddSubAccount(account)}
                  title="إضافة حساب فرعي"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && account.children && (
          <div>
            {account.children.map((child, index) => {
              try {
                return renderAccount(child, depth + 1)
              } catch (error) {
                console.error('خطأ في عرض الحساب الفرعي:', child, error)
                return (
                  <div key={`child-error-${index}`} className="p-2 text-red-500 text-sm ml-6">
                    خطأ في عرض الحساب الفرعي: {child?.account_name || 'غير معروف'}
                  </div>
                )
              }
            })}
          </div>
        )}
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <BookOpen className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">دليل الحسابات</h1>
              <p className="text-gray-600">إدارة الهيكل المحاسبي للشركة (4 مستويات)</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            إضافة حساب جديد
          </Button>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex space-x-4 space-x-reverse items-center">
              {/* مربع البحث - 50% من الحجم الأصلي */}
              <div className="w-1/2">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الحسابات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 text-sm"
                  />
                </div>
              </div>

              {/* قائمة مستوى الحسابات */}
              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="تصفية حسب المستوى" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المستويات</SelectItem>
                  <SelectItem value="1">المستوى الأول</SelectItem>
                  <SelectItem value="2">المستوى الثاني</SelectItem>
                  <SelectItem value="3">المستوى الثالث</SelectItem>
                  <SelectItem value="4">المستوى الرابع</SelectItem>
                </SelectContent>
              </Select>

              {/* زر عرض شجرة الحسابات */}
              <Button
                variant={showTreeView ? "default" : "outline"}
                size="sm"
                onClick={handleShowTreeView}
                className="flex items-center space-x-2 space-x-reverse"
                title={showTreeView ? "إخفاء شجرة الحسابات" : "عرض شجرة الحسابات"}
              >
                <TreePine className="h-4 w-4" />
                <span className="text-sm">
                  {showTreeView ? "إخفاء الشجرة" : "عرض الشجرة"}
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الحسابات */}
        <Card>
          <CardHeader>
            <CardTitle>الحسابات المحاسبية</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {/* معلومات إضافية عند عرض الشجرة */}
            {showTreeView && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2 space-x-reverse text-sm text-blue-700">
                  <TreePine className="h-4 w-4" />
                  <span className="font-medium">عرض شجرة الحسابات</span>
                  <span className="text-blue-600">
                    - {selectedLevel === 'all' ? 'جميع المستويات' : `المستوى ${selectedLevel}`}
                  </span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  يتم عرض الحسابات في شكل شجرة هرمية حسب المستوى المحدد
                </p>
              </div>
            )}

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل الحسابات...</p>
              </div>
            ) : error ? (
              <div className="p-8 text-center">
                <div className="text-red-500 mb-4">
                  <svg className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-red-600 font-medium mb-2">حدث خطأ في تحميل الحسابات</p>
                <p className="text-gray-600 text-sm mb-4">{error}</p>
                <button
                  onClick={fetchAccounts}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  إعادة المحاولة
                </button>
              </div>
            ) : !Array.isArray(filteredAccounts) || filteredAccounts.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد حسابات مطابقة للبحث</p>
                {accounts.length === 0 && (
                  <button
                    onClick={fetchAccounts}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    تحديث البيانات
                  </button>
                )}
              </div>
            ) : (
              <div className={`${showTreeView ? 'max-h-[500px]' : 'max-h-96'} overflow-y-auto`}>
                {filteredAccounts.map((account, index) => {
                  try {
                    return renderAccount(account)
                  } catch (error) {
                    console.error('خطأ في عرض الحساب:', account, error)
                    return (
                      <div key={`error-${index}`} className="p-2 text-red-500 text-sm">
                        خطأ في عرض الحساب: {account?.account_name || 'غير معروف'}
                      </div>
                    )
                  }
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل الحساب */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingAccount ? 'تعديل الحساب' : parentAccount ? `إضافة حساب فرعي تحت: ${parentAccount.account_name}` : 'إضافة حساب جديد'}
              </DialogTitle>
              <DialogDescription>
                {editingAccount
                  ? 'قم بتعديل بيانات الحساب المحدد'
                  : parentAccount
                    ? 'إضافة حساب فرعي جديد تحت الحساب المحدد'
                    : 'إضافة حساب جديد إلى دليل الحسابات'
                }
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account_code">رمز الحساب</Label>
                  <Input
                    id="account_code"
                    value={formData.account_code}
                    onChange={(e) => setFormData({...formData, account_code: e.target.value})}
                    required
                    placeholder="مثال: 0101"
                  />
                </div>

                <div>
                  <Label htmlFor="account_name">اسم الحساب</Label>
                  <Input
                    id="account_name"
                    value={formData.account_name}
                    onChange={(e) => setFormData({...formData, account_name: e.target.value})}
                    required
                    placeholder="اسم الحساب بالعربية"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="account_name_en">اسم الحساب بالإنجليزية (اختياري)</Label>
                <Input
                  id="account_name_en"
                  value={formData.account_name_en}
                  onChange={(e) => setFormData({...formData, account_name_en: e.target.value})}
                  placeholder="Account Name in English"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account_type">نوع الحساب</Label>
                  <Select value={formData.account_type} onValueChange={(value) => setFormData({...formData, account_type: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الحساب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="أصول">أصول</SelectItem>
                      <SelectItem value="خصوم">خصوم</SelectItem>
                      <SelectItem value="حقوق ملكية">حقوق ملكية</SelectItem>
                      <SelectItem value="إيرادات">إيرادات</SelectItem>
                      <SelectItem value="مصروفات">مصروفات</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="account_nature">طبيعة الحساب</Label>
                  <Select value={formData.account_nature} onValueChange={(value) => setFormData({...formData, account_nature: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طبيعة الحساب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="مدين">مدين</SelectItem>
                      <SelectItem value="دائن">دائن</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="linked_table">ربط بجدول خارجي (اختياري)</Label>
                  <Select value={formData.linked_table} onValueChange={(value) => {
                    setFormData({
                      ...formData,
                      linked_table: value,
                      // تعطيل المعاملات تلقائياً عند ربط جدول خارجي
                      allow_transactions: value === 'none' ? formData.allow_transactions : false
                    })
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الجدول المرتبط" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون ربط</SelectItem>
                      <SelectItem value="clients">العملاء</SelectItem>
                      <SelectItem value="employees">الموظفين</SelectItem>
                      <SelectItem value="suppliers">الموردين</SelectItem>
                      <SelectItem value="cases">القضايا</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse pt-6">
                  <input
                    type="checkbox"
                    id="auto_create_sub_accounts"
                    checked={formData.auto_create_sub_accounts}
                    onChange={(e) => setFormData({...formData, auto_create_sub_accounts: e.target.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="auto_create_sub_accounts">إنشاء حسابات فرعية تلقائياً</Label>
                </div>
              </div>

              {/* خيار يقبل معاملات */}
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <input
                    type="checkbox"
                    id="allow_transactions"
                    checked={formData.allow_transactions}
                    onChange={(e) => setFormData({...formData, allow_transactions: e.target.checked})}
                    disabled={formData.linked_table && formData.linked_table !== 'none'}
                    className="rounded w-4 h-4 text-yellow-600 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                  <Label htmlFor="allow_transactions" className={`flex items-center space-x-2 space-x-reverse cursor-pointer ${
                    formData.linked_table && formData.linked_table !== 'none' ? 'opacity-50' : ''
                  }`}>
                    <span className="text-lg">💰</span>
                    <div className="flex flex-col">
                      <span className="font-medium text-yellow-800">يقبل معاملات</span>
                      <span className="text-xs text-yellow-600">الحسابات التي تقبل المعاملات تظهر في السندات والقيود المحاسبية</span>
                    </div>
                  </Label>
                </div>

                {formData.linked_table && formData.linked_table !== 'none' && (
                  <div className="space-y-3">
                    <div className="text-xs text-blue-700 bg-blue-100 p-2 rounded border border-blue-200">
                      <strong>ربط الحساب:</strong> سيتم ربط جميع السجلات النشطة في جدول {
                        formData.linked_table === 'clients' ? 'العملاء' :
                        formData.linked_table === 'employees' ? 'الموظفين' :
                        formData.linked_table === 'suppliers' ? 'الموردين' : formData.linked_table
                      } بهذا الحساب مباشرة.
                    </div>

                    {editingAccount && (
                      <Button
                        type="button"
                        onClick={() => handleApplyLinkFromForm(editingAccount, formData.linked_table)}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        disabled={loading}
                      >
                        <Link2 className="h-4 w-4 ml-2" />
                        {loading ? 'جاري تطبيق الربط...' : `تطبيق الربط مع ${
                          formData.linked_table === 'clients' ? 'العملاء' :
                          formData.linked_table === 'employees' ? 'الموظفين' :
                          formData.linked_table === 'suppliers' ? 'الموردين' : formData.linked_table
                        }`}
                      </Button>
                    )}

                    {linkingResults && (
                      <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                        <h4 className="font-bold text-green-800 mb-2">نتائج الربط:</h4>
                        <div className="text-sm text-green-700">
                          <div>🔗 تم ربط {linkingResults.linked_records} سجل بهذا الحساب</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-2 text-xs text-yellow-700 bg-yellow-100 p-2 rounded">
                  <strong>ملاحظة:</strong> عادة ما تكون الحسابات الفرعية (المستوى 4) هي التي تقبل المعاملات، بينما الحسابات الرئيسية تستخدم للتجميع فقط.
                </div>
              </div>

              <div>
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف الحساب ووظيفته"
                />
              </div>

              {parentAccount && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>الحساب الأب:</strong> {parentAccount.account_code} - {parentAccount.account_name}
                  </p>
                  <p className="text-sm text-blue-600">
                    المستوى الجديد: {parentAccount.account_level + 1}
                  </p>
                </div>
              )}

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => {
                  setShowAddDialog(false)
                  setEditingAccount(null)
                  setParentAccount(null)
                  resetForm()
                }}>
                  إلغاء
                </Button>
                <Button type="submit">
                  {editingAccount ? 'تحديث' : 'حفظ'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>


        </div>
      </div>
    </MainLayout>
  )
}
