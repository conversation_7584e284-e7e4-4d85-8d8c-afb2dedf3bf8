/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/accounting/payment-vouchers/route";
exports.ids = ["app/api/accounting/payment-vouchers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&page=%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&page=%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_accounting_payment_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/accounting/payment-vouchers/route.ts */ \"(rsc)/./src/app/api/accounting/payment-vouchers/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_accounting_payment_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_accounting_payment_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/accounting/payment-vouchers/route\",\n        pathname: \"/api/accounting/payment-vouchers\",\n        filename: \"route\",\n        bundlePath: \"app/api/accounting/payment-vouchers/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\accounting\\\\payment-vouchers\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_accounting_payment_vouchers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&page=%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/accounting/payment-vouchers/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/accounting/payment-vouchers/route.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// دالة لضمان وجود جدول سندات الصرف\nasync function ensurePaymentVouchersTable() {\n    try {\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE TABLE IF NOT EXISTS payment_vouchers (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        voucher_number VARCHAR(50),\n        entry_date DATE NOT NULL,\n        voucher_date DATE,\n        payee_name VARCHAR(255) NOT NULL,\n        payee_type VARCHAR(50) DEFAULT 'external',\n        debit_account_id INTEGER,\n        credit_account_id INTEGER,\n        amount DECIMAL(15,2) NOT NULL,\n        currency_id INTEGER DEFAULT 1,\n        payment_method_id INTEGER,\n        cost_center_id INTEGER,\n        description TEXT,\n        reference_number VARCHAR(100),\n        case_id INTEGER,\n        service_id INTEGER,\n        status VARCHAR(20) DEFAULT 'draft',\n        created_by VARCHAR(100) DEFAULT 'النظام',\n        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (debit_account_id) REFERENCES chart_of_accounts(id),\n        FOREIGN KEY (credit_account_id) REFERENCES chart_of_accounts(id)\n      )\n    `);\n        // إضافة فهارس\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_entry_date ON payment_vouchers(entry_date);\n      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_status ON payment_vouchers(status);\n      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_payee_type ON payment_vouchers(payee_type);\n    `);\n    } catch (error) {\n        console.error('خطأ في إنشاء جدول سندات الصرف:', error);\n    }\n}\n// دالة لتوليد رقم سند جديد\nasync function generateVoucherNumber() {\n    const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number\n    FROM journal_entries\n    WHERE entry_number ~ '^PV[0-9]+$'\n  `);\n    const nextNumber = result.rows[0]?.next_number || 1;\n    return `PV${String(nextNumber).padStart(6, '0')}`;\n}\n// دالة لتوليد رقم قيد يومية جديد\nasync function generateJournalEntryNumber() {\n    const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number\n    FROM journal_entries\n    WHERE entry_number ~ '^JE[0-9]+$'\n  `);\n    const nextNumber = result.rows[0].next_number;\n    return `JE${nextNumber.toString().padStart(6, '0')}`;\n}\n// دالة لحفظ السند في القيود اليومية\nasync function createJournalEntryFromVoucher(voucher, voucherType) {\n    try {\n        // ضمان وجود جدول القيود اليومية\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        entry_date DATE NOT NULL,\n        description TEXT NOT NULL,\n        total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,\n        total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,\n        status VARCHAR(20) DEFAULT 'draft',\n        created_by VARCHAR(100) DEFAULT 'النظام',\n        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        voucher_type VARCHAR(20),\n        voucher_id INTEGER,\n        voucher_number VARCHAR(50)\n      )\n    `);\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      CREATE TABLE IF NOT EXISTS journal_entry_details (\n        id SERIAL PRIMARY KEY,\n        journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,\n        account_id INTEGER,\n        account_name VARCHAR(255),\n        debit_amount DECIMAL(15,2) DEFAULT 0,\n        credit_amount DECIMAL(15,2) DEFAULT 0,\n        description TEXT,\n        line_order INTEGER DEFAULT 1\n      )\n    `);\n        // توليد رقم قيد جديد\n        const entryNumber = await generateJournalEntryNumber();\n        // إنشاء القيد الرئيسي\n        const journalEntry = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entries (\n        entry_number, entry_date, description, total_debit, total_credit,\n        status, created_by, voucher_type, voucher_id, voucher_number\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)\n      RETURNING id\n    `, [\n            entryNumber,\n            voucher.entry_date,\n            `${voucherType === 'receipt' ? 'سند قبض' : 'سند صرف'} رقم ${voucher.entry_number} - ${voucher.description}`,\n            voucher.amount,\n            voucher.amount,\n            'draft',\n            'النظام',\n            voucherType,\n            voucher.id,\n            voucher.entry_number\n        ]);\n        const journalEntryId = journalEntry.rows[0].id;\n        // الحصول على أسماء الحسابات\n        const debitAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name FROM chart_of_accounts WHERE id = $1\n    `, [\n            voucher.debit_account_id\n        ]);\n        const creditAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name FROM chart_of_accounts WHERE id = $1\n    `, [\n            voucher.credit_account_id\n        ]);\n        // إنشاء السطر المدين\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7)\n    `, [\n            journalEntryId,\n            voucher.debit_account_id,\n            debitAccount.rows[0]?.account_name || 'حساب غير معروف',\n            voucher.amount,\n            0,\n            `${voucherType === 'receipt' ? 'استلام من' : 'دفع إلى'} ${voucher.payee_name}`,\n            1\n        ]);\n        // إنشاء السطر الدائن\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7)\n    `, [\n            journalEntryId,\n            voucher.credit_account_id,\n            creditAccount.rows[0]?.account_name || 'حساب غير معروف',\n            0,\n            voucher.amount,\n            `${voucherType === 'receipt' ? 'إيراد من' : 'مصروف لـ'} ${voucher.payee_name}`,\n            2\n        ]);\n        return entryNumber;\n    } catch (error) {\n        console.error('❌ خطأ في إنشاء القيد اليومية:', error);\n        throw error;\n    }\n}\n// GET - جلب سندات الصرف\nasync function GET(request) {\n    try {\n        await ensurePaymentVouchersTable();\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const status = searchParams.get('status');\n        const dateFrom = searchParams.get('dateFrom');\n        const dateTo = searchParams.get('dateTo');\n        const search = searchParams.get('search');\n        const offset = (page - 1) * limit;\n        let sql = `\n      SELECT\n        je.*,\n        (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count\n      FROM journal_entries je\n      WHERE je.entry_type = 'payment'\n    `;\n        const params = [];\n        let paramIndex = 1;\n        // تصفية حسب الحالة\n        if (status && status !== 'all') {\n            sql += ` AND je.status = $${paramIndex}`;\n            params.push(status);\n            paramIndex++;\n        }\n        // تصفية حسب التاريخ\n        if (dateFrom) {\n            sql += ` AND je.entry_date >= $${paramIndex}`;\n            params.push(dateFrom);\n            paramIndex++;\n        }\n        if (dateTo) {\n            sql += ` AND je.entry_date <= $${paramIndex}`;\n            params.push(dateTo);\n            paramIndex++;\n        }\n        // البحث في النص\n        if (search) {\n            sql += ` AND (je.party_name ILIKE $${paramIndex} OR je.description ILIKE $${paramIndex} OR je.entry_number ILIKE $${paramIndex})`;\n            params.push(`%${search}%`);\n            paramIndex++;\n        }\n        // ترتيب وتحديد العدد\n        sql += ` ORDER BY je.entry_date DESC, je.id DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;\n        params.push(limit, offset);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(sql, params);\n        // جلب تفاصيل كل سند وتحويل البيانات للتوافق مع الواجهة\n        const vouchers = [];\n        for (const row of result.rows){\n            // جلب تفاصيل القيد\n            const detailsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        SELECT * FROM journal_entry_details\n        WHERE journal_entry_id = $1\n        ORDER BY line_order\n      `, [\n                row.id\n            ]);\n            // العثور على الحساب المدين والدائن\n            const debitDetail = detailsResult.rows.find((d)=>parseFloat(d.debit_amount) > 0);\n            const creditDetail = detailsResult.rows.find((d)=>parseFloat(d.credit_amount) > 0);\n            vouchers.push({\n                id: row.id,\n                entry_number: row.entry_number,\n                voucher_number: row.entry_number,\n                entry_date: row.entry_date,\n                voucher_date: row.entry_date,\n                amount: parseFloat(row.total_debit || 0),\n                total_debit: parseFloat(row.total_debit || 0),\n                payee_name: row.party_name,\n                payee_type: row.party_type,\n                beneficiary_name: row.party_name,\n                beneficiary_type: row.party_type,\n                description: row.description,\n                reference_number: row.reference_number,\n                status: row.status,\n                debit_account_id: debitDetail?.account_id,\n                credit_account_id: creditDetail?.account_id,\n                debit_account_name: debitDetail?.account_name,\n                debit_account_code: debitDetail?.account_code,\n                credit_account_name: creditDetail?.account_name,\n                credit_account_code: creditDetail?.account_code,\n                currency_id: 1,\n                payment_method_id: 1,\n                cost_center_id: null,\n                case_id: null,\n                service_id: null,\n                created_by: row.created_by,\n                created_date: row.created_date,\n                details: detailsResult.rows\n            });\n        }\n        // حساب العدد الإجمالي\n        let countSql = `SELECT COUNT(*) as total FROM journal_entries je WHERE je.entry_type = 'payment'`;\n        const countParams = [];\n        let countParamIndex = 1;\n        if (status && status !== 'all') {\n            countSql += ` AND je.status = $${countParamIndex}`;\n            countParams.push(status);\n            countParamIndex++;\n        }\n        if (dateFrom) {\n            countSql += ` AND je.entry_date >= $${countParamIndex}`;\n            countParams.push(dateFrom);\n            countParamIndex++;\n        }\n        if (dateTo) {\n            countSql += ` AND je.entry_date <= $${countParamIndex}`;\n            countParams.push(dateTo);\n            countParamIndex++;\n        }\n        if (search) {\n            countSql += ` AND (je.party_name ILIKE $${countParamIndex} OR je.description ILIKE $${countParamIndex} OR je.entry_number ILIKE $${countParamIndex})`;\n            countParams.push(`%${search}%`);\n        }\n        const countResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(countSql, countParams);\n        const total = parseInt(countResult.rows[0].total);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            vouchers: vouchers,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            },\n            total,\n            message: 'تم جلب سندات الصرف بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في جلب سندات الصرف:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب سندات الصرف'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة سند صرف جديد\nasync function POST(request) {\n    try {\n        await ensurePaymentVouchersTable();\n        const body = await request.json();\n        console.log('📥 بيانات سند الصرف المستلمة:', body);\n        const { entry_date, payee_name, payee_type = 'external', beneficiary_name, beneficiary_type, debit_account_id, credit_account_id, amount, description, reference_number, status = 'draft' } = body;\n        // دعم كلا من payee و beneficiary للتوافق مع النماذج المختلفة\n        const finalPayeeName = payee_name || beneficiary_name;\n        const finalPayeeType = beneficiary_type || payee_type || 'external';\n        // التحقق من البيانات المطلوبة\n        if (!entry_date || !finalPayeeName || !debit_account_id || !credit_account_id || !amount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات المطلوبة مفقودة',\n                details: 'يجب توفير: تاريخ السند، اسم المستفيد، الحساب المدين، الحساب الدائن، والمبلغ'\n            }, {\n                status: 400\n            });\n        }\n        // توليد رقم سند جديد\n        const entry_number = await generateVoucherNumber();\n        // إنشاء القيد في الهيكل الموحد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entries (\n        entry_number, entry_type, entry_date, description,\n        party_name, party_type, reference_number,\n        total_debit, total_credit, status, created_by\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\n      RETURNING *\n    `, [\n            entry_number,\n            'payment',\n            entry_date,\n            description,\n            finalPayeeName,\n            finalPayeeType,\n            reference_number,\n            amount,\n            amount,\n            status,\n            'النظام'\n        ]);\n        const newEntry = result.rows[0];\n        const journalEntryId = newEntry.id;\n        // الحصول على بيانات الحسابات\n        const debitAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1\n    `, [\n            debit_account_id\n        ]);\n        const creditAccount = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1\n    `, [\n            credit_account_id\n        ]);\n        // إضافة السطر المدين\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, account_code,\n        debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n    `, [\n            journalEntryId,\n            debit_account_id,\n            debitAccount.rows[0]?.account_name || 'حساب غير معروف',\n            debitAccount.rows[0]?.account_code || '',\n            amount,\n            0,\n            `دفع إلى ${finalPayeeName}`,\n            1\n        ]);\n        // إضافة السطر الدائن\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entry_details (\n        journal_entry_id, account_id, account_name, account_code,\n        debit_amount, credit_amount, description, line_order\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n    `, [\n            journalEntryId,\n            credit_account_id,\n            creditAccount.rows[0]?.account_name || 'حساب غير معروف',\n            creditAccount.rows[0]?.account_code || '',\n            0,\n            amount,\n            `مصروف لـ ${finalPayeeName}`,\n            2\n        ]);\n        // تحويل البيانات للتوافق مع الواجهة\n        const voucher = {\n            id: newEntry.id,\n            entry_number: newEntry.entry_number,\n            entry_date: newEntry.entry_date,\n            payee_name: newEntry.party_name,\n            payee_type: newEntry.party_type,\n            amount: newEntry.total_debit,\n            description: newEntry.description,\n            reference_number: newEntry.reference_number,\n            status: newEntry.status,\n            created_by: newEntry.created_by,\n            created_date: newEntry.created_date\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voucher,\n            message: `تم إنشاء سند الصرف ${voucher.entry_number} بنجاح`\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إنشاء سند الصرف:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء سند الصرف',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/accounting/payment-vouchers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQUd6QiwwQ0FBMEM7QUFDMUMsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLENBQUNDLFdBQVcsRUFBRTtJQUM1QixNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxNQUFNQyxPQUFPLElBQUlMLG9DQUFJQSxDQUFDO0lBQ3BCTSxNQUFNTCxRQUFRQyxHQUFHLENBQUNLLE9BQU8sSUFBSTtJQUM3QkMsTUFBTVAsUUFBUUMsR0FBRyxDQUFDTyxPQUFPLElBQUk7SUFDN0JDLFVBQVVULFFBQVFDLEdBQUcsQ0FBQ1MsT0FBTyxJQUFJO0lBQ2pDQyxVQUFVWCxRQUFRQyxHQUFHLENBQUNDLFdBQVcsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7SUFDaEVVLE1BQU1DLFNBQVNiLFFBQVFDLEdBQUcsQ0FBQ2EsT0FBTyxJQUFJO0FBQ3hDO0FBRU8sZUFBZUMsTUFBTUMsSUFBWSxFQUFFQyxNQUFjO0lBQ3RELE1BQU1DLFNBQVMsTUFBTWQsS0FBS2UsT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNRixPQUFPSCxLQUFLLENBQUNDLE1BQU1DO1FBQ3hDLE9BQU9HO0lBQ1QsU0FBVTtRQUNSRixPQUFPRyxPQUFPO0lBQ2hCO0FBQ0Y7QUFFZSIsInNvdXJjZXMiOlsiRDpcXG1vaGFtaW5ld1xcc3JjXFxsaWJcXGRiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcblxuXG4vLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZg9mE2YXYqSDZhdix2YjYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbmlmICghcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdEQl9QQVNTV09SRCBlbnZpcm9ubWVudCB2YXJpYWJsZSBpcyByZXF1aXJlZCcpXG59XG5cbmNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gIHVzZXI6IHByb2Nlc3MuZW52LkRCX1VTRVIgfHwgJ3Bvc3RncmVzJyxcbiAgaG9zdDogcHJvY2Vzcy5lbnYuREJfSE9TVCB8fCAnbG9jYWxob3N0JyxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ21vaGFtbWknLFxuICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgJ3lvdXJfcGFzc3dvcmRfaGVyZScsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbn0pXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeSh0ZXh0OiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKSB7XG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbmV4cG9ydCB7IHBvb2wgfVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJwcm9jZXNzIiwiZW52IiwiREJfUEFTU1dPUkQiLCJFcnJvciIsInBvb2wiLCJ1c2VyIiwiREJfVVNFUiIsImhvc3QiLCJEQl9IT1NUIiwiZGF0YWJhc2UiLCJEQl9OQU1FIiwicGFzc3dvcmQiLCJwb3J0IiwicGFyc2VJbnQiLCJEQl9QT1JUIiwicXVlcnkiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsInJlbGVhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&page=%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fpayment-vouchers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();