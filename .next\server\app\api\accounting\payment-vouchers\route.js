(()=>{var e={};e.id=6170,e.ids=[6170],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>_});var n=r(64939),s=r(29021),o=r.n(s),c=r(33873),i=r.n(c),u=e([n]);n=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),y=new n.Pool(p);async function _(e,t){let r=await y.connect();try{return await r.query(e,t)}finally{r.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30534:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>u,POST:()=>_});var n=r(32190),s=r(5069),o=e([s]);async function c(){try{await (0,s.P)(`
      CREATE TABLE IF NOT EXISTS payment_vouchers (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_number VARCHAR(50),
        entry_date DATE NOT NULL,
        voucher_date DATE,
        payee_name VARCHAR(255) NOT NULL,
        payee_type VARCHAR(50) DEFAULT 'external',
        debit_account_id INTEGER,
        credit_account_id INTEGER,
        amount DECIMAL(15,2) NOT NULL,
        currency_id INTEGER DEFAULT 1,
        payment_method_id INTEGER,
        cost_center_id INTEGER,
        description TEXT,
        reference_number VARCHAR(100),
        case_id INTEGER,
        service_id INTEGER,
        status VARCHAR(20) DEFAULT 'draft',
        created_by VARCHAR(100) DEFAULT 'النظام',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (debit_account_id) REFERENCES chart_of_accounts(id),
        FOREIGN KEY (credit_account_id) REFERENCES chart_of_accounts(id)
      )
    `),await (0,s.P)(`
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_entry_date ON payment_vouchers(entry_date);
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_status ON payment_vouchers(status);
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_payee_type ON payment_vouchers(payee_type);
    `)}catch(e){console.error("خطأ في إنشاء جدول سندات الصرف:",e)}}async function i(){let e=await (0,s.P)(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries
    WHERE entry_number ~ '^PV[0-9]+$'
  `),t=e.rows[0]?.next_number||1;return`PV${String(t).padStart(6,"0")}`}async function u(e){try{await c();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),o=t.get("status"),i=t.get("dateFrom"),u=t.get("dateTo"),_=t.get("search"),d=(r-1)*a,p=`
      SELECT
        je.*,
        (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count
      FROM journal_entries je
      WHERE je.entry_type = 'payment'
    `,y=[],E=1;o&&"all"!==o&&(p+=` AND je.status = $${E}`,y.push(o),E++),i&&(p+=` AND je.entry_date >= $${E}`,y.push(i),E++),u&&(p+=` AND je.entry_date <= $${E}`,y.push(u),E++),_&&(p+=` AND (je.party_name ILIKE $${E} OR je.description ILIKE $${E} OR je.entry_number ILIKE $${E})`,y.push(`%${_}%`),E++),p+=` ORDER BY je.entry_date DESC, je.id DESC LIMIT $${E} OFFSET $${E+1}`,y.push(a,d);let l=await (0,s.P)(p,y),m=[];for(let e of l.rows){let t=await (0,s.P)(`
        SELECT * FROM journal_entry_details
        WHERE journal_entry_id = $1
        ORDER BY line_order
      `,[e.id]),r=t.rows.find(e=>parseFloat(e.debit_amount)>0),a=t.rows.find(e=>parseFloat(e.credit_amount)>0);m.push({id:e.id,entry_number:e.entry_number,voucher_number:e.entry_number,entry_date:e.entry_date,voucher_date:e.entry_date,amount:parseFloat(e.total_debit||0),total_debit:parseFloat(e.total_debit||0),payee_name:e.party_name,payee_type:e.party_type,beneficiary_name:e.party_name,beneficiary_type:e.party_type,description:e.description,reference_number:e.reference_number,status:e.status,debit_account_id:r?.account_id,credit_account_id:a?.account_id,debit_account_name:r?.account_name,debit_account_code:r?.account_code,credit_account_name:a?.account_name,credit_account_code:a?.account_code,currency_id:1,payment_method_id:1,cost_center_id:null,case_id:null,service_id:null,created_by:e.created_by,created_date:e.created_date,details:t.rows})}let R="SELECT COUNT(*) as total FROM journal_entries je WHERE je.entry_type = 'payment'",T=[],$=1;o&&"all"!==o&&(R+=` AND je.status = $${$}`,T.push(o),$++),i&&(R+=` AND je.entry_date >= $${$}`,T.push(i),$++),u&&(R+=` AND je.entry_date <= $${$}`,T.push(u),$++),_&&(R+=` AND (je.party_name ILIKE $${$} OR je.description ILIKE $${$} OR je.entry_number ILIKE $${$})`,T.push(`%${_}%`));let h=await (0,s.P)(R,T),N=parseInt(h.rows[0].total);return n.NextResponse.json({success:!0,vouchers:m,pagination:{page:r,limit:a,total:N,pages:Math.ceil(N/a)},total:N,message:"تم جلب سندات الصرف بنجاح"})}catch(e){return console.error("خطأ في جلب سندات الصرف:",e),n.NextResponse.json({success:!1,error:"فشل في جلب سندات الصرف"},{status:500})}}async function _(e){try{await c();let t=await e.json();console.log("\uD83D\uDCE5 بيانات سند الصرف المستلمة:",t);let{entry_date:r,payee_name:a,payee_type:o="external",beneficiary_name:u,beneficiary_type:_,debit_account_id:d,credit_account_id:p,amount:y,description:E,reference_number:l,status:m="draft"}=t,R=a||u;if(!r||!R||!d||!p||!y)return n.NextResponse.json({success:!1,error:"البيانات المطلوبة مفقودة",details:"يجب توفير: تاريخ السند، اسم المستفيد، الحساب المدين، الحساب الدائن، والمبلغ"},{status:400});let T=await i(),$=(await (0,s.P)(`
      INSERT INTO journal_entries (
        entry_number, entry_type, entry_date, description,
        party_name, party_type, reference_number,
        total_debit, total_credit, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `,[T,"payment",r,E,R,_||o||"external",l,y,y,m,"النظام"])).rows[0],h=$.id,N=await (0,s.P)(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `,[d]),A=await (0,s.P)(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `,[p]);await (0,s.P)(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `,[h,d,N.rows[0]?.account_name||"حساب غير معروف",N.rows[0]?.account_code||"",y,0,`دفع إلى ${R}`,1]),await (0,s.P)(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `,[h,p,A.rows[0]?.account_name||"حساب غير معروف",A.rows[0]?.account_code||"",0,y,`مصروف لـ ${R}`,2]);let b={id:$.id,entry_number:$.entry_number,entry_date:$.entry_date,payee_name:$.party_name,payee_type:$.party_type,amount:$.total_debit,description:$.description,reference_number:$.reference_number,status:$.status,created_by:$.created_by,created_date:$.created_date};return n.NextResponse.json({success:!0,voucher:b,message:`تم إنشاء سند الصرف ${b.entry_number} بنجاح`})}catch(e){return console.error("❌ خطأ في إنشاء سند الصرف:",e),n.NextResponse.json({success:!1,error:"فشل في إنشاء سند الصرف",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}s=(o.then?(await o)():o)[0],a()}catch(e){a(e)}})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54600:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>_,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var n=r(96559),s=r(48088),o=r(37719),c=r(30534),i=e([c]);c=(i.then?(await i)():i)[0];let _=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/payment-vouchers/route",pathname:"/api/accounting/payment-vouchers",filename:"route",bundlePath:"app/api/accounting/payment-vouchers/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\payment-vouchers\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:y}=_;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(54600));module.exports=a})();