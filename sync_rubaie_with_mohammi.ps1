# سكريپت مزامنة قاعدة البيانات rubaie مع mohammi
# Sync Rubaie Database with Mohammi

Write-Host "🔄 مزامنة قاعدة البيانات rubaie مع mohammi" -ForegroundColor Green

# تعيين كلمة المرور
$env:PGPASSWORD = "yemen123"

Write-Host "`n🔍 التحقق من قواعد البيانات..." -ForegroundColor Yellow

# التحقق من وجود قاعدة البيانات المصدر
$sourceExists = psql -U postgres -lqt | Select-String "mohammi"
if (-not $sourceExists) {
    Write-Host "❌ قاعدة البيانات المصدر 'mohammi' غير موجودة" -ForegroundColor Red
    exit 1
}

Write-Host "✅ قاعدة البيانات المصدر موجودة" -ForegroundColor Green

# قطع الاتصالات بقاعدة البيانات الهدف
Write-Host "`n🔌 قطع الاتصالات بقاعدة البيانات rubaie..." -ForegroundColor Yellow
psql -U postgres -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'rubaie' AND pid <> pg_backend_pid();" 2>$null

# حذف وإعادة إنشاء قاعدة البيانات
Write-Host "🗑️ حذف قاعدة البيانات rubaie..." -ForegroundColor Yellow
psql -U postgres -c "DROP DATABASE IF EXISTS rubaie;" 2>$null

Write-Host "🏗️ إنشاء قاعدة البيانات rubaie جديدة..." -ForegroundColor Yellow
psql -U postgres -c "CREATE DATABASE rubaie;"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في إنشاء قاعدة البيانات" -ForegroundColor Red
    exit 1
}

# نسخ البيانات
Write-Host "`n📋 نسخ الهيكل والبيانات..." -ForegroundColor Yellow
pg_dump -U postgres mohammi | psql -U postgres rubaie

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم تحديث قاعدة البيانات بنجاح!" -ForegroundColor Green
    
    # عرض الإحصائيات
    Write-Host "`n📊 إحصائيات قاعدة البيانات المحدثة:" -ForegroundColor Cyan
    $stats = psql -U postgres -d rubaie -t -c "
    SELECT 
        'العملاء: ' || COALESCE((SELECT COUNT(*) FROM clients), 0) ||
        ' | القضايا: ' || COALESCE((SELECT COUNT(*) FROM issues), 0) ||
        ' | المحاكم: ' || COALESCE((SELECT COUNT(*) FROM courts), 0) ||
        ' | العملات: ' || COALESCE((SELECT COUNT(*) FROM currencies), 0);"
    Write-Host "   $($stats.Trim())" -ForegroundColor White
    
    Write-Host "`n🎉 تم تحديث قاعدة البيانات rubaie بنجاح!" -ForegroundColor Green
    Write-Host "🚀 يمكنك الآن تشغيل النظام على المنفذ 8914" -ForegroundColor Yellow
    
} else {
    Write-Host "❌ فشل في تحديث قاعدة البيانات" -ForegroundColor Red
}
