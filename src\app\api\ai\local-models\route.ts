import { NextRequest, NextResponse } from 'next/server'
// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة

// دالة معالجة النماذج المحلية
async function handleLocalModel(model: any, message: string, conversationId: string) {
  const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: "${message}". قدم استشارة قانونية مفيدة ومحددة.`

  if (model.type === 'ollama') {
    const response = await fetch(model.endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: model.model,
        prompt: legalPrompt,
        stream: false,
        options: { temperature: 0.7, num_predict: 400 }
      }),
      signal: AbortSignal.timeout(30000)
    })

    if (response.ok) {
      const data = await response.json()
      return NextResponse.json({
        success: true,
        response: data.response + '\n\n---\n🤖 المساعد الذكي للمكتب',
        model: model.name,
        conversationId,
        timestamp: new Date().toISOString(),
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
      })
    }
  }

  // fallback إذا فشل كل شيء
  return NextResponse.json({
    success: true,
    response: generateSmartFallback(message),
    model: 'Fallback System',
    conversationId,
    timestamp: new Date().toISOString(),
    usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
  })
}

// دالة لتوليد رد احتياطي ذكي
function generateSmartFallback(userMessage: string): string {
  if (userMessage.includes('طلاق')) {
    return `بخصوص قضايا الطلاق في القانون اليمني:\n\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\n• يجب مراعاة حقوق الزوجة والأطفال\n• التوثيق أمام المحكمة ضروري\n\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`
  } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {
    return `بخصوص عقود العمل والعقود القانونية:\n\n• حقوق العامل محمية بالقانون اليمني\n• العقد يجب أن يتضمن الراتب وساعات العمل\n• الإجازات والتأمينات حق للعامل\n• يمكن مراجعة وصياغة العقود\n\nنساعدك في حماية حقوقك القانونية.`
  } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {
    return `بخصوص القضايا والمحاكم:\n\n• نمثلك أمام جميع المحاكم اليمنية\n• إعداد المذكرات والمرافعات\n• متابعة القضية في جميع المراحل\n• استشارة قانونية متخصصة\n\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`
  } else {
    return `مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`
  }
}

// إعدادات النماذج - GPT-4 فقط
const LOCAL_MODELS = {
  'openai-gpt4': {
    name: 'GPT-4 (OpenAI)',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    model: 'gpt-4',
    description: 'نموذج GPT-4 من OpenAI - الأفضل للقضايا القانونية والاستشارات',
    type: 'openai-api',
    requiresKey: true
  }
}

// GET - جلب النماذج المتاحة
export async function GET() {
  try {
    // فحص حالة نموذج GPT-4
    const modelsStatus = await Promise.all(
      Object.entries(LOCAL_MODELS).map(async ([key, model]) => {
        try {
          // فحص OpenAI API فقط
          const apiKey = process.env.OPENAI_API_KEY
          if (apiKey) {
            try {
              const testResponse = await fetch('https://api.openai.com/v1/models', {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${apiKey}`,
                  'Content-Type': 'application/json'
                },
                signal: AbortSignal.timeout(10000)
              })

              return {
                key,
                ...model,
                status: testResponse.ok ? 'available' : 'api_error',
                lastChecked: new Date().toISOString()
              }
            } catch (apiError) {
              return {
                key,
                ...model,
                status: 'api_error',
                error: 'فشل في الاتصال بـ OpenAI API',
                lastChecked: new Date().toISOString()
              }
            }
          } else {
            return {
              key,
              ...model,
              status: 'api_key_required',
              error: 'OPENAI_API_KEY مطلوب في متغيرات البيئة',
              lastChecked: new Date().toISOString()
            }
          }
        } catch (error) {
          return {
            key,
            ...model,
            status: 'offline',
            error: error instanceof Error ? error.message : 'خطأ غير معروف',
            lastChecked: new Date().toISOString()
          }
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        models: modelsStatus,
        ollamaService: modelsStatus.some(m => m.status !== 'offline') ? 'online' : 'offline'
      }
    })
  } catch (error) {
    console.error('Error checking local models:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في فحص النماذج المحلية' },
      { status: 500 }
    )
  }
}

// POST - إرسال رسالة للنموذج المحلي
export async function POST(request: NextRequest) {
  try {
    const { message, model = 'codellama', conversationId, context = [] } = await request.json()

    if (!message || !message.trim()) {
      return NextResponse.json(
        { success: false, error: 'الرسالة مطلوبة' },
        { status: 400 }
      )
    }

    console.log('🤖 AI Request:', { message, model, conversationId })

    // تم تعطيل البحث في الملفات المحلية لتحسين السرعة
    console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...')
    const legalSearchResult = { found: false, content: '', sources: [], confidence: 0 }

    const selectedModel = LOCAL_MODELS[model as keyof typeof LOCAL_MODELS]
    if (!selectedModel) {
      return NextResponse.json(
        { success: false, error: 'النموذج المحدد غير متاح' },
        { status: 400 }
      )
    }

    // إعداد السياق للمحادثة القانونية
    const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.

مهامك القانونية فقط:
1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد
2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية
3. شرح الإجراءات القانونية والقوانين اليمنية
4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية
5. الرد باللغة العربية بشكل مهني وودود

قواعد مهمة:
- اجب فقط عن الأسئلة القانونية
- لا تذكر البرمجة أو الكود أو التطوير أبداً
- قدم معلومات قانونية عامة مفيدة
- اشرح الخطوات والإجراءات القانونية بوضوح
- كن مهذباً ومهنياً ومفيداً
- اجعل كل رد مخصص للسؤال القانوني المطروح
- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة

خدمات المكتب القانونية:
- الاستشارات القانونية في جميع المجالات
- صياغة العقود والوثائق القانونية
- التمثيل أمام المحاكم والجهات القضائية
- القضايا المدنية والتجارية والجنائية
- قضايا الأحوال الشخصية والميراث
- القضايا الإدارية والعمالية
- التحكيم وحل النزاعات

رسالة العميل: "${message}"

اجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`

    // إرسال الطلب للنموذج المحلي
    let aiResponse
    let responseText = ''

    if (selectedModel.type === 'openai-compatible' || selectedModel.type === 'openai-api' || selectedModel.type === 'groq-api') {
      // إعداد headers حسب نوع API
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // إضافة API key لـ OpenAI
      const apiKey = process.env.OPENAI_API_KEY
      if (!apiKey) {
        return NextResponse.json({
          success: false,
          error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4.'
        }, { status: 500 })
      }

      headers['Authorization'] = `Bearer ${apiKey}`

      // إعداد الـ prompt المحسن للقضايا القانونية (محسن للسرعة والدقة)
      const legalSystemPrompt = `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.

خبرتك تشمل:
- قانون العمل اليمني رقم 5 لسنة 1995
- قانون الأحوال الشخصية اليمني
- قانون الجرائم والعقوبات اليمني
- قانون المرافعات المدنية والتجارية
- أحكام الميراث في الشريعة الإسلامية
- قانون الشركات والاستثمار اليمني

مهامك:
- تقديم استشارات قانونية دقيقة ومفصلة
- الاستشهاد بالمواد القانونية ذات الصلة
- شرح الإجراءات العملية خطوة بخطوة
- تقديم نصائح عملية قابلة للتطبيق

أسلوبك: مهني، واضح، مفصل، باللغة العربية.`

      const userPrompt = `استشارة قانونية: "${message}"\n\nقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.`

      aiResponse = await fetch(selectedModel.endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          model: selectedModel.model,
          messages: [
            {
              role: 'system',
              content: legalSystemPrompt
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature: 0.7,
          max_tokens: 600,
          stream: false
        }),
        signal: AbortSignal.timeout(45000)
      })
    } else {
      // استخدام Ollama API
      aiResponse = await fetch(selectedModel.endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model,
          prompt: systemPrompt,
          stream: false,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 500,
            stop: ['\n\nUser:', '\n\nالعميل:', '\n\nالمستخدم:']
          }
        }),
        signal: AbortSignal.timeout(30000)
      })
    }

    if (!aiResponse.ok) {
      throw new Error(`AI service error: ${aiResponse.status}`)
    }

    const aiData = await aiResponse.json()
    console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2))

    if (selectedModel.type === 'openai-compatible' || selectedModel.type === 'openai-api' || selectedModel.type === 'groq-api') {
      // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)
      if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {
        responseText = aiData.choices[0].message.content
      } else {
        throw new Error('لم يتم الحصول على رد من النموذج')
      }
    } else {
      // معالجة رد Ollama
      if (!aiData.response) {
        throw new Error('لم يتم الحصول على رد من النموذج')
      }
      responseText = aiData.response
    }

    // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه
    const strongProgrammingKeywords = ['```', 'def ', 'function', 'import ', 'class ', 'مساعدك في البرمجة', 'شرح المفاهيم البرمجية', 'كتابة الكود', 'تطوير المشاريع']
    const isProgrammingResponse = strongProgrammingKeywords.some(keyword =>
      responseText.toLowerCase().includes(keyword.toLowerCase())
    ) || (responseText.includes('برمجة') && responseText.includes('كود'))

    let cleanedResponse = responseText
      .trim()
      .replace(/^(المساعد|الذكي|AI|Assistant):\s*/i, '')
      .replace(/\n{3,}/g, '\n\n')
      .trim()

    // تسجيل الرد الأصلي للتشخيص
    console.log('🔍 Original AI Response:', responseText.substring(0, 200))
    console.log('🔍 Programming keywords detected:', isProgrammingResponse)

    // فلترة محسنة للردود البرمجية
    console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200))
    console.log('🔍 Programming keywords detected:', isProgrammingResponse)

    // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة
    if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {
      console.log('⚠️ Detected clear programming response, using smart fallback')
      cleanedResponse = generateSmartFallback(message)
    } else {

    }



    // إضافة توقيع المساعد الذكي
    if (!cleanedResponse.includes('المساعد الذكي')) {
      cleanedResponse += '\n\n---\n🤖 المساعد الذكي للمكتب'
    }

    return NextResponse.json({
      success: true,
      response: cleanedResponse,
      model: selectedModel.name,
      conversationId,
      timestamp: new Date().toISOString(),
      usage: {
        prompt_tokens: aiData.prompt_eval_count || 0,
        completion_tokens: aiData.eval_count || 0,
        total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)
      }
    })

  } catch (error) {
    console.error('❌ Error with local AI model:', error)
    console.error('📊 Error details:', {
      message: error.message,
      stack: error.stack,
      selectedModel: selectedModel?.name,
      endpoint: selectedModel?.endpoint,
      requestData: { message, model, conversationId }
    })

    // رد احتياطي في حالة فشل النموذج
    const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً.

يرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.

📞 للاستفسارات العاجلة: اتصل بالمكتب
💬 أو انتظر قليلاً وحاول مرة أخرى

---
🤖 المساعد الذكي للمكتب (وضع الطوارئ)`

    return NextResponse.json({
      success: false,
      error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,
      fallback_response: fallbackResponse,
      model: selectedModel?.name || 'unknown',
      conversationId,
      timestamp: new Date().toISOString(),
      details: {
        errorType: error.constructor.name,
        errorMessage: error.message,
        selectedModel: selectedModel?.name
      }
    }, { status: 500 })
  }
}