(()=>{var e={};e.id=4657,e.ids=[4657],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>p});var a=r(64939),o=r(29021),n=r.n(o),c=r(33873),i=r.n(c),u=e([a]);a=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let E=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new a.Pool(E);async function p(e,t){let r=await l.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23980:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>E});var a=r(96559),o=r(48088),n=r(37719),c=r(28763),i=e([c]);c=(i.then?(await i)():i)[0];let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/vouchers/route",pathname:"/api/accounting/vouchers",filename:"route",bundlePath:"app/api/accounting/vouchers/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\vouchers\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:E,serverHooks:l}=p;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:E})}s()}catch(e){s(e)}})},28763:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>p,GET:()=>i,POST:()=>c,PUT:()=>u});var a=r(32190),o=r(5069),n=e([o]);async function c(e){try{let{reference:t,transdate:r,description:s,person_id:n,notes:c,journal_entries:i}=await e.json();if(!t||!r||!i||i.length<2)return a.NextResponse.json({success:!1,error:"البيانات المطلوبة ناقصة"},{status:400});let u=i.reduce((e,t)=>e+(t.debit_amount||0),0),p=i.reduce((e,t)=>e+(t.credit_amount||0),0);if(Math.abs(u-p)>.01)return a.NextResponse.json({success:!1,error:"القيود غير متوازنة"},{status:400});await (0,o.P)("BEGIN");try{let e=(await (0,o.P)(`
        INSERT INTO gl (reference, description, transdate, person_id, notes, approved)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `,[t,s,r,n,c,!1])).rows[0].id;for(let t of i)if(t.account_id&&(t.debit_amount>0||t.credit_amount>0)){let s=t.debit_amount>0?t.debit_amount:-t.credit_amount;await (0,o.P)(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
          `,[e,t.account_id,s,r,t.memo,!1])}return await (0,o.P)("COMMIT"),a.NextResponse.json({success:!0,message:"تم حفظ السند بنجاح",data:{id:e,reference:t}})}catch(e){throw await (0,o.P)("ROLLBACK"),e}}catch(e){return console.error("Error saving voucher:",e),a.NextResponse.json({success:!1,error:"فشل في حفظ السند"},{status:500})}}async function i(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),n=(r-1)*s,c=await (0,o.P)(`
      SELECT 
        gl.*,
        COUNT(at.entry_id) as entries_count,
        SUM(CASE WHEN at.amount > 0 THEN at.amount ELSE 0 END) as total_debit,
        SUM(CASE WHEN at.amount < 0 THEN ABS(at.amount) ELSE 0 END) as total_credit
      FROM gl
      LEFT JOIN acc_trans at ON gl.id = at.trans_id
      GROUP BY gl.id
      ORDER BY gl.transdate DESC, gl.id DESC
      LIMIT $1 OFFSET $2
    `,[s,n]),i=await (0,o.P)("SELECT COUNT(*) as total FROM gl"),u=parseInt(i.rows[0].total);return a.NextResponse.json({success:!0,data:c.rows,pagination:{page:r,limit:s,total:u,pages:Math.ceil(u/s)}})}catch(e){return console.error("Error fetching vouchers:",e),a.NextResponse.json({success:!1,error:"فشل في جلب السندات"},{status:500})}}async function u(e){try{let{id:t,reference:r,description:s,transdate:n,person_id:c,notes:i,approved:u}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف السند مطلوب"},{status:400});let p=await (0,o.P)(`
      UPDATE gl 
      SET 
        reference = COALESCE($2, reference),
        description = COALESCE($3, description),
        transdate = COALESCE($4, transdate),
        person_id = $5,
        notes = COALESCE($6, notes),
        approved = COALESCE($7, approved),
        approved_at = CASE WHEN $7 = true THEN CURRENT_TIMESTAMP ELSE approved_at END
      WHERE id = $1
      RETURNING *
    `,[t,r,s,n,c,i,u]);if(0===p.rows.length)return a.NextResponse.json({success:!1,error:"السند غير موجود"},{status:404});return u&&await (0,o.P)(`
        UPDATE acc_trans 
        SET approved = true 
        WHERE trans_id = $1
      `,[t]),a.NextResponse.json({success:!0,message:"تم تحديث السند بنجاح",data:p.rows[0]})}catch(e){return console.error("Error updating voucher:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث السند"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return a.NextResponse.json({success:!1,error:"معرف السند مطلوب"},{status:400});let s=await (0,o.P)("SELECT approved FROM gl WHERE id = $1",[r]);if(0===s.rows.length)return a.NextResponse.json({success:!1,error:"السند غير موجود"},{status:404});if(s.rows[0].approved)return a.NextResponse.json({success:!1,error:"لا يمكن حذف سند معتمد"},{status:400});await (0,o.P)("BEGIN");try{return await (0,o.P)("DELETE FROM acc_trans WHERE trans_id = $1",[r]),await (0,o.P)("DELETE FROM gl WHERE id = $1",[r]),await (0,o.P)("COMMIT"),a.NextResponse.json({success:!0,message:"تم حذف السند بنجاح"})}catch(e){throw await (0,o.P)("ROLLBACK"),e}}catch(e){return console.error("Error deleting voucher:",e),a.NextResponse.json({success:!1,error:"فشل في حذف السند"},{status:500})}}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(23980));module.exports=s})();