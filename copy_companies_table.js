// نسخ جدول الشركات من mohammi إلى rubaie
const { Pool } = require('pg');

async function copyCompaniesTable() {
  console.log('🔄 بدء نسخ جدول الشركات من mohammi إلى rubaie...\n');

  // الاتصال بقاعدة البيانات الأصلية (mohammi)
  const sourcePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'mohammi',
    password: 'yemen123',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات الهدف (rubaie)
  const targetPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    console.log('📋 الخطوة 1: جلب هيكل جدول الشركات من mohammi...');
    
    // جلب هيكل الجدول
    const structureResult = await sourcePool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'companies' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log(`   ✅ تم العثور على ${structureResult.rows.length} عمود`);
    
    console.log('📋 الخطوة 2: إنشاء جدول الشركات في rubaie...');
    
    // إنشاء جدول الشركات في قاعدة البيانات الهدف
    await targetPool.query(`
      CREATE TABLE IF NOT EXISTS companies (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        legal_name VARCHAR(255),
        description TEXT,
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        phone VARCHAR(50),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url VARCHAR(500),
        logo_image_url VARCHAR(500),
        logo_right_text TEXT,
        logo_left_text TEXT,
        established_date DATE,
        registration_number VARCHAR(100),
        legal_form VARCHAR(100),
        capital DECIMAL(15,2),
        tax_number VARCHAR(100),
        is_active BOOLEAN DEFAULT true,
        working_hours TEXT,
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('   ✅ تم إنشاء جدول الشركات');
    
    console.log('📋 الخطوة 3: جلب بيانات الشركات من mohammi...');
    
    // جلب البيانات من الجدول الأصلي
    const dataResult = await sourcePool.query('SELECT * FROM companies');
    const companies = dataResult.rows;
    
    console.log(`   📊 تم العثور على ${companies.length} شركة`);
    
    if (companies.length === 0) {
      console.log('⚠️  لا توجد بيانات شركات للنسخ');
      return;
    }
    
    console.log('📋 الخطوة 4: حذف البيانات الموجودة في rubaie...');
    
    // حذف البيانات الموجودة
    await targetPool.query('DELETE FROM companies');
    
    console.log('📋 الخطوة 5: نسخ البيانات إلى rubaie...');
    
    // نسخ البيانات شركة بشركة
    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      
      console.log(`   📄 نسخ الشركة ${i + 1}/${companies.length}: ${company.name || 'بدون اسم'}`);
      
      try {
        // تحضير البيانات للإدراج
        const columns = Object.keys(company).filter(key => key !== 'id'); // استبعاد id للـ auto increment
        const values = columns.map(col => company[col]);
        const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
        const columnsStr = columns.join(', ');
        
        const insertQuery = `
          INSERT INTO companies (${columnsStr}) 
          VALUES (${placeholders})
        `;
        
        await targetPool.query(insertQuery, values);
        
      } catch (insertError) {
        console.log(`   ❌ خطأ في نسخ الشركة ${company.name}:`, insertError.message);
        
        // محاولة إدراج بدون الأعمدة المشكلة
        try {
          const basicColumns = ['name', 'legal_name', 'description', 'address', 'city', 'country', 'phone', 'email', 'website', 'is_active'];
          const basicValues = basicColumns.map(col => company[col] || '');
          const basicPlaceholders = basicValues.map((_, index) => `$${index + 1}`).join(', ');
          
          const basicInsertQuery = `
            INSERT INTO companies (${basicColumns.join(', ')}) 
            VALUES (${basicPlaceholders})
          `;
          
          await targetPool.query(basicInsertQuery, basicValues);
          console.log(`   ✅ تم إدراج الشركة بالبيانات الأساسية`);
          
        } catch (basicError) {
          console.log(`   ❌ فشل في إدراج الشركة نهائياً:`, basicError.message);
        }
      }
    }
    
    console.log('\n🎉 تم الانتهاء من نسخ جدول الشركات بنجاح!');
    
    // التحقق من النتائج
    const verifyResult = await targetPool.query('SELECT COUNT(*) as count FROM companies');
    console.log(`📊 عدد الشركات في rubaie: ${verifyResult.rows[0].count}`);
    
    // عرض أسماء الشركات المنسوخة
    const companiesResult = await targetPool.query('SELECT id, name FROM companies ORDER BY id');
    console.log('\n📋 الشركات المنسوخة:');
    companiesResult.rows.forEach(company => {
      console.log(`   ${company.id}. ${company.name}`);
    });

  } catch (error) {
    console.error('❌ خطأ عام في نسخ جدول الشركات:', error);
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }
}

// تشغيل النسخ
copyCompaniesTable().catch(console.error);
