# سكريپت تشغيل خادم واحد
# Start Single Server Script

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("mohammi", "rubaie")]
    [string]$System
)

Write-Host "🚀 تشغيل نظام $System" -ForegroundColor Green

# إعدادات النظامين
$configs = @{
    "mohammi" = @{
        Port = 7443
        Database = "mohammi"
        Name = "نظام إدارة المحاماة - محمد"
        Env = @"
DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/mohammi
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mohammi
DB_USER=postgres
DB_PASSWORD=yemen123
PORT=7443
NODE_ENV=development
NEXTAUTH_SECRET=mohammi-secret-key-2025
NEXTAUTH_URL=http://localhost:7443
APP_NAME=نظام إدارة المحاماة - محمد
NEXT_TELEMETRY_DISABLED=1
"@
    }
    "rubaie" = @{
        Port = 8914
        Database = "rubaie"
        Name = "نظام إدارة المحاماة - الربعي"
        Env = @"
DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/rubaie
DB_HOST=localhost
DB_PORT=5432
DB_NAME=rubaie
DB_USER=postgres
DB_PASSWORD=yemen123
PORT=8914
NODE_ENV=development
NEXTAUTH_SECRET=rubaie-secret-key-2025
NEXTAUTH_URL=http://localhost:8914
APP_NAME=نظام إدارة المحاماة - الربعي
NEXT_TELEMETRY_DISABLED=1
"@
    }
}

$config = $configs[$System]

# التحقق من قاعدة البيانات
Write-Host "`n🔍 التحقق من قاعدة البيانات..." -ForegroundColor Yellow
$env:PGPASSWORD = "yemen123"
$dbExists = psql -U postgres -h localhost -lqt | Select-String $config.Database

if (-not $dbExists) {
    Write-Host "❌ قاعدة البيانات '$($config.Database)' غير موجودة" -ForegroundColor Red
    
    if ($System -eq "rubaie") {
        Write-Host "💡 نسخ قاعدة البيانات من mohammi..." -ForegroundColor Yellow
        .\quick_copy_db.ps1
    } else {
        Write-Host "❌ لا يمكن المتابعة بدون قاعدة البيانات" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ قاعدة البيانات متوفرة" -ForegroundColor Green

# إعداد ملف البيئة
Write-Host "`n📋 إعداد متغيرات البيئة..." -ForegroundColor Yellow
$config.Env | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ تم إعداد متغيرات البيئة" -ForegroundColor Green

# عرض معلومات النظام
Write-Host "`n📊 معلومات النظام:" -ForegroundColor Cyan
Write-Host "   🏢 النظام: $($config.Name)" -ForegroundColor White
Write-Host "   🌐 الرابط: http://localhost:$($config.Port)" -ForegroundColor White
Write-Host "   🗄️ قاعدة البيانات: $($config.Database)" -ForegroundColor White

# تشغيل الخادم
Write-Host "`n🚀 بدء تشغيل الخادم..." -ForegroundColor Green
Write-Host "⏹️ للإيقاف: اضغط Ctrl+C" -ForegroundColor Yellow

npm run dev
