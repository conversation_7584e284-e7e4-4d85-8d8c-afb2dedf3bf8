'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: number
  username: string
  name: string
  type: 'user' | 'client'
  role?: string
  role_display_name?: string
  permissions?: string[]
  user_type?: 'admin' | 'user'
  token: string
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = () => {
    try {
      const userSession = localStorage.getItem('userSession')
      if (userSession) {
        const userData = JSON.parse(userSession)
        setUser(userData)
        
        // حفظ في الكوكيز أيضاً للـ middleware
        document.cookie = `userSession=${userSession}; path=/; max-age=86400`
      } else {
        // إذا لم توجد جلسة، توجيه للدخول
        router.push('/login')
      }
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error)
      logout()
    } finally {
      setLoading(false)
    }
  }

  const login = (userData: User) => {
    setUser(userData)
    localStorage.setItem('userSession', JSON.stringify(userData))
    document.cookie = `userSession=${JSON.stringify(userData)}; path=/; max-age=86400`
    
    // توجيه حسب نوع المستخدم
    if (userData.type === 'client') {
      router.push('/client-portal')
    } else {
      router.push('/dashboard')
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem('userSession')
    localStorage.removeItem('clientToken')
    document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    router.push('/login')
  }

  const hasPermission = (permission: string): boolean => {
    if (!user || !user.permissions) return false

    // مدير النظام لديه جميع الصلاحيات
    if (user.user_type === 'admin' || user.role === 'admin') return true

    // فحص الصلاحية المحددة
    return user.permissions.includes(permission) ||
           user.permissions.includes('system_admin') ||
           user.permissions.includes('all')
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

    const isAdmin = (): boolean => {
      return user?.user_type === 'admin' ||
            user?.role === 'admin' ||
            user?.role === 'super_admin' ||
            hasPermission('system_admin')
    }

    const isManager = (): boolean => {
      return user?.user_type === 'admin' ||
            user?.role === 'manager' ||
            user?.role === 'admin' ||
            user?.role === 'super_admin' ||
            hasPermission('system_admin')
    }

  const canAddFollows = (): boolean => {
    // 🚨 إيقاف فحص الصلاحيات مؤقتاً - جميع المستخدمين يمكنهم إضافة المتابعات
    return true

    // الكود الأصلي (معطل مؤقتاً):
    // return hasAnyPermission(['add_follows', 'manage_follows', 'manage_users'])
  }

  const canEditFollows = (): boolean => {
    // 🚨 إيقاف فحص الصلاحيات مؤقتاً - جميع المستخدمين يمكنهم تعديل المتابعات
    return true

    // الكود الأصلي (معطل مؤقتاً):
    // return hasAnyPermission(['manage_follows', 'manage_users'])
  }

  const canDeleteFollows = (): boolean => {
    // 🚨 إيقاف فحص الصلاحيات مؤقتاً - جميع المستخدمين يمكنهم حذف المتابعات
    return true

    // الكود الأصلي (معطل مؤقتاً):
    // return hasAnyPermission(['manage_follows', 'manage_users'])
  }

  const canApproveFollows = (): boolean => {
    // 🚨 إيقاف فحص الصلاحيات مؤقتاً - جميع المستخدمين يمكنهم اعتماد المتابعات
    return true

    // الكود الأصلي (معطل مؤقتاً):
    // return hasAnyPermission(['approve_follows', 'manage_users'])
  }

  return {
    user,
    loading,
    login,
    logout,
    hasPermission,
    hasAnyPermission,
    isAdmin,
    isManager,
    canAddFollows,
    canEditFollows,
    canDeleteFollows,
    canApproveFollows,
    checkAuth
  }
}