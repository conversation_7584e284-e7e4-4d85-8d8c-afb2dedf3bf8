"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-settings/page",{

/***/ "(app-pages-browser)/./src/app/admin/ai-settings/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/ai-settings/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AISettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AISettingsPage() {\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: true,\n        model: 'openai-gpt4',\n        delay_seconds: 3,\n        working_hours_only: false,\n        working_hours_start: '00:00',\n        working_hours_end: '23:59',\n        working_days: [\n            'الأحد',\n            'الاثنين',\n            'الثلاثاء',\n            'الأربعاء',\n            'الخميس',\n            'الجمعة',\n            'السبت'\n        ],\n        max_responses_per_conversation: 20,\n        keywords_trigger: [\n            'مساعدة',\n            'استفسار',\n            'سؤال',\n            'معلومات',\n            'خدمة',\n            'مرحبا',\n            'السلام',\n            'أهلا',\n            'استشارة',\n            'قانوني',\n            'محامي'\n        ],\n        excluded_keywords: [],\n        auto_responses: {\n            greeting: 'مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟',\n            working_hours: 'أنا متاح للمساعدة على مدار 24 ساعة. كيف يمكنني مساعدتك؟',\n            max_reached: 'تم الوصول للحد الأقصى من الردود التلقائية. سيقوم أحد المحامين بالرد عليك قريباً.'\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testMessage, setTestMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('مرحبا، أحتاج استشارة قانونية');\n    const [testResponse, setTestResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AISettingsPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"AISettingsPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        setLoading(true);\n        try {\n            // جلب النماذج المتاحة\n            const modelsResponse = await fetch('/api/ai/local-models');\n            const modelsData = await modelsResponse.json();\n            // جلب الإعدادات من API الجديد\n            const settingsResponse = await fetch('/api/ai/settings');\n            const settingsData = await settingsResponse.json();\n            if (modelsData.success) {\n                setModels(modelsData.data.models || []);\n            }\n            if (settingsData.success) {\n                setSettings(settingsData.data);\n            }\n        } catch (error) {\n            console.error('Error fetching data:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في جلب البيانات'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage(null);\n        try {\n            const response = await fetch('/api/ai/settings', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMessage({\n                    type: 'success',\n                    text: 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح'\n                });\n                // إعادة جلب الإعدادات للتأكد من الحفظ\n                setTimeout(()=>{\n                    fetchData();\n                }, 1000);\n            } else {\n                setMessage({\n                    type: 'error',\n                    text: result.error || 'فشل في حفظ الإعدادات'\n                });\n            }\n        } catch (error) {\n            console.error('Error saving settings:', error);\n            setMessage({\n                type: 'error',\n                text: 'حدث خطأ في حفظ الإعدادات'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleTestAI = async ()=>{\n        if (!testMessage.trim()) {\n            setMessage({\n                type: 'error',\n                text: 'يرجى إدخال رسالة للاختبار'\n            });\n            return;\n        }\n        setTesting(true);\n        setTestResponse('');\n        setMessage(null);\n        try {\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: testMessage,\n                    model: settings.model,\n                    conversationId: 'test-' + Date.now()\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.response) {\n                setTestResponse(data.response);\n                setMessage({\n                    type: 'success',\n                    text: 'تم اختبار الذكاء الاصطناعي بنجاح!'\n                });\n            } else {\n                throw new Error(data.error || 'لم يتم الحصول على رد من النموذج');\n            }\n        } catch (error) {\n            console.error('Error testing AI:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في اختبار الذكاء الاصطناعي: ' + error.message\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const testAI = async ()=>{\n        setTesting(true);\n        setMessage(null);\n        try {\n            console.log('🧪 بدء اختبار الذكاء الاصطناعي...');\n            console.log('النموذج المحدد:', settings.model);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: 'مرحبا، هذا اختبار للنظام',\n                    model: settings.model,\n                    conversationId: 'quick-test-' + Date.now()\n                })\n            });\n            console.log('📡 استجابة الخادم:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP Error: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (result.success && result.response) {\n                setMessage({\n                    type: 'success',\n                    text: \"تم اختبار النموذج بنجاح! الرد: \".concat(result.response.substring(0, 100), \"...\")\n                });\n            } else {\n                setMessage({\n                    type: 'error',\n                    text: result.error || 'لم يتم الحصول على رد من النموذج'\n                });\n            }\n        } catch (error) {\n            console.error('❌ خطأ في اختبار الذكاء الاصطناعي:', error);\n            setMessage({\n                type: 'error',\n                text: \"فشل في الاتصال بالنموذج المحلي: \".concat(error.message)\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'available':\n                return 'bg-green-100 text-green-800';\n            case 'not_found':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'service_unavailable':\n                return 'bg-orange-100 text-orange-800';\n            case 'offline':\n                return 'bg-red-100 text-red-800';\n            case 'api_key_required':\n                return 'bg-blue-100 text-blue-800';\n            case 'api_error':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'available':\n                return 'متاح';\n            case 'not_found':\n                return 'غير موجود';\n            case 'service_unavailable':\n                return 'الخدمة غير متاحة';\n            case 'offline':\n                return 'غير متصل';\n            case 'api_key_required':\n                return 'يحتاج API Key';\n            case 'api_error':\n                return 'خطأ في API';\n            default:\n                return 'غير معروف';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"إعدادات الذكاء الاصطناعي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"إدارة النماذج المحلية والرد التلقائي للعملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: fetchData,\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: testAI,\n                                    variant: \"outline\",\n                                    disabled: testing || !settings.enabled,\n                                    children: [\n                                        testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"اختبار النموذج\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleSave,\n                                    disabled: saving,\n                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                    children: [\n                                        saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"حفظ الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                    className: message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50',\n                    children: [\n                        message.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                            className: message.type === 'success' ? 'text-green-800' : 'text-red-800',\n                            children: message.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حالة النماذج المحلية والخارجية\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\",\n                                                children: \"محسن للسرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: fetchData,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2 \".concat(loading ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: model.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                            className: getStatusColor(model.status),\n                                                            children: getStatusText(model.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                    children: model.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"النموذج: \",\n                                                                model.model\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"آخر فحص: \",\n                                                                new Date(model.lastChecked).toLocaleString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        model.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 mt-1\",\n                                                            children: [\n                                                                \"خطأ: \",\n                                                                model.error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, model.key, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الإعدادات العامة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-base font-medium\",\n                                                    children: \"تفعيل الرد التلقائي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"تشغيل أو إيقاف المساعد الذكي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                            checked: settings.enabled,\n                                            onCheckedChange: (checked)=>setSettings({\n                                                    ...settings,\n                                                    enabled: checked\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"النموذج المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: settings.model,\n                                                    onValueChange: (value)=>setSettings({\n                                                            ...settings,\n                                                            model: value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: models.filter((m)=>m.status === 'available').map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: model.key,\n                                                                    children: model.name\n                                                                }, model.key, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"تأخير الرد (بالثواني)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"number\",\n                                                    value: settings.delay_seconds,\n                                                    onChange: (e)=>setSettings({\n                                                            ...settings,\n                                                            delay_seconds: parseInt(e.target.value)\n                                                        }),\n                                                    min: \"5\",\n                                                    max: \"300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الحد الأقصى للردود لكل محادثة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            type: \"number\",\n                                            value: settings.max_responses_per_conversation,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    max_responses_per_conversation: parseInt(e.target.value)\n                                                }),\n                                            min: \"1\",\n                                            max: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"ساعات العمل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-base font-medium\",\n                                                    children: \"حالة التشغيل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"النظام يعمل 24 ساعة طالما مفتاح التشغيل مفعل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"متاح 24/7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                            children: \"تم تعطيل قيود ساعات العمل. المساعد الذكي متاح على مدار 24 ساعة طالما أن مفتاح التشغيل مفعل.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"ساعات العمل الافتراضية (للعرض فقط)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"time\",\n                                                    value: \"00:00\",\n                                                    disabled: true,\n                                                    className: \"bg-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"بداية: منتصف الليل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"نهاية ساعات العمل (للعرض فقط)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"time\",\n                                                    value: \"23:59\",\n                                                    disabled: true,\n                                                    className: \"bg-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"نهاية: قبل منتصف الليل بدقيقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الكلمات المفتاحية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الكلمات المحفزة للرد (مفصولة بفاصلة)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.keywords_trigger.join(', '),\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    keywords_trigger: e.target.value.split(',').map((k)=>k.trim()).filter((k)=>k)\n                                                }),\n                                            placeholder: \"مساعدة, استفسار, سؤال, معلومات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الكلمات المستبعدة (مفصولة بفاصلة)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.excluded_keywords.join(', '),\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    excluded_keywords: e.target.value.split(',').map((k)=>k.trim()).filter((k)=>k)\n                                                }),\n                                            placeholder: \"عاجل, طارئ, مهم جداً, محامي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الردود التلقائية المحددة مسبقاً\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الترحيب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.greeting,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        greeting: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة خارج ساعات العمل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.working_hours,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        working_hours: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الوصول للحد الأقصى\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.max_reached,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        max_reached: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"اختبار الذكاء الاصطناعي\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الاختبار\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: testMessage,\n                                            onChange: (e)=>setTestMessage(e.target.value),\n                                            placeholder: \"اكتب رسالة لاختبار الذكاء الاصطناعي...\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleTestAI,\n                                    disabled: testing || !testMessage.trim(),\n                                    className: \"w-full\",\n                                    children: testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"جاري الاختبار...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"اختبار الذكاء الاصطناعي\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                testResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رد الذكاء الاصطناعي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"whitespace-pre-wrap text-sm\",\n                                                children: testResponse\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(AISettingsPage, \"f1PA+25c0EetKv05TXNYojgeFsU=\");\n_c = AISettingsPage;\nvar _c;\n$RefreshReg$(_c, \"AISettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/ai-settings/page.tsx\n"));

/***/ })

});