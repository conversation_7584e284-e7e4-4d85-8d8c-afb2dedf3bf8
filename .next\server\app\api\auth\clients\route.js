(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={2817:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{DELETE:()=>p,GET:()=>l,POST:()=>c});var n=t(32190),o=t(5069),a=t(43205),i=t.n(a),u=e([o]);o=(u.then?(await u)():u)[0];let d=process.env.JWT_SECRET||"your-secret-key";async function c(e){try{let{username:s,password:t}=await e.json();if(!s||!t)return n.NextResponse.json({success:!1,error:"اسم المستخدم وكلمة المرور مطلوبان"},{status:400});let r=await (0,o.P)(`
      SELECT * FROM clients
      WHERE username = $1
    `,[s]);if(0===r.rows.length)return n.NextResponse.json({success:!1,error:"اسم المستخدم أو كلمة المرور غير صحيحة"},{status:401});let a=r.rows[0];console.log("\uD83D\uDD10 التحقق من كلمة المرور للعميل:",s),console.log("\uD83D\uDCCB كلمة المرور المدخلة:",t),console.log("\uD83D\uDCCB كلمة المرور المحفوظة:",a.password_hash);let u=s.split("_")[1]||t;if(t!==u&&t!==a.password_hash)return await (0,o.P)(`
        UPDATE clients
        SET login_attempts = login_attempts + 1
        WHERE id = $1
      `,[a.id]),n.NextResponse.json({success:!1,error:"اسم المستخدم أو كلمة المرور غير صحيحة"},{status:401});if("active"!==a.status)return n.NextResponse.json({success:!1,error:"حساب العميل غير نشط"},{status:403});if(a.login_attempts>=5)return await (0,o.P)(`
        UPDATE clients
        SET locked_until = CURRENT_TIMESTAMP + INTERVAL '30 minutes'
        WHERE id = $1
      `,[a.id]),n.NextResponse.json({success:!1,error:"تم قفل الحساب مؤقتاً بسبب محاولات دخول فاشلة متعددة"},{status:423});if(a.locked_until&&new Date(a.locked_until)>new Date)return n.NextResponse.json({success:!1,error:"الحساب مقفل مؤقتاً، يرجى المحاولة لاحقاً"},{status:423});await (0,o.P)(`
      UPDATE clients
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0,
          locked_until = NULL
      WHERE id = $1
    `,[a.id]);let c=i().sign({clientId:a.id,username:a.username,type:"client"},d,{expiresIn:"24h"}),{password_hash:l,...p}=a;return n.NextResponse.json({success:!0,message:"تم تسجيل الدخول بنجاح",user:p,token:c})}catch(e){return console.error("Error during client login:",e),n.NextResponse.json({success:!1,error:"حدث خطأ في تسجيل الدخول"},{status:500})}}async function l(e){try{let s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return n.NextResponse.json({success:!1,error:"رمز المصادقة مطلوب"},{status:401});let t=s.substring(7),r=i().verify(t,d),a=await (0,o.P)(`
      SELECT * FROM clients
      WHERE id = $1 AND status = 'active'
    `,[r.clientId]);if(0===a.rows.length)return n.NextResponse.json({success:!1,error:"العميل غير موجود أو غير نشط"},{status:401});let{password_hash:u,...c}=a.rows[0];return n.NextResponse.json({success:!0,user:c})}catch(e){return console.error("Error verifying client token:",e),n.NextResponse.json({success:!1,error:"رمز المصادقة غير صالح"},{status:401})}}async function p(e){try{let s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return n.NextResponse.json({success:!1,error:"رمز المصادقة مطلوب"},{status:401});let t=s.substring(7),r=i().verify(t,d);return await (0,o.P)(`
      UPDATE clients
      SET is_online = false
      WHERE id = $1
    `,[r.clientId]),n.NextResponse.json({success:!0,message:"تم تسجيل الخروج بنجاح"})}catch(e){return console.error("Error during client logout:",e),n.NextResponse.json({success:!1,error:"حدث خطأ في تسجيل الخروج"},{status:500})}}r()}catch(e){r(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.d(s,{P:()=>l});var n=t(64939),o=t(29021),a=t.n(o),i=t(33873),u=t.n(i),c=e([n]);n=(c.then?(await c)():c)[0];let p=null;try{let e=u().join(process.cwd(),"routing.config.json"),s=a().readFileSync(e,"utf8");p=JSON.parse(s)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let s=p.routes[e],t=p.default_config;return{database:s.database,user:t.db_user,host:t.db_host,password:process.env.DB_PASSWORD||t.db_password,port:t.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),h=new n.Pool(d);async function l(e,s){let t=await h.connect();try{return await t.query(e,s)}finally{t.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26782:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var n=t(96559),o=t(48088),a=t(37719),i=t(2817),u=e([i]);i=(u.then?(await u)():u)[0];let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/clients/route",pathname:"/api/auth/clients",filename:"route",bundlePath:"app/api/auth/clients/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\auth\\clients\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:h}=l;function c(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580,3205],()=>t(26782));module.exports=r})();