'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  FolderOpen, 
  RefreshCw, 
  Settings, 
  FileText, 
  Download,
  AlertCircle,
  CheckCircle,
  Folder,
  Save,
  Eye,
  Sync
} from 'lucide-react'
import toast from 'react-hot-toast'

interface FileInfo {
  name: string
  path: string
  size: number
  extension: string
  modified: string
  created: string
}

interface LibrarySettings {
  legal_library_path: string
  legal_library_max_file_size: number
  legal_library_allowed_extensions: string
  legal_library_auto_scan: boolean
  legal_library_backup_enabled: boolean
}

export function LegalLibraryManager() {
  const [files, setFiles] = useState<FileInfo[]>([])
  const [settings, setSettings] = useState<LibrarySettings | null>(null)
  const [currentPath, setCurrentPath] = useState('')
  const [newPath, setNewPath] = useState('')
  const [loading, setLoading] = useState(false)
  const [syncing, setSyncing] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  // تحميل البيانات الأولية
  useEffect(() => {
    loadLibraryData()
  }, [])

  // تحميل بيانات المكتبة
  const loadLibraryData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/legal-library/files?action=list')
      const result = await response.json()

      if (result.success) {
        setFiles(result.data.files || [])
        setSettings(result.data.settings || {})
        setCurrentPath(result.data.path || '')
        setNewPath(result.data.path || '')
      } else {
        toast.error(result.error || 'خطأ في تحميل البيانات')
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات المكتبة:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  // تحديث مسار المكتبة
  const updateLibraryPath = async () => {
    if (!newPath.trim()) {
      toast.error('يجب إدخال مسار صحيح')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/system-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          setting_key: 'legal_library_path',
          setting_value: newPath.trim()
        })
      })

      const result = await response.json()

      if (result.success) {
        setCurrentPath(newPath.trim())
        toast.success('تم تحديث مسار المكتبة بنجاح')
        // إعادة تحميل البيانات
        await loadLibraryData()
      } else {
        toast.error(result.error || 'خطأ في تحديث المسار')
      }
    } catch (error) {
      console.error('خطأ في تحديث المسار:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  // مزامنة الملفات
  const syncFiles = async () => {
    setSyncing(true)
    try {
      const response = await fetch('/api/legal-library/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'sync'
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message || 'تم مزامنة الملفات بنجاح')
        // إعادة تحميل البيانات
        await loadLibraryData()
      } else {
        toast.error(result.error || 'خطأ في المزامنة')
      }
    } catch (error) {
      console.error('خطأ في مزامنة الملفات:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setSyncing(false)
    }
  }

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 space-x-reverse">
          <FolderOpen className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">إدارة ملفات المكتبة القانونية</h2>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <Button
            onClick={() => setShowSettings(!showSettings)}
            variant="outline"
            size="sm"
          >
            <Settings className="h-4 w-4 mr-2" />
            الإعدادات
          </Button>
          
          <Button
            onClick={loadLibraryData}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* إعدادات المسار */}
      {showSettings && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              إعدادات المكتبة القانونية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="library-path">مسار مجلد المكتبة القانونية</Label>
              <div className="flex items-center space-x-2 space-x-reverse mt-2">
                <Input
                  id="library-path"
                  value={newPath}
                  onChange={(e) => setNewPath(e.target.value)}
                  placeholder="/home/<USER>/Downloads/legal-system/laws"
                  className="flex-1"
                />
                <Button
                  onClick={updateLibraryPath}
                  disabled={loading || newPath === currentPath}
                >
                  <Save className="h-4 w-4 mr-2" />
                  حفظ
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                المسار الحالي: {currentPath}
              </p>
            </div>

            {settings && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <Label>الامتدادات المسموحة</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {settings.legal_library_allowed_extensions || '.pdf,.doc,.docx,.txt'}
                  </p>
                </div>
                <div>
                  <Label>الحد الأقصى لحجم الملف</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {settings.legal_library_max_file_size || 50} ميجابايت
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* معلومات المجلد */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <Folder className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">مجلد المكتبة القانونية</h3>
                <p className="text-sm text-gray-500">{currentPath}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Badge variant="secondary">
                {files.length} ملف
              </Badge>
              
              <Button
                onClick={syncFiles}
                disabled={syncing}
                size="sm"
              >
                <Sync className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
                {syncing ? 'جاري المزامنة...' : 'مزامنة الملفات'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة الملفات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            الملفات الموجودة ({files.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-blue-600 mb-2" />
              <p className="text-gray-500">جاري تحميل الملفات...</p>
            </div>
          ) : files.length === 0 ? (
            <div className="text-center py-8">
              <FolderOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">لا توجد ملفات في المجلد</p>
              <p className="text-sm text-gray-400 mt-1">
                أضف ملفات إلى المجلد: {currentPath}
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">{file.name}</h4>
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <span>{formatFileSize(file.size)}</span>
                        <span>{file.extension}</span>
                        <span>آخر تعديل: {formatDate(file.modified)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Badge variant="outline">
                      {file.extension}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* تعليمات */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>تعليمات:</strong>
          <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
            <li>أضف الملفات مباشرة إلى المجلد المحدد في الإعدادات</li>
            <li>اضغط على "مزامنة الملفات" لإضافة الملفات الجديدة إلى قاعدة البيانات</li>
            <li>يمكن تغيير مسار المجلد من الإعدادات</li>
            <li>الامتدادات المدعومة: PDF, DOC, DOCX, TXT</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  )
}
