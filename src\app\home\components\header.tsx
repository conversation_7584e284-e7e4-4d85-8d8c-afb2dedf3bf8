'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, LogIn } from 'lucide-react';

type CompanyData = {
  id: number;
  name: string;
  legal_name: string;
  logo_url: string;
  logo_image_url: string;
};

interface HeaderProps {
  companyData: CompanyData;
  onContactClick?: () => void;
}

export function Header({ companyData, onContactClick }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Add scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const navLinks = [
    { href: '#home', label: 'الرئيسية' },
    { href: '#services', label: 'خدماتنا' },
    { href: '#library', label: 'المكتبة القانونية' },
    { href: '#about', label: 'من نحن' },
    { href: '#contact', label: 'اتصل بنا' },
  ];

  return (
    <header
      className={`fixed w-full z-50 transition-all duration-500 ${
        isScrolled
          ? 'backdrop-blur-md shadow-xl py-3'
          : 'py-6'
      }`}
      style={{
        background: isScrolled
          ? 'linear-gradient(135deg, rgba(51, 51, 51, 0.95) 0%, rgba(23, 23, 23, 0.95) 100%)'
          : 'linear-gradient(135deg, #333333 0%, #**********%)',
        borderBottom: '1px solid rgba(234, 179, 8, 0.2)'
      }}
    >
      <div className="container mx-auto px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo - بحجم أكبر بدون خلفية */}
          <Link href="/" className="flex items-center group">
            {companyData.logo_image_url ? (
              <div className="w-16 h-16 overflow-hidden">
                <img
                  src={companyData.logo_image_url}
                  alt={companyData.name}
                  className="w-full h-full object-contain"
                />
              </div>
            ) : (
              <div className="w-16 h-16 flex items-center justify-center text-white">
                <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
            )}
            <div className="mr-4 text-right">
              <h1 className={`text-2xl font-bold transition-colors duration-300 ${
                isScrolled ? 'text-white' : 'text-white'
              }`}>
                {companyData.name}
              </h1>
              <p className={`text-lg transition-colors duration-300 ${
                isScrolled ? 'text-yellow-400' : 'text-yellow-300'
              }`}>
                {companyData.legal_name}
              </p>
            </div>
          </Link>

          {/* Desktop Navigation - بتصميم تفاهم */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="tafahum-nav-link relative font-medium transition-all duration-300 hover:scale-105 after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-yellow-500 after:to-yellow-600 after:transition-all after:duration-300 hover:after:w-full"
              >
                {link.label}
              </a>
            ))}
          </nav>

          {/* Action Buttons - بتصميم تفاهم */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <Link
              href="/login"
              className="font-medium transition-all duration-300 px-4 py-2 flex items-center text-white hover:text-yellow-300 hover:scale-105"
            >
              <LogIn className="w-4 h-4 ml-2" />
              تسجيل الدخول
            </Link>

            <button
              className="px-6 py-2 shadow-lg hover:shadow-xl font-semibold rounded-xl transition-all duration-300 hover:transform hover:-translate-y-1"
              style={{
                background: 'linear-gradient(to right, #eab308, #f59e0b)',
                color: '#1f2937'
              }}
              onClick={onContactClick}
            >
              احجز استشارة
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center space-x-3 space-x-reverse">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-2 rounded-lg transition-colors duration-300 ${
                isScrolled
                  ? 'text-gray-700 hover:bg-gray-100'
                  : 'text-white hover:bg-white/10'
              }`}
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
              <span className="sr-only">القائمة الرئيسية</span>
            </button>

            <Link
              href="/login"
              className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${
                isScrolled
                  ? 'text-gray-700 hover:bg-gray-100'
                  : 'text-white hover:bg-white/10'
              }`}
            >
              <LogIn className="w-5 h-5" />
              <span className="sr-only">تسجيل الدخول</span>
            </Link>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden mt-6 pb-6">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-gray-100 p-6">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <a
                    key={link.href}
                    href={link.href}
                    className="px-4 py-3 rounded-xl hover:bg-blue-50 hover:text-blue-600 font-medium text-right transition-all duration-300 text-gray-700"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.label}
                  </a>
                ))}
                <div className="pt-4 border-t border-yellow-600/20 space-y-3">
                  <Link
                    href="/login"
                    className="w-full font-medium flex items-center justify-center px-6 py-3 rounded-xl border-2 transition-all duration-300 hover:transform hover:-translate-y-1"
                    style={{
                      borderColor: '#eab308',
                      color: '#eab308',
                      backgroundColor: 'transparent'
                    }}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <LogIn className="w-4 h-4 ml-2" />
                    تسجيل الدخول
                  </Link>
                  <button
                    className="w-full font-medium px-6 py-3 rounded-xl transition-all duration-300 hover:transform hover:-translate-y-1"
                    style={{
                      background: 'linear-gradient(to right, #eab308, #f59e0b)',
                      color: '#1f2937'
                    }}
                    onClick={() => {
                      onContactClick();
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    احجز استشارة
                  </button>
                </div>
              </nav>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
