"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [dbError, setDbError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_name: '',\n        issue_type: '',\n        status: 'pending',\n        amount: '',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n    });\n    // جلب المحاكم من قاعدة البيانات\n    const fetchCourts = async ()=>{\n        try {\n            const response = await fetch('/api/courts');\n            const result = await response.json();\n            if (result.success) {\n                setCourts(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching courts:', error);\n        }\n    };\n    // جلب البيانات من قاعدة البيانات\n    const fetchIssues = async ()=>{\n        setIsLoading(true);\n        setDbError(null);\n        try {\n            const response = await fetch('/api/issues');\n            const result = await response.json();\n            if (result.success) {\n                setIssues(result.data);\n            } else {\n                setDbError(result.error || 'فشل في جلب بيانات القضايا');\n                setIssues([]);\n            }\n        } catch (error) {\n            console.error('Network error:', error);\n            setDbError('فشل في الاتصال بقاعدة البيانات');\n            setIssues([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            fetchIssues();\n            fetchCourts();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'new':\n                return 'bg-purple-100 text-purple-800';\n            case 'pending':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'new':\n                return 'جديدة';\n            case 'pending':\n                return 'معلقة';\n            case 'in_progress':\n                return 'قيد المعالجة';\n            case 'completed':\n                return 'مكتملة';\n            case 'cancelled':\n                return 'ملغية';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const handleAddNew = ()=>{\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_name: '',\n            issue_type: '',\n            status: 'pending',\n            amount: '',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n    };\n    const handleClientChange = (clientId, clientData)=>{\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: clientData ? clientData.name : '',\n            client_phone: clientData ? clientData.phone : ''\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                // إضافة رقم المستخدم تلقائياً (يمكن الحصول عليه من الجلسة لاحقاً)\n                const dataToSubmit = {\n                    ...formData,\n                    created_by: 1 // رقم المستخدم الافتراضي - يجب تحديثه ليأخذ من الجلسة\n                };\n                console.log('Data being submitted for new issue:', dataToSubmit);\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                // إرسال جميع البيانات للتحديث\n                const dataToSubmit = {\n                    ...formData\n                };\n                console.log('Sending data to API:', dataToSubmit);\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    const handleViewIssue = (issue)=>{\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || '',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status || 'new',\n            amount: issue.amount ? issue.amount.toString() : '0',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        });\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    const handleEditIssue = (issue)=>{\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || '',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status || 'new',\n            amount: issue.amount ? issue.amount.toString() : '0',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        });\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    const handleDeleteIssue = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه القضية؟')) return;\n        try {\n            const response = await fetch(\"/api/issues/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert('تم حذف القضية بنجاح');\n                fetchIssues();\n            } else {\n                alert(result.error || 'فشل في حذف القضية');\n            }\n        } catch (error) {\n            console.error('Error deleting issue:', error);\n            alert('حدث خطأ في حذف القضية');\n        }\n    };\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-6 bg-white min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إدارة القضايا\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mt-1\",\n                                        children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAddNew,\n                                className: \"bg-emerald-600 hover:bg-emerald-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إضافة قضية جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"إجمالي القضايا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.new\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا جديدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قيد المعالجة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.completed\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا مكتملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"new\",\n                                                    children: \"جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pending\",\n                                                    children: \"معلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"in_progress\",\n                                                    children: \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة القضايا (\",\n                                        filteredIssues.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"رقم القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"الموكل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المبلغ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredIssues.map((issue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium text-blue-600\",\n                                                                children: issue.case_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium\",\n                                                                children: issue.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        issue.client_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: issue.client_phone || 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: issue.court_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-blue-50 text-blue-700\",\n                                                                    children: issue.issue_type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: issue.contract_method === 'بالجلسة' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700',\n                                                                    children: issue.contract_method\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    className: getStatusColor(issue.status),\n                                                                    children: getStatusText(issue.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4 font-medium text-green-600\",\n                                                                children: [\n                                                                    issue.amount ? Math.floor(issue.amount).toLocaleString() : '0',\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleViewIssue(issue),\n                                                                            className: \"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300\",\n                                                                            title: \"عرض تفاصيل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleEditIssue(issue),\n                                                                            className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300\",\n                                                                            title: \"تعديل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleDeleteIssue(issue.id),\n                                                                            className: \"bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300\",\n                                                                            title: \"حذف القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, issue.id, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto border-t-4 border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this),\n                                                modalType === 'add' && '📋 إضافة قضية جديدة',\n                                                modalType === 'edit' && '✏️ تعديل القضية',\n                                                modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsModalOpen(false),\n                                            className: \"hover:bg-red-50 hover:text-red-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-10 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_number\",\n                                                            className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"case_number\",\n                                                            value: formData.case_number,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    case_number: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل رقم القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-7\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"title\",\n                                                            value: formData.title,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    title: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل عنوان القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"description\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"description\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل وصف مفصل للقضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDC64 الموكل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__.ClientSelect, {\n                                                                value: formData.client_id,\n                                                                onChange: handleClientChange,\n                                                                label: \"\",\n                                                                placeholder: \"اختر الموكل...\",\n                                                                required: true,\n                                                                disabled: modalType === 'view'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"court_name\",\n                                                            className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"court_name\",\n                                                            value: formData.court_name || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    court_name: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المحكمة...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: court.name,\n                                                                        children: [\n                                                                            court.name,\n                                                                            \" - \",\n                                                                            court.governorate_name\n                                                                        ]\n                                                                    }, court.id, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_method\",\n                                                            className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC4 طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"contract_method\",\n                                                            value: formData.contract_method || \"بالجلسة\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_method: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالجلسة\",\n                                                                    children: \"بالجلسة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالعقد\",\n                                                                    children: \"بالعقد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 655,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDD04 حالة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            value: formData.status || \"new\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"new\",\n                                                                    children: \"جديدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"pending\",\n                                                                    children: \"معلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in_progress\",\n                                                                    children: \"قيد المعالجة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"completed\",\n                                                                    children: \"مكتملة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cancelled\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"issue_type\",\n                                                            className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"⚖️ نوع القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"issue_type\",\n                                                            value: formData.issue_type || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    issue_type: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر نوع القضية...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"مدنية\",\n                                                                    children: \"مدنية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"تجارية\",\n                                                                    children: \"تجارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"جنائية\",\n                                                                    children: \"جنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"أحوال شخصية\",\n                                                                    children: \"أحوال شخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"عمالية\",\n                                                                    children: \"عمالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"إدارية\",\n                                                                    children: \"إدارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"amount\",\n                                                            className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"amount\",\n                                                            type: \"number\",\n                                                            step: \"1\",\n                                                            min: \"0\",\n                                                            value: formData.amount,\n                                                            onChange: (e)=>{\n                                                                const value = e.target.value;\n                                                                // التأكد من أن القيمة رقم صحيح\n                                                                if (value === '' || /^\\d+$/.test(value)) {\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        amount: value\n                                                                    });\n                                                                }\n                                                            },\n                                                            className: \"h-10 bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none\",\n                                                            placeholder: \"أدخل قيمة القضية (أرقام صحيحة فقط)...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_date\",\n                                                            className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"contract_date\",\n                                                            type: \"date\",\n                                                            value: formData.contract_date,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_date: e.target.value\n                                                                }),\n                                                            className: \"h-10 bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"notes\",\n                                                        value: formData.notes,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                notes: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                            children: [\n                                                modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setIsModalOpen(false),\n                                                    className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                    children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 319,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"uTrsIW0hAcwsvd1ZHzCgc+3ou90=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});