#!/usr/bin/env node

// ملف التشغيل الموحد للنظام مع نظام التوجيه
const UnifiedServer = require('./server');
const readline = require('readline');

const server = new UnifiedServer();
server.setupSignalHandlers();

// إعداد واجهة سطر الأوامر
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// عرض القائمة الرئيسية
function showMainMenu() {
  console.clear();
  console.log('🚀 مدير النسخ - نظام إدارة المكاتب القانونية');
  console.log('='.repeat(60));
  console.log('1. عرض التوجيهات المتاحة');
  console.log('2. تشغيل خادم');
  console.log('3. إيقاف خادم');
  console.log('4. عرض حالة الخوادم');
  console.log('5. تشغيل جميع الخوادم');
  console.log('6. إيقاف جميع الخوادم');
  console.log('7. إضافة توجيه جديد');
  console.log('8. إعادة تحميل ملف التوجيه');
  console.log('0. خروج');
  console.log('='.repeat(60));

  rl.question('اختر رقم العملية: ', handleMenuChoice);
}

// معالجة اختيار القائمة
function handleMenuChoice(choice) {
  switch (choice.trim()) {
    case '1':
      server.showRoutes();
      waitForEnter();
      break;

    case '2':
      startServerMenu();
      break;

    case '3':
      stopServerMenu();
      break;

    case '4':
      server.showStatus();
      waitForEnter();
      break;

    case '5':
      startAllServers();
      break;

    case '6':
      server.stopAllServers();
      waitForEnter();
      break;

    case '7':
      addRouteMenu();
      break;

    case '8':
      server.reloadConfig();
      waitForEnter();
      break;

    case '0':
      console.log('👋 وداعاً!');
      server.stopAllServers();
      rl.close();
      process.exit(0);
      break;

    default:
      console.log('❌ اختيار غير صحيح');
      waitForEnter();
  }
}

// قائمة تشغيل الخوادم
function startServerMenu() {
  console.log('\n📋 الخوادم المتاحة للتشغيل:');
  server.showRoutes();

  rl.question('\nأدخل رقم المنفذ للتشغيل (أو اضغط Enter للعودة): ', async (port) => {
    if (port.trim()) {
      await server.startServer(port.trim());
      setTimeout(() => {
        waitForEnter();
      }, 2000);
    } else {
      showMainMenu();
    }
  });
}

// قائمة إيقاف الخوادم
function stopServerMenu() {
  console.log('\n📋 الخوادم قيد التشغيل:');
  server.showStatus();

  rl.question('\nأدخل رقم المنفذ للإيقاف (أو اضغط Enter للعودة): ', (port) => {
    if (port.trim()) {
      server.stopServer(port.trim());
    }
    waitForEnter();
  });
}

// تشغيل جميع الخوادم
async function startAllServers() {
  await server.startAllServers();

  setTimeout(() => {
    server.showStatus();
    waitForEnter();
  }, 3000);
}

// إضافة توجيه جديد
function addRouteMenu() {
  console.log('\n➕ إضافة توجيه جديد:');

  rl.question('رقم المنفذ: ', (port) => {
    if (!port.trim()) {
      console.log('❌ رقم المنفذ مطلوب');
      waitForEnter();
      return;
    }

    rl.question('اسم قاعدة البيانات: ', (database) => {
      if (!database.trim()) {
        console.log('❌ اسم قاعدة البيانات مطلوب');
        waitForEnter();
        return;
      }

      rl.question('اسم الشركة: ', (companyName) => {
        rl.question('لون الموضوع (اختياري): ', (themeColor) => {

          if (server.addRoute(
            port.trim(),
            database.trim(),
            companyName.trim() || `شركة ${database}`,
            themeColor.trim() || '#2563eb'
          )) {
            console.log('✅ تم إضافة التوجيه بنجاح');
            console.log(`📍 المنفذ ${port} → قاعدة البيانات ${database}`);
          }

          waitForEnter();
        });
      });
    });
  });
}

// انتظار ضغط Enter
function waitForEnter() {
  rl.question('\nاضغط Enter للمتابعة...', () => {
    showMainMenu();
  });
}

// معالجة الأوامر من سطر الأوامر
function handleCommandLineArgs() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    showMainMenu();
    return;
  }

  const command = args[0];
  const instanceKey = args[1];

  switch (command) {
    case 'start':
      if (instanceKey) {
        manager.startInstance(instanceKey).then(() => {
          console.log(`✅ تم تشغيل النسخة ${instanceKey}`);
        });
      } else {
        console.log('❌ يجب تحديد اسم النسخة');
        console.log('مثال: node start.js start mohammi');
      }
      break;

    case 'stop':
      if (instanceKey) {
        manager.stopInstance(instanceKey);
      } else {
        manager.stopAllInstances();
      }
      break;

    case 'list':
      manager.listInstances();
      break;

    case 'status':
      manager.showStatus();
      break;

    case 'start-all':
      startAllInstances();
      break;

    default:
      console.log('❌ أمر غير معروف');
      console.log('الأوامر المتاحة: start, stop, list, status, start-all');
  }
}

// بدء التطبيق
console.log('🚀 مرحباً بك في مدير النسخ');
handleCommandLineArgs();
