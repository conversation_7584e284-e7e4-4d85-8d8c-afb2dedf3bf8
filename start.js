#!/usr/bin/env node

// ملف التشغيل الموحد للنظام
const InstanceManager = require('./instance-manager');
const readline = require('readline');

const manager = new InstanceManager();
manager.setupSignalHandlers();

// إعداد واجهة سطر الأوامر
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// عرض القائمة الرئيسية
function showMainMenu() {
  console.clear();
  console.log('🚀 مدير النسخ - نظام إدارة المكاتب القانونية');
  console.log('='.repeat(60));
  console.log('1. عرض النسخ المتاحة');
  console.log('2. تشغيل نسخة');
  console.log('3. إيقاف نسخة');
  console.log('4. عرض حالة النسخ');
  console.log('5. تشغيل جميع النسخ');
  console.log('6. إيقاف جميع النسخ');
  console.log('7. إضافة نسخة جديدة');
  console.log('0. خروج');
  console.log('='.repeat(60));
  
  rl.question('اختر رقم العملية: ', handleMenuChoice);
}

// معالجة اختيار القائمة
function handleMenuChoice(choice) {
  switch (choice.trim()) {
    case '1':
      manager.listInstances();
      waitForEnter();
      break;
      
    case '2':
      startInstanceMenu();
      break;
      
    case '3':
      stopInstanceMenu();
      break;
      
    case '4':
      manager.showStatus();
      waitForEnter();
      break;
      
    case '5':
      startAllInstances();
      break;
      
    case '6':
      manager.stopAllInstances();
      waitForEnter();
      break;
      
    case '7':
      addInstanceMenu();
      break;
      
    case '0':
      console.log('👋 وداعاً!');
      manager.stopAllInstances();
      rl.close();
      process.exit(0);
      break;
      
    default:
      console.log('❌ اختيار غير صحيح');
      waitForEnter();
  }
}

// قائمة تشغيل النسخ
function startInstanceMenu() {
  console.log('\n📋 النسخ المتاحة للتشغيل:');
  manager.listInstances();
  
  rl.question('\nأدخل اسم النسخة للتشغيل (أو اضغط Enter للعودة): ', async (instanceKey) => {
    if (instanceKey.trim()) {
      await manager.startInstance(instanceKey.trim());
      setTimeout(() => {
        waitForEnter();
      }, 2000);
    } else {
      showMainMenu();
    }
  });
}

// قائمة إيقاف النسخ
function stopInstanceMenu() {
  console.log('\n📋 النسخ قيد التشغيل:');
  manager.showStatus();
  
  rl.question('\nأدخل اسم النسخة للإيقاف (أو اضغط Enter للعودة): ', (instanceKey) => {
    if (instanceKey.trim()) {
      manager.stopInstance(instanceKey.trim());
    }
    waitForEnter();
  });
}

// تشغيل جميع النسخ
async function startAllInstances() {
  console.log('🚀 تشغيل جميع النسخ...');
  
  const instances = Object.keys(manager.config.instances);
  
  for (const instanceKey of instances) {
    await manager.startInstance(instanceKey);
    // انتظار قصير بين تشغيل النسخ
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  setTimeout(() => {
    manager.showStatus();
    waitForEnter();
  }, 3000);
}

// إضافة نسخة جديدة
function addInstanceMenu() {
  console.log('\n➕ إضافة نسخة جديدة:');
  
  rl.question('اسم النسخة (مثل: client3): ', (key) => {
    if (!key.trim()) {
      console.log('❌ اسم النسخة مطلوب');
      waitForEnter();
      return;
    }
    
    rl.question('اسم الشركة: ', (name) => {
      rl.question('رقم المنفذ: ', (port) => {
        rl.question('اسم قاعدة البيانات: ', (database) => {
          
          const config = {
            name: name || `شركة ${key}`,
            port: parseInt(port) || 8000,
            database: database || key,
            api_url: `http://localhost:${port || 8000}`,
            description: `نسخة ${key}`,
            company_id: 1,
            theme_color: "#2563eb",
            logo_text: name || `شركة ${key}`
          };
          
          if (manager.addInstance(key, config)) {
            console.log('✅ تم إضافة النسخة بنجاح');
          }
          
          waitForEnter();
        });
      });
    });
  });
}

// انتظار ضغط Enter
function waitForEnter() {
  rl.question('\nاضغط Enter للمتابعة...', () => {
    showMainMenu();
  });
}

// معالجة الأوامر من سطر الأوامر
function handleCommandLineArgs() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    showMainMenu();
    return;
  }
  
  const command = args[0];
  const instanceKey = args[1];
  
  switch (command) {
    case 'start':
      if (instanceKey) {
        manager.startInstance(instanceKey).then(() => {
          console.log(`✅ تم تشغيل النسخة ${instanceKey}`);
        });
      } else {
        console.log('❌ يجب تحديد اسم النسخة');
        console.log('مثال: node start.js start mohammi');
      }
      break;
      
    case 'stop':
      if (instanceKey) {
        manager.stopInstance(instanceKey);
      } else {
        manager.stopAllInstances();
      }
      break;
      
    case 'list':
      manager.listInstances();
      break;
      
    case 'status':
      manager.showStatus();
      break;
      
    case 'start-all':
      startAllInstances();
      break;
      
    default:
      console.log('❌ أمر غير معروف');
      console.log('الأوامر المتاحة: start, stop, list, status, start-all');
  }
}

// بدء التطبيق
console.log('🚀 مرحباً بك في مدير النسخ');
handleCommandLineArgs();
