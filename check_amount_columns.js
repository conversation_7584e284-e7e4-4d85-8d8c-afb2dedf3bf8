// فحص الأعمدة المستخدمة للمبالغ في قاعدة البيانات
const { Pool } = require('pg');

async function checkAmountColumns() {
  console.log('🔍 فحص الأعمدة المستخدمة للمبالغ...\n');

  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 فحص قاعدة البيانات: ${dbName}`);
    console.log('='.repeat(40));
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص أعمدة المبالغ في جدول القضايا
      console.log('\n   💰 فحص أعمدة المبالغ في جدول القضايا:');
      
      const columnsQuery = `
        SELECT 
          column_name,
          data_type,
          column_default,
          is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name IN ('amount', 'case_amount', 'amount_yer')
        ORDER BY column_name
      `;

      const columns = await pool.query(columnsQuery);
      
      console.log('      الأعمدة الموجودة:');
      columns.rows.forEach(col => {
        console.log(`         - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        if (col.column_default) {
          console.log(`           default: ${col.column_default}`);
        }
      });

      // 2. فحص البيانات الموجودة
      console.log('\n   📊 فحص البيانات الموجودة:');
      
      const dataQuery = `
        SELECT 
          case_number,
          amount,
          case_amount,
          amount_yer,
          currency_id
        FROM issues 
        ORDER BY id
        LIMIT 5
      `;

      const data = await pool.query(dataQuery);
      
      if (data.rows.length > 0) {
        console.log('      عينة من البيانات:');
        data.rows.forEach(row => {
          console.log(`         ${row.case_number}:`);
          console.log(`            - amount: ${row.amount}`);
          console.log(`            - case_amount: ${row.case_amount}`);
          console.log(`            - amount_yer: ${row.amount_yer}`);
          console.log(`            - currency_id: ${row.currency_id}`);
        });
      } else {
        console.log('      لا توجد بيانات في الجدول');
      }

      // 3. فحص الـ triggers المرتبطة بالأعمدة
      console.log('\n   🔄 فحص الـ triggers المرتبطة:');
      
      const triggersQuery = `
        SELECT 
          trigger_name,
          event_manipulation,
          action_statement
        FROM information_schema.triggers
        WHERE event_object_table = 'issues'
        AND (action_statement LIKE '%amount%' OR action_statement LIKE '%case_amount%' OR action_statement LIKE '%amount_yer%')
      `;

      const triggers = await pool.query(triggersQuery);
      
      if (triggers.rows.length > 0) {
        console.log('      الـ triggers المرتبطة بالمبالغ:');
        triggers.rows.forEach(trigger => {
          console.log(`         - ${trigger.trigger_name}: ${trigger.event_manipulation}`);
        });
      } else {
        console.log('      لا توجد triggers مرتبطة بالمبالغ');
      }

      // 4. فحص الدوال المرتبطة
      console.log('\n   ⚙️ فحص الدوال المرتبطة:');
      
      const functionsQuery = `
        SELECT 
          routine_name,
          routine_definition
        FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND (routine_definition LIKE '%amount%' OR routine_definition LIKE '%case_amount%' OR routine_definition LIKE '%amount_yer%')
      `;

      const functions = await pool.query(functionsQuery);
      
      if (functions.rows.length > 0) {
        console.log('      الدوال المرتبطة بالمبالغ:');
        functions.rows.forEach(func => {
          console.log(`         - ${func.routine_name}`);
        });
      } else {
        console.log('      لا توجد دوال مرتبطة بالمبالغ');
      }

    } catch (error) {
      console.error(`   ❌ خطأ في فحص قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من فحص الأعمدة');
  
  console.log('\n📋 التوصيات:');
  console.log('1. إذا كان amount_yer يُحسب تلقائياً، فهو العمود المطلوب');
  console.log('2. إذا كان case_amount يحتوي على المبلغ الأصلي، فهو مطلوب أيضاً');
  console.log('3. العمود amount قد يكون قديماً ويمكن حذفه');
  console.log('4. يجب التأكد من الـ triggers قبل حذف أي عمود');
}

// تشغيل الفحص
checkAmountColumns().catch(console.error);
