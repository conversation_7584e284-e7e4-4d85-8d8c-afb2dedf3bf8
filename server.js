// خادم التشغيل الموحد مع نظام التوجيه
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class UnifiedServer {
  constructor() {
    this.configPath = path.join(__dirname, 'routing.config.json');
    this.config = this.loadConfig();
    this.runningServers = new Map();
  }

  // تحميل ملف التوجيه
  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('❌ خطأ في تحميل ملف التوجيه:', error.message);
      process.exit(1);
    }
  }

  // إعادة تحميل ملف التوجيه
  reloadConfig() {
    console.log('🔄 إعادة تحميل ملف التوجيه...');
    this.config = this.loadConfig();
    console.log('✅ تم إعادة تحميل ملف التوجيه');
  }

  // عرض التوجيهات المتاحة
  showRoutes() {
    console.log('\n📋 التوجيهات المتاحة:');
    console.log('='.repeat(80));
    console.log('المنفذ'.padEnd(10) + 'قاعدة البيانات'.padEnd(15) + 'اسم الشركة'.padEnd(30) + 'الحالة');
    console.log('='.repeat(80));
    
    Object.entries(this.config.routes).forEach(([port, route]) => {
      const status = this.runningServers.has(port) ? '🟢 يعمل' : '🔴 متوقف';
      console.log(
        port.padEnd(10) + 
        route.database.padEnd(15) + 
        route.company_name.padEnd(30) + 
        status
      );
    });
    
    console.log('='.repeat(80));
  }

  // تشغيل خادم على منفذ محدد
  async startServer(port) {
    if (!this.config.routes[port]) {
      console.error(`❌ لا يوجد توجيه للمنفذ ${port}`);
      return false;
    }

    if (this.runningServers.has(port)) {
      console.log(`⚠️ الخادم على المنفذ ${port} يعمل بالفعل`);
      return false;
    }

    const route = this.config.routes[port];
    const defaultConfig = this.config.default_config;

    console.log(`🚀 بدء تشغيل الخادم:`);
    console.log(`   📍 المنفذ: ${port}`);
    console.log(`   🏢 الشركة: ${route.company_name}`);
    console.log(`   🗄️ قاعدة البيانات: ${route.database}`);

    // إعداد متغيرات البيئة
    const env = {
      ...process.env,
      PORT: port,
      DB_NAME: route.database,
      DB_HOST: defaultConfig.db_host,
      DB_PORT: defaultConfig.db_port,
      DB_USER: defaultConfig.db_user,
      DB_PASSWORD: process.env.DB_PASSWORD || defaultConfig.db_password,
      NEXT_PUBLIC_API_URL: `http://localhost:${port}`,
      JWT_SECRET: defaultConfig.jwt_secret,
      COMPANY_NAME: route.company_name,
      THEME_COLOR: route.theme_color,
      LOGO_TEXT: route.logo_text
    };

    try {
      // تشغيل Next.js
      const child = spawn('npx', ['next', 'dev', '-p', port], {
        env,
        stdio: 'pipe',
        shell: true
      });

      // حفظ معلومات الخادم
      this.runningServers.set(port, {
        process: child,
        route,
        startTime: new Date()
      });

      // معالجة المخرجات
      child.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Ready in')) {
          console.log(`✅ الخادم جاهز على المنفذ ${port}: http://localhost:${port}`);
          console.log(`   🏢 ${route.company_name}`);
          console.log(`   🗄️ قاعدة البيانات: ${route.database}`);
        }
        // عرض المخرجات المهمة فقط
        if (output.includes('▲ Next.js') || output.includes('Ready in') || output.includes('Error')) {
          console.log(`[${port}] ${output.trim()}`);
        }
      });

      child.stderr.on('data', (data) => {
        const error = data.toString();
        if (!error.includes('Warning') && !error.includes('Invalid next.config.js')) {
          console.error(`[${port}] ❌ ${error.trim()}`);
        }
      });

      child.on('close', (code) => {
        console.log(`🔴 الخادم على المنفذ ${port} توقف بالكود: ${code}`);
        this.runningServers.delete(port);
      });

      child.on('error', (error) => {
        console.error(`❌ خطأ في تشغيل الخادم على المنفذ ${port}:`, error.message);
        this.runningServers.delete(port);
      });

      return true;

    } catch (error) {
      console.error(`❌ فشل في تشغيل الخادم على المنفذ ${port}:`, error.message);
      return false;
    }
  }

  // إيقاف خادم على منفذ محدد
  stopServer(port) {
    if (!this.runningServers.has(port)) {
      console.log(`⚠️ لا يوجد خادم يعمل على المنفذ ${port}`);
      return false;
    }

    const server = this.runningServers.get(port);
    
    console.log(`🛑 إيقاف الخادم على المنفذ ${port} (${server.route.company_name})`);
    
    server.process.kill('SIGTERM');
    this.runningServers.delete(port);
    
    console.log(`✅ تم إيقاف الخادم على المنفذ ${port}`);
    return true;
  }

  // تشغيل جميع الخوادم
  async startAllServers() {
    console.log('🚀 تشغيل جميع الخوادم...');
    
    const ports = Object.keys(this.config.routes);
    
    for (const port of ports) {
      await this.startServer(port);
      // انتظار قصير بين تشغيل الخوادم
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // إيقاف جميع الخوادم
  stopAllServers() {
    console.log('🛑 إيقاف جميع الخوادم...');
    
    for (const [port, server] of this.runningServers) {
      console.log(`   🔴 إيقاف ${server.route.company_name} (المنفذ ${port})`);
      server.process.kill('SIGTERM');
    }
    
    this.runningServers.clear();
    console.log('✅ تم إيقاف جميع الخوادم');
  }

  // عرض حالة الخوادم
  showStatus() {
    console.log('\n📊 حالة الخوادم:');
    console.log('='.repeat(80));
    
    if (this.runningServers.size === 0) {
      console.log('🔴 لا توجد خوادم قيد التشغيل');
    } else {
      for (const [port, server] of this.runningServers) {
        const uptime = Math.floor((new Date() - server.startTime) / 1000);
        console.log(`🟢 ${server.route.company_name}`);
        console.log(`   📍 الرابط: http://localhost:${port}`);
        console.log(`   ⏱️ مدة التشغيل: ${uptime} ثانية`);
        console.log(`   🗄️ قاعدة البيانات: ${server.route.database}`);
        console.log('');
      }
    }
    
    console.log('='.repeat(80));
  }

  // إضافة توجيه جديد
  addRoute(port, database, companyName, themeColor = '#2563eb') {
    if (this.config.routes[port]) {
      console.error(`❌ يوجد توجيه بالفعل للمنفذ ${port}`);
      return false;
    }

    this.config.routes[port] = {
      database: database,
      company_name: companyName,
      theme_color: themeColor,
      logo_text: companyName,
      description: `نسخة ${companyName}`
    };

    this.saveConfig();
    console.log(`✅ تم إضافة توجيه جديد: المنفذ ${port} → قاعدة البيانات ${database}`);
    return true;
  }

  // حذف توجيه
  removeRoute(port) {
    if (!this.config.routes[port]) {
      console.error(`❌ لا يوجد توجيه للمنفذ ${port}`);
      return false;
    }

    // إيقاف الخادم إذا كان يعمل
    if (this.runningServers.has(port)) {
      this.stopServer(port);
    }

    delete this.config.routes[port];
    this.saveConfig();
    console.log(`✅ تم حذف التوجيه للمنفذ ${port}`);
    return true;
  }

  // حفظ ملف التوجيه
  saveConfig() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('❌ خطأ في حفظ ملف التوجيه:', error.message);
    }
  }

  // معالجة إشارات النظام
  setupSignalHandlers() {
    process.on('SIGINT', () => {
      console.log('\n🛑 تم استلام إشارة الإيقاف...');
      this.stopAllServers();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 تم استلام إشارة الإنهاء...');
      this.stopAllServers();
      process.exit(0);
    });
  }
}

module.exports = UnifiedServer;
