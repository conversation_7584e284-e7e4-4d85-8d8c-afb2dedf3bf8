'use client'

import { useState, useEffect } from 'react'

// مكون بسيط جداً لاختيار المستفيدين
export function SuperSimpleBeneficiarySelect({ 
  value, 
  onChange, 
  label = "المستفيد" 
}: {
  value: string
  onChange: (value: string) => void
  label?: string
}) {
  const [clients, setClients] = useState<any[]>([])
  const [employees, setEmployees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      
      
      try {
        // جلب العملاء
        const clientsResponse = await fetch('/api/clients')
        const clientsData = await clientsResponse.json()
        
        
        if (clientsData.success) {
          setClients(clientsData.clients || [])
        }

        // جلب الموظفين
        const employeesResponse = await fetch('/api/employees')
        const employeesData = await employeesResponse.json()
        
        
        if (employeesData.success) {
          setEmployees(employeesData.employees || [])
        }

      } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  console.log('🔍 SuperSimpleBeneficiarySelect: العملاء =', clients.length, 'الموظفين =', employees.length)

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full p-3 border border-gray-300 rounded-md bg-white"
      >
        <option value="">
          {loading ? "جاري التحميل..." : "اختر المستفيد..."}
        </option>
        
        {!loading && (
          <>
            {clients.length > 0 && (
              <optgroup label={`العملاء (${clients.length})`}>
                {clients.map((client) => (
                  <option key={`client_${client.id}`} value={`client_${client.id}`}>
                    {client.name} - {client.account_code || 'بدون كود'}
                  </option>
                ))}
              </optgroup>
            )}
            
            {employees.length > 0 && (
              <optgroup label={`الموظفين (${employees.length})`}>
                {employees.map((employee) => (
                  <option key={`employee_${employee.id}`} value={`employee_${employee.id}`}>
                    {employee.name} - {employee.account_code || 'بدون كود'}
                  </option>
                ))}
              </optgroup>
            )}
          </>
        )}
      </select>
      
      {/* عرض حالة التحميل والبيانات */}
      <div className="mt-2 text-xs text-gray-500">
        {loading ? (
          "جاري التحميل..."
        ) : (
          `تم تحميل ${clients.length} عميل و ${employees.length} موظف`
        )}
      </div>
    </div>
  )
}

// مكون بسيط جداً لاختيار الحسابات
export function SuperSimpleAccountSelect({ 
  value, 
  onChange, 
  label = "الحساب",
  accountTypes = []
}: {
  value: string
  onChange: (value: string) => void
  label?: string
  accountTypes?: string[]
}) {
  const [accounts, setAccounts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAccounts = async () => {
      
      
      try {
        const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=false')
        const data = await response.json()
        
        
        if (data.success) {
          let filteredAccounts = data.accounts.filter((acc: any) => acc.allow_transactions)
          
          if (accountTypes.length > 0) {
            filteredAccounts = filteredAccounts.filter((acc: any) => 
              accountTypes.includes(acc.account_type)
            )
          }
          
          setAccounts(filteredAccounts || [])
        }

      } catch (error) {
        console.error('❌ خطأ في جلب الحسابات:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAccounts()
  }, [accountTypes])

  console.log('🔍 SuperSimpleAccountSelect: الحسابات =', accounts.length)

  // تجميع الحسابات حسب النوع
  const groupedAccounts = accounts.reduce((groups, account) => {
    const type = account.account_type || 'أخرى'
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(account)
    return groups
  }, {} as Record<string, any[]>)

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full p-3 border border-gray-300 rounded-md bg-white"
      >
        <option value="">
          {loading ? "جاري التحميل..." : "اختر الحساب..."}
        </option>
        
        {!loading && Object.entries(groupedAccounts).map(([type, typeAccounts]) => (
          <optgroup key={type} label={`${type} (${typeAccounts.length})`}>
            {typeAccounts.map((account) => (
              <option key={account.id} value={account.id.toString()}>
                {account.account_code} - {account.account_name}
              </option>
            ))}
          </optgroup>
        ))}
      </select>
      
      {/* عرض حالة التحميل والبيانات */}
      <div className="mt-2 text-xs text-gray-500">
        {loading ? (
          "جاري التحميل..."
        ) : (
          `تم تحميل ${accounts.length} حساب`
        )}
      </div>
    </div>
  )
}
