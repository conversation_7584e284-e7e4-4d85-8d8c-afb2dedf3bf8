import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب قضية واحدة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    console.log('GET Issue API: Fetching issue with ID:', id)

    const result = await query(
      'SELECT * FROM issues WHERE id = $1',
      [id]
    )

    console.log('GET Issue API: Query result:', result.rows)

    if (result.rows.length === 0) {
      console.log('GET Issue API: Issue not found with ID:', id)
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    const issueData = result.rows[0]
    console.log('GET Issue API: Returning issue data:', issueData)

    return NextResponse.json({
      success: true,
      data: issueData
    })
  } catch (error) {
    console.error('GET Issue API: Error fetching issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث قضية
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    const {
      case_number, title, description, client_id, client_name, client_phone, court_name,
      issue_type, status, amount, notes, contract_method, contract_date
    } = body

    console.log('Received data for update:', {
      case_number, title, description, client_name, court_name,
      issue_type, status, amount, notes, contract_method, contract_date
    })

    if (!case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE id = $1',
      [id]
    )

    if (existingIssue.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار رقم القضية (باستثناء القضية الحالية)
    const duplicateCheck = await query(
      'SELECT id FROM issues WHERE case_number = $1 AND id != $2',
      [case_number, id]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تحديث بيانات القضية
    const result = await query(`
      UPDATE issues
      SET case_number = $1, title = $2, description = $3, client_id = $4, client_name = $5,
          client_phone = $6, court_name = $7, issue_type = $8, status = $9, amount = $10,
          notes = $11, contract_method = $12, contract_date = $13,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $14
      RETURNING *
    `, [
      case_number, title, description, client_id || null, client_name, client_phone,
      court_name, issue_type, status, parseFloat(amount) || 0, notes, contract_method, contract_date, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating issue:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    })
    return NextResponse.json(
      { success: false, error: `فشل في تحديث القضية: ${error.message}` },
      { status: 500 }
    )
  }
}

// DELETE - حذف قضية
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    console.log('DELETE Issue API: Attempting to delete issue with ID:', id)

    // التحقق من صحة المعرف
    if (!id || isNaN(parseInt(id))) {
      console.log('DELETE Issue API: Invalid ID provided:', id)
      return NextResponse.json(
        { success: false, error: 'معرف القضية غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id, case_number, title FROM issues WHERE id = $1',
      [id]
    )

    console.log('DELETE Issue API: Existing issue check result:', existingIssue.rows)

    if (existingIssue.rows.length === 0) {
      console.log('DELETE Issue API: Issue not found with ID:', id)
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    const issueToDelete = existingIssue.rows[0]
    console.log('DELETE Issue API: Found issue to delete:', issueToDelete)

    // التحقق من وجود توزيعات مرتبطة بالقضية
    const distributionsCheck = await query(
      'SELECT id FROM case_distributions WHERE issue_id = $1',
      [id]
    )

    if (distributionsCheck.rows.length > 0) {
      console.log('DELETE Issue API: Issue has distributions, cannot delete')
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف القضية لأنها مرتبطة بتوزيعات. يجب حذف التوزيعات أولاً.' },
        { status: 400 }
      )
    }

    // حذف القضية
    const deleteResult = await query('DELETE FROM issues WHERE id = $1 RETURNING *', [id])

    console.log('DELETE Issue API: Delete result:', deleteResult.rows)

    if (deleteResult.rows.length === 0) {
      console.log('DELETE Issue API: No rows affected during delete')
      return NextResponse.json(
        { success: false, error: 'فشل في حذف القضية - لم يتم العثور على القضية' },
        { status: 404 }
      )
    }

    console.log('DELETE Issue API: Successfully deleted issue:', deleteResult.rows[0])

    return NextResponse.json({
      success: true,
      message: 'تم حذف القضية بنجاح',
      data: deleteResult.rows[0]
    })
  } catch (error) {
    console.error('DELETE Issue API: Error deleting issue:', error)
    console.error('DELETE Issue API: Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    })

    return NextResponse.json(
      { success: false, error: `فشل في حذف القضية: ${error.message}` },
      { status: 500 }
    )
  }
}
