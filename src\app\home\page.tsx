'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Toaster } from 'react-hot-toast';

import { SimpleChatWidget } from './components/simple-chat-widget';
import { LegalLibrarySectionNew } from './components/legal-library-section-new';
import { AnnouncementsBar } from './components/announcements-bar';
import { MapSection } from './components/map-section';
import './styles.css';

// Interfaces
export interface CompanyData {
  id: number;
  name: string;
  legal_name: string;
  description: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  logo_image_url: string;
  established_date: string;
  registration_number: string;
  legal_form: string;
  capital: number;
  tax_number: string;
  is_active: boolean;
  working_hours?: string;
  latitude?: number;
  longitude?: number;
}

interface Stat {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
  legalDocuments?: number;
}

// Mock data
const companyData: CompanyData = {
  id: 1,
  name: 'شركة تفاهم للمحاماة والاستشارات القانونية',
  legal_name: 'شركة تفاهم للمحاماة والاستشارات القانونية المحدودة',
  description: 'نقدم خدمات قانونية متميزة بأعلى معايير الجودة والاحترافية',
  address: 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
  city: 'الرياض',
  country: 'المملكة العربية السعودية',
  phone: '+966 50 000 1124',
  email: '<EMAIL>',
  website: 'www.tafahum-law.sa',
  logo_url: '/images/simple-logo.svg',
  logo_image_url: '/images/simple-logo.svg',
  established_date: '2010-01-01',
  registration_number: '1234567890',
  legal_form: 'شركة محدودة المسؤولية',
  capital: 2000000,
  tax_number: '*********',
  is_active: true,
  working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'
};

// سيتم تعريف stats داخل المكون

// Dynamic imports for components

const HeaderComponent = dynamic(
  () => import('./components/header').then((mod) => mod.Header),
  { ssr: false, loading: () => null }
);

const HeroSection = dynamic(
  () => import('./components/hero-section').then((mod) => mod.HeroSection),
  { ssr: false, loading: () => null }
);

const ServicesSection = dynamic(
  () => import('./components/services-section').then((mod) => mod.ServicesSection),
  { ssr: false, loading: () => null }
);







const TestimonialsSection = dynamic(
  () => import('./components/testimonials-section').then((mod) => mod.TestimonialsSection),
  { ssr: false, loading: () => null }
);



const Footer = dynamic(
  () => import('./components/footer').then((mod) => mod.Footer),
  { ssr: false, loading: () => null }
);

export default function HomePage() {
  const [isChatWidgetOpen, setIsChatWidgetOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [companyDataState, setCompanyDataState] = useState<CompanyData | null>(null);
  const [legalDocumentsCount, setLegalDocumentsCount] = useState(0);

  // جلب بيانات الشركة من قاعدة البيانات
  useEffect(() => {
    const fetchCompanyData = async () => {
      try {

        const response = await fetch('/api/company');
        const result = await response.json();

        if (result.success && result.data && result.data.length > 0) {
          // أخذ أول شركة من القائمة (الشركة الرئيسية)
          const company = result.data[0];
          setCompanyDataState({
            ...company,
            working_hours: company.working_hours || 'الأحد - الخميس: 8 صباحاً - 6 مساءً'
          });
        } else {
          // استخدام البيانات الافتراضية في حالة عدم وجود شركة
          setCompanyDataState(companyData);
        }
      } catch (error) {
        console.error('خطأ في جلب بيانات الشركة:', error);
        // استخدام البيانات الافتراضية في حالة الخطأ
        setCompanyDataState(companyData);
      }
    };

    fetchCompanyData();
  }, []);

  // جلب عدد الملفات القانونية
  useEffect(() => {
    const fetchLegalDocumentsCount = async () => {
      try {
        const response = await fetch('/api/legal-library');
        const result = await response.json();

        if (result.success && result.data) {
          setLegalDocumentsCount(result.data.length);
        }
      } catch (error) {
        console.error('خطأ في جلب عدد الملفات القانونية:', error);
      }
    };

    fetchLegalDocumentsCount();
  }, []);

  // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية
  const currentCompanyData = companyDataState || companyData;

  // إحصائيات المكتب
  const stats: Stat = {
    clients: 1200,
    issues: 5000,
    employees: 25,
    completedIssues: 4900,
    newIssues: 100,
    courts: 15,
    successRate: 98,
    experienceYears: 15,
    legalDocuments: legalDocumentsCount
  };





  const scrollToServices = () => {
    const librarySection = document.getElementById('library');
    if (librarySection) {
      librarySection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // إذا لم توجد المكتبة، انتقل للخدمات
      const servicesSection = document.getElementById('services');
      servicesSection?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      dir="rtl"
      className="min-h-screen text-white"
      style={{
        background: 'linear-gradient(135deg, #333333 0%, #**********%)',
        minHeight: '100vh'
      }}
    >
      <Toaster />

      {/* Announcements Bar */}
      <AnnouncementsBar />

      {/* Header */}
      <HeaderComponent
        companyData={currentCompanyData}
      />

      <main>
        {/* Hero Section */}
        <HeroSection
          companyData={currentCompanyData}
          stats={stats}
          onServicesClick={scrollToServices}
        />

        {/* Services Section */}
        <section id="services" className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <ServicesSection
            searchQuery={searchQuery}
            onSearch={setSearchQuery}
          />
        </section>



        {/* Legal Library Section */}
        <LegalLibrarySectionNew />



        {/* Testimonials Section */}
        <section className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <TestimonialsSection />
        </section>

        {/* Map Section */}
        <MapSection companyData={currentCompanyData} />

      </main>

      {/* Footer */}
      <Footer companyData={currentCompanyData} />



      {/* Chat Widget */}
      <SimpleChatWidget
        isOpen={isChatWidgetOpen}
        onClose={() => setIsChatWidgetOpen(false)}
        onOpen={() => setIsChatWidgetOpen(true)}
      />
    </div>
  );
}