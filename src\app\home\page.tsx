'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Toaster } from 'react-hot-toast';

import { SimpleChatWidget } from './components/simple-chat-widget';
import { LegalLibrarySectionNew } from './components/legal-library-section-new';
import { AnnouncementsBar } from './components/announcements-bar';
import { MapSection } from './components/map-section';
import { useCompanyData } from '@/hooks/useCompanyData';
import './styles.css';

// Interfaces
export interface CompanyData {
  id: number;
  name: string;
  legal_name: string;
  description: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  logo_image_url: string;
  established_date: string;
  registration_number: string;
  legal_form: string;
  capital: number;
  tax_number: string;
  is_active: boolean;
  working_hours?: string;
  latitude?: number;
  longitude?: number;
}

interface Stat {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
  legalDocuments?: number;
}

// Default company data (fallback) - updated to match actual database
const companyData: CompanyData = {
  id: 1,
  name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
  legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',
  description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
  address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',
  city: 'صنعاء',
  country: 'اليمن',
  phone: '+967-1-123456',
  email: '<EMAIL>',
  website: 'www.legalfirm.ye',
  logo_url: '/images/company-logo.png',
  logo_image_url: '/images/logo.png',
  established_date: '2020-01-14',
  registration_number: 'CR-2024-001',
  legal_form: 'شركة محدودة المسؤولية',
  capital: 1000000,
  tax_number: 'TAX-*********',
  is_active: true,
  working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'
};

// سيتم تعريف stats داخل المكون

// Dynamic imports for components

const HeaderComponent = dynamic(
  () => import('./components/header').then((mod) => mod.Header),
  { ssr: false, loading: () => null }
);

const HeroSection = dynamic(
  () => import('./components/hero-section').then((mod) => mod.HeroSection),
  { ssr: false, loading: () => null }
);

const ServicesSection = dynamic(
  () => import('./components/services-section').then((mod) => mod.ServicesSection),
  { ssr: false, loading: () => null }
);







const TestimonialsSection = dynamic(
  () => import('./components/testimonials-section').then((mod) => mod.TestimonialsSection),
  { ssr: false, loading: () => null }
);



const Footer = dynamic(
  () => import('./components/footer').then((mod) => mod.Footer),
  { ssr: false, loading: () => null }
);

export default function HomePage() {
  const [isChatWidgetOpen, setIsChatWidgetOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [legalDocumentsCount, setLegalDocumentsCount] = useState(0);

  // استخدام hook بيانات الشركة مع التخزين المحلي
  const {
    companyData,
    loading: companyLoading,
    error: companyError,
    getThemeColor,
    getCompanyName,
    isDataAvailable
  } = useCompanyData();

  // استخدام البيانات المحملة أو الافتراضية
  const companyDataState = companyData || defaultCompanyData;

  // جلب عدد الملفات القانونية
  useEffect(() => {
    const fetchLegalDocumentsCount = async () => {
      try {
        const response = await fetch('/api/legal-library');
        const result = await response.json();

        if (result.success && result.data) {
          setLegalDocumentsCount(result.data.length);
        }
      } catch (error) {
        console.error('خطأ في جلب عدد الملفات القانونية:', error);
      }
    };

    fetchLegalDocumentsCount();
  }, []);

  // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية
  const currentCompanyData = companyDataState || companyData;

  // إحصائيات المكتب
  const stats: Stat = {
    clients: 1200,
    issues: 5000,
    employees: 25,
    completedIssues: 4900,
    newIssues: 100,
    courts: 15,
    successRate: 98,
    experienceYears: 15,
    legalDocuments: legalDocumentsCount
  };





  const scrollToServices = () => {
    const librarySection = document.getElementById('library');
    if (librarySection) {
      librarySection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // إذا لم توجد المكتبة، انتقل للخدمات
      const servicesSection = document.getElementById('services');
      servicesSection?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // مؤشر التحميل للبيانات الأساسية
  if (companyLoading && !isDataAvailable) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">جاري تحميل بيانات الشركة...</p>
          <p className="text-gray-400 text-sm mt-2">يتم حفظ البيانات محلياً لتسريع التصفح مستقبلاً</p>
        </div>
      </div>
    );
  }

  return (
    <div
      dir="rtl"
      className="min-h-screen text-white"
      style={{
        background: 'linear-gradient(135deg, #333333 0%, #**********%)',
        minHeight: '100vh'
      }}
    >
      <Toaster />

      {/* إشعار حالة البيانات */}
      {companyError && !isDataAvailable && (
        <div className="bg-yellow-600 text-white px-4 py-2 text-center text-sm">
          ⚠️ تعذر تحميل أحدث البيانات، يتم عرض البيانات المحفوظة محلياً
        </div>
      )}

      {/* Announcements Bar */}
      <AnnouncementsBar />

      {/* Header */}
      <HeaderComponent
        companyData={currentCompanyData}
      />

      <main>
        {/* Hero Section */}
        <HeroSection
          companyData={currentCompanyData}
          stats={stats}
          onServicesClick={scrollToServices}
        />

        {/* Services Section */}
        <section id="services" className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <ServicesSection
            searchQuery={searchQuery}
            onSearch={setSearchQuery}
          />
        </section>

        {/* Legal Library Section */}
        <LegalLibrarySectionNew />

        {/* Testimonials Section */}
        <section className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <TestimonialsSection />
        </section>

        {/* Map Section */}
        <MapSection companyData={currentCompanyData} />

      </main>

      {/* Footer */}
      <Footer companyData={currentCompanyData} />

      {/* Chat Widget */}
      <SimpleChatWidget
        isOpen={isChatWidgetOpen}
        onClose={() => setIsChatWidgetOpen(false)}
        onOpen={() => setIsChatWidgetOpen(true)}
      />
    </div>
  );
}