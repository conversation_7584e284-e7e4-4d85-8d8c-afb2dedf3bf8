# نظام إدارة المكاتب القانونية المتقدم

نظام شامل ومتطور لإدارة المكاتب القانونية مبني بتقنيات حديثة مع واجهة باللغة العربية وميزات متقدمة.

## 🚀 الميزات الجديدة والمتقدمة

### 📁 نظام إدارة الوثائق المتقدم
- **أرشفة ذكية**: تصنيف تلقائي وفهرسة للوثائق
- **بحث في المحتوى**: بحث متقدم داخل نصوص الوثائق
- **إدارة الصلاحيات**: تحكم دقيق في الوصول للوثائق
- **تتبع الإصدارات**: حفظ تاريخ التعديلات
- **المعاينة المباشرة**: عرض الوثائق بدون تحميل

### 👥 بوابة العملاء التفاعلية
- **حساب شخصي**: لوحة تحكم مخصصة لكل عميل
- **تتبع القضايا**: متابعة مباشرة لحالة القضايا
- **رفع الوثائق**: إمكانية رفع المستندات المطلوبة
- **الإشعارات الفورية**: تنبيهات للتحديثات المهمة
- **طلب الخدمات**: نظام طلبات متكامل

### ⏱️ نظام إدارة الوقت والفوترة
- **تتبع الوقت الذكي**: مؤقتات دقيقة لكل مهمة
- **فوترة تلقائية**: إنشاء فواتير من تسجيلات الوقت
- **تقارير الإنتاجية**: تحليل أداء الفريق
- **معدلات مرنة**: أسعار مختلفة حسب نوع العمل

### 📊 تقارير وإحصائيات متقدمة
- **لوحة تحكم تفاعلية**: رسوم بيانية ديناميكية
- **تحليل الأداء**: مؤشرات أداء رئيسية (KPIs)
- **التنبؤ المالي**: توقعات الإيرادات
- **تقارير مخصصة**: إنشاء تقارير حسب الحاجة
- **تصدير متعدد**: PDF, Excel, CSV

## الميزات الأساسية

### 🏢 إدارة البيانات الأساسية
- **بيانات الشركة**: إدارة معلومات المكتب القانوني
- **إدارة الموكلين**: قاعدة بيانات شاملة للعملاء
- **إدارة الموظفين**: تتبع بيانات الفريق القانوني
- **إدارة المستخدمين**: نظام صلاحيات متقدم

### ⚖️ إدارة القضايا
- **القضايا**: تتبع شامل لجميع القضايا
- **أنواع القضايا**: تصنيف مرن للقضايا
- **توزيع القضايا**: توزيع ذكي على المحامين
- **المتابعات**: جدولة المواعيد والمهام
- **الحركات**: تسجيل جميع الإجراءات القانونية

### 💰 النظام المحاسبي المتكامل
- **دليل الحسابات**: هيكل محاسبي مرن
- **سندات الصرف والقبض**: إدارة المدفوعات
- **القيود اليومية**: تسجيل العمليات المحاسبية
- **الأرصدة الافتتاحية**: إعداد الحسابات
- **التقارير المحاسبية**: تقارير مالية شاملة

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL 15+
- **Authentication**: NextAuth.js
- **File Storage**: Local/Cloud Storage
- **Icons**: Lucide React
- **Charts**: Recharts

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd legal-system
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb mohammi

# تشغيل ملفات الإعداد
cd database
node setup_database.js

# إدراج البيانات التجريبية
node basic_sample_data.js
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env.local
```

قم بتحديث الملف `.env.local`:
```env
DATABASE_URL="postgresql://postgres:yemen123@localhost:5432/mohammi"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=52428800
```

### 5. إنشاء مجلدات التحميل
```bash
mkdir -p public/uploads/documents
mkdir -p public/uploads/avatars
```

### 6. تشغيل التطبيق
```bash
npm run dev
```

التطبيق سيكون متاحاً على: `http://localhost:3000`

## الاستخدام

### تسجيل الدخول الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### بيانات العميل التجريبي (بوابة العملاء)
- **اسم المستخدم**: demo_client
- **كلمة المرور**: password123

### الميزات الجديدة

#### 1. إدارة الوثائق المتقدمة
- انتقل إلى "إدارة الوثائق" → "مكتبة الوثائق"
- رفع وثائق جديدة مع التصنيف التلقائي
- البحث في محتوى الوثائق
- إدارة صلاحيات الوصول

#### 2. تتبع الوقت والفوترة
- انتقل إلى "تتبع الوقت والفوترة"
- بدء مؤقت جديد لمهمة معينة
- مراجعة تسجيلات الوقت
- إنشاء فواتير تلقائية

#### 3. بوابة العملاء
- الوصول عبر `/client-portal`
- تسجيل دخول العميل
- متابعة القضايا والوثائق
- إرسال طلبات جديدة

#### 4. التقارير المتقدمة
- انتقل إلى "التقارير والإحصائيات" → "التقارير المتقدمة"
- عرض إحصائيات شاملة
- تصدير التقارير بصيغ مختلفة
- تحليل الأداء المالي

#### 5. إدارة حسابات العملاء
- انتقل إلى "بوابة العملاء" → "حسابات العملاء"
- إنشاء حسابات جديدة للعملاء
- إدارة الصلاحيات والإعدادات
- مراقبة النشاط

## الإصدارات

### الإصدار 1.5.0 (الحالي)
- ✅ نظام إدارة الوثائق المتقدم
- ✅ بوابة العملاء التفاعلية
- ✅ نظام تتبع الوقت والفوترة
- ✅ التقارير والإحصائيات المتقدمة
- ✅ إدارة حسابات العملاء

### الإصدار 1.0.0
- الميزات الأساسية لإدارة المكتب القانوني
- النظام المحاسبي المتكامل
- التقارير والإحصائيات الأساسية

---

**🌟 النظام الآن جاهز للاستخدام مع جميع الميزات المتقدمة!**

تم تطوير هذا النظام المتقدم لتلبية احتياجات المكاتب القانونية الحديثة في المنطقة العربية مع التركيز على الابتكار والكفاءة والأمان.
