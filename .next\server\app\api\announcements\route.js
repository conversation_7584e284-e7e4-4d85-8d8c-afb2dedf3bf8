(()=>{var e={};e.id=9610,e.ids=[9610],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{P:()=>p});var s=r(64939),a=r(29021),o=r.n(a),c=r(33873),u=r.n(c),i=e([s]);s=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),m=new s.Pool(l);async function p(e,t){let r=await m.connect();try{return await r.query(e,t)}finally{r.release()}}n()}catch(e){n(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41668:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{patchFetch:()=>i,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s=r(96559),a=r(48088),o=r(37719),c=r(79874),u=e([c]);c=(u.then?(await u)():u)[0];let p=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/announcements/route",pathname:"/api/announcements",filename:"route",bundlePath:"app/api/announcements/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\announcements\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function i(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}n()}catch(e){n(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},79874:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GET:()=>c,POST:()=>u});var s=r(32190),a=r(5069),o=e([a]);async function c(e){try{let e=await (0,a.P)("SELECT * FROM announcements WHERE is_active = $1 ORDER BY created_date DESC",[!0]);return s.NextResponse.json({success:!0,data:e.rows})}catch(e){return console.error("Error fetching announcements:",e),s.NextResponse.json({success:!1,error:"Failed to fetch announcements"},{status:500})}}async function u(e){try{let{announcement_1:t,announcement_2:r,announcement_3:n,announcement_4:o,is_active:c}=await e.json(),u=await (0,a.P)(`INSERT INTO announcements (announcement_1, announcement_2, announcement_3, announcement_4, is_active, created_date, updated_date)
       VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
       RETURNING *`,[t,r,n,o,c]);return s.NextResponse.json({success:!0,data:u.rows[0]})}catch(e){return console.error("Error creating announcement:",e),s.NextResponse.json({success:!1,error:"Failed to create announcement"},{status:500})}}a=(o.then?(await o)():o)[0],n()}catch(e){n(e)}})},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580],()=>r(41668));module.exports=n})();