// التحقق النهائي من العلاقات المصححة
const { Pool } = require('pg');

async function verifyFinalRelations() {
  console.log('🔍 التحقق النهائي من العلاقات المصححة...\n');

  // قواعد البيانات المطلوب فحصها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 فحص قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص العلاقات الحالية
      console.log('\n   🔗 العلاقات الحالية:');
      
      const relations = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule,
          rc.update_rule
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name IN ('issues', 'issue_courts')
        ORDER BY tc.table_name, kcu.column_name
      `);

      relations.rows.forEach(rel => {
        console.log(`      ✅ ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
        console.log(`         (ON DELETE ${rel.delete_rule}, ON UPDATE ${rel.update_rule})`);
      });

      // 2. فحص الجداول الموجودة
      console.log('\n   📊 الجداول الموجودة:');
      
      const tables = await pool.query(`
        SELECT table_name, 
               (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
        FROM information_schema.tables t
        WHERE table_schema = 'public' 
        AND table_name IN ('clients', 'issues', 'courts', 'issue_courts', 'issue_types')
        ORDER BY table_name
      `);

      tables.rows.forEach(table => {
        console.log(`      📋 ${table.table_name} (${table.column_count} أعمدة)`);
      });

      // 3. فحص البيانات
      console.log('\n   📈 إحصائيات البيانات:');
      
      const stats = await pool.query(`
        SELECT 
          'clients' as table_name,
          COUNT(*) as record_count
        FROM clients
        UNION ALL
        SELECT 
          'issues' as table_name,
          COUNT(*) as record_count
        FROM issues
        UNION ALL
        SELECT 
          'courts' as table_name,
          COUNT(*) as record_count
        FROM courts
        UNION ALL
        SELECT 
          'issue_courts' as table_name,
          COUNT(*) as record_count
        FROM issue_courts
        ORDER BY table_name
      `);

      stats.rows.forEach(stat => {
        console.log(`      📊 ${stat.table_name}: ${stat.record_count} سجل`);
      });

      // 4. اختبار العلاقات
      console.log('\n   🧪 اختبار العلاقات:');

      // اختبار علاقة العملاء -> القضايا
      const clientIssues = await pool.query(`
        SELECT 
          c.name as client_name,
          COUNT(i.id) as issue_count
        FROM clients c
        LEFT JOIN issues i ON c.id = i.client_id
        GROUP BY c.id, c.name
        HAVING COUNT(i.id) > 0
        ORDER BY issue_count DESC
        LIMIT 5
      `);

      console.log('      👥 العملاء وقضاياهم:');
      clientIssues.rows.forEach(client => {
        console.log(`         - ${client.client_name}: ${client.issue_count} قضية`);
      });

      // اختبار علاقة القضايا <-> المحاكم
      const issueCourts = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          COUNT(ic.court_id) as court_count,
          STRING_AGG(c.name, ', ') as court_names
        FROM issues i
        LEFT JOIN issue_courts ic ON i.id = ic.issue_id
        LEFT JOIN courts c ON ic.court_id = c.id
        GROUP BY i.id, i.case_number, i.title
        HAVING COUNT(ic.court_id) > 0
        ORDER BY court_count DESC
        LIMIT 5
      `);

      console.log('\n      🏛️ القضايا ومحاكمها:');
      issueCourts.rows.forEach(issue => {
        console.log(`         - ${issue.case_number}: ${issue.court_count} محكمة`);
        console.log(`           المحاكم: ${issue.court_names || 'غير محدد'}`);
      });

      // 5. فحص سلامة البيانات
      console.log('\n   🔍 فحص سلامة البيانات:');

      // فحص القضايا بدون عملاء
      const orphanIssues = await pool.query(`
        SELECT COUNT(*) as count
        FROM issues 
        WHERE client_id IS NULL
      `);
      console.log(`      - قضايا بدون عملاء: ${orphanIssues.rows[0].count}`);

      // فحص القضايا بدون محاكم
      const issuesWithoutCourts = await pool.query(`
        SELECT COUNT(*) as count
        FROM issues i
        LEFT JOIN issue_courts ic ON i.id = ic.issue_id
        WHERE ic.issue_id IS NULL
      `);
      console.log(`      - قضايا بدون محاكم: ${issuesWithoutCourts.rows[0].count}`);

      // فحص العلاقات المكسورة
      const brokenClientRefs = await pool.query(`
        SELECT COUNT(*) as count
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        WHERE i.client_id IS NOT NULL AND c.id IS NULL
      `);
      console.log(`      - مراجع عملاء مكسورة: ${brokenClientRefs.rows[0].count}`);

      const brokenCourtRefs = await pool.query(`
        SELECT COUNT(*) as count
        FROM issue_courts ic
        LEFT JOIN courts c ON ic.court_id = c.id
        WHERE c.id IS NULL
      `);
      console.log(`      - مراجع محاكم مكسورة: ${brokenCourtRefs.rows[0].count}`);

      // 6. عرض مثال على الاستعلام المحسن
      console.log('\n   📝 مثال على استعلام محسن:');
      
      const optimizedQuery = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          c.name as client_name,
          c.phone as client_phone,
          STRING_AGG(DISTINCT ct.name, ', ') as courts,
          i.status,
          i.amount
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN issue_courts ic ON i.id = ic.issue_id
        LEFT JOIN courts ct ON ic.court_id = ct.id
        GROUP BY i.id, i.case_number, i.title, c.name, c.phone, i.status, i.amount
        ORDER BY i.id
        LIMIT 3
      `);

      optimizedQuery.rows.forEach(row => {
        console.log(`      📄 ${row.case_number}: ${row.title}`);
        console.log(`         العميل: ${row.client_name || 'غير محدد'} (${row.client_phone || 'بدون هاتف'})`);
        console.log(`         المحاكم: ${row.courts || 'غير محدد'}`);
        console.log(`         الحالة: ${row.status || 'غير محدد'} | المبلغ: ${row.amount || 0}`);
        console.log('');
      });

    } catch (error) {
      console.error(`   ❌ خطأ في فحص قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من التحقق النهائي');
  
  console.log('\n📋 ملخص العلاقات المصححة:');
  console.log('1. ✅ clients -> issues: One-to-Many (عميل واحد له عدة قضايا)');
  console.log('2. ✅ issues <-> courts: Many-to-Many عبر issue_courts');
  console.log('3. ✅ تم حذف جميع العلاقات الخاطئة السابقة');
  console.log('4. ✅ تم إنشاء جدول issue_courts للربط بين القضايا والمحاكم');
  console.log('5. ✅ تم نقل البيانات الموجودة بنجاح');
}

// تشغيل التحقق
verifyFinalRelations().catch(console.error);
