(()=>{var e={};e.id=787,e.ids=[787],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59070:(e,a,t)=>{"use strict";t.a(e,async(e,n)=>{try{t.r(a),t.d(a,{patchFetch:()=>l,routeModule:()=>d,serverHooks:()=>u,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var o=t(96559),r=t(48088),c=t(37719),s=t(94971),i=e([s]);s=(i.then?(await i)():i)[0];let d=new o.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/accounting/debtors-creditors/route",pathname:"/api/accounting/debtors-creditors",filename:"route",bundlePath:"app/api/accounting/debtors-creditors/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\debtors-creditors\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:u}=d;function l(){return(0,c.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}n()}catch(e){n(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},94971:(e,a,t)=>{"use strict";t.a(e,async(e,n)=>{try{t.r(a),t.d(a,{GET:()=>s});var o=t(32190),r=t(64939),c=e([r]);if(r=(c.then?(await c)():c)[0],!process.env.DB_PASSWORD)throw Error("DB_PASSWORD environment variable is required");let i=new r.Pool({user:"postgres",host:"localhost",database:"mohammi",password:process.env.DB_PASSWORD||"your_password_here",port:5432});async function s(e){try{let{searchParams:a}=new URL(e.url),t=a.get("as_of_date")||new Date().toISOString().split("T")[0],n=await i.connect();try{let e=`
        WITH account_balances AS (
          SELECT 
            coa.id,
            coa.account_code,
            coa.account_name,
            coa.account_type,
            coa.account_nature,
            coa.is_linked_record,
            coa.original_table,
            
            -- حساب الأرصدة
            COALESCE(SUM(
              CASE 
                WHEN jed.debit_amount > 0 THEN jed.debit_amount 
                ELSE 0 
              END
            ), 0) as debit_balance,
            
            COALESCE(SUM(
              CASE 
                WHEN jed.credit_amount > 0 THEN jed.credit_amount 
                ELSE 0 
              END
            ), 0) as credit_balance,
            
            -- عدد المعاملات
            COUNT(jed.id) as transactions_count,
            
            -- آخر معاملة
            MAX(je.entry_date) as last_transaction_date,
            
            -- معلومات إضافية للحسابات المرتبطة
            CASE 
              WHEN coa.original_table = 'clients' THEN c.name
              WHEN coa.original_table = 'employees' THEN e.name
              WHEN coa.original_table = 'suppliers' THEN s.name
              ELSE NULL
            END as linked_entity_name,
            
            CASE 
              WHEN coa.original_table = 'clients' THEN c.phone
              WHEN coa.original_table = 'employees' THEN e.phone
              WHEN coa.original_table = 'suppliers' THEN s.phone
              ELSE NULL
            END as linked_entity_phone,
            
            CASE 
              WHEN coa.original_table = 'clients' THEN c.email
              WHEN coa.original_table = 'employees' THEN e.email
              WHEN coa.original_table = 'suppliers' THEN s.email
              ELSE NULL
            END as linked_entity_email
            
          FROM chart_of_accounts coa
          LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
          LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id 
            AND je.entry_date <= $1
            AND je.status = 'approved'
          LEFT JOIN clients c ON coa.original_table = 'clients' 
            AND coa.linked_record_id = c.id
          LEFT JOIN employees e ON coa.original_table = 'employees' 
            AND coa.linked_record_id = e.id
          LEFT JOIN suppliers s ON coa.original_table = 'suppliers' 
            AND coa.linked_record_id = s.id
          
          WHERE coa.allow_transactions = true
          GROUP BY 
            coa.id, coa.account_code, coa.account_name, coa.account_type, 
            coa.account_nature, coa.is_linked_record, coa.original_table,
            c.name, c.phone, c.email,
            e.name, e.phone, e.email,
            s.name, s.phone, s.email
        )
        SELECT 
          *,
          -- حساب صافي الرصيد
          (debit_balance - credit_balance) as net_balance,
          
          -- تحديد نوع الرصيد
          CASE 
            WHEN (debit_balance - credit_balance) > 0 THEN 'مدين'
            WHEN (debit_balance - credit_balance) < 0 THEN 'دائن'
            ELSE 'متوازن'
          END as balance_type
          
        FROM account_balances
        WHERE debit_balance > 0 OR credit_balance > 0 OR transactions_count > 0
        ORDER BY 
          account_type,
          CASE WHEN is_linked_record THEN 1 ELSE 0 END,
          account_code
      `,a=await n.query(e,[t]),r=`
        WITH account_balances AS (
          SELECT 
            coa.id,
            coa.account_type,
            COALESCE(SUM(
              CASE 
                WHEN jed.debit_amount > 0 THEN jed.debit_amount 
                ELSE 0 
              END
            ), 0) as debit_balance,
            COALESCE(SUM(
              CASE 
                WHEN jed.credit_amount > 0 THEN jed.credit_amount 
                ELSE 0 
              END
            ), 0) as credit_balance
            
          FROM chart_of_accounts coa
          LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
          LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id 
            AND je.entry_date <= $1
            AND je.status = 'approved'
          
          WHERE coa.allow_transactions = true
          GROUP BY coa.id, coa.account_type
        ),
        balance_summary AS (
          SELECT 
            *,
            (debit_balance - credit_balance) as net_balance
          FROM account_balances
          WHERE debit_balance > 0 OR credit_balance > 0
        )
        SELECT 
          COALESCE(SUM(CASE WHEN net_balance > 0 THEN net_balance ELSE 0 END), 0) as total_debtors,
          COALESCE(SUM(CASE WHEN net_balance < 0 THEN ABS(net_balance) ELSE 0 END), 0) as total_creditors,
          COUNT(CASE WHEN net_balance > 0 THEN 1 END) as debtors_count,
          COUNT(CASE WHEN net_balance < 0 THEN 1 END) as creditors_count,
          COALESCE(SUM(net_balance), 0) as net_position
        FROM balance_summary
      `,c=await n.query(r,[t]);return o.NextResponse.json({success:!0,balances:a.rows,summary:c.rows[0]||{total_debtors:0,total_creditors:0,debtors_count:0,creditors_count:0,net_position:0},as_of_date:t})}finally{n.release()}}catch(e){return console.error("خطأ في جلب قائمة الدائنين والمدينين:",e),o.NextResponse.json({success:!1,error:"فشل في جلب قائمة الدائنين والمدينين",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}n()}catch(e){n(e)}})},96487:()=>{}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),n=a.X(0,[4447,580],()=>t(59070));module.exports=n})();