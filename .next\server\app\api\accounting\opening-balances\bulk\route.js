(()=>{var e={};e.id=7453,e.ids=[7453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>l});var a=r(64939),n=r(29021),o=r.n(n),i=r(33873),c=r.n(i),u=e([a]);a=(u.then?(await u)():u)[0];let p=null;try{let e=c().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],r=p.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),g=new a.Pool(d);async function l(e,t){let r=await g.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11178:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a=r(96559),n=r(48088),o=r(37719),i=r(30745),c=e([i]);i=(c.then?(await c)():c)[0];let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/opening-balances/bulk/route",pathname:"/api/accounting/opening-balances/bulk",filename:"route",bundlePath:"app/api/accounting/opening-balances/bulk/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\opening-balances\\bulk\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:g}=l;function u(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30745:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>i});var a=r(32190),n=r(5069),o=e([n]);async function i(e){try{let{balances:t}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return a.NextResponse.json({success:!1,error:"يجب إرسال مصفوفة من الأرصدة"},{status:400});let r=t.reduce((e,t)=>e+(t.debit_balance||0),0),s=t.reduce((e,t)=>e+(t.credit_balance||0),0);if(Math.abs(r-s)>.01)return a.NextResponse.json({success:!1,error:`الأرصدة غير متوازنة. المدين: ${r.toFixed(2)}, الدائن: ${s.toFixed(2)}`},{status:400});let o=t.map(e=>e.account_id),i=o.filter(e=>!String(e).startsWith("client_")&&!String(e).startsWith("employee_"));if(i.length>0&&(await (0,n.P)("SELECT id FROM chart_of_accounts WHERE id = ANY($1)",[i])).rows.length!==i.length)return a.NextResponse.json({success:!1,error:"بعض الحسابات المحددة غير موجودة"},{status:400});let c=o.filter(e=>String(e).startsWith("client_")).map(e=>String(e).replace("client_",""));if(c.length>0&&(await (0,n.P)("SELECT id FROM clients WHERE id = ANY($1)",[c])).rows.length!==c.length)return a.NextResponse.json({success:!1,error:"بعض العملاء المحددين غير موجودين"},{status:400});let u=o.filter(e=>String(e).startsWith("employee_")).map(e=>String(e).replace("employee_",""));if(u.length>0&&(await (0,n.P)("SELECT id FROM employees WHERE id = ANY($1)",[u])).rows.length!==u.length)return a.NextResponse.json({success:!1,error:"بعض الموظفين المحددين غير موجودين"},{status:400});let l=o.map(e=>String(e).startsWith("client_")||String(e).startsWith("employee_")?String(e):e);await (0,n.P)("DELETE FROM opening_balances WHERE account_id::text = ANY($1)",[l.map(e=>String(e))]);let p=t.map(e=>(0,n.P)(`INSERT INTO opening_balances (account_id, debit_balance, credit_balance, balance_date)
         VALUES ($1, $2, $3, $4)`,[String(e.account_id),e.debit_balance||0,e.credit_balance||0,e.balance_date]));return await Promise.all(p),a.NextResponse.json({success:!0,message:`تم حفظ ${t.length} رصيد افتتاحي بنجاح`,data:{count:t.length,totalDebit:r.toFixed(2),totalCredit:s.toFixed(2)}})}catch(e){return console.error("خطأ في حفظ الأرصدة الافتتاحية:",e),a.NextResponse.json({success:!1,error:"حدث خطأ في حفظ الأرصدة الافتتاحية"},{status:500})}}n=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(11178));module.exports=s})();