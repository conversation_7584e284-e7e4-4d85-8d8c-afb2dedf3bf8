(()=>{var e={};e.id=9877,e.ids=[9877],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>d});var s=r(64939),n=r(29021),o=r.n(n),i=r(33873),c=r.n(i),p=e([s]);s=(p.then?(await p)():p)[0];let u=null;try{let e=c().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");u=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!u?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let E=(()=>{let e=process.env.PORT||"7443";if(u&&u.routes[e]){let t=u.routes[e],r=u.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),y=new s.Pool(E);async function d(e,t){let r=await y.connect();try{return await r.query(e,t)}finally{r.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30892:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>p,routeModule:()=>d,serverHooks:()=>y,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>E});var s=r(96559),n=r(48088),o=r(37719),i=r(57271),c=e([i]);i=(c.then?(await c)():c)[0];let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/payments/route",pathname:"/api/accounting/payments",filename:"route",bundlePath:"app/api/accounting/payments/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\payments\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:u,workUnitAsyncStorage:E,serverHooks:y}=d;function p(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:E})}a()}catch(e){a(e)}})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57271:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{DELETE:()=>d,GET:()=>i,POST:()=>c,PUT:()=>p});var s=r(32190),n=r(5069),o=e([n]);async function i(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"20"),o=(r-1)*a,i=await (0,n.P)(`
      SELECT 
        p.*,
        e.name as entity_name,
        c.name as client_name,
        SUM(CASE WHEN pl.type = 1 THEN at.amount ELSE 0 END) as total_amount
      FROM payment p
      LEFT JOIN entity e ON p.entity_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      LEFT JOIN payment_links pl ON p.id = pl.payment_id
      LEFT JOIN acc_trans at ON pl.entry_id = at.entry_id
      GROUP BY p.id, e.name, c.name
      ORDER BY p.payment_date DESC, p.id DESC
      LIMIT $1 OFFSET $2
    `,[a,o]),c=await (0,n.P)("SELECT COUNT(*) as total FROM payment"),p=parseInt(c.rows[0].total);return s.NextResponse.json({success:!0,data:i.rows.map(e=>({...e,amount:parseFloat(e.total_amount||0)})),pagination:{page:r,limit:a,total:p,pages:Math.ceil(p/a)}})}catch(e){return console.error("Error fetching payments:",e),s.NextResponse.json({success:!1,error:"فشل في جلب المدفوعات"},{status:500})}}async function c(e){try{let{reference:t,payment_date:r,payment_class:a,entity_id:o,currency:i="SAR",amount:c,notes:p,employee_id:d,department_id:u}=await e.json();if(!t||!r||!a||!c)return s.NextResponse.json({success:!1,error:"البيانات المطلوبة ناقصة"},{status:400});if((await (0,n.P)("SELECT id FROM payment WHERE reference = $1",[t])).rows.length>0)return s.NextResponse.json({success:!1,error:"رقم المرجع موجود مسبقاً"},{status:400});await (0,n.P)("BEGIN");try{let e=(await (0,n.P)(`
        INSERT INTO gl (reference, description, transdate, notes, approved)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      `,[t,`${1===a?"مقبوضات":"مدفوعات"} - ${t}`,r,p,!1])).rows[0].id,E=await (0,n.P)(`
        INSERT INTO payment (
          reference, payment_date, payment_class, entity_id, 
          currency, notes, employee_id, department_id, gl_id, approved
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `,[t,r,a,o,i,p,d,u,e,!1]),y=E.rows[0].id,l=await (0,n.P)("SELECT id FROM chart_of_accounts WHERE accno = '1110'"),R=await (0,n.P)("SELECT id FROM chart_of_accounts WHERE accno = '1210'");if(l.rows.length>0&&R.rows.length>0)if(1===a){let a=await (0,n.P)(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `,[e,l.rows[0].id,c,r,`مقبوضات من العميل - ${t}`,!1]),s=await (0,n.P)(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `,[e,R.rows[0].id,-c,r,`مقبوضات من العميل - ${t}`,!1]);await (0,n.P)(`
            INSERT INTO payment_links (payment_id, entry_id, type)
            VALUES ($1, $2, 1), ($1, $3, 2)
          `,[y,a.rows[0].entry_id,s.rows[0].entry_id])}else{let a=await (0,n.P)(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `,[e,l.rows[0].id,-c,r,`مدفوعات للمورد - ${t}`,!1]),s=await (0,n.P)(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING entry_id
          `,[e,R.rows[0].id,c,r,`مدفوعات للمورد - ${t}`,!1]);await (0,n.P)(`
            INSERT INTO payment_links (payment_id, entry_id, type)
            VALUES ($1, $2, 1), ($1, $3, 2)
          `,[y,s.rows[0].entry_id,a.rows[0].entry_id])}return await (0,n.P)("COMMIT"),s.NextResponse.json({success:!0,message:"تم إنشاء المدفوعة بنجاح",data:E.rows[0]})}catch(e){throw await (0,n.P)("ROLLBACK"),e}}catch(e){return console.error("Error creating payment:",e),s.NextResponse.json({success:!1,error:"فشل في إنشاء المدفوعة"},{status:500})}}async function p(e){try{let{id:t,reference:r,payment_date:a,payment_class:o,entity_id:i,currency:c,notes:p,approved:d}=await e.json();if(!t)return s.NextResponse.json({success:!1,error:"معرف المدفوعة مطلوب"},{status:400});let u=await (0,n.P)(`
      UPDATE payment 
      SET 
        reference = COALESCE($2, reference),
        payment_date = COALESCE($3, payment_date),
        payment_class = COALESCE($4, payment_class),
        entity_id = COALESCE($5, entity_id),
        currency = COALESCE($6, currency),
        notes = COALESCE($7, notes),
        approved = COALESCE($8, approved)
      WHERE id = $1
      RETURNING *
    `,[t,r,a,o,i,c,p,d]);if(0===u.rows.length)return s.NextResponse.json({success:!1,error:"المدفوعة غير موجودة"},{status:404});return d&&(await (0,n.P)(`
        UPDATE gl 
        SET approved = true, approved_at = CURRENT_TIMESTAMP 
        WHERE id = (SELECT gl_id FROM payment WHERE id = $1)
      `,[t]),await (0,n.P)(`
        UPDATE acc_trans 
        SET approved = true 
        WHERE trans_id = (SELECT gl_id FROM payment WHERE id = $1)
      `,[t])),s.NextResponse.json({success:!0,message:"تم تحديث المدفوعة بنجاح",data:u.rows[0]})}catch(e){return console.error("Error updating payment:",e),s.NextResponse.json({success:!1,error:"فشل في تحديث المدفوعة"},{status:500})}}async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return s.NextResponse.json({success:!1,error:"معرف المدفوعة مطلوب"},{status:400});let a=await (0,n.P)("SELECT approved, gl_id FROM payment WHERE id = $1",[r]);if(0===a.rows.length)return s.NextResponse.json({success:!1,error:"المدفوعة غير موجودة"},{status:404});if(a.rows[0].approved)return s.NextResponse.json({success:!1,error:"لا يمكن حذف مدفوعة معتمدة"},{status:400});await (0,n.P)("BEGIN");try{let e=a.rows[0].gl_id;return await (0,n.P)("DELETE FROM payment_links WHERE payment_id = $1",[r]),e&&(await (0,n.P)("DELETE FROM acc_trans WHERE trans_id = $1",[e]),await (0,n.P)("DELETE FROM gl WHERE id = $1",[e])),await (0,n.P)("DELETE FROM payment WHERE id = $1",[r]),await (0,n.P)("COMMIT"),s.NextResponse.json({success:!0,message:"تم حذف المدفوعة بنجاح"})}catch(e){throw await (0,n.P)("ROLLBACK"),e}}catch(e){return console.error("Error deleting payment:",e),s.NextResponse.json({success:!1,error:"فشل في حذف المدفوعة"},{status:500})}}n=(o.then?(await o)():o)[0],a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(30892));module.exports=a})();