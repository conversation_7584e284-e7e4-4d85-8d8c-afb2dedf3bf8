import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 إنشاء بيانات تجريبية للعملاء والموظفين...')
    
    // 1. إنشاء جدول العملاء إذا لم يكن موجوداً
    await query(`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        id_number VARCHAR(20) UNIQUE,
        client_type VARCHAR(100),
        username VARCHAR(100),
        password_hash VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active',
        client_number VARCHAR(50),
        account_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    // 2. إن<PERSON><PERSON><PERSON> جدول الموظفين إذا لم يكن موجوداً
    await query(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255),
        department_id INTEGER,
        branch_id INTEGER,
        governorate_id INTEGER,
        phone VARCHAR(20),
        email VARCHAR(255),
        salary DECIMAL(10,2),
        hire_date DATE,
        employee_number VARCHAR(50),
        account_id INTEGER,
        status VARCHAR(20) DEFAULT 'active',
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    
    // 3. حذف البيانات الموجودة
    await query('DELETE FROM clients')
    await query('DELETE FROM employees')
    
    // 4. إنشاء عملاء تجريبيين
    const clients = [
      {
        name: 'فاطمة أحمد حسن',
        phone: '**********',
        email: '<EMAIL>',
        address: 'الرياض، حي النخيل',
        id_number: '**********',
        client_type: 'فرد',
        client_number: 'C001'
      },
      {
        name: 'محمد عبدالله صالح',
        phone: '**********',
        email: '<EMAIL>',
        address: 'جدة، حي الصفا',
        id_number: '**********',
        client_type: 'فرد',
        client_number: 'C002'
      },
      {
        name: 'عائشة علي محمد',
        phone: '0551122334',
        email: '<EMAIL>',
        address: 'الدمام، حي الفيصلية',
        id_number: '1122334455',
        client_type: 'فرد',
        client_number: 'C003'
      },
      {
        name: 'شركة النور للتجارة',
        phone: '0112345678',
        email: '<EMAIL>',
        address: 'الرياض، حي العليا',
        id_number: '7001234567',
        client_type: 'شركة',
        client_number: 'C004'
      },
      {
        name: 'أحمد سعد الغامدي',
        phone: '0509876543',
        email: '<EMAIL>',
        address: 'مكة المكرمة، حي العزيزية',
        id_number: '2233445566',
        client_type: 'فرد',
        client_number: 'C005'
      }
    ]
    
    let clientsInserted = 0
    for (const client of clients) {
      await query(`
        INSERT INTO clients (
          name, phone, email, address, id_number, 
          client_type, client_number, status, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', true)
      `, [
        client.name, client.phone, client.email, client.address,
        client.id_number, client.client_type, client.client_number
      ])
      clientsInserted++
    }
    
    // 5. إنشاء موظفين تجريبيين
    const employees = [
      {
        name: 'خالد أحمد المحامي',
        position: 'محامي أول',
        phone: '0501111111',
        email: '<EMAIL>',
        salary: 15000.00,
        employee_number: 'E001'
      },
      {
        name: 'سارة محمد المستشارة',
        position: 'مستشارة قانونية',
        phone: '0502222222',
        email: '<EMAIL>',
        salary: 12000.00,
        employee_number: 'E002'
      },
      {
        name: 'عبدالرحمن علي الكاتب',
        position: 'كاتب قانوني',
        phone: '0503333333',
        email: '<EMAIL>',
        salary: 8000.00,
        employee_number: 'E003'
      },
      {
        name: 'نورا سالم المحاسبة',
        position: 'محاسبة',
        phone: '0504444444',
        email: '<EMAIL>',
        salary: 9000.00,
        employee_number: 'E004'
      },
      {
        name: 'فهد عبدالله المدير',
        position: 'مدير المكتب',
        phone: '0505555555',
        email: '<EMAIL>',
        salary: 20000.00,
        employee_number: 'E005'
      }
    ]
    
    let employeesInserted = 0
    for (const employee of employees) {
      await query(`
        INSERT INTO employees (
          name, position, phone, email, salary, 
          employee_number, hire_date, status, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_DATE, 'active', true)
      `, [
        employee.name, employee.position, employee.phone, 
        employee.email, employee.salary, employee.employee_number
      ])
      employeesInserted++
    }
    
    
    
    return NextResponse.json({
      success: true,
      message: `تم إنشاء ${clientsInserted} عميل و ${employeesInserted} موظف بنجاح`,
      data: {
        clientsInserted,
        employeesInserted
      }
    })
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error)
    return NextResponse.json({
      success: false,
      message: 'فشل في إنشاء البيانات التجريبية',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
