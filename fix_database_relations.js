// إصلاح العلاقات في قاعدة البيانات
const { Pool } = require('pg');

async function fixDatabaseRelations() {
  console.log('🔧 إصلاح العلاقات في قاعدة البيانات...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص العلاقات الحالية
      console.log('\n   🔍 فحص العلاقات الحالية:');
      
      const currentConstraints = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name IN ('issues', 'courts')
        ORDER BY tc.table_name, kcu.column_name
      `);

      console.log('      المفاتيح الخارجية الحالية:');
      currentConstraints.rows.forEach(constraint => {
        console.log(`         - ${constraint.table_name}.${constraint.column_name} -> ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
      });

      // 2. حذف جميع المفاتيح الخارجية الخاطئة
      console.log('\n   🗑️ حذف المفاتيح الخارجية الخاطئة:');
      
      for (const constraint of currentConstraints.rows) {
        try {
          await pool.query(`ALTER TABLE ${constraint.table_name} DROP CONSTRAINT IF EXISTS ${constraint.constraint_name}`);
          console.log(`      ✅ تم حذف: ${constraint.constraint_name}`);
        } catch (error) {
          console.log(`      ⚠️ خطأ في حذف ${constraint.constraint_name}: ${error.message}`);
        }
      }

      // 3. إنشاء جدول المحاكم إذا لم يكن موجوداً
      console.log('\n   🏛️ إنشاء/تحديث جدول المحاكم:');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS courts (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          type VARCHAR(100), -- نوع المحكمة (جنائية، مدنية، تجارية، إلخ)
          location VARCHAR(255),
          address TEXT,
          phone VARCHAR(20),
          email VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('      ✅ تم إنشاء/تحديث جدول المحاكم');

      // إدراج المحاكم الأساسية
      const defaultCourts = [
        { name: 'المحكمة الجنائية الابتدائية', type: 'جنائية', location: 'صنعاء' },
        { name: 'المحكمة المدنية الابتدائية', type: 'مدنية', location: 'صنعاء' },
        { name: 'المحكمة التجارية', type: 'تجارية', location: 'صنعاء' },
        { name: 'محكمة الأحوال الشخصية', type: 'أحوال شخصية', location: 'صنعاء' },
        { name: 'المحكمة الإدارية', type: 'إدارية', location: 'صنعاء' },
        { name: 'محكمة الاستئناف', type: 'استئناف', location: 'صنعاء' },
        { name: 'المحكمة العليا', type: 'عليا', location: 'صنعاء' }
      ];

      for (const court of defaultCourts) {
        try {
          await pool.query(`
            INSERT INTO courts (name, type, location)
            VALUES ($1, $2, $3)
            ON CONFLICT (name) DO NOTHING
          `, [court.name, court.type, court.location]);
        } catch (error) {
          console.log(`      ⚠️ خطأ في إدراج محكمة ${court.name}: ${error.message}`);
        }
      }
      console.log('      ✅ تم إدراج المحاكم الأساسية');

      // 4. إنشاء جدول العلاقة بين القضايا والمحاكم (Many-to-Many)
      console.log('\n   🔗 إنشاء جدول العلاقة بين القضايا والمحاكم:');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS issue_courts (
          id SERIAL PRIMARY KEY,
          issue_id INTEGER NOT NULL,
          court_id INTEGER NOT NULL,
          court_level VARCHAR(50), -- مستوى المحكمة في هذه القضية (ابتدائية، استئناف، عليا)
          case_status VARCHAR(100), -- حالة القضية في هذه المحكمة
          start_date DATE, -- تاريخ بداية القضية في هذه المحكمة
          end_date DATE, -- تاريخ انتهاء القضية في هذه المحكمة
          next_hearing_date TIMESTAMP, -- تاريخ الجلسة القادمة
          notes TEXT, -- ملاحظات خاصة بهذه المحكمة
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(issue_id, court_id, court_level) -- منع التكرار لنفس القضية في نفس المحكمة بنفس المستوى
        )
      `);
      console.log('      ✅ تم إنشاء جدول issue_courts');

      // 5. إصلاح جدول القضايا - إزالة court_id وإضافة client_id صحيح
      console.log('\n   📋 إصلاح جدول القضايا:');
      
      // التحقق من وجود العمود court_id وحذفه
      const courtIdExists = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'court_id'
      `);

      if (courtIdExists.rows.length > 0) {
        console.log('      🗑️ حذف العمود court_id من جدول القضايا...');
        await pool.query(`ALTER TABLE issues DROP COLUMN IF EXISTS court_id`);
        console.log('      ✅ تم حذف العمود court_id');
      }

      // التأكد من وجود client_id
      const clientIdExists = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'client_id'
      `);

      if (clientIdExists.rows.length === 0) {
        console.log('      ➕ إضافة العمود client_id...');
        await pool.query(`ALTER TABLE issues ADD COLUMN client_id INTEGER`);
        console.log('      ✅ تم إضافة العمود client_id');
      }

      // 6. إنشاء المفاتيح الخارجية الصحيحة
      console.log('\n   🔗 إنشاء المفاتيح الخارجية الصحيحة:');

      // مفتاح خارجي للعملاء (One-to-Many)
      try {
        await pool.query(`
          ALTER TABLE issues 
          ADD CONSTRAINT fk_issues_client_id 
          FOREIGN KEY (client_id) REFERENCES clients(id)
          ON DELETE SET NULL
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء مفتاح خارجي: issues.client_id -> clients.id');
      } catch (error) {
        console.log(`      ⚠️ مفتاح العملاء: ${error.message}`);
      }

      // مفاتيح خارجية لجدول issue_courts (Many-to-Many)
      try {
        await pool.query(`
          ALTER TABLE issue_courts 
          ADD CONSTRAINT fk_issue_courts_issue_id 
          FOREIGN KEY (issue_id) REFERENCES issues(id)
          ON DELETE CASCADE
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء مفتاح خارجي: issue_courts.issue_id -> issues.id');
      } catch (error) {
        console.log(`      ⚠️ مفتاح القضايا: ${error.message}`);
      }

      try {
        await pool.query(`
          ALTER TABLE issue_courts 
          ADD CONSTRAINT fk_issue_courts_court_id 
          FOREIGN KEY (court_id) REFERENCES courts(id)
          ON DELETE CASCADE
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء مفتاح خارجي: issue_courts.court_id -> courts.id');
      } catch (error) {
        console.log(`      ⚠️ مفتاح المحاكم: ${error.message}`);
      }

      // 7. نقل البيانات الموجودة
      console.log('\n   📦 نقل البيانات الموجودة:');
      
      // نقل بيانات المحاكم من court_name إلى جدول المحاكم وربطها
      const issuesWithCourtNames = await pool.query(`
        SELECT id, case_number, court_name 
        FROM issues 
        WHERE court_name IS NOT NULL AND court_name != ''
      `);

      for (const issue of issuesWithCourtNames.rows) {
        try {
          // البحث عن المحكمة أو إنشاؤها
          let court = await pool.query(`
            SELECT id FROM courts WHERE name = $1
          `, [issue.court_name]);

          let courtId;
          if (court.rows.length === 0) {
            // إنشاء محكمة جديدة
            const newCourt = await pool.query(`
              INSERT INTO courts (name, type, location)
              VALUES ($1, 'عامة', 'غير محدد')
              RETURNING id
            `, [issue.court_name]);
            courtId = newCourt.rows[0].id;
            console.log(`      ➕ تم إنشاء محكمة جديدة: "${issue.court_name}" (ID: ${courtId})`);
          } else {
            courtId = court.rows[0].id;
          }

          // ربط القضية بالمحكمة
          await pool.query(`
            INSERT INTO issue_courts (issue_id, court_id, court_level, case_status)
            VALUES ($1, $2, 'ابتدائية', 'نشطة')
            ON CONFLICT (issue_id, court_id, court_level) DO NOTHING
          `, [issue.id, courtId]);

        } catch (error) {
          console.log(`      ❌ خطأ في نقل بيانات القضية ${issue.case_number}: ${error.message}`);
        }
      }

      console.log(`      ✅ تم نقل بيانات ${issuesWithCourtNames.rows.length} قضية`);

      // 8. التحقق النهائي
      console.log('\n   📊 التحقق النهائي:');
      
      const finalCheck = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM clients) as total_clients,
          (SELECT COUNT(*) FROM courts) as total_courts,
          (SELECT COUNT(*) FROM issue_courts) as total_issue_courts,
          (SELECT COUNT(*) FROM issues WHERE client_id IS NOT NULL) as issues_with_clients
      `);

      const stats = finalCheck.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - إجمالي العملاء: ${stats.total_clients}`);
      console.log(`      - إجمالي المحاكم: ${stats.total_courts}`);
      console.log(`      - علاقات القضايا-المحاكم: ${stats.total_issue_courts}`);
      console.log(`      - قضايا مرتبطة بعملاء: ${stats.issues_with_clients}`);

      // فحص العلاقات الجديدة
      const newConstraints = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name IN ('issues', 'issue_courts')
        ORDER BY tc.table_name, kcu.column_name
      `);

      console.log('\n      العلاقات الجديدة:');
      newConstraints.rows.forEach(constraint => {
        console.log(`         ✅ ${constraint.table_name}.${constraint.column_name} -> ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
      });

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح العلاقات');
  
  console.log('\n📋 ملخص العلاقات الجديدة:');
  console.log('1. 👥 العملاء -> القضايا: One-to-Many (عميل واحد له عدة قضايا)');
  console.log('2. 🏛️ المحاكم <-> القضايا: Many-to-Many (قضية في عدة محاكم، محكمة لها عدة قضايا)');
  console.log('3. 🔗 جدول issue_courts: يربط القضايا بالمحاكم مع معلومات إضافية');
  console.log('4. 🗑️ تم حذف جميع العلاقات الخاطئة السابقة');
}

// تشغيل الإصلاح
fixDatabaseRelations().catch(console.error);
