(()=>{var e={};e.id=4763,e.ids=[4763],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\default-accounts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\default-accounts\\page.tsx","default")},7194:(e,t,s)=>{Promise.resolve().then(s.bind(s,82539))},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>x});var a=s(60687),r=s(43210),l=s(58106),n=s(78272),c=s(3589),i=s(13964),d=s(4780);let o=l.bL;l.YJ;let x=l.WT,m=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 text-gray-700"})})]}));m.displayName=l.l9.displayName;let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(c.A,{className:"h-4 w-4"})}));u.displayName=l.PP.displayName;let h=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let p=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})}));p.displayName=l.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.JU.displayName;let f=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:t})]}));f.displayName=l.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.wv.displayName},15882:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),c=s(30893),i={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);s.d(t,i);let d={children:["",{children:["accounting",{children:["default-accounts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5441)),"D:\\mohaminew\\src\\app\\accounting\\default-accounts\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\default-accounts\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/accounting/default-accounts/page",pathname:"/accounting/default-accounts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},17810:(e,t,s)=>{Promise.resolve().then(s.bind(s,5441))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35583:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70352:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>x});var a=s(60687),r=s(43210),l=s(49384);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=l.$;var i=s(4780);let d=((e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return c(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:r,defaultVariants:l}=t,i=Object.keys(r).map(e=>{let t=null==s?void 0:s[e],a=null==l?void 0:l[e];if(null===t)return null;let c=n(t)||n(a);return r[e][c]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return c(e,i,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...d}[t]):({...l,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)})("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(d({variant:t}),e),...s}));o.displayName="Alert",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let x=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));x.displayName="AlertDescription"},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},82539:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(60687),r=s(43210),l=s(98254),n=s(44493),c=s(29523),i=s(15079),d=s(70352);s(25541),s(12640),s(35583),s(23928);var o=s(84027),x=s(78122),m=s(8819),u=s(5336),h=s(93613);function p(){let[e,t]=(0,r.useState)([]),[s,p]=(0,r.useState)([]),[f,g]=(0,r.useState)(!0),[j,v]=(0,r.useState)(!1),[y,N]=(0,r.useState)(null),[b,w]=(0,r.useState)({}),_=async()=>{g(!0);try{let e=await fetch("/api/accounting/default-accounts"),s=await e.json(),a=await fetch("/api/accounting/chart-of-accounts?only_transactional=true"),r=await a.json();if(s.success&&r.success){t(s.data||[]),p(r.accounts||[]);let e={};s.data?.forEach(t=>{e[t.id]=t.chart_account_id}),w(e)}else N({type:"error",text:"فشل في جلب البيانات"})}catch(e){console.error("Error fetching data:",e),N({type:"error",text:"حدث خطأ في جلب البيانات"})}finally{g(!1)}},A=(e,t)=>{w({...b,[e]:t?parseInt(t):null})},k=async()=>{v(!0),N(null);try{Object.entries(b).map(([e,t])=>({id:parseInt(e),chart_account_id:t}));let e=Object.entries(b).map(async([e,t])=>t?(await fetch("/api/accounting/default-accounts",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:parseInt(e),chart_account_id:t})})).json():{success:!0});(await Promise.all(e)).every(e=>e.success)?(N({type:"success",text:"تم حفظ ربط الحسابات الأساسية بنجاح"}),_()):N({type:"error",text:"فشل في حفظ بعض الحسابات"})}catch(e){console.error("Error saving main accounts:",e),N({type:"error",text:"حدث خطأ في حفظ ربط الحسابات"})}finally{v(!1)}};return f?(0,a.jsx)(l.O,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"mr-2",children:"جاري التحميل..."})]})}):(0,a.jsx)(l.O,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-emerald-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"الحسابات الأساسية"}),(0,a.jsx)("p",{className:"text-gray-600",children:"ربط الحسابات الأساسية للنظام بدليل الحسابات المحاسبي"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,a.jsxs)(c.$,{onClick:_,variant:"outline",disabled:f,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 ml-2"}),"تحديث"]}),(0,a.jsxs)(c.$,{onClick:k,disabled:j,className:"bg-emerald-600 hover:bg-emerald-700",children:[j?(0,a.jsx)(x.A,{className:"h-4 w-4 ml-2 animate-spin"}):(0,a.jsx)(m.A,{className:"h-4 w-4 ml-2"}),"حفظ الربط"]})]})]}),y&&(0,a.jsxs)(d.Fc,{className:"success"===y.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:["success"===y.type?(0,a.jsx)(u.A,{className:"h-4 w-4"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(d.TN,{className:"success"===y.type?"text-green-800":"text-red-800",children:y.text})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي الحسابات الأساسية"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>e.chart_account_id).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"الحسابات المربوطة"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.filter(e=>!e.chart_account_id).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"الحسابات غير المربوطة"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.filter(e=>e.is_required).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"الحسابات المطلوبة"})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border",dir:"rtl",children:(0,a.jsxs)("div",{className:"divide-y divide-gray-200",children:[(0,a.jsx)("div",{className:"bg-gray-100 px-6 py-3 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"العملاء"})}),e.filter(e=>e.account_name.includes("عملاء")||e.account_name.includes("العملاء")).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-4 px-6 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"flex-1 text-right",children:(0,a.jsx)("span",{className:"text-lg font-medium text-gray-900",children:e.account_name})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(i.l6,{value:b[e.id]?.toString()||"",onValueChange:t=>A(e.id,t),children:[(0,a.jsx)(i.bq,{className:"w-80 h-10 text-right",children:(0,a.jsx)(i.yv,{placeholder:"اختر حساب من دليل الحسابات"})}),(0,a.jsx)(i.gC,{className:"max-h-60",children:s.map(e=>(0,a.jsx)(i.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full text-right",children:[(0,a.jsx)("span",{className:"flex-1",children:e.account_name}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:e.account_code})]})},e.id))})]}),e.chart_account_id&&(0,a.jsx)("div",{className:"text-sm text-green-600 min-w-20 text-center",children:(0,a.jsx)("span",{children:e.account_code})})]})]},e.id)),(0,a.jsx)("div",{className:"bg-gray-100 px-6 py-3 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"الموظفين"})}),e.filter(e=>e.account_name.includes("موظف")||e.account_name.includes("الموظفين")).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-4 px-6 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"flex-1 text-right",children:(0,a.jsx)("span",{className:"text-lg font-medium text-gray-900",children:e.account_name})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(i.l6,{value:b[e.id]?.toString()||"",onValueChange:t=>A(e.id,t),children:[(0,a.jsx)(i.bq,{className:"w-80 h-10 text-right",children:(0,a.jsx)(i.yv,{placeholder:"اختر حساب من دليل الحسابات"})}),(0,a.jsx)(i.gC,{className:"max-h-60",children:s.map(e=>(0,a.jsx)(i.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full text-right",children:[(0,a.jsx)("span",{className:"flex-1",children:e.account_name}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:e.account_code})]})},e.id))})]}),e.chart_account_id&&(0,a.jsx)("div",{className:"text-sm text-green-600 min-w-20 text-center",children:(0,a.jsx)("span",{children:e.account_code})})]})]},e.id)),(0,a.jsx)("div",{className:"bg-gray-100 px-6 py-3 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"المدينين"})}),e.filter(e=>e.account_name.includes("مدين")||e.account_name.includes("المدينين")).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-4 px-6 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"flex-1 text-right",children:(0,a.jsx)("span",{className:"text-lg font-medium text-gray-900",children:e.account_name})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(i.l6,{value:b[e.id]?.toString()||"",onValueChange:t=>A(e.id,t),children:[(0,a.jsx)(i.bq,{className:"w-80 h-10 text-right",children:(0,a.jsx)(i.yv,{placeholder:"اختر حساب من دليل الحسابات"})}),(0,a.jsx)(i.gC,{className:"max-h-60",children:s.map(e=>(0,a.jsx)(i.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full text-right",children:[(0,a.jsx)("span",{className:"flex-1",children:e.account_name}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:e.account_code})]})},e.id))})]}),e.chart_account_id&&(0,a.jsx)("div",{className:"text-sm text-green-600 min-w-20 text-center",children:(0,a.jsx)("span",{children:e.account_code})})]})]},e.id)),(0,a.jsx)("div",{className:"bg-gray-100 px-6 py-3 border-b",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"حسابات فرعية"})}),e.filter(e=>!e.account_name.includes("عملاء")&&!e.account_name.includes("العملاء")&&!e.account_name.includes("موظف")&&!e.account_name.includes("الموظفين")&&!e.account_name.includes("مدين")&&!e.account_name.includes("المدينين")).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-4 px-6 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"flex-1 text-right",children:(0,a.jsx)("span",{className:"text-lg font-medium text-gray-900",children:e.account_name})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(i.l6,{value:b[e.id]?.toString()||"",onValueChange:t=>A(e.id,t),children:[(0,a.jsx)(i.bq,{className:"w-80 h-10 text-right",children:(0,a.jsx)(i.yv,{placeholder:"اختر حساب من دليل الحسابات"})}),(0,a.jsx)(i.gC,{className:"max-h-60",children:s.map(e=>(0,a.jsx)(i.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full text-right",children:[(0,a.jsx)("span",{className:"flex-1",children:e.account_name}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:e.account_code})]})},e.id))})]}),e.chart_account_id&&(0,a.jsx)("div",{className:"text-sm text-green-600 min-w-20 text-center",children:(0,a.jsx)("span",{children:e.account_code})})]})]},e.id))]})}),(0,a.jsx)(n.Zp,{className:"bg-blue-50 border-blue-200",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"معلومات مهمة"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• يتم ربط كل حساب أساسي بحساب محدد من دليل الحسابات"}),(0,a.jsx)("li",{children:"• عند الربط، يتم تحديث كود الحساب تلقائياً في جدول الحسابات الأساسية"}),(0,a.jsx)("li",{children:"• يُفضل اختيار الحسابات التفصيلية (المستوى 3 أو أعلى) للربط"}),(0,a.jsx)("li",{children:"• الحسابات المطلوبة يجب ربطها لضمان عمل النظام بشكل صحيح"}),(0,a.jsx)("li",{children:'• يمكن إلغاء الربط في أي وقت عن طريق اختيار "إلغاء الربط"'})]})]})]})})})]})})}},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,8409,4036,8106,7932],()=>s(15882));module.exports=a})();