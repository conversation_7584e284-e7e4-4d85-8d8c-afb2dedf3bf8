"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    var _currencies_find;\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [dbError, setDbError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currencies, setCurrencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [issueTypes, setIssueTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_id: '',\n        court_name: '',\n        issue_type_id: '',\n        issue_type: '',\n        status: 'pending',\n        case_amount: '',\n        currency_id: '1',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n    });\n    // جلب المحاكم من قاعدة البيانات\n    const fetchCourts = async ()=>{\n        try {\n            const response = await fetch('/api/courts');\n            const result = await response.json();\n            if (result.success) {\n                setCourts(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching courts:', error);\n        }\n    };\n    // جلب العملات من قاعدة البيانات\n    const fetchCurrencies = async ()=>{\n        try {\n            const response = await fetch('/api/currencies');\n            const result = await response.json();\n            if (result.success) {\n                setCurrencies(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching currencies:', error);\n        }\n    };\n    // جلب العملاء من قاعدة البيانات\n    const fetchClients = async ()=>{\n        try {\n            const response = await fetch('/api/clients');\n            const result = await response.json();\n            if (result.success) {\n                setClients(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching clients:', error);\n        }\n    };\n    // جلب أنواع القضايا من قاعدة البيانات\n    const fetchIssueTypes = async ()=>{\n        try {\n            const response = await fetch('/api/issue-types');\n            const result = await response.json();\n            if (result.success) {\n                setIssueTypes(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching issue types:', error);\n        }\n    };\n    // حساب المبلغ بالريال اليمني\n    const calculateAmountYer = (amount, currencyId)=>{\n        const numAmount = parseFloat(amount) || 0;\n        const currency = currencies.find((c)=>c.id.toString() === currencyId);\n        if (!currency) return numAmount;\n        if (currency.is_base_currency) {\n            return numAmount;\n        }\n        return numAmount * currency.exchange_rate;\n    };\n    // جلب البيانات من قاعدة البيانات\n    const fetchIssues = async ()=>{\n        setIsLoading(true);\n        setDbError(null);\n        try {\n            const response = await fetch('/api/issues');\n            const result = await response.json();\n            if (result.success) {\n                setIssues(result.data);\n            } else {\n                setDbError(result.error || 'فشل في جلب بيانات القضايا');\n                setIssues([]);\n            }\n        } catch (error) {\n            console.error('Network error:', error);\n            setDbError('فشل في الاتصال بقاعدة البيانات');\n            setIssues([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            fetchIssues();\n            fetchCourts();\n            fetchCurrencies();\n            fetchClients();\n            fetchIssueTypes();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'new':\n                return 'bg-purple-100 text-purple-800';\n            case 'pending':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'new':\n                return 'جديدة';\n            case 'pending':\n                return 'معلقة';\n            case 'in_progress':\n                return 'قيد المعالجة';\n            case 'completed':\n                return 'مكتملة';\n            case 'cancelled':\n                return 'ملغية';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const handleAddNew = ()=>{\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_id: '',\n            court_name: '',\n            issue_type_id: '',\n            issue_type: '',\n            status: 'pending',\n            case_amount: '',\n            currency_id: '1',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n    };\n    const handleClientChange = (clientId, clientData)=>{\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: clientData ? clientData.name : '',\n            client_phone: clientData ? clientData.phone : ''\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                // إضافة رقم المستخدم تلقائياً (يمكن الحصول عليه من الجلسة لاحقاً)\n                const dataToSubmit = {\n                    ...formData,\n                    created_by: 1 // رقم المستخدم الافتراضي - يجب تحديثه ليأخذ من الجلسة\n                };\n                console.log('Data being submitted for new issue:', dataToSubmit);\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                // إرسال جميع البيانات للتحديث\n                const dataToSubmit = {\n                    ...formData\n                };\n                console.log('Sending data to API:', dataToSubmit);\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    const handleViewIssue = (issue)=>{\n        console.log('handleViewIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        // معالجة البيانات الفارغة أو null\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || 'لا يوجد وصف',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || 'غير محدد',\n            court_id: issue.court_id ? issue.court_id.toString() : '',\n            court_name: issue.court_name || 'غير محدد',\n            issue_type_id: issue.issue_type_id ? issue.issue_type_id.toString() : '',\n            issue_type: issue.issue_type || 'عام',\n            status: issue.status || 'new',\n            case_amount: issue.case_amount ? issue.case_amount.toString() : '0',\n            currency_id: issue.currency_id ? issue.currency_id.toString() : '1',\n            notes: issue.notes || 'لا توجد ملاحظات',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleViewIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    const handleEditIssue = (issue)=>{\n        console.log('handleEditIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        // معالجة البيانات الفارغة أو null\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || 'لا يوجد وصف',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || 'غير محدد',\n            court_id: issue.court_id ? issue.court_id.toString() : '',\n            court_name: issue.court_name || 'غير محدد',\n            issue_type_id: issue.issue_type_id ? issue.issue_type_id.toString() : '',\n            issue_type: issue.issue_type || 'عام',\n            status: issue.status || 'new',\n            case_amount: issue.case_amount ? issue.case_amount.toString() : '0',\n            currency_id: issue.currency_id ? issue.currency_id.toString() : '1',\n            notes: issue.notes || 'لا توجد ملاحظات',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleEditIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    const handleDeleteIssue = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه القضية؟')) return;\n        try {\n            const response = await fetch(\"/api/issues/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert('تم حذف القضية بنجاح');\n                fetchIssues();\n            } else {\n                alert(result.error || 'فشل في حذف القضية');\n            }\n        } catch (error) {\n            console.error('Error deleting issue:', error);\n            alert('حدث خطأ في حذف القضية');\n        }\n    };\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-6 bg-white min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إدارة القضايا\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mt-1\",\n                                        children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAddNew,\n                                className: \"bg-emerald-600 hover:bg-emerald-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إضافة قضية جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"إجمالي القضايا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.new\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا جديدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قيد المعالجة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.completed\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا مكتملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"new\",\n                                                    children: \"جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pending\",\n                                                    children: \"معلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"in_progress\",\n                                                    children: \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة القضايا (\",\n                                        filteredIssues.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"رقم القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"الموكل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المبلغ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredIssues.map((issue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium text-blue-600\",\n                                                                children: issue.case_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium\",\n                                                                children: issue.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        issue.client_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: issue.client_phone || 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: issue.court_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-blue-50 text-blue-700\",\n                                                                    children: issue.issue_type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: issue.contract_method === 'بالجلسة' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700',\n                                                                    children: issue.contract_method\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    className: getStatusColor(issue.status),\n                                                                    children: getStatusText(issue.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4 font-medium text-green-600\",\n                                                                children: [\n                                                                    issue.amount ? Math.floor(issue.amount).toLocaleString() : '0',\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleViewIssue(issue),\n                                                                            className: \"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300\",\n                                                                            title: \"عرض تفاصيل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleEditIssue(issue),\n                                                                            className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300\",\n                                                                            title: \"تعديل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleDeleteIssue(issue.id),\n                                                                            className: \"bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300\",\n                                                                            title: \"حذف القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, issue.id, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this),\n                    isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto border-t-4 border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 19\n                                                }, this),\n                                                modalType === 'add' && '📋 إضافة قضية جديدة',\n                                                modalType === 'edit' && '✏️ تعديل القضية',\n                                                modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsModalOpen(false),\n                                            className: \"hover:bg-red-50 hover:text-red-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-10 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_number\",\n                                                            className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"case_number\",\n                                                            value: formData.case_number,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    case_number: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل رقم القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-7\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"title\",\n                                                            value: formData.title,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    title: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل عنوان القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"description\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"description\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل وصف مختصر للقضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDC64 الموكل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__.ClientSelect, {\n                                                                value: formData.client_id,\n                                                                onChange: handleClientChange,\n                                                                label: \"\",\n                                                                placeholder: \"اختر الموكل...\",\n                                                                required: true,\n                                                                disabled: modalType === 'view'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"court_id\",\n                                                            className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"court_id\",\n                                                            value: formData.court_id || \"\",\n                                                            onChange: (e)=>{\n                                                                const selectedCourt = courts.find((c)=>c.id.toString() === e.target.value);\n                                                                setFormData({\n                                                                    ...formData,\n                                                                    court_id: e.target.value,\n                                                                    court_name: (selectedCourt === null || selectedCourt === void 0 ? void 0 : selectedCourt.name) || ''\n                                                                });\n                                                            },\n                                                            className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المحكمة...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: court.id,\n                                                                        children: [\n                                                                            court.name,\n                                                                            \" - \",\n                                                                            court.governorate_name\n                                                                        ]\n                                                                    }, court.id, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_method\",\n                                                            className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC4 طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"contract_method\",\n                                                            value: formData.contract_method || \"بالجلسة\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_method: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالجلسة\",\n                                                                    children: \"بالجلسة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالعقد\",\n                                                                    children: \"بالعقد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDD04 حالة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            value: formData.status || \"new\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"new\",\n                                                                    children: \"جديدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"pending\",\n                                                                    children: \"معلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in_progress\",\n                                                                    children: \"قيد المعالجة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"completed\",\n                                                                    children: \"مكتملة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cancelled\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"issue_type\",\n                                                            className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"⚖️ نوع القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"issue_type\",\n                                                            value: formData.issue_type || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    issue_type: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر نوع القضية...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"مدنية\",\n                                                                    children: \"مدنية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"تجارية\",\n                                                                    children: \"تجارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"جنائية\",\n                                                                    children: \"جنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"أحوال شخصية\",\n                                                                    children: \"أحوال شخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"عمالية\",\n                                                                    children: \"عمالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"إدارية\",\n                                                                    children: \"إدارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCCA حالة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            value: formData.status,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"new\",\n                                                                    children: \"جديدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"pending\",\n                                                                    children: \"معلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in_progress\",\n                                                                    children: \"قيد المعالجة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"completed\",\n                                                                    children: \"مكتملة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cancelled\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"issue_type\",\n                                                            className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"⚖️ نوع القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"issue_type\",\n                                                            value: formData.issue_type || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    issue_type: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر نوع القضية...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"مدنية\",\n                                                                    children: \"مدنية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"تجارية\",\n                                                                    children: \"تجارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"جنائية\",\n                                                                    children: \"جنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"أحوال شخصية\",\n                                                                    children: \"أحوال شخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"عمالية\",\n                                                                    children: \"عمالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"إدارية\",\n                                                                    children: \"إدارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_method\",\n                                                            className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"contract_method\",\n                                                            value: formData.contract_method,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_method: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالجلسة\",\n                                                                    children: \"بالجلسة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالعقد\",\n                                                                    children: \"بالعقد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_date\",\n                                                            className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"contract_date\",\n                                                            type: \"date\",\n                                                            value: formData.contract_date,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_date: e.target.value\n                                                                }),\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors', \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"currency_id\",\n                                                            className: \"text-sm font-semibold text-yellow-700 bg-yellow-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB1 العملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"currency_id\",\n                                                            value: formData.currency_id || '1',\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    currency_id: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-yellow-50 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: currency.id,\n                                                                    children: [\n                                                                        currency.currency_name,\n                                                                        \" (\",\n                                                                        currency.symbol,\n                                                                        \")\",\n                                                                        currency.is_base_currency && ' - الأساسية'\n                                                                    ]\n                                                                }, currency.id, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_amount\",\n                                                            className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"case_amount\",\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    min: \"0\",\n                                                                    value: formData.case_amount || '',\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            case_amount: e.target.value\n                                                                        }),\n                                                                    readOnly: modalType === 'view',\n                                                                    className: \"h-10 pr-12 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors', \" text-sm\"),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: ((_currencies_find = currencies.find((c)=>c.id.toString() === (formData.currency_id || '1'))) === null || _currencies_find === void 0 ? void 0 : _currencies_find.symbol) || 'ر.ي'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"amount_yer\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB5 بالريال اليمني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"amount_yer\",\n                                                                    type: \"text\",\n                                                                    value: Math.round(calculateAmountYer(formData.case_amount || '0', formData.currency_id || '1')).toLocaleString('en-US'),\n                                                                    readOnly: true,\n                                                                    className: \"h-10 pr-12 bg-gray-50 text-gray-700 text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"ر.ي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"notes\",\n                                                            className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"notes\",\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    notes: e.target.value\n                                                                }),\n                                                            readOnly: modalType === 'view',\n                                                            className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                            children: [\n                                                modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setIsModalOpen(false),\n                                                    className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                    children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, \"modal-\".concat(modalType, \"-\").concat((editingIssue === null || editingIssue === void 0 ? void 0 : editingIssue.id) || 'new'), true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 409,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 408,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"ziQOElTFb6jA2kNLhP+ooDz2/1c=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaXNzdWVzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNpQjtBQUNtQjtBQUNoQztBQUNGO0FBQ0E7QUFDQTtBQUNlO0FBY3ZDO0FBZ0NOLFNBQVN1QjtRQWcxQkdDOztJQS8wQnpCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHMUIsK0NBQVFBLENBQVUsRUFBRTtJQUNoRCxNQUFNLENBQUMyQixZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2QixjQUFjQyxnQkFBZ0IsR0FBRzlCLCtDQUFRQSxDQUFTO0lBQ3pELE1BQU0sQ0FBQytCLFNBQVNDLFdBQVcsR0FBR2hDLCtDQUFRQSxDQUFnQjtJQUN0RCxNQUFNLENBQUNpQyxXQUFXQyxhQUFhLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNtQyxhQUFhQyxlQUFlLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNxQyxXQUFXQyxhQUFhLEdBQUd0QywrQ0FBUUEsQ0FBMEI7SUFDcEUsTUFBTSxDQUFDdUMsY0FBY0MsZ0JBQWdCLEdBQUd4QywrQ0FBUUEsQ0FBZTtJQUMvRCxNQUFNLENBQUN5QyxRQUFRQyxVQUFVLEdBQUcxQywrQ0FBUUEsQ0FBVSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ3dCLFlBQVltQixjQUFjLEdBQUczQywrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RELE1BQU0sQ0FBQzRDLFNBQVNDLFdBQVcsR0FBRzdDLCtDQUFRQSxDQUFRLEVBQUU7SUFDaEQsTUFBTSxDQUFDOEMsWUFBWUMsY0FBYyxHQUFHL0MsK0NBQVFBLENBQVEsRUFBRTtJQUN0RCxNQUFNLENBQUNnRCxVQUFVQyxZQUFZLEdBQUdqRCwrQ0FBUUEsQ0FBQztRQUN2Q2tELGFBQWE7UUFDYkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLGVBQWUsSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyx1QkFBdUI7SUFDL0U7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTUMsY0FBYztRQUNsQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUNsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCaEMsVUFBVThCLE9BQU9HLElBQUk7WUFDdkI7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUM7SUFDRjtJQUVBLGdDQUFnQztJQUNoQyxNQUFNRSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1SLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFDbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQi9CLGNBQWM2QixPQUFPRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1FBQzlDO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTUcsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTVQsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUNsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCN0IsV0FBVzJCLE9BQU9HLElBQUk7WUFDeEI7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDM0M7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNSSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1WLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFDbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQjNCLGNBQWN5QixPQUFPRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUsscUJBQXFCLENBQUNDLFFBQWdCQztRQUMxQyxNQUFNQyxZQUFZQyxXQUFXSCxXQUFXO1FBQ3hDLE1BQU1JLFdBQVc5RCxXQUFXK0QsSUFBSSxDQUFDLENBQUNDLElBQVdBLEVBQUVDLEVBQUUsQ0FBQ0MsUUFBUSxPQUFPUDtRQUVqRSxJQUFJLENBQUNHLFVBQVUsT0FBT0Y7UUFFdEIsSUFBSUUsU0FBU0ssZ0JBQWdCLEVBQUU7WUFDN0IsT0FBT1A7UUFDVDtRQUVBLE9BQU9BLFlBQVlFLFNBQVNNLGFBQWE7SUFDM0M7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTUMsY0FBYztRQUNsQjNELGFBQWE7UUFDYkYsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNc0MsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUVsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCaEQsVUFBVThDLE9BQU9HLElBQUk7WUFDdkIsT0FBTztnQkFDTDNDLFdBQVd3QyxPQUFPSSxLQUFLLElBQUk7Z0JBQzNCbEQsVUFBVSxFQUFFO1lBQ2Q7UUFDRixFQUFFLE9BQU9rRCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQkFBa0JBO1lBQ2hDNUMsV0FBVztZQUNYTixVQUFVLEVBQUU7UUFDZCxTQUFVO1lBQ1JRLGFBQWE7UUFDZjtJQUNGO0lBRUFqQyxnREFBU0E7Z0NBQUM7WUFDUjRGO1lBQ0F4QjtZQUNBUztZQUNBQztZQUNBQztRQUNGOytCQUFHLEVBQUU7SUFFTCxNQUFNYyxpQkFBaUJyRSxPQUFPc0UsTUFBTSxDQUFDQyxDQUFBQTtRQUNuQyxNQUFNQyxnQkFDSkQsTUFBTTlDLFdBQVcsQ0FBQ2dELFdBQVcsR0FBR0MsUUFBUSxDQUFDeEUsV0FBV3VFLFdBQVcsT0FDL0RGLE1BQU03QyxLQUFLLENBQUMrQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3hFLFdBQVd1RSxXQUFXLE9BQ3pERixNQUFNMUMsV0FBVyxDQUFDNEMsV0FBVyxHQUFHQyxRQUFRLENBQUN4RSxXQUFXdUUsV0FBVztRQUVqRSxNQUFNRSxnQkFBZ0J2RSxpQkFBaUIsU0FBU21FLE1BQU1wQyxNQUFNLEtBQUsvQjtRQUVqRSxPQUFPb0UsaUJBQWlCRztJQUMxQjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDekM7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFPLE9BQU87WUFDbkIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQWUsT0FBTztZQUMzQixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBYSxPQUFPO1lBQ3pCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU0wQyxnQkFBZ0IsQ0FBQzFDO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBTyxPQUFPO1lBQ25CLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFlLE9BQU87WUFDM0IsS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQWEsT0FBTztZQUN6QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNMkMsZUFBZTtRQUNuQi9ELGdCQUFnQjtRQUNoQlMsWUFBWTtZQUNWQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxXQUFXO1lBQ1hDLGFBQWE7WUFDYkMsY0FBYztZQUNkQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWkMsZUFBZTtZQUNmQyxZQUFZO1lBQ1pDLFFBQVE7WUFDUkMsYUFBYTtZQUNiQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxlQUFlLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsdUJBQXVCO1FBQy9FO1FBQ0E5QixhQUFhO1FBQ2JGLGVBQWU7SUFDakI7SUFFQSxNQUFNb0UscUJBQXFCLENBQUNDLFVBQWtCQztRQUM1Q3pELFlBQVk7WUFDVixHQUFHRCxRQUFRO1lBQ1hLLFdBQVdvRDtZQUNYbkQsYUFBYW9ELGFBQWFBLFdBQVdDLElBQUksR0FBRztZQUM1Q3BELGNBQWNtRCxhQUFhQSxXQUFXRSxLQUFLLEdBQUc7UUFDaEQ7SUFDRjtJQUVBLE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSTtZQUNGLElBQUkxRSxjQUFjLE9BQU87Z0JBQ3ZCLGtFQUFrRTtnQkFDbEUsTUFBTTJFLGVBQWU7b0JBQ25CLEdBQUdoRSxRQUFRO29CQUNYaUUsWUFBWSxFQUFFLHNEQUFzRDtnQkFDdEU7Z0JBRUFwQyxRQUFRcUMsR0FBRyxDQUFDLHVDQUF1Q0Y7Z0JBRW5ELE1BQU0xQyxXQUFXLE1BQU1DLE1BQU0sZUFBZTtvQkFDMUM0QyxRQUFRO29CQUNSQyxTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7b0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNQO2dCQUN2QjtnQkFFQSxNQUFNeEMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO2dCQUVsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7b0JBQ2xCOEMsTUFBTTtvQkFDTjNCO2dCQUNGLE9BQU87b0JBQ0wyQixNQUFNaEQsT0FBT0ksS0FBSyxJQUFJO29CQUN0QjtnQkFDRjtZQUNGLE9BQU8sSUFBSXZDLGNBQWMsVUFBVUUsY0FBYztnQkFDL0MsOEJBQThCO2dCQUM5QixNQUFNeUUsZUFBZTtvQkFDbkIsR0FBR2hFLFFBQVE7Z0JBQ2I7Z0JBRUE2QixRQUFRcUMsR0FBRyxDQUFDLHdCQUF3QkY7Z0JBRXBDLE1BQU0xQyxXQUFXLE1BQU1DLE1BQU0sZUFBK0IsT0FBaEJoQyxhQUFha0QsRUFBRSxHQUFJO29CQUM3RDBCLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtvQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1A7Z0JBQ3ZCO2dCQUVBLE1BQU14QyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7Z0JBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtvQkFDbEI4QyxNQUFNO29CQUNOM0I7Z0JBQ0YsT0FBTztvQkFDTDJCLE1BQU1oRCxPQUFPSSxLQUFLLElBQUk7b0JBQ3RCO2dCQUNGO1lBQ0Y7WUFFQXhDLGVBQWU7WUFDZkksZ0JBQWdCO1FBQ2xCLEVBQUUsT0FBT29DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEM0QyxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLGtCQUFrQixDQUFDekI7UUFDdkJuQixRQUFRcUMsR0FBRyxDQUFDLHlDQUF5Q2xCO1FBRXJEeEQsZ0JBQWdCd0Q7UUFFaEIsa0NBQWtDO1FBQ2xDLE1BQU0wQixnQkFBZ0I7WUFDcEJ4RSxhQUFhOEMsTUFBTTlDLFdBQVcsSUFBSTtZQUNsQ0MsT0FBTzZDLE1BQU03QyxLQUFLLElBQUk7WUFDdEJDLGFBQWE0QyxNQUFNNUMsV0FBVyxJQUFJO1lBQ2xDQyxXQUFXMkMsTUFBTTNDLFNBQVMsR0FBRzJDLE1BQU0zQyxTQUFTLENBQUNxQyxRQUFRLEtBQUs7WUFDMURwQyxhQUFhMEMsTUFBTTFDLFdBQVcsSUFBSTtZQUNsQ0MsY0FBY3lDLE1BQU16QyxZQUFZLElBQUk7WUFDcENDLFVBQVV3QyxNQUFNeEMsUUFBUSxHQUFHd0MsTUFBTXhDLFFBQVEsQ0FBQ2tDLFFBQVEsS0FBSztZQUN2RGpDLFlBQVl1QyxNQUFNdkMsVUFBVSxJQUFJO1lBQ2hDQyxlQUFlc0MsTUFBTXRDLGFBQWEsR0FBR3NDLE1BQU10QyxhQUFhLENBQUNnQyxRQUFRLEtBQUs7WUFDdEUvQixZQUFZcUMsTUFBTXJDLFVBQVUsSUFBSTtZQUNoQ0MsUUFBUW9DLE1BQU1wQyxNQUFNLElBQUk7WUFDeEJDLGFBQWFtQyxNQUFNbkMsV0FBVyxHQUFHbUMsTUFBTW5DLFdBQVcsQ0FBQzZCLFFBQVEsS0FBSztZQUNoRTVCLGFBQWFrQyxNQUFNbEMsV0FBVyxHQUFHa0MsTUFBTWxDLFdBQVcsQ0FBQzRCLFFBQVEsS0FBSztZQUNoRTNCLE9BQU9pQyxNQUFNakMsS0FBSyxJQUFJO1lBQ3RCQyxpQkFBaUJnQyxNQUFNaEMsZUFBZSxJQUFJO1lBQzFDQyxlQUFlK0IsTUFBTS9CLGFBQWEsR0FBRyxJQUFJQyxLQUFLOEIsTUFBTS9CLGFBQWEsRUFBRUUsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJRixPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUN6STtRQUVBUyxRQUFRcUMsR0FBRyxDQUFDLHNDQUFzQ1E7UUFFbER6RSxZQUFZeUU7UUFDWnBGLGFBQWE7UUFDYkYsZUFBZTtJQUNqQjtJQUVBLE1BQU11RixrQkFBa0IsQ0FBQzNCO1FBQ3ZCbkIsUUFBUXFDLEdBQUcsQ0FBQyx5Q0FBeUNsQjtRQUVyRHhELGdCQUFnQndEO1FBRWhCLGtDQUFrQztRQUNsQyxNQUFNMEIsZ0JBQWdCO1lBQ3BCeEUsYUFBYThDLE1BQU05QyxXQUFXLElBQUk7WUFDbENDLE9BQU82QyxNQUFNN0MsS0FBSyxJQUFJO1lBQ3RCQyxhQUFhNEMsTUFBTTVDLFdBQVcsSUFBSTtZQUNsQ0MsV0FBVzJDLE1BQU0zQyxTQUFTLEdBQUcyQyxNQUFNM0MsU0FBUyxDQUFDcUMsUUFBUSxLQUFLO1lBQzFEcEMsYUFBYTBDLE1BQU0xQyxXQUFXLElBQUk7WUFDbENDLGNBQWN5QyxNQUFNekMsWUFBWSxJQUFJO1lBQ3BDQyxVQUFVd0MsTUFBTXhDLFFBQVEsR0FBR3dDLE1BQU14QyxRQUFRLENBQUNrQyxRQUFRLEtBQUs7WUFDdkRqQyxZQUFZdUMsTUFBTXZDLFVBQVUsSUFBSTtZQUNoQ0MsZUFBZXNDLE1BQU10QyxhQUFhLEdBQUdzQyxNQUFNdEMsYUFBYSxDQUFDZ0MsUUFBUSxLQUFLO1lBQ3RFL0IsWUFBWXFDLE1BQU1yQyxVQUFVLElBQUk7WUFDaENDLFFBQVFvQyxNQUFNcEMsTUFBTSxJQUFJO1lBQ3hCQyxhQUFhbUMsTUFBTW5DLFdBQVcsR0FBR21DLE1BQU1uQyxXQUFXLENBQUM2QixRQUFRLEtBQUs7WUFDaEU1QixhQUFha0MsTUFBTWxDLFdBQVcsR0FBR2tDLE1BQU1sQyxXQUFXLENBQUM0QixRQUFRLEtBQUs7WUFDaEUzQixPQUFPaUMsTUFBTWpDLEtBQUssSUFBSTtZQUN0QkMsaUJBQWlCZ0MsTUFBTWhDLGVBQWUsSUFBSTtZQUMxQ0MsZUFBZStCLE1BQU0vQixhQUFhLEdBQUcsSUFBSUMsS0FBSzhCLE1BQU0vQixhQUFhLEVBQUVFLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsSUFBSUYsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDekk7UUFFQVMsUUFBUXFDLEdBQUcsQ0FBQyxzQ0FBc0NRO1FBRWxEekUsWUFBWXlFO1FBQ1pwRixhQUFhO1FBQ2JGLGVBQWU7SUFDakI7SUFFQSxNQUFNd0Ysb0JBQW9CLE9BQU9uQztRQUMvQixJQUFJLENBQUNvQyxRQUFRLG9DQUFvQztRQUVqRCxJQUFJO1lBQ0YsTUFBTXZELFdBQVcsTUFBTUMsTUFBTSxlQUFrQixPQUFIa0IsS0FBTTtnQkFDaEQwQixRQUFRO1lBQ1Y7WUFFQSxNQUFNM0MsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEI4QyxNQUFNO2dCQUNOM0I7WUFDRixPQUFPO2dCQUNMMkIsTUFBTWhELE9BQU9JLEtBQUssSUFBSTtZQUN4QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2QzRDLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTU0sUUFBUTtRQUNaQyxPQUFPdEcsT0FBT3VHLE1BQU07UUFDcEJDLEtBQUt4RyxPQUFPc0UsTUFBTSxDQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRXRFLE1BQU0sS0FBSyxPQUFPb0UsTUFBTTtRQUNsREcsU0FBUzFHLE9BQU9zRSxNQUFNLENBQUNtQyxDQUFBQSxJQUFLQSxFQUFFdEUsTUFBTSxLQUFLLFdBQVdvRSxNQUFNO1FBQzFESSxhQUFhM0csT0FBT3NFLE1BQU0sQ0FBQ21DLENBQUFBLElBQUtBLEVBQUV0RSxNQUFNLEtBQUssZUFBZW9FLE1BQU07UUFDbEVLLFdBQVc1RyxPQUFPc0UsTUFBTSxDQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRXRFLE1BQU0sS0FBSyxhQUFhb0UsTUFBTTtJQUNoRTtJQUVBLHFCQUNFLDhEQUFDOUgsc0VBQVVBO2tCQUNULDRFQUFDb0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUVmLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0U7d0NBQUdELFdBQVU7OzBEQUNaLDhEQUFDbkgsMklBQUtBO2dEQUFDbUgsV0FBVTs7Ozs7OzRDQUFrQzs7Ozs7OztrREFHckQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7OzBDQUdwQyw4REFBQ2hJLHlEQUFNQTtnQ0FBQ21JLFNBQVNuQztnQ0FBY2dDLFdBQVU7O2tEQUN2Qyw4REFBQzFILDRJQUFJQTt3Q0FBQzBILFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7a0NBTXJDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNwSSxxREFBSUE7MENBQ0gsNEVBQUNDLDREQUFXQTtvQ0FBQ21JLFdBQVU7OENBQ3JCLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDM0gsNElBQVFBO29EQUFDMkgsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUFvQ1QsTUFBTUMsS0FBSzs7Ozs7O2tFQUM5RCw4REFBQ087d0RBQUlDLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0vQyw4REFBQ3BJLHFEQUFJQTswQ0FDSCw0RUFBQ0MsNERBQVdBO29DQUFDbUksV0FBVTs4Q0FDckIsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMzSCw0SUFBUUE7b0RBQUMySCxXQUFVOzs7Ozs7Ozs7OzswREFFdEIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQW9DVCxNQUFNRyxHQUFHOzs7Ozs7a0VBQzVELDhEQUFDSzt3REFBSUMsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTS9DLDhEQUFDcEkscURBQUlBOzBDQUNILDRFQUFDQyw0REFBV0E7b0NBQUNtSSxXQUFVOzhDQUNyQiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ25ILDJJQUFLQTtvREFBQ21ILFdBQVU7Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0NULE1BQU1NLFdBQVc7Ozs7OztrRUFDcEUsOERBQUNFO3dEQUFJQyxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNL0MsOERBQUNwSSxxREFBSUE7MENBQ0gsNEVBQUNDLDREQUFXQTtvQ0FBQ21JLFdBQVU7OENBQ3JCLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDM0gsNElBQVFBO29EQUFDMkgsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUFvQ1QsTUFBTU8sU0FBUzs7Ozs7O2tFQUNsRSw4REFBQ0M7d0RBQUlDLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVFqRCw4REFBQ3BJLHFEQUFJQTtrQ0FDSCw0RUFBQ0MsNERBQVdBOzRCQUFDbUksV0FBVTtzQ0FDckIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDekgsNElBQU1BO2dEQUFDeUgsV0FBVTs7Ozs7OzBEQUNsQiw4REFBQy9ILHVEQUFLQTtnREFDSm1JLGFBQVk7Z0RBQ1pDLE9BQU9qSDtnREFDUGtILFVBQVUsQ0FBQy9CLElBQU1sRixjQUFja0YsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSztnREFDN0NMLFdBQVU7Ozs7Ozs7Ozs7OztrREFHZCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNROzRDQUNDSCxPQUFPL0c7NENBQ1BnSCxVQUFVLENBQUMvQixJQUFNaEYsZ0JBQWdCZ0YsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSzs0Q0FDL0NMLFdBQVU7OzhEQUVWLDhEQUFDUztvREFBT0osT0FBTTs4REFBTTs7Ozs7OzhEQUNwQiw4REFBQ0k7b0RBQU9KLE9BQU07OERBQU07Ozs7Ozs4REFDcEIsOERBQUNJO29EQUFPSixPQUFNOzhEQUFVOzs7Ozs7OERBQ3hCLDhEQUFDSTtvREFBT0osT0FBTTs4REFBYzs7Ozs7OzhEQUM1Qiw4REFBQ0k7b0RBQU9KLE9BQU07OERBQVk7Ozs7Ozs4REFDMUIsOERBQUNJO29EQUFPSixPQUFNOzhEQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUXBDLDhEQUFDekkscURBQUlBOzswQ0FDSCw4REFBQ0UsMkRBQVVBOzBDQUNULDRFQUFDQywwREFBU0E7b0NBQUNpSSxXQUFVOztzREFDbkIsOERBQUMzSCw0SUFBUUE7NENBQUMySCxXQUFVOzs7Ozs7d0NBQWlCO3dDQUNyQnpDLGVBQWVrQyxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7MENBRzFDLDhEQUFDNUgsNERBQVdBOzBDQUNWLDRFQUFDa0k7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNVO3dDQUFNVixXQUFVOzswREFDZiw4REFBQ1c7MERBQ0MsNEVBQUNDO29EQUFHWixXQUFVOztzRUFDWiw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQXVDOzs7Ozs7c0VBQ3JELDhEQUFDYTs0REFBR2IsV0FBVTtzRUFBdUM7Ozs7OztzRUFDckQsOERBQUNhOzREQUFHYixXQUFVO3NFQUF1Qzs7Ozs7O3NFQUNyRCw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQXdDOzs7Ozs7c0VBQ3RELDhEQUFDYTs0REFBR2IsV0FBVTtzRUFBd0M7Ozs7OztzRUFDdEQsOERBQUNhOzREQUFHYixXQUFVO3NFQUF3Qzs7Ozs7O3NFQUN0RCw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQXdDOzs7Ozs7c0VBQ3RELDhEQUFDYTs0REFBR2IsV0FBVTtzRUFBd0M7Ozs7OztzRUFDdEQsOERBQUNhOzREQUFHYixXQUFVO3NFQUF3Qzs7Ozs7O3NFQUN0RCw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQXdDOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHMUQsOERBQUNjOzBEQUNFdkQsZUFBZXdELEdBQUcsQ0FBQyxDQUFDdEQsc0JBQ25CLDhEQUFDbUQ7d0RBQWtCWixXQUFVOzswRUFDM0IsOERBQUNnQjtnRUFBR2hCLFdBQVU7MEVBQWlDdkMsTUFBTTlDLFdBQVc7Ozs7OzswRUFDaEUsOERBQUNxRztnRUFBR2hCLFdBQVU7MEVBQW1CdkMsTUFBTTdDLEtBQUs7Ozs7OzswRUFDNUMsOERBQUNvRztnRUFBR2hCLFdBQVU7MEVBQ1osNEVBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3JILDRJQUFJQTs0RUFBQ3FILFdBQVU7Ozs7Ozt3RUFDZnZDLE1BQU0xQyxXQUFXOzs7Ozs7Ozs7Ozs7MEVBR3RCLDhEQUFDaUc7Z0VBQUdoQixXQUFVOzBFQUNaLDRFQUFDRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ2lCO3dFQUFLakIsV0FBVTtrRkFDYnZDLE1BQU16QyxZQUFZLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBSTdCLDhEQUFDZ0c7Z0VBQUdoQixXQUFVOzBFQUNaLDRFQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNwSCw0SUFBUUE7NEVBQUNvSCxXQUFVOzs7Ozs7c0ZBQ3BCLDhEQUFDaUI7NEVBQUtqQixXQUFVO3NGQUFldkMsTUFBTXZDLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUduRCw4REFBQzhGO2dFQUFHaEIsV0FBVTswRUFDWiw0RUFBQzdILHVEQUFLQTtvRUFBQytJLFNBQVE7b0VBQVVsQixXQUFVOzhFQUNoQ3ZDLE1BQU1yQyxVQUFVOzs7Ozs7Ozs7OzswRUFHckIsOERBQUM0RjtnRUFBR2hCLFdBQVU7MEVBQ1osNEVBQUM3SCx1REFBS0E7b0VBQ0orSSxTQUFRO29FQUNSbEIsV0FBV3ZDLE1BQU1oQyxlQUFlLEtBQUssWUFBWSwrQkFBK0I7OEVBRS9FZ0MsTUFBTWhDLGVBQWU7Ozs7Ozs7Ozs7OzBFQUcxQiw4REFBQ3VGO2dFQUFHaEIsV0FBVTswRUFDWiw0RUFBQzdILHVEQUFLQTtvRUFBQzZILFdBQVdsQyxlQUFlTCxNQUFNcEMsTUFBTTs4RUFDMUMwQyxjQUFjTixNQUFNcEMsTUFBTTs7Ozs7Ozs7Ozs7MEVBRy9CLDhEQUFDMkY7Z0VBQUdoQixXQUFVOztvRUFDWHZDLE1BQU1kLE1BQU0sR0FBR3dFLEtBQUtDLEtBQUssQ0FBQzNELE1BQU1kLE1BQU0sRUFBRTBFLGNBQWMsS0FBSztvRUFBSTs7Ozs7OzswRUFFbEUsOERBQUNMO2dFQUFHaEIsV0FBVTswRUFDWiw0RUFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDaEkseURBQU1BOzRFQUNMc0osTUFBSzs0RUFDTEosU0FBUTs0RUFDUmYsU0FBUyxJQUFNakIsZ0JBQWdCekI7NEVBQy9CdUMsV0FBVTs0RUFDVnBGLE9BQU07c0ZBRU4sNEVBQUNwQyw0SUFBR0E7Z0ZBQUN3SCxXQUFVOzs7Ozs7Ozs7OztzRkFFakIsOERBQUNoSSx5REFBTUE7NEVBQ0xzSixNQUFLOzRFQUNMSixTQUFROzRFQUNSZixTQUFTLElBQU1mLGdCQUFnQjNCOzRFQUMvQnVDLFdBQVU7NEVBQ1ZwRixPQUFNO3NGQUVOLDRFQUFDbkMsNElBQUlBO2dGQUFDdUgsV0FBVTs7Ozs7Ozs7Ozs7c0ZBRWxCLDhEQUFDaEkseURBQU1BOzRFQUNMc0osTUFBSzs0RUFDTEosU0FBUTs0RUFDUmYsU0FBUyxJQUFNZCxrQkFBa0I1QixNQUFNUCxFQUFFOzRFQUN6QzhDLFdBQVU7NEVBQ1ZwRixPQUFNO3NGQUVOLDRFQUFDbEMsNElBQU1BO2dGQUFDc0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBdEVqQnZDLE1BQU1QLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQW1GNUJ0RCw2QkFDQyw4REFBQ21HO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBNERDLFdBQVU7OzhDQUVyRSw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDdUI7NENBQUd2QixXQUFVOzs4REFDWiw4REFBQzNILDRJQUFRQTtvREFBQzJILFdBQVU7Ozs7OztnREFDbkJsRyxjQUFjLFNBQVM7Z0RBQ3ZCQSxjQUFjLFVBQVU7Z0RBQ3hCQSxjQUFjLFVBQVU7Ozs7Ozs7c0RBRTNCLDhEQUFDOUIseURBQU1BOzRDQUFDa0osU0FBUTs0Q0FBUUksTUFBSzs0Q0FBS25CLFNBQVMsSUFBTXRHLGVBQWU7NENBQVFtRyxXQUFVO3NEQUNoRiw0RUFBQ2xILDRJQUFDQTtnREFBQ2tILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlqQiw4REFBQ3dCO29DQUFLQyxVQUFVbkQ7b0NBQWMwQixXQUFVOztzREFFdEMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDOUgsdURBQUtBOzREQUFDd0osU0FBUTs0REFBYzFCLFdBQVU7c0VBQXdGOzs7Ozs7c0VBRy9ILDhEQUFDL0gsdURBQUtBOzREQUNKaUYsSUFBRzs0REFDSG1ELE9BQU81RixTQUFTRSxXQUFXOzREQUMzQjJGLFVBQVUsQ0FBQy9CLElBQU03RCxZQUFZO29FQUFDLEdBQUdELFFBQVE7b0VBQUVFLGFBQWE0RCxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO2dFQUFBOzREQUN0RXNCLFVBQVU3SCxjQUFjOzREQUN4QjhILFVBQVU5SCxjQUFjOzREQUN4QmtHLFdBQVcsUUFBa0ksT0FBMUhsRyxjQUFjLFNBQVMsZUFBZTs0REFDekRzRyxhQUFZOzs7Ozs7Ozs7Ozs7OERBR2hCLDhEQUFDTDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM5SCx1REFBS0E7NERBQUN3SixTQUFROzREQUFRMUIsV0FBVTtzRUFBMEY7Ozs7OztzRUFHM0gsOERBQUMvSCx1REFBS0E7NERBQ0ppRixJQUFHOzREQUNIbUQsT0FBTzVGLFNBQVNHLEtBQUs7NERBQ3JCMEYsVUFBVSxDQUFDL0IsSUFBTTdELFlBQVk7b0VBQUMsR0FBR0QsUUFBUTtvRUFBRUcsT0FBTzJELEVBQUVnQyxNQUFNLENBQUNGLEtBQUs7Z0VBQUE7NERBQ2hFc0IsVUFBVTdILGNBQWM7NERBQ3hCOEgsVUFBVTlILGNBQWM7NERBQ3hCa0csV0FBVyxRQUFxSSxPQUE3SGxHLGNBQWMsU0FBUyxlQUFlOzREQUN6RHNHLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNbEIsOERBQUNMOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDs7a0VBQ0MsOERBQUM3SCx1REFBS0E7d0RBQUN3SixTQUFRO3dEQUFjMUIsV0FBVTtrRUFBd0Y7Ozs7OztrRUFHL0gsOERBQUM2Qjt3REFDQzNFLElBQUc7d0RBQ0htRCxPQUFPNUYsU0FBU0ksV0FBVzt3REFDM0J5RixVQUFVLENBQUMvQixJQUFNN0QsWUFBWTtnRUFBQyxHQUFHRCxRQUFRO2dFQUFFSSxhQUFhMEQsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSzs0REFBQTt3REFDdEV1QixVQUFVOUgsY0FBYzt3REFDeEJrRyxXQUFXLCtEQUF5TCxPQUExSGxHLGNBQWMsU0FBUyxlQUFlO3dEQUNoSHNHLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1sQiw4REFBQ0w7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM5SCx1REFBS0E7NERBQUM4SCxXQUFVO3NFQUE0Rjs7Ozs7O3NFQUc3Ryw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUM1SCxzRUFBWUE7Z0VBQ1hpSSxPQUFPNUYsU0FBU0ssU0FBUztnRUFDekJ3RixVQUFVckM7Z0VBQ1Y2RCxPQUFNO2dFQUNOMUIsYUFBWTtnRUFDWnVCLFFBQVE7Z0VBQ1JJLFVBQVVqSSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJOUIsOERBQUNpRzs7c0VBQ0MsOERBQUM3SCx1REFBS0E7NERBQUN3SixTQUFROzREQUFXMUIsV0FBVTtzRUFBNEY7Ozs7OztzRUFHaEksOERBQUNROzREQUNDdEQsSUFBRzs0REFDSG1ELE9BQU81RixTQUFTUSxRQUFRLElBQUk7NERBQzVCcUYsVUFBVSxDQUFDL0I7Z0VBQ1QsTUFBTXlELGdCQUFnQjlILE9BQU84QyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsQ0FBQ0MsUUFBUSxPQUFPb0IsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSztnRUFDekUzRixZQUFZO29FQUNWLEdBQUdELFFBQVE7b0VBQ1hRLFVBQVVzRCxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO29FQUN4Qm5GLFlBQVk4RyxDQUFBQSwwQkFBQUEsb0NBQUFBLGNBQWU1RCxJQUFJLEtBQUk7Z0VBQ3JDOzREQUNGOzREQUNBNEIsV0FBVTs0REFDVitCLFVBQVVqSSxjQUFjOzs4RUFFeEIsOERBQUMyRztvRUFBT0osT0FBTTs4RUFBRzs7Ozs7O2dFQUNoQm5HLE9BQU82RyxHQUFHLENBQUMsQ0FBQ2tCLHNCQUNYLDhEQUFDeEI7d0VBQXNCSixPQUFPNEIsTUFBTS9FLEVBQUU7OzRFQUNuQytFLE1BQU03RCxJQUFJOzRFQUFDOzRFQUFJNkQsTUFBTUMsZ0JBQWdCOzt1RUFEM0JELE1BQU0vRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFTN0IsOERBQUM2Qzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQzdILHVEQUFLQTs0REFBQ3dKLFNBQVE7NERBQWtCMUIsV0FBVTtzRUFBd0Y7Ozs7OztzRUFHbkksOERBQUNROzREQUNDdEQsSUFBRzs0REFDSG1ELE9BQU81RixTQUFTZ0IsZUFBZSxJQUFJOzREQUNuQzZFLFVBQVUsQ0FBQy9CLElBQU03RCxZQUFZO29FQUFDLEdBQUdELFFBQVE7b0VBQUVnQixpQkFBaUI4QyxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO2dFQUFBOzREQUMxRUwsV0FBVTs0REFDVitCLFVBQVVqSSxjQUFjOzs4RUFFeEIsOERBQUMyRztvRUFBT0osT0FBTTs4RUFBVTs7Ozs7OzhFQUN4Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHM0IsOERBQUNOOztzRUFDQyw4REFBQzdILHVEQUFLQTs0REFBQ3dKLFNBQVE7NERBQVMxQixXQUFVO3NFQUFzRjs7Ozs7O3NFQUd4SCw4REFBQ1E7NERBQ0N0RCxJQUFHOzREQUNIbUQsT0FBTzVGLFNBQVNZLE1BQU0sSUFBSTs0REFDMUJpRixVQUFVLENBQUMvQixJQUFNN0QsWUFBWTtvRUFBQyxHQUFHRCxRQUFRO29FQUFFWSxRQUFRa0QsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSztnRUFBQTs0REFDakVMLFdBQVU7NERBQ1YrQixVQUFVakksY0FBYzs7OEVBRXhCLDhEQUFDMkc7b0VBQU9KLE9BQU07OEVBQU07Ozs7Ozs4RUFDcEIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFVOzs7Ozs7OEVBQ3hCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBYzs7Ozs7OzhFQUM1Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVk7Ozs7Ozs4RUFDMUIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBRzlCLDhEQUFDTjs7c0VBQ0MsOERBQUM3SCx1REFBS0E7NERBQUN3SixTQUFROzREQUFhMUIsV0FBVTtzRUFBNEY7Ozs7OztzRUFHbEksOERBQUNROzREQUNDdEQsSUFBRzs0REFDSG1ELE9BQU81RixTQUFTVyxVQUFVLElBQUk7NERBQzlCa0YsVUFBVSxDQUFDL0IsSUFBTTdELFlBQVk7b0VBQUMsR0FBR0QsUUFBUTtvRUFBRVcsWUFBWW1ELEVBQUVnQyxNQUFNLENBQUNGLEtBQUs7Z0VBQUE7NERBQ3JFTCxXQUFVOzREQUNWK0IsVUFBVWpJLGNBQWM7OzhFQUV4Qiw4REFBQzJHO29FQUFPSixPQUFNOzhFQUFHOzs7Ozs7OEVBQ2pCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBUTs7Ozs7OzhFQUN0Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVM7Ozs7Ozs4RUFDdkIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFTOzs7Ozs7OEVBQ3ZCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBYzs7Ozs7OzhFQUM1Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVM7Ozs7Ozs4RUFDdkIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTTdCLDhEQUFDTjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQzdILHVEQUFLQTs0REFBQ3dKLFNBQVE7NERBQVMxQixXQUFVO3NFQUFzRjs7Ozs7O3NFQUd4SCw4REFBQ1E7NERBQ0N0RCxJQUFHOzREQUNIbUQsT0FBTzVGLFNBQVNZLE1BQU07NERBQ3RCaUYsVUFBVSxDQUFDL0IsSUFBTTdELFlBQVk7b0VBQUMsR0FBR0QsUUFBUTtvRUFBRVksUUFBUWtELEVBQUVnQyxNQUFNLENBQUNGLEtBQUs7Z0VBQUE7NERBQ2pFTCxXQUFVOzREQUNWK0IsVUFBVWpJLGNBQWM7OzhFQUV4Qiw4REFBQzJHO29FQUFPSixPQUFNOzhFQUFNOzs7Ozs7OEVBQ3BCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBVTs7Ozs7OzhFQUN4Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQWM7Ozs7Ozs4RUFDNUIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFZOzs7Ozs7OEVBQzFCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUc5Qiw4REFBQ047O3NFQUNDLDhEQUFDN0gsdURBQUtBOzREQUFDd0osU0FBUTs0REFBYTFCLFdBQVU7c0VBQTRGOzs7Ozs7c0VBR2xJLDhEQUFDUTs0REFDQ3RELElBQUc7NERBQ0htRCxPQUFPNUYsU0FBU1csVUFBVSxJQUFJOzREQUM5QmtGLFVBQVUsQ0FBQy9CLElBQU03RCxZQUFZO29FQUFDLEdBQUdELFFBQVE7b0VBQUVXLFlBQVltRCxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO2dFQUFBOzREQUNyRUwsV0FBVTs0REFDVitCLFVBQVVqSSxjQUFjOzs4RUFFeEIsOERBQUMyRztvRUFBT0osT0FBTTs4RUFBRzs7Ozs7OzhFQUNqQiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVE7Ozs7Ozs4RUFDdEIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFTOzs7Ozs7OEVBQ3ZCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBUzs7Ozs7OzhFQUN2Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQWM7Ozs7Ozs4RUFDNUIsOERBQUNJO29FQUFPSixPQUFNOzhFQUFTOzs7Ozs7OEVBQ3ZCLDhEQUFDSTtvRUFBT0osT0FBTTs4RUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUczQiw4REFBQ047O3NFQUNDLDhEQUFDN0gsdURBQUtBOzREQUFDd0osU0FBUTs0REFBa0IxQixXQUFVO3NFQUF3Rjs7Ozs7O3NFQUduSSw4REFBQ1E7NERBQ0N0RCxJQUFHOzREQUNIbUQsT0FBTzVGLFNBQVNnQixlQUFlOzREQUMvQjZFLFVBQVUsQ0FBQy9CLElBQU03RCxZQUFZO29FQUFDLEdBQUdELFFBQVE7b0VBQUVnQixpQkFBaUI4QyxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO2dFQUFBOzREQUMxRUwsV0FBVTs0REFDVitCLFVBQVVqSSxjQUFjOzs4RUFFeEIsOERBQUMyRztvRUFBT0osT0FBTTs4RUFBVTs7Ozs7OzhFQUN4Qiw4REFBQ0k7b0VBQU9KLE9BQU07OEVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHM0IsOERBQUNOOztzRUFDQyw4REFBQzdILHVEQUFLQTs0REFBQ3dKLFNBQVE7NERBQWdCMUIsV0FBVTtzRUFBd0Y7Ozs7OztzRUFHakksOERBQUMvSCx1REFBS0E7NERBQ0ppRixJQUFHOzREQUNIaUYsTUFBSzs0REFDTDlCLE9BQU81RixTQUFTaUIsYUFBYTs0REFDN0I0RSxVQUFVLENBQUMvQixJQUFNN0QsWUFBWTtvRUFBQyxHQUFHRCxRQUFRO29FQUFFaUIsZUFBZTZDLEVBQUVnQyxNQUFNLENBQUNGLEtBQUs7Z0VBQUE7NERBQ3hFdUIsVUFBVTlILGNBQWM7NERBQ3hCa0csV0FBVyxRQUFrSSxPQUExSGxHLGNBQWMsU0FBUyxlQUFlLHFGQUFvRjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1uSiw4REFBQ2lHOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDN0gsdURBQUtBOzREQUFDd0osU0FBUTs0REFBYzFCLFdBQVU7c0VBQTRGOzs7Ozs7c0VBR25JLDhEQUFDUTs0REFDQ3RELElBQUc7NERBQ0htRCxPQUFPNUYsU0FBU2MsV0FBVyxJQUFJOzREQUMvQitFLFVBQVUsQ0FBQy9CLElBQU03RCxZQUFZO29FQUFDLEdBQUdELFFBQVE7b0VBQUVjLGFBQWFnRCxFQUFFZ0MsTUFBTSxDQUFDRixLQUFLO2dFQUFBOzREQUN0RUwsV0FBVTs0REFDVitCLFVBQVVqSSxjQUFjO3NFQUV2QmIsV0FBVzhILEdBQUcsQ0FBQ2hFLENBQUFBLHlCQUNkLDhEQUFDMEQ7b0VBQXlCSixPQUFPdEQsU0FBU0csRUFBRTs7d0VBQ3pDSCxTQUFTcUYsYUFBYTt3RUFBQzt3RUFBR3JGLFNBQVNzRixNQUFNO3dFQUFDO3dFQUMxQ3RGLFNBQVNLLGdCQUFnQixJQUFJOzttRUFGbkJMLFNBQVNHLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTzlCLDhEQUFDNkM7O3NFQUNDLDhEQUFDN0gsdURBQUtBOzREQUFDd0osU0FBUTs0REFBYzFCLFdBQVU7c0VBQThGOzs7Ozs7c0VBR3JJLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMvSCx1REFBS0E7b0VBQ0ppRixJQUFHO29FQUNIaUYsTUFBSztvRUFDTEcsTUFBSztvRUFDTEMsS0FBSTtvRUFDSmxDLE9BQU81RixTQUFTYSxXQUFXLElBQUk7b0VBQy9CZ0YsVUFBVSxDQUFDL0IsSUFBTTdELFlBQVk7NEVBQUMsR0FBR0QsUUFBUTs0RUFBRWEsYUFBYWlELEVBQUVnQyxNQUFNLENBQUNGLEtBQUs7d0VBQUE7b0VBQ3RFdUIsVUFBVTlILGNBQWM7b0VBQ3hCa0csV0FBVyxjQUFpSixPQUFuSWxHLGNBQWMsU0FBUyxlQUFlLDhGQUE2RjtvRUFDNUpzRyxhQUFZOzs7Ozs7OEVBRWQsOERBQUNMO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDaUI7d0VBQUtqQixXQUFVO2tGQUNiL0csRUFBQUEsbUJBQUFBLFdBQVcrRCxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsQ0FBQ0MsUUFBUSxPQUFRMUMsQ0FBQUEsU0FBU2MsV0FBVyxJQUFJLEdBQUUsZ0JBQXBFdEMsdUNBQUFBLGlCQUF5RW9KLE1BQU0sS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzVGLDhEQUFDdEM7O3NFQUNDLDhEQUFDN0gsdURBQUtBOzREQUFDd0osU0FBUTs0REFBYTFCLFdBQVU7c0VBQTBGOzs7Ozs7c0VBR2hJLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMvSCx1REFBS0E7b0VBQ0ppRixJQUFHO29FQUNIaUYsTUFBSztvRUFDTDlCLE9BQU9jLEtBQUtxQixLQUFLLENBQUM5RixtQkFBbUJqQyxTQUFTYSxXQUFXLElBQUksS0FBS2IsU0FBU2MsV0FBVyxJQUFJLE1BQU04RixjQUFjLENBQUM7b0VBQy9HTyxRQUFRO29FQUNSNUIsV0FBVTs7Ozs7OzhFQUVaLDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ2lCO3dFQUFLakIsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU9oRCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUM3SCx1REFBS0E7NERBQUN3SixTQUFROzREQUFRMUIsV0FBVTtzRUFBd0Y7Ozs7OztzRUFHekgsOERBQUM2Qjs0REFDQzNFLElBQUc7NERBQ0htRCxPQUFPNUYsU0FBU2UsS0FBSzs0REFDckI4RSxVQUFVLENBQUMvQixJQUFNN0QsWUFBWTtvRUFBQyxHQUFHRCxRQUFRO29FQUFFZSxPQUFPK0MsRUFBRWdDLE1BQU0sQ0FBQ0YsS0FBSztnRUFBQTs0REFDaEV1QixVQUFVOUgsY0FBYzs0REFDeEJrRyxXQUFXLCtEQUF5TCxPQUExSGxHLGNBQWMsU0FBUyxlQUFlOzREQUNoSHNHLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFHaEIsOERBQUNMOzs7Ozs7Ozs7OztzREFJSCw4REFBQ0E7NENBQUlDLFdBQVU7O2dEQUNabEcsY0FBYyx3QkFDYiw4REFBQzlCLHlEQUFNQTtvREFBQ21LLE1BQUs7b0RBQVNuQyxXQUFVOztzRUFDOUIsOERBQUNqSCw0SUFBSUE7NERBQUNpSCxXQUFVOzs7Ozs7d0RBQ2ZsRyxjQUFjLFFBQVEsb0JBQW9COzs7Ozs7OzhEQUcvQyw4REFBQzlCLHlEQUFNQTtvREFDTG1LLE1BQUs7b0RBQ0xqQixTQUFRO29EQUNSZixTQUFTLElBQU10RyxlQUFlO29EQUM5Qm1HLFdBQVcsR0FBOEMsT0FBM0NsRyxjQUFjLFNBQVMsV0FBVyxVQUFTOzhEQUV4REEsY0FBYyxTQUFTLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBM1VsQyxTQUFzQkUsT0FBYkYsV0FBVSxLQUE2QixPQUExQkUsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFja0QsRUFBRSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXNWaEU7R0FuNUJ3QmxFO0tBQUFBIiwic291cmNlcyI6WyJEOlxcbW9oYW1pbmV3XFxzcmNcXGFwcFxcaXNzdWVzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTWFpbkxheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvbWFpbi1sYXlvdXQnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgQ2xpZW50U2VsZWN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NsaWVudC1zZWxlY3QnXG5pbXBvcnQge1xuICBGaWxlVGV4dCxcbiAgUGx1cyxcbiAgU2VhcmNoLFxuICBFeWUsXG4gIEVkaXQsXG4gIFRyYXNoMixcbiAgRmlsdGVyLFxuICBVc2VyLFxuICBCdWlsZGluZyxcbiAgU2NhbGUsXG4gIFgsXG4gIFNhdmVcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgSXNzdWUge1xuICBpZDogbnVtYmVyXG4gIGNhc2VfbnVtYmVyOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBjbGllbnRfaWQ/OiBudW1iZXJcbiAgY2xpZW50X25hbWU6IHN0cmluZ1xuICBjbGllbnRfcGhvbmU6IHN0cmluZ1xuICBjb3VydF9pZD86IG51bWJlclxuICBjb3VydF9uYW1lOiBzdHJpbmdcbiAgaXNzdWVfdHlwZV9pZD86IG51bWJlclxuICBpc3N1ZV90eXBlOiBzdHJpbmdcbiAgc3RhdHVzOiAnbmV3JyB8ICdwZW5kaW5nJyB8ICdpbl9wcm9ncmVzcycgfCAnY29tcGxldGVkJyB8ICdjYW5jZWxsZWQnXG4gIGNyZWF0ZWRfZGF0ZTogc3RyaW5nXG4gIGFtb3VudDogbnVtYmVyXG4gIGNhc2VfYW1vdW50PzogbnVtYmVyXG4gIGN1cnJlbmN5X2lkPzogbnVtYmVyXG4gIHN0YXJ0X2RhdGU/OiBzdHJpbmdcbiAgY29udHJhY3RfbWV0aG9kOiAn2KjYp9mE2KzZhNiz2KknIHwgJ9io2KfZhNi52YLYrydcbiAgY29udHJhY3RfZGF0ZTogc3RyaW5nXG4gIG5vdGVzPzogc3RyaW5nXG59XG5cbmludGVyZmFjZSBDb3VydCB7XG4gIGlkOiBudW1iZXJcbiAgbmFtZTogc3RyaW5nXG4gIGJyYW5jaF9uYW1lOiBzdHJpbmdcbiAgZ292ZXJub3JhdGVfbmFtZTogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIElzc3Vlc1BhZ2UoKSB7XG4gIGNvbnN0IFtpc3N1ZXMsIHNldElzc3Vlc10gPSB1c2VTdGF0ZTxJc3N1ZVtdPihbXSlcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtzdGF0dXNGaWx0ZXIsIHNldFN0YXR1c0ZpbHRlcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdhbGwnKVxuICBjb25zdCBbZGJFcnJvciwgc2V0RGJFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2lzTW9kYWxPcGVuLCBzZXRJc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW21vZGFsVHlwZSwgc2V0TW9kYWxUeXBlXSA9IHVzZVN0YXRlPCdhZGQnIHwgJ2VkaXQnIHwgJ3ZpZXcnPignYWRkJylcbiAgY29uc3QgW2VkaXRpbmdJc3N1ZSwgc2V0RWRpdGluZ0lzc3VlXSA9IHVzZVN0YXRlPElzc3VlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2NvdXJ0cywgc2V0Q291cnRzXSA9IHVzZVN0YXRlPENvdXJ0W10+KFtdKVxuICBjb25zdCBbY3VycmVuY2llcywgc2V0Q3VycmVuY2llc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pXG4gIGNvbnN0IFtjbGllbnRzLCBzZXRDbGllbnRzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW2lzc3VlVHlwZXMsIHNldElzc3VlVHlwZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKVxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBjYXNlX251bWJlcjogJycsXG4gICAgdGl0bGU6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBjbGllbnRfaWQ6ICcnLFxuICAgIGNsaWVudF9uYW1lOiAnJyxcbiAgICBjbGllbnRfcGhvbmU6ICcnLFxuICAgIGNvdXJ0X2lkOiAnJyxcbiAgICBjb3VydF9uYW1lOiAnJyxcbiAgICBpc3N1ZV90eXBlX2lkOiAnJyxcbiAgICBpc3N1ZV90eXBlOiAnJyxcbiAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICBjYXNlX2Ftb3VudDogJycsXG4gICAgY3VycmVuY3lfaWQ6ICcxJyxcbiAgICBub3RlczogJycsXG4gICAgY29udHJhY3RfbWV0aG9kOiAn2KjYp9mE2KzZhNiz2KknLFxuICAgIGNvbnRyYWN0X2RhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdIC8vINiq2KfYsdmK2K4g2KfZhNmK2YjZhSDZg9in2YHYqtix2KfYttmKXG4gIH0pXG5cbiAgLy8g2KzZhNioINin2YTZhdit2KfZg9mFINmF2YYg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG4gIGNvbnN0IGZldGNoQ291cnRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NvdXJ0cycpXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRDb3VydHMocmVzdWx0LmRhdGEpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNvdXJ0czonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyDYrNmE2Kgg2KfZhNi52YXZhNin2Kog2YXZhiDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbiAgY29uc3QgZmV0Y2hDdXJyZW5jaWVzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2N1cnJlbmNpZXMnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Q3VycmVuY2llcyhyZXN1bHQuZGF0YSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY3VycmVuY2llczonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyDYrNmE2Kgg2KfZhNi52YXZhNin2KEg2YXZhiDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbiAgY29uc3QgZmV0Y2hDbGllbnRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudHMnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Q2xpZW50cyhyZXN1bHQuZGF0YSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2xpZW50czonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyDYrNmE2Kgg2KPZhtmI2KfYuSDYp9mE2YLYttin2YrYpyDZhdmGINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqlxuICBjb25zdCBmZXRjaElzc3VlVHlwZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvaXNzdWUtdHlwZXMnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0SXNzdWVUeXBlcyhyZXN1bHQuZGF0YSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgaXNzdWUgdHlwZXM6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgLy8g2K3Ys9in2Kgg2KfZhNmF2KjZhNi6INio2KfZhNix2YrYp9mEINin2YTZitmF2YbZilxuICBjb25zdCBjYWxjdWxhdGVBbW91bnRZZXIgPSAoYW1vdW50OiBzdHJpbmcsIGN1cnJlbmN5SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG51bUFtb3VudCA9IHBhcnNlRmxvYXQoYW1vdW50KSB8fCAwXG4gICAgY29uc3QgY3VycmVuY3kgPSBjdXJyZW5jaWVzLmZpbmQoKGM6IGFueSkgPT4gYy5pZC50b1N0cmluZygpID09PSBjdXJyZW5jeUlkKVxuXG4gICAgaWYgKCFjdXJyZW5jeSkgcmV0dXJuIG51bUFtb3VudFxuXG4gICAgaWYgKGN1cnJlbmN5LmlzX2Jhc2VfY3VycmVuY3kpIHtcbiAgICAgIHJldHVybiBudW1BbW91bnRcbiAgICB9XG5cbiAgICByZXR1cm4gbnVtQW1vdW50ICogY3VycmVuY3kuZXhjaGFuZ2VfcmF0ZVxuICB9XG5cbiAgLy8g2KzZhNioINin2YTYqNmK2KfZhtin2Kog2YXZhiDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbiAgY29uc3QgZmV0Y2hJc3N1ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgc2V0RGJFcnJvcihudWxsKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvaXNzdWVzJylcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0SXNzdWVzKHJlc3VsdC5kYXRhKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RGJFcnJvcihyZXN1bHQuZXJyb3IgfHwgJ9mB2LTZhCDZgdmKINis2YTYqCDYqNmK2KfZhtin2Kog2KfZhNmC2LbYp9mK2KcnKVxuICAgICAgICBzZXRJc3N1ZXMoW10pXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ05ldHdvcmsgZXJyb3I6JywgZXJyb3IpXG4gICAgICBzZXREYkVycm9yKCfZgdi02YQg2YHZiiDYp9mE2KfYqti12KfZhCDYqNmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqicpXG4gICAgICBzZXRJc3N1ZXMoW10pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoSXNzdWVzKClcbiAgICBmZXRjaENvdXJ0cygpXG4gICAgZmV0Y2hDdXJyZW5jaWVzKClcbiAgICBmZXRjaENsaWVudHMoKVxuICAgIGZldGNoSXNzdWVUeXBlcygpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGZpbHRlcmVkSXNzdWVzID0gaXNzdWVzLmZpbHRlcihpc3N1ZSA9PiB7XG4gICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9XG4gICAgICBpc3N1ZS5jYXNlX251bWJlci50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgIGlzc3VlLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgaXNzdWUuY2xpZW50X25hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpXG5cbiAgICBjb25zdCBtYXRjaGVzU3RhdHVzID0gc3RhdHVzRmlsdGVyID09PSAnYWxsJyB8fCBpc3N1ZS5zdGF0dXMgPT09IHN0YXR1c0ZpbHRlclxuXG4gICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc1N0YXR1c1xuICB9KVxuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ25ldyc6IHJldHVybiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnXG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgIGNhc2UgJ2luX3Byb2dyZXNzJzogcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJ1xuICAgICAgY2FzZSAnY29tcGxldGVkJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICBjYXNlICdjYW5jZWxsZWQnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c1RleHQgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnbmV3JzogcmV0dXJuICfYrNiv2YrYr9ipJ1xuICAgICAgY2FzZSAncGVuZGluZyc6IHJldHVybiAn2YXYudmE2YLYqSdcbiAgICAgIGNhc2UgJ2luX3Byb2dyZXNzJzogcmV0dXJuICfZgtmK2K8g2KfZhNmF2LnYp9mE2KzYqSdcbiAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6IHJldHVybiAn2YXZg9iq2YXZhNipJ1xuICAgICAgY2FzZSAnY2FuY2VsbGVkJzogcmV0dXJuICfZhdmE2LrZitipJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICfYutmK2LEg2YXYrdiv2K8nXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQWRkTmV3ID0gKCkgPT4ge1xuICAgIHNldEVkaXRpbmdJc3N1ZShudWxsKVxuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIGNhc2VfbnVtYmVyOiAnJyxcbiAgICAgIHRpdGxlOiAnJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgIGNsaWVudF9pZDogJycsXG4gICAgICBjbGllbnRfbmFtZTogJycsXG4gICAgICBjbGllbnRfcGhvbmU6ICcnLFxuICAgICAgY291cnRfaWQ6ICcnLFxuICAgICAgY291cnRfbmFtZTogJycsXG4gICAgICBpc3N1ZV90eXBlX2lkOiAnJyxcbiAgICAgIGlzc3VlX3R5cGU6ICcnLFxuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICBjYXNlX2Ftb3VudDogJycsXG4gICAgICBjdXJyZW5jeV9pZDogJzEnLFxuICAgICAgbm90ZXM6ICcnLFxuICAgICAgY29udHJhY3RfbWV0aG9kOiAn2KjYp9mE2KzZhNiz2KknLFxuICAgICAgY29udHJhY3RfZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0gLy8g2KrYp9ix2YrYriDYp9mE2YrZiNmFINmD2KfZgdiq2LHYp9i22YpcbiAgICB9KVxuICAgIHNldE1vZGFsVHlwZSgnYWRkJylcbiAgICBzZXRJc01vZGFsT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ2xpZW50Q2hhbmdlID0gKGNsaWVudElkOiBzdHJpbmcsIGNsaWVudERhdGE6IGFueSkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgY2xpZW50X2lkOiBjbGllbnRJZCxcbiAgICAgIGNsaWVudF9uYW1lOiBjbGllbnREYXRhID8gY2xpZW50RGF0YS5uYW1lIDogJycsXG4gICAgICBjbGllbnRfcGhvbmU6IGNsaWVudERhdGEgPyBjbGllbnREYXRhLnBob25lIDogJydcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuXG4gICAgdHJ5IHtcbiAgICAgIGlmIChtb2RhbFR5cGUgPT09ICdhZGQnKSB7XG4gICAgICAgIC8vINil2LbYp9mB2Kkg2LHZgtmFINin2YTZhdiz2KrYrtiv2YUg2KrZhNmC2KfYptmK2KfZiyAo2YrZhdmD2YYg2KfZhNit2LXZiNmEINi52YTZitmHINmF2YYg2KfZhNis2YTYs9ipINmE2KfYrdmC2KfZiylcbiAgICAgICAgY29uc3QgZGF0YVRvU3VibWl0ID0ge1xuICAgICAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgICAgIGNyZWF0ZWRfYnk6IDEgLy8g2LHZgtmFINin2YTZhdiz2KrYrtiv2YUg2KfZhNin2YHYqtix2KfYttmKIC0g2YrYrNioINiq2K3Yr9mK2KvZhyDZhNmK2KPYrtiwINmF2YYg2KfZhNis2YTYs9ipXG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZygnRGF0YSBiZWluZyBzdWJtaXR0ZWQgZm9yIG5ldyBpc3N1ZTonLCBkYXRhVG9TdWJtaXQpXG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9pc3N1ZXMnLCB7XG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YVRvU3VibWl0KVxuICAgICAgICB9KVxuXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgIGFsZXJ0KCfYqtmFINil2LbYp9mB2Kkg2KfZhNmC2LbZitipINio2YbYrNin2K0nKVxuICAgICAgICAgIGZldGNoSXNzdWVzKClcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBhbGVydChyZXN1bHQuZXJyb3IgfHwgJ9mB2LTZhCDZgdmKINil2LbYp9mB2Kkg2KfZhNmC2LbZitipJylcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChtb2RhbFR5cGUgPT09ICdlZGl0JyAmJiBlZGl0aW5nSXNzdWUpIHtcbiAgICAgICAgLy8g2KXYsdiz2KfZhCDYrNmF2YrYuSDYp9mE2KjZitin2YbYp9iqINmE2YTYqtit2K/ZitirXG4gICAgICAgIGNvbnN0IGRhdGFUb1N1Ym1pdCA9IHtcbiAgICAgICAgICAuLi5mb3JtRGF0YVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1NlbmRpbmcgZGF0YSB0byBBUEk6JywgZGF0YVRvU3VibWl0KVxuXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvaXNzdWVzLyR7ZWRpdGluZ0lzc3VlLmlkfWAsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGRhdGFUb1N1Ym1pdClcbiAgICAgICAgfSlcblxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICBhbGVydCgn2KrZhSDYqtit2K/ZitirINin2YTZgti22YrYqSDYqNmG2KzYp9itJylcbiAgICAgICAgICBmZXRjaElzc3VlcygpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgYWxlcnQocmVzdWx0LmVycm9yIHx8ICfZgdi02YQg2YHZiiDYqtit2K/ZitirINin2YTZgti22YrYqScpXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgc2V0SXNNb2RhbE9wZW4oZmFsc2UpXG4gICAgICBzZXRFZGl0aW5nSXNzdWUobnVsbClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3VibWl0dGluZyBmb3JtOicsIGVycm9yKVxuICAgICAgYWxlcnQoJ9it2K/YqyDYrti32KMg2YHZiiDYp9mE2KfYqti12KfZhCcpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVmlld0lzc3VlID0gKGlzc3VlOiBJc3N1ZSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdoYW5kbGVWaWV3SXNzdWU6IE9yaWdpbmFsIGlzc3VlIGRhdGE6JywgaXNzdWUpXG5cbiAgICBzZXRFZGl0aW5nSXNzdWUoaXNzdWUpXG5cbiAgICAvLyDZhdi52KfZhNis2Kkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2YHYp9ix2LrYqSDYo9mIIG51bGxcbiAgICBjb25zdCBmb3JtRGF0YVRvU2V0ID0ge1xuICAgICAgY2FzZV9udW1iZXI6IGlzc3VlLmNhc2VfbnVtYmVyIHx8ICcnLFxuICAgICAgdGl0bGU6IGlzc3VlLnRpdGxlIHx8ICcnLFxuICAgICAgZGVzY3JpcHRpb246IGlzc3VlLmRlc2NyaXB0aW9uIHx8ICfZhNinINmK2YjYrNivINmI2LXZgScsXG4gICAgICBjbGllbnRfaWQ6IGlzc3VlLmNsaWVudF9pZCA/IGlzc3VlLmNsaWVudF9pZC50b1N0cmluZygpIDogJycsXG4gICAgICBjbGllbnRfbmFtZTogaXNzdWUuY2xpZW50X25hbWUgfHwgJycsXG4gICAgICBjbGllbnRfcGhvbmU6IGlzc3VlLmNsaWVudF9waG9uZSB8fCAn2LrZitixINmF2K3Yr9ivJyxcbiAgICAgIGNvdXJ0X2lkOiBpc3N1ZS5jb3VydF9pZCA/IGlzc3VlLmNvdXJ0X2lkLnRvU3RyaW5nKCkgOiAnJyxcbiAgICAgIGNvdXJ0X25hbWU6IGlzc3VlLmNvdXJ0X25hbWUgfHwgJ9i62YrYsSDZhdit2K/YrycsXG4gICAgICBpc3N1ZV90eXBlX2lkOiBpc3N1ZS5pc3N1ZV90eXBlX2lkID8gaXNzdWUuaXNzdWVfdHlwZV9pZC50b1N0cmluZygpIDogJycsXG4gICAgICBpc3N1ZV90eXBlOiBpc3N1ZS5pc3N1ZV90eXBlIHx8ICfYudin2YUnLFxuICAgICAgc3RhdHVzOiBpc3N1ZS5zdGF0dXMgfHwgJ25ldycsXG4gICAgICBjYXNlX2Ftb3VudDogaXNzdWUuY2FzZV9hbW91bnQgPyBpc3N1ZS5jYXNlX2Ftb3VudC50b1N0cmluZygpIDogJzAnLFxuICAgICAgY3VycmVuY3lfaWQ6IGlzc3VlLmN1cnJlbmN5X2lkID8gaXNzdWUuY3VycmVuY3lfaWQudG9TdHJpbmcoKSA6ICcxJyxcbiAgICAgIG5vdGVzOiBpc3N1ZS5ub3RlcyB8fCAn2YTYpyDYqtmI2KzYryDZhdmE2KfYrdi42KfYqicsXG4gICAgICBjb250cmFjdF9tZXRob2Q6IGlzc3VlLmNvbnRyYWN0X21ldGhvZCB8fCAn2KjYp9mE2KzZhNiz2KknLFxuICAgICAgY29udHJhY3RfZGF0ZTogaXNzdWUuY29udHJhY3RfZGF0ZSA/IG5ldyBEYXRlKGlzc3VlLmNvbnRyYWN0X2RhdGUpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ2hhbmRsZVZpZXdJc3N1ZTogRm9ybSBkYXRhIHRvIHNldDonLCBmb3JtRGF0YVRvU2V0KVxuXG4gICAgc2V0Rm9ybURhdGEoZm9ybURhdGFUb1NldClcbiAgICBzZXRNb2RhbFR5cGUoJ3ZpZXcnKVxuICAgIHNldElzTW9kYWxPcGVuKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0SXNzdWUgPSAoaXNzdWU6IElzc3VlKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ2hhbmRsZUVkaXRJc3N1ZTogT3JpZ2luYWwgaXNzdWUgZGF0YTonLCBpc3N1ZSlcblxuICAgIHNldEVkaXRpbmdJc3N1ZShpc3N1ZSlcblxuICAgIC8vINmF2LnYp9mE2KzYqSDYp9mE2KjZitin2YbYp9iqINin2YTZgdin2LHYutipINij2YggbnVsbFxuICAgIGNvbnN0IGZvcm1EYXRhVG9TZXQgPSB7XG4gICAgICBjYXNlX251bWJlcjogaXNzdWUuY2FzZV9udW1iZXIgfHwgJycsXG4gICAgICB0aXRsZTogaXNzdWUudGl0bGUgfHwgJycsXG4gICAgICBkZXNjcmlwdGlvbjogaXNzdWUuZGVzY3JpcHRpb24gfHwgJ9mE2Kcg2YrZiNis2K8g2YjYtdmBJyxcbiAgICAgIGNsaWVudF9pZDogaXNzdWUuY2xpZW50X2lkID8gaXNzdWUuY2xpZW50X2lkLnRvU3RyaW5nKCkgOiAnJyxcbiAgICAgIGNsaWVudF9uYW1lOiBpc3N1ZS5jbGllbnRfbmFtZSB8fCAnJyxcbiAgICAgIGNsaWVudF9waG9uZTogaXNzdWUuY2xpZW50X3Bob25lIHx8ICfYutmK2LEg2YXYrdiv2K8nLFxuICAgICAgY291cnRfaWQ6IGlzc3VlLmNvdXJ0X2lkID8gaXNzdWUuY291cnRfaWQudG9TdHJpbmcoKSA6ICcnLFxuICAgICAgY291cnRfbmFtZTogaXNzdWUuY291cnRfbmFtZSB8fCAn2LrZitixINmF2K3Yr9ivJyxcbiAgICAgIGlzc3VlX3R5cGVfaWQ6IGlzc3VlLmlzc3VlX3R5cGVfaWQgPyBpc3N1ZS5pc3N1ZV90eXBlX2lkLnRvU3RyaW5nKCkgOiAnJyxcbiAgICAgIGlzc3VlX3R5cGU6IGlzc3VlLmlzc3VlX3R5cGUgfHwgJ9i52KfZhScsXG4gICAgICBzdGF0dXM6IGlzc3VlLnN0YXR1cyB8fCAnbmV3JyxcbiAgICAgIGNhc2VfYW1vdW50OiBpc3N1ZS5jYXNlX2Ftb3VudCA/IGlzc3VlLmNhc2VfYW1vdW50LnRvU3RyaW5nKCkgOiAnMCcsXG4gICAgICBjdXJyZW5jeV9pZDogaXNzdWUuY3VycmVuY3lfaWQgPyBpc3N1ZS5jdXJyZW5jeV9pZC50b1N0cmluZygpIDogJzEnLFxuICAgICAgbm90ZXM6IGlzc3VlLm5vdGVzIHx8ICfZhNinINiq2YjYrNivINmF2YTYp9it2LjYp9iqJyxcbiAgICAgIGNvbnRyYWN0X21ldGhvZDogaXNzdWUuY29udHJhY3RfbWV0aG9kIHx8ICfYqNin2YTYrNmE2LPYqScsXG4gICAgICBjb250cmFjdF9kYXRlOiBpc3N1ZS5jb250cmFjdF9kYXRlID8gbmV3IERhdGUoaXNzdWUuY29udHJhY3RfZGF0ZSkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdIDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF1cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnaGFuZGxlRWRpdElzc3VlOiBGb3JtIGRhdGEgdG8gc2V0OicsIGZvcm1EYXRhVG9TZXQpXG5cbiAgICBzZXRGb3JtRGF0YShmb3JtRGF0YVRvU2V0KVxuICAgIHNldE1vZGFsVHlwZSgnZWRpdCcpXG4gICAgc2V0SXNNb2RhbE9wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUlzc3VlID0gYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ9mH2YQg2KPZhtiqINmF2KrYo9mD2K8g2YXZhiDYrdiw2YEg2YfYsNmHINin2YTZgti22YrYqdifJykpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvaXNzdWVzLyR7aWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnXG4gICAgICB9KVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIGFsZXJ0KCfYqtmFINit2LDZgSDYp9mE2YLYttmK2Kkg2KjZhtis2KfYrScpXG4gICAgICAgIGZldGNoSXNzdWVzKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KHJlc3VsdC5lcnJvciB8fCAn2YHYtNmEINmB2Yog2K3YsNmBINin2YTZgti22YrYqScpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIGlzc3VlOicsIGVycm9yKVxuICAgICAgYWxlcnQoJ9it2K/YqyDYrti32KMg2YHZiiDYrdiw2YEg2KfZhNmC2LbZitipJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBzdGF0cyA9IHtcbiAgICB0b3RhbDogaXNzdWVzLmxlbmd0aCxcbiAgICBuZXc6IGlzc3Vlcy5maWx0ZXIoaSA9PiBpLnN0YXR1cyA9PT0gJ25ldycpLmxlbmd0aCxcbiAgICBwZW5kaW5nOiBpc3N1ZXMuZmlsdGVyKGkgPT4gaS5zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RoLFxuICAgIGluX3Byb2dyZXNzOiBpc3N1ZXMuZmlsdGVyKGkgPT4gaS5zdGF0dXMgPT09ICdpbl9wcm9ncmVzcycpLmxlbmd0aCxcbiAgICBjb21wbGV0ZWQ6IGlzc3Vlcy5maWx0ZXIoaSA9PiBpLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8TWFpbkxheW91dD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgcC02IGJnLXdoaXRlIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICB7Lyog2KfZhNi52YbZiNin2YYg2YjYp9mE2KXYrdi12KfYptmK2KfYqiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxTY2FsZSBjbGFzc05hbWU9XCJoLTggdy04IG1yLTMgdGV4dC1lbWVyYWxkLTYwMFwiIC8+XG4gICAgICAgICAgICAgINil2K/Yp9ix2Kkg2KfZhNmC2LbYp9mK2KdcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIG10LTFcIj7Ypdiv2KfYsdipINmI2YXYqtin2KjYudipINis2YXZiti5INin2YTZgti22KfZitinINin2YTZgtin2YbZiNmG2YrYqTwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlQWRkTmV3fSBjbGFzc05hbWU9XCJiZy1lbWVyYWxkLTYwMCBob3ZlcjpiZy1lbWVyYWxkLTcwMFwiPlxuICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgINil2LbYp9mB2Kkg2YLYttmK2Kkg2KzYr9mK2K/YqVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog2KjYt9in2YLYp9iqINin2YTYpdit2LXYp9im2YrYp9iqICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTQgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWVtZXJhbGQtMTAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZW1lcmFsZC02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0cy50b3RhbH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+2KXYrNmF2KfZhNmKINin2YTZgti22KfZitinPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLXB1cnBsZS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1yLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57c3RhdHMubmV3fTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj7Zgti22KfZitinINis2K/Zitiv2Kk8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctYmx1ZS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPFNjYWxlIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtci00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3N0YXRzLmluX3Byb2dyZXNzfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj7ZgtmK2K8g2KfZhNmF2LnYp9mE2KzYqTwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmVlbi0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0cy5jb21wbGV0ZWR9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPtmC2LbYp9mK2Kcg2YXZg9iq2YXZhNipPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYtNix2YrYtyDYp9mE2KjYrdirINmI2KfZhNmB2YTYqtix2KkgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS03MDAgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItin2YTYqNit2Ksg2KjYsdmC2YUg2KfZhNmC2LbZitip2Iwg2KfZhNi52YbZiNin2YbYjCDYo9mIINin2LPZhSDYp9mE2YXZiNmD2YQuLi5cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHItMTBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c3RhdHVzRmlsdGVyfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTdGF0dXNGaWx0ZXIoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj7YrNmF2YrYuSDYp9mE2K3Yp9mE2KfYqjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5ld1wiPtis2K/Zitiv2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwZW5kaW5nXCI+2YXYudmE2YLYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImluX3Byb2dyZXNzXCI+2YLZitivINin2YTZhdi52KfZhNis2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb21wbGV0ZWRcIj7ZhdmD2KrZhdmE2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjYW5jZWxsZWRcIj7ZhdmE2LrZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDYrNiv2YjZhCDYp9mE2YLYttin2YrYpyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICDZgtin2KbZhdipINin2YTZgti22KfZitinICh7ZmlsdGVyZWRJc3N1ZXMubGVuZ3RofSlcbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci1jb2xsYXBzZVwiPlxuICAgICAgICAgICAgICAgIDx0aGVhZD5cbiAgICAgICAgICAgICAgICAgIDx0ciBjbGFzc05hbWU9XCJib3JkZXItYiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHAtNCBmb250LXNlbWlib2xkIHRleHQtbGdcIj7YsdmC2YUg2KfZhNmC2LbZitipPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcC00IGZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPtin2YTYudmG2YjYp9mGPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcC00IGZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPtin2YTZhdmI2YPZhDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+2KfZhNmH2KfYqtmBPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCBmb250LXNlbWlib2xkIHRleHQtbGdcIj7Yp9mE2YXYrdmD2YXYqTwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+2KfZhNmG2YjYuTwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+2LfYsdmK2YLYqSDYp9mE2KrYudin2YLYrzwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+2KfZhNit2KfZhNipPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCBmb250LXNlbWlib2xkIHRleHQtbGdcIj7Yp9mE2YXYqNmE2Lo8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC00IGZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPtin2YTYpdis2LHYp9ih2KfYqjwvdGg+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkSXNzdWVzLm1hcCgoaXNzdWUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17aXNzdWUuaWR9IGNsYXNzTmFtZT1cImJvcmRlci1iIGhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC00IGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDBcIj57aXNzdWUuY2FzZV9udW1iZXJ9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicC00IGZvbnQtbWVkaXVtXCI+e2lzc3VlLnRpdGxlfTwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgdGV4dC1ncmF5LTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpc3N1ZS5jbGllbnRfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzc3VlLmNsaWVudF9waG9uZSB8fCAn2LrZitixINmF2K3Yr9ivJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdWlsZGluZyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgdGV4dC1ncmF5LTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2lzc3VlLmNvdXJ0X25hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2lzc3VlLmlzc3VlX3R5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtpc3N1ZS5jb250cmFjdF9tZXRob2QgPT09ICfYqNin2YTYrNmE2LPYqScgPyAnYmctZ3JlZW4tNTAgdGV4dC1ncmVlbi03MDAnIDogJ2JnLXB1cnBsZS01MCB0ZXh0LXB1cnBsZS03MDAnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNzdWUuY29udHJhY3RfbWV0aG9kfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9e2dldFN0YXR1c0NvbG9yKGlzc3VlLnN0YXR1cyl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzVGV4dChpc3N1ZS5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgZm9udC1tZWRpdW0gdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc3N1ZS5hbW91bnQgPyBNYXRoLmZsb29yKGlzc3VlLmFtb3VudCkudG9Mb2NhbGVTdHJpbmcoKSA6ICcwJ30g2LHZitin2YRcbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3SXNzdWUoaXNzdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgaG92ZXI6YmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCBib3JkZXItYmx1ZS0yMDAgaG92ZXI6Ym9yZGVyLWJsdWUtMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIti52LHYtiDYqtmB2KfYtdmK2YQg2KfZhNmC2LbZitipXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdElzc3VlKGlzc3VlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAgaG92ZXI6YmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy03MDAgYm9yZGVyLXllbGxvdy0yMDAgaG92ZXI6Ym9yZGVyLXllbGxvdy0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi2KrYudiv2YrZhCDYp9mE2YLYttmK2KlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlSXNzdWUoaXNzdWUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC01MCBob3ZlcjpiZy1yZWQtMTAwIHRleHQtcmVkLTcwMCBib3JkZXItcmVkLTIwMCBob3Zlcjpib3JkZXItcmVkLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLYrdiw2YEg2KfZhNmC2LbZitipXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIE1vZGFsINmE2KXYttin2YHYqSDZgti22YrYqSDYrNiv2YrYr9ipICovfVxuICAgICAgICB7aXNNb2RhbE9wZW4gJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cbiAgICAgICAgICAgIDxkaXYga2V5PXtgbW9kYWwtJHttb2RhbFR5cGV9LSR7ZWRpdGluZ0lzc3VlPy5pZCB8fCAnbmV3J31gfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy0yeGwgcC04IHctZnVsbCBtYXgtdy00eGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LXktYXV0byBib3JkZXItdC00IGJvcmRlci1ibHVlLTUwMFwiPlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTYgdy02IG1yLTMgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICB7bW9kYWxUeXBlID09PSAnYWRkJyAmJiAn8J+TiyDYpdi22KfZgdipINmC2LbZitipINis2K/Zitiv2KknfVxuICAgICAgICAgICAgICAgICAge21vZGFsVHlwZSA9PT0gJ2VkaXQnICYmICfinI/vuI8g2KrYudiv2YrZhCDYp9mE2YLYttmK2KknfVxuICAgICAgICAgICAgICAgICAge21vZGFsVHlwZSA9PT0gJ3ZpZXcnICYmICfwn5GB77iPINi52LHYtiDYqtmB2KfYtdmK2YQg2KfZhNmC2LbZitipJ31cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgb25DbGljaz17KCkgPT4gc2V0SXNNb2RhbE9wZW4oZmFsc2UpfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1yZWQtNTAgaG92ZXI6dGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgey8qINin2YTYtdmBINin2YTYo9mI2YQ6INix2YLZhSDYp9mE2YLYttmK2Kkg2YjYudmG2YjYp9mGINin2YTZgti22YrYqSAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEwIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjYXNlX251bWJlclwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtNzAwIGJnLWJsdWUtNTAgcHgtMyBweS0xIHJvdW5kZWQtbWQgaW5saW5lLWJsb2NrIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDwn5OLINix2YLZhSDYp9mE2YLYttmK2KkgKlxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImNhc2VfbnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY2FzZV9udW1iZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIGNhc2VfbnVtYmVyOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXttb2RhbFR5cGUgIT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgICByZWFkT25seT17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC0xMCAke21vZGFsVHlwZSA9PT0gJ3ZpZXcnID8gJ2JnLWdyYXktNTAnIDogJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMzAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINix2YLZhSDYp9mE2YLYttmK2KkuLi5cIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTdcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0aXRsZVwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTcwMCBiZy1ncmVlbi01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCfk50g2LnZhtmI2KfZhiDYp9mE2YLYttmK2KkgKlxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInRpdGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIHRpdGxlOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXttb2RhbFR5cGUgIT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgICByZWFkT25seT17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC0xMCAke21vZGFsVHlwZSA9PT0gJ3ZpZXcnID8gJ2JnLWdyYXktNTAnIDogJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0zMDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINi52YbZiNin2YYg2KfZhNmC2LbZitipLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINin2YTYtdmBINin2YTYq9in2YbZijog2YjYtdmBINin2YTZgti22YrYqSAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGVzY3JpcHRpb25cIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMCBiZy1ncmF5LTUwIHB4LTMgcHktMSByb3VuZGVkLW1kIGlubGluZS1ibG9jayBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+ThCDZiNi12YEg2KfZhNmC2LbZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9e21vZGFsVHlwZSA9PT0gJ3ZpZXcnfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBoLTEyIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1tZCByZXNpemUtbm9uZSB0ZXh0LXNtICR7bW9kYWxUeXBlID09PSAndmlldycgPyAnYmctZ3JheS01MCcgOiAnYmctZ3JheS01MCBib3JkZXItZ3JheS0zMDAgZm9jdXM6Ym9yZGVyLWdyYXktNTAwIGZvY3VzOmJnLXdoaXRlIHRyYW5zaXRpb24tY29sb3JzJ31gfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KPYr9iu2YQg2YjYtdmBINmF2K7Yqti12LEg2YTZhNmC2LbZitipLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINin2YTYtdmBINin2YTYq9in2YTYqzog2KfZhNmF2YjZg9mEINmI2KfZhNmF2K3Zg9mF2KkgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS03MDAgYmctcHVycGxlLTUwIHB4LTMgcHktMSByb3VuZGVkLW1kIGlubGluZS1ibG9jayBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+RpCDYp9mE2YXZiNmD2YQgKlxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXB1cnBsZS01MCBib3JkZXItcHVycGxlLTMwMCBmb2N1cy13aXRoaW46Ym9yZGVyLXB1cnBsZS01MDAgZm9jdXMtd2l0aGluOmJnLXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2xpZW50U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY2xpZW50X2lkfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNsaWVudENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KfYrtiq2LEg2KfZhNmF2YjZg9mELi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY291cnRfaWRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1vcmFuZ2UtNzAwIGJnLW9yYW5nZS01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCfj5vvuI8g2KfZhNmF2K3Zg9mF2KlcbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY291cnRfaWRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb3VydF9pZCB8fCBcIlwifVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRDb3VydCA9IGNvdXJ0cy5maW5kKGMgPT4gYy5pZC50b1N0cmluZygpID09PSBlLnRhcmdldC52YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZm9ybURhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJ0X2lkOiBlLnRhcmdldC52YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY291cnRfbmFtZTogc2VsZWN0ZWRDb3VydD8ubmFtZSB8fCAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTEwIHB4LTMgcHktMiBiZy1vcmFuZ2UtNTAgYm9yZGVyIGJvcmRlci1vcmFuZ2UtMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9yYW5nZS01MDAgZm9jdXM6Ym9yZGVyLW9yYW5nZS01MDAgZm9jdXM6Ymctd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXttb2RhbFR5cGUgPT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7Yp9iu2KrYsSDYp9mE2YXYrdmD2YXYqS4uLjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3VydHMubWFwKChjb3VydCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NvdXJ0LmlkfSB2YWx1ZT17Y291cnQuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnQubmFtZX0gLSB7Y291cnQuZ292ZXJub3JhdGVfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINin2YTYtdmBINin2YTYq9in2YTYqzog2LfYsdmK2YLYqSDYp9mE2KrYudin2YLYryDZiNit2KfZhNipINin2YTZgti22YrYqSDZiNmG2YjYuSDYp9mE2YLYttmK2KkgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbnRyYWN0X21ldGhvZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXRlYWwtNzAwIGJnLXRlYWwtNTAgcHgtMyBweS0xIHJvdW5kZWQtbWQgaW5saW5lLWJsb2NrIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDwn5OEINi32LHZitmC2Kkg2KfZhNiq2LnYp9mC2K9cbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY29udHJhY3RfbWV0aG9kXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29udHJhY3RfbWV0aG9kIHx8IFwi2KjYp9mE2KzZhNiz2KlcIn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgY29udHJhY3RfbWV0aG9kOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTEwIHB4LTMgcHktMiBiZy10ZWFsLTUwIGJvcmRlciBib3JkZXItdGVhbC0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctdGVhbC01MDAgZm9jdXM6Ym9yZGVyLXRlYWwtNTAwIGZvY3VzOmJnLXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXttb2RhbFR5cGUgPT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLYqNin2YTYrNmE2LPYqVwiPtio2KfZhNis2YTYs9ipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cItio2KfZhNi52YLYr1wiPtio2KfZhNi52YLYrzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzdGF0dXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1yZWQtNzAwIGJnLXJlZC01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCflIQg2K3Yp9mE2Kkg2KfZhNmC2LbZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInN0YXR1c1wiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnN0YXR1cyB8fCBcIm5ld1wifVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCBzdGF0dXM6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtMTAgcHgtMyBweS0yIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibmV3XCI+2KzYr9mK2K/YqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwZW5kaW5nXCI+2YXYudmE2YLYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJpbl9wcm9ncmVzc1wiPtmC2YrYryDYp9mE2YXYudin2YTYrNipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNvbXBsZXRlZFwiPtmF2YPYqtmF2YTYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjYW5jZWxsZWRcIj7ZhdmE2LrZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImlzc3VlX3R5cGVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1pbmRpZ28tNzAwIGJnLWluZGlnby01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIOKalu+4jyDZhtmI2Lkg2KfZhNmC2LbZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImlzc3VlX3R5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5pc3N1ZV90eXBlIHx8IFwiXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIGlzc3VlX3R5cGU6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtMTAgcHgtMyBweS0yIGJnLWluZGlnby01MCBib3JkZXIgYm9yZGVyLWluZGlnby0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+2KfYrtiq2LEg2YbZiNi5INin2YTZgti22YrYqS4uLjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLZhdiv2YbZitipXCI+2YXYr9mG2YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLYqtis2KfYsdmK2KlcIj7Yqtis2KfYsdmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi2KzZhtin2KbZitipXCI+2KzZhtin2KbZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cItij2K3ZiNin2YQg2LTYrti12YrYqVwiPtij2K3ZiNin2YQg2LTYrti12YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLYudmF2KfZhNmK2KlcIj7YudmF2KfZhNmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi2KXYr9in2LHZitipXCI+2KXYr9in2LHZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Lyog2KfZhNi12YEg2KfZhNir2KfZhNirOiDYp9mE2K3Yp9mE2Kkg2YjZhtmI2Lkg2KfZhNmC2LbZitipINmI2LfYsdmK2YLYqSDYp9mE2KrYudin2YLYryDZiNiq2KfYsdmK2K4g2KfZhNiq2LnYp9mC2K8gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy00IGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInN0YXR1c1wiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXJlZC03MDAgYmctcmVkLTUwIHB4LTMgcHktMSByb3VuZGVkLW1kIGlubGluZS1ibG9jayBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+TiiDYrdin2YTYqSDYp9mE2YLYttmK2KlcbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwic3RhdHVzXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCBzdGF0dXM6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtMTAgcHgtMyBweS0yIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcmVkLTUwMCBmb2N1czpib3JkZXItcmVkLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibmV3XCI+2KzYr9mK2K/YqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwZW5kaW5nXCI+2YXYudmE2YLYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJpbl9wcm9ncmVzc1wiPtmC2YrYryDYp9mE2YXYudin2YTYrNipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNvbXBsZXRlZFwiPtmF2YPYqtmF2YTYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjYW5jZWxsZWRcIj7ZhdmE2LrZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImlzc3VlX3R5cGVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1pbmRpZ28tNzAwIGJnLWluZGlnby01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIOKalu+4jyDZhtmI2Lkg2KfZhNmC2LbZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImlzc3VlX3R5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5pc3N1ZV90eXBlIHx8IFwiXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIGlzc3VlX3R5cGU6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtMTAgcHgtMyBweS0yIGJnLWluZGlnby01MCBib3JkZXIgYm9yZGVyLWluZGlnby0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMCBmb2N1czpiZy13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bW9kYWxUeXBlID09PSAndmlldyd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+2KfYrtiq2LEg2YbZiNi5INin2YTZgti22YrYqS4uLjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLZhdiv2YbZitipXCI+2YXYr9mG2YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLYqtis2KfYsdmK2KlcIj7Yqtis2KfYsdmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi2KzZhtin2KbZitipXCI+2KzZhtin2KbZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cItij2K3ZiNin2YQg2LTYrti12YrYqVwiPtij2K3ZiNin2YQg2LTYrti12YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCLYudmF2KfZhNmK2KlcIj7YudmF2KfZhNmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi2KXYr9in2LHZitipXCI+2KXYr9in2LHZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbnRyYWN0X21ldGhvZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXRlYWwtNzAwIGJnLXRlYWwtNTAgcHgtMyBweS0xIHJvdW5kZWQtbWQgaW5saW5lLWJsb2NrIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDwn5OdINi32LHZitmC2Kkg2KfZhNiq2LnYp9mC2K9cbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY29udHJhY3RfbWV0aG9kXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29udHJhY3RfbWV0aG9kfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCBjb250cmFjdF9tZXRob2Q6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtMTAgcHgtMyBweS0yIGJnLXRlYWwtNTAgYm9yZGVyIGJvcmRlci10ZWFsLTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy10ZWFsLTUwMCBmb2N1czpib3JkZXItdGVhbC01MDAgZm9jdXM6Ymctd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e21vZGFsVHlwZSA9PT0gJ3ZpZXcnfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cItio2KfZhNis2YTYs9ipXCI+2KjYp9mE2KzZhNiz2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi2KjYp9mE2LnZgtivXCI+2KjYp9mE2LnZgtivPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbnRyYWN0X2RhdGVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1waW5rLTcwMCBiZy1waW5rLTUwIHB4LTMgcHktMSByb3VuZGVkLW1kIGlubGluZS1ibG9jayBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+ThSDYqtin2LHZitiuINin2YTYqti52KfZgtivXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY29udHJhY3RfZGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb250cmFjdF9kYXRlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCBjb250cmFjdF9kYXRlOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXttb2RhbFR5cGUgPT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTEwICR7bW9kYWxUeXBlID09PSAndmlldycgPyAnYmctZ3JheS01MCcgOiAnYmctcGluay01MCBib3JkZXItcGluay0zMDAgZm9jdXM6Ym9yZGVyLXBpbmstNTAwIGZvY3VzOmJnLXdoaXRlIHRyYW5zaXRpb24tY29sb3JzJ30gdGV4dC1zbWB9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDYp9mE2LXZgSDYp9mE2LHYp9io2Lk6INin2YTYudmF2YTYqSDZiNin2YTZhdio2KfZhNi6ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW5jeV9pZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXllbGxvdy03MDAgYmcteWVsbG93LTUwIHB4LTMgcHktMSByb3VuZGVkLW1kIGlubGluZS1ibG9jayBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+SsSDYp9mE2LnZhdmE2KlcbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3VycmVuY3lfaWRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jdXJyZW5jeV9pZCB8fCAnMSd9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIGN1cnJlbmN5X2lkOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTEwIHB4LTMgcHktMiBiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXllbGxvdy01MDAgZm9jdXM6Ym9yZGVyLXllbGxvdy01MDAgZm9jdXM6Ymctd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e21vZGFsVHlwZSA9PT0gJ3ZpZXcnfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbmNpZXMubWFwKGN1cnJlbmN5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjdXJyZW5jeS5pZH0gdmFsdWU9e2N1cnJlbmN5LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2N1cnJlbmN5LmN1cnJlbmN5X25hbWV9ICh7Y3VycmVuY3kuc3ltYm9sfSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2N1cnJlbmN5LmlzX2Jhc2VfY3VycmVuY3kgJiYgJyAtINin2YTYo9iz2KfYs9mK2KknfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNhc2VfYW1vdW50XCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZW1lcmFsZC03MDAgYmctZW1lcmFsZC01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCfkrAg2YLZitmF2Kkg2KfZhNmC2LbZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY2FzZV9hbW91bnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGVwPVwiMC4wMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jYXNlX2Ftb3VudCB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCBjYXNlX2Ftb3VudDogZS50YXJnZXQudmFsdWV9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXttb2RhbFR5cGUgPT09ICd2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtMTAgcHItMTIgJHttb2RhbFR5cGUgPT09ICd2aWV3JyA/ICdiZy1ncmF5LTUwJyA6ICdiZy1lbWVyYWxkLTUwIGJvcmRlci1lbWVyYWxkLTMwMCBmb2N1czpib3JkZXItZW1lcmFsZC01MDAgZm9jdXM6Ymctd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMnfSB0ZXh0LXNtYH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMC4wMFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0wIHByLTMgZmxleCBpdGVtcy1jZW50ZXIgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW5jaWVzLmZpbmQoYyA9PiBjLmlkLnRvU3RyaW5nKCkgPT09IChmb3JtRGF0YS5jdXJyZW5jeV9pZCB8fCAnMScpKT8uc3ltYm9sIHx8ICfYsS7Ziid9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImFtb3VudF95ZXJcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi03MDAgYmctZ3JlZW4tNTAgcHgtMyBweS0xIHJvdW5kZWQtbWQgaW5saW5lLWJsb2NrIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDwn5K1INio2KfZhNix2YrYp9mEINin2YTZitmF2YbZilxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImFtb3VudF95ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e01hdGgucm91bmQoY2FsY3VsYXRlQW1vdW50WWVyKGZvcm1EYXRhLmNhc2VfYW1vdW50IHx8ICcwJywgZm9ybURhdGEuY3VycmVuY3lfaWQgfHwgJzEnKSkudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICByZWFkT25seVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xMCBwci0xMiBiZy1ncmF5LTUwIHRleHQtZ3JheS03MDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0wIHByLTMgZmxleCBpdGVtcy1jZW50ZXIgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+2LEu2Yo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Lyog2KfZhNi12YEg2KfZhNij2K7ZitixOiDYp9mE2YXZhNin2K3YuNin2KogKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5vdGVzXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS03MDAgYmctZ3JheS01MCBweC0zIHB5LTEgcm91bmRlZC1tZCBpbmxpbmUtYmxvY2sgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCfk50g2YXZhNin2K3YuNin2Kog2KXYttin2YHZitipXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwibm90ZXNcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ub3Rlc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgbm90ZXM6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9e21vZGFsVHlwZSA9PT0gJ3ZpZXcnfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBoLTEyIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1tZCByZXNpemUtbm9uZSB0ZXh0LXNtICR7bW9kYWxUeXBlID09PSAndmlldycgPyAnYmctZ3JheS01MCcgOiAnYmctZ3JheS01MCBib3JkZXItZ3JheS0zMDAgZm9jdXM6Ym9yZGVyLWdyYXktNTAwIGZvY3VzOmJnLXdoaXRlIHRyYW5zaXRpb24tY29sb3JzJ31gfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KPYr9iu2YQg2KPZiiDZhdmE2KfYrdi42KfYqiDYpdi22KfZgdmK2KkuLi5cIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINij2LLYsdin2LEg2KfZhNil2KzYsdin2KHYp9iqICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTQgc3BhY2UteC1yZXZlcnNlIHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICB7bW9kYWxUeXBlICE9PSAndmlldycgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIGgtMTIgdGV4dC1sZyBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7bW9kYWxUeXBlID09PSAnYWRkJyA/ICfwn5K+INil2LbYp9mB2Kkg2KfZhNmC2LbZitipJyA6ICfwn5K+INit2YHYuCDYp9mE2KrYutmK2YrYsdin2KonfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHttb2RhbFR5cGUgPT09ICd2aWV3JyA/ICd3LWZ1bGwnIDogJ2ZsZXgtMSd9IGgtMTIgdGV4dC1sZyBmb250LXNlbWlib2xkIGJvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTUwYH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge21vZGFsVHlwZSA9PT0gJ3ZpZXcnID8gJ+KdjCDYpdi62YTYp9mCJyA6ICfwn5qrINil2YTYutin2KEnfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTWFpbkxheW91dD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTWFpbkxheW91dCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiQmFkZ2UiLCJDbGllbnRTZWxlY3QiLCJGaWxlVGV4dCIsIlBsdXMiLCJTZWFyY2giLCJFeWUiLCJFZGl0IiwiVHJhc2gyIiwiVXNlciIsIkJ1aWxkaW5nIiwiU2NhbGUiLCJYIiwiU2F2ZSIsIklzc3Vlc1BhZ2UiLCJjdXJyZW5jaWVzIiwiaXNzdWVzIiwic2V0SXNzdWVzIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzdGF0dXNGaWx0ZXIiLCJzZXRTdGF0dXNGaWx0ZXIiLCJkYkVycm9yIiwic2V0RGJFcnJvciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImlzTW9kYWxPcGVuIiwic2V0SXNNb2RhbE9wZW4iLCJtb2RhbFR5cGUiLCJzZXRNb2RhbFR5cGUiLCJlZGl0aW5nSXNzdWUiLCJzZXRFZGl0aW5nSXNzdWUiLCJjb3VydHMiLCJzZXRDb3VydHMiLCJzZXRDdXJyZW5jaWVzIiwiY2xpZW50cyIsInNldENsaWVudHMiLCJpc3N1ZVR5cGVzIiwic2V0SXNzdWVUeXBlcyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJjYXNlX251bWJlciIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjbGllbnRfaWQiLCJjbGllbnRfbmFtZSIsImNsaWVudF9waG9uZSIsImNvdXJ0X2lkIiwiY291cnRfbmFtZSIsImlzc3VlX3R5cGVfaWQiLCJpc3N1ZV90eXBlIiwic3RhdHVzIiwiY2FzZV9hbW91bnQiLCJjdXJyZW5jeV9pZCIsIm5vdGVzIiwiY29udHJhY3RfbWV0aG9kIiwiY29udHJhY3RfZGF0ZSIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiZmV0Y2hDb3VydHMiLCJyZXNwb25zZSIsImZldGNoIiwicmVzdWx0IiwianNvbiIsInN1Y2Nlc3MiLCJkYXRhIiwiZXJyb3IiLCJjb25zb2xlIiwiZmV0Y2hDdXJyZW5jaWVzIiwiZmV0Y2hDbGllbnRzIiwiZmV0Y2hJc3N1ZVR5cGVzIiwiY2FsY3VsYXRlQW1vdW50WWVyIiwiYW1vdW50IiwiY3VycmVuY3lJZCIsIm51bUFtb3VudCIsInBhcnNlRmxvYXQiLCJjdXJyZW5jeSIsImZpbmQiLCJjIiwiaWQiLCJ0b1N0cmluZyIsImlzX2Jhc2VfY3VycmVuY3kiLCJleGNoYW5nZV9yYXRlIiwiZmV0Y2hJc3N1ZXMiLCJmaWx0ZXJlZElzc3VlcyIsImZpbHRlciIsImlzc3VlIiwibWF0Y2hlc1NlYXJjaCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJtYXRjaGVzU3RhdHVzIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNUZXh0IiwiaGFuZGxlQWRkTmV3IiwiaGFuZGxlQ2xpZW50Q2hhbmdlIiwiY2xpZW50SWQiLCJjbGllbnREYXRhIiwibmFtZSIsInBob25lIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwiZGF0YVRvU3VibWl0IiwiY3JlYXRlZF9ieSIsImxvZyIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImFsZXJ0IiwiaGFuZGxlVmlld0lzc3VlIiwiZm9ybURhdGFUb1NldCIsImhhbmRsZUVkaXRJc3N1ZSIsImhhbmRsZURlbGV0ZUlzc3VlIiwiY29uZmlybSIsInN0YXRzIiwidG90YWwiLCJsZW5ndGgiLCJuZXciLCJpIiwicGVuZGluZyIsImluX3Byb2dyZXNzIiwiY29tcGxldGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwib25DbGljayIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInNlbGVjdCIsIm9wdGlvbiIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJtYXAiLCJ0ZCIsInNwYW4iLCJ2YXJpYW50IiwiTWF0aCIsImZsb29yIiwidG9Mb2NhbGVTdHJpbmciLCJzaXplIiwiaDMiLCJmb3JtIiwib25TdWJtaXQiLCJodG1sRm9yIiwicmVxdWlyZWQiLCJyZWFkT25seSIsInRleHRhcmVhIiwibGFiZWwiLCJkaXNhYmxlZCIsInNlbGVjdGVkQ291cnQiLCJjb3VydCIsImdvdmVybm9yYXRlX25hbWUiLCJ0eXBlIiwiY3VycmVuY3lfbmFtZSIsInN5bWJvbCIsInN0ZXAiLCJtaW4iLCJyb3VuZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});