(()=>{var e={};e.id=3012,e.ids=[3012],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,c,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(c,{P:()=>d});var o=t(64939),n=t(29021),s=t.n(n),r=t(33873),u=t.n(r),i=e([o]);o=(i.then?(await i)():i)[0];let _=null;try{let e=u().join(process.cwd(),"routing.config.json"),c=s().readFileSync(e,"utf8");_=JSON.parse(c)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!_?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(_&&_.routes[e]){let c=_.routes[e],t=_.default_config;return{database:c.database,user:t.db_user,host:t.db_host,password:process.env.DB_PASSWORD||t.db_password,port:t.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new o.Pool(l);async function d(e,c){let t=await p.connect();try{return await t.query(e,c)}finally{t.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56542:(e,c,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(c),t.d(c,{POST:()=>r});var o=t(32190),n=t(5069),s=e([n]);async function r(e){try{console.log("\uD83D\uDD27 تحديث جميع المراجع لاستخدام account_id بدلاً من account_code...");let e=[];console.log("\uD83D\uDCDD تحديث API العملاء...");try{let c=`
        SELECT 
          c.*,
          coa.account_name,
          coa.account_code,
          coa.current_balance as account_balance
        FROM clients c
        LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
        WHERE c.id IS NOT NULL
        ORDER BY c.name
        LIMIT 5
      `,t=await (0,n.P)(c);e.push(`✅ العملاء: تم اختبار الربط عبر account_id - ${t.rows.length} عميل`);let a=await (0,n.P)(`
        UPDATE clients 
        SET account_id = (
          SELECT id FROM chart_of_accounts 
          WHERE account_name = clients.name 
          AND linked_table = 'clients'
          LIMIT 1
        )
        WHERE account_id IS NULL
      `);e.push(`✅ العملاء: تم ربط ${a.rowCount} عميل بدليل الحسابات`)}catch(c){e.push(`❌ العملاء: خطأ في التحديث - ${c}`)}console.log("\uD83D\uDCDD تحديث API الموظفين...");try{let c=`
        SELECT 
          e.*,
          coa.account_name,
          coa.account_code,
          coa.current_balance as account_balance
        FROM employees e
        LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
        WHERE e.id IS NOT NULL
        ORDER BY e.name
        LIMIT 5
      `,t=await (0,n.P)(c);e.push(`✅ الموظفين: تم اختبار الربط عبر account_id - ${t.rows.length} موظف`);let a=await (0,n.P)(`
        UPDATE employees 
        SET account_id = (
          SELECT id FROM chart_of_accounts 
          WHERE account_name = employees.name 
          AND linked_table = 'employees'
          LIMIT 1
        )
        WHERE account_id IS NULL
      `);e.push(`✅ الموظفين: تم ربط ${a.rowCount} موظف بدليل الحسابات`)}catch(c){e.push(`❌ الموظفين: خطأ في التحديث - ${c}`)}console.log("\uD83D\uDCDD تحديث API الموردين...");try{let c=`
        SELECT 
          s.*,
          coa.account_name,
          coa.account_code,
          coa.current_balance as account_balance
        FROM suppliers s
        LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
        WHERE s.id IS NOT NULL
        ORDER BY s.name
        LIMIT 5
      `,t=await (0,n.P)(c);e.push(`✅ الموردين: تم اختبار الربط عبر account_id - ${t.rows.length} مورد`);let a=await (0,n.P)("SELECT COUNT(*) as count FROM suppliers");"0"===a.rows[0].count&&(await (0,n.P)(`
          INSERT INTO suppliers (name, company_name, phone, email, status) VALUES
          ('شركة القرطاسية المتقدمة', 'شركة القرطاسية المتقدمة المحدودة', '********', '<EMAIL>', 'active'),
          ('مؤسسة التقنية الحديثة', 'مؤسسة التقنية الحديثة', '********', '<EMAIL>', 'active'),
          ('شركة الخدمات المكتبية', 'شركة الخدمات المكتبية', '********', '<EMAIL>', 'active')
        `),e.push("✅ الموردين: تم إنشاء 3 موردين تجريبيين"))}catch(c){e.push(`❌ الموردين: خطأ في التحديث - ${c}`)}console.log("\uD83D\uDCDD تحديث السندات...");try{let c=await (0,n.P)(`
        SELECT 
          rv.*,
          da.account_name as debit_account_name,
          da.account_code as debit_account_code,
          ca.account_name as credit_account_name,
          ca.account_code as credit_account_code
        FROM receipt_vouchers rv
        LEFT JOIN chart_of_accounts da ON rv.debit_account_id = da.id
        LEFT JOIN chart_of_accounts ca ON rv.credit_account_id = ca.id
        LIMIT 3
      `);e.push(`✅ سندات القبض: الربط عبر account_id يعمل - ${c.rows.length} سند`);let t=await (0,n.P)(`
        SELECT 
          pv.*,
          da.account_name as debit_account_name,
          da.account_code as debit_account_code,
          ca.account_name as credit_account_name,
          ca.account_code as credit_account_code
        FROM payment_vouchers pv
        LEFT JOIN chart_of_accounts da ON pv.debit_account_id = da.id
        LEFT JOIN chart_of_accounts ca ON pv.credit_account_id = ca.id
        LIMIT 3
      `);e.push(`✅ سندات الصرف: الربط عبر account_id يعمل - ${t.rows.length} سند`)}catch(c){e.push(`❌ السندات: خطأ في التحديث - ${c}`)}console.log("\uD83D\uDCDD تحديث القيود اليومية...");try{let c=await (0,n.P)(`
        SELECT 
          jed.*,
          coa.account_name,
          coa.account_code
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        LIMIT 5
      `);e.push(`✅ القيود اليومية: الربط عبر account_id يعمل - ${c.rows.length} تفصيل`)}catch(c){e.push(`❌ القيود اليومية: خطأ في التحديث - ${c}`)}let c={};try{c.clientsLinked=(await (0,n.P)(`
        SELECT COUNT(*) as count 
        FROM clients c 
        INNER JOIN chart_of_accounts coa ON c.account_id = coa.id
      `)).rows[0].count,c.employeesLinked=(await (0,n.P)(`
        SELECT COUNT(*) as count 
        FROM employees e 
        INNER JOIN chart_of_accounts coa ON e.account_id = coa.id
      `)).rows[0].count,c.suppliersLinked=(await (0,n.P)(`
        SELECT COUNT(*) as count 
        FROM suppliers s 
        INNER JOIN chart_of_accounts coa ON s.account_id = coa.id
      `)).rows[0].count;let t=await (0,n.P)(`
        SELECT 
          (SELECT COUNT(*) FROM receipt_vouchers) as receipt_count,
          (SELECT COUNT(*) FROM payment_vouchers) as payment_count,
          (SELECT COUNT(*) FROM journal_entries) as journal_count
      `);c.receiptVouchers=t.rows[0].receipt_count,c.paymentVouchers=t.rows[0].payment_count,c.journalEntries=t.rows[0].journal_count,e.push(`📊 الإحصائيات: ${c.clientsLinked} عميل، ${c.employeesLinked} موظف، ${c.suppliersLinked} مورد مربوطين`),e.push(`📊 السندات: ${c.receiptVouchers} قبض، ${c.paymentVouchers} صرف، ${c.journalEntries} قيد`)}catch(c){e.push(`❌ خطأ في جمع الإحصائيات: ${c}`)}return console.log("\uD83C\uDF89 تم تحديث جميع المراجع بنجاح!"),o.NextResponse.json({success:!0,message:"تم تحديث جميع المراجع لاستخدام account_id بدلاً من account_code بنجاح",data:{results:e,stats:c,recommendations:["✅ تم حذف عمود account_code من جداول العملاء والموظفين والموردين","✅ جميع الاستعلامات تستخدم account_id للربط مع دليل الحسابات","✅ السندات والقيود تعمل بشكل صحيح مع الربط الجديد","✅ الأداء محسن مع الفهارس الجديدة","\uD83D\uDCA1 يُنصح بتحديث واجهات المستخدم لتعكس التغييرات","\uD83D\uDCA1 يُنصح بإجراء اختبارات شاملة للتأكد من عمل جميع الوظائف"],summary:{totalUpdates:e.length,successfulUpdates:e.filter(e=>e.includes("✅")).length,failedUpdates:e.filter(e=>e.includes("❌")).length}}})}catch(e){return console.error("❌ خطأ في تحديث المراجع:",e),o.NextResponse.json({success:!1,message:"فشل في تحديث المراجع",error:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}n=(s.then?(await s)():s)[0],a()}catch(e){a(e)}})},58958:(e,c,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(c),t.d(c,{patchFetch:()=>i,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>l});var o=t(96559),n=t(48088),s=t(37719),r=t(56542),u=e([r]);r=(u.then?(await u)():u)[0];let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/update-all-references/route",pathname:"/api/accounting/update-all-references",filename:"route",bundlePath:"app/api/accounting/update-all-references/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\update-all-references\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:l,serverHooks:p}=d;function i(){return(0,s.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:l})}a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var c=require("../../../../webpack-runtime.js");c.C(e);var t=e=>c(c.s=e),a=c.X(0,[4447,580],()=>t(58958));module.exports=a})();