(()=>{var e={};e.id=4830,e.ids=[4830],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4698:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>E,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>_});var a=r(96559),n=r(48088),o=r(37719),i=r(99568),c=e([i]);i=(c.then?(await c)():c)[0];let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/journal-entries/route",pathname:"/api/accounting/journal-entries",filename:"route",bundlePath:"app/api/accounting/journal-entries/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\journal-entries\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:l,workUnitAsyncStorage:_,serverHooks:E}=d;function u(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:_})}s()}catch(e){s(e)}})},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>d});var a=r(64939),n=r(29021),o=r.n(n),i=r(33873),c=r.n(i),u=e([a]);a=(u.then?(await u)():u)[0];let l=null;try{let e=c().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let _=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],r=l.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new a.Pool(_);async function d(e,t){let r=await E.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{},99568:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>u,POST:()=>d,PUT:()=>l});var a=r(32190),n=r(5069),o=e([n]);async function i(){await (0,n.P)(`
    CREATE TABLE IF NOT EXISTS journal_entries (
      id SERIAL PRIMARY KEY,
      entry_number VARCHAR(50) UNIQUE NOT NULL,
      entry_date DATE NOT NULL,
      description TEXT NOT NULL,
      total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
      total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
      status VARCHAR(20) DEFAULT 'draft',
      created_by VARCHAR(100) DEFAULT 'النظام',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `),await (0,n.P)(`
    CREATE TABLE IF NOT EXISTS journal_entry_details (
      id SERIAL PRIMARY KEY,
      journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,
      account_id INTEGER,
      account_name VARCHAR(255),
      debit_amount DECIMAL(15,2) DEFAULT 0,
      credit_amount DECIMAL(15,2) DEFAULT 0,
      description TEXT,
      line_order INTEGER DEFAULT 1
    )
  `)}async function c(){let e=await (0,n.P)(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries 
    WHERE entry_number ~ '^JE[0-9]+$'
  `),t=e.rows[0]?.next_number||1;return`JE${String(t).padStart(6,"0")}`}async function u(e){try{await i();let{searchParams:t}=new URL(e.url),r=t.get("status"),s=t.get("date_from"),o=t.get("date_to"),c=`
      SELECT
        je.*,
        COUNT(jed.id) as details_count
      FROM journal_entries je
      LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
      WHERE (je.entry_type = 'journal' OR je.entry_type IS NULL)
    `,u=[],d=1;r&&"all"!==r&&(c+=` AND je.status = $${d}`,u.push(r),d++),s&&(c+=` AND je.entry_date >= $${d}`,u.push(s),d++),o&&(c+=` AND je.entry_date <= $${d}`,u.push(o),d++),c+=" GROUP BY je.id ORDER BY je.entry_date DESC, je.entry_number DESC",console.log("\uD83D\uDD0D SQL Query:",c),console.log("\uD83D\uDD0D Parameters:",u);let l=await (0,n.P)(c,u),_=[];for(let e of l.rows){let t=await (0,n.P)(`
        SELECT
          jed.*,
          COALESCE(coa.account_name, jed.account_name) as account_name,
          coa.account_code
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE jed.journal_entry_id = $1
        ORDER BY jed.line_order
      `,[e.id]);_.push({...e,details:t.rows,total_debit:parseFloat(e.total_debit||0),total_credit:parseFloat(e.total_credit||0)})}return a.NextResponse.json({success:!0,entries:_,total:_.length,message:"تم جلب القيود اليومية بنجاح"})}catch(e){return console.error("❌ خطأ في جلب القيود اليومية:",e),a.NextResponse.json({success:!1,error:"فشل في جلب القيود اليومية",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function d(e){try{await i();let t=await e.json();console.log("\uD83D\uDCE5 بيانات القيد اليومي المستلمة:",t);let{entry_date:r,description:s,details:o=[],status:u="draft"}=t,d=[];if(r||d.push("تاريخ القيد (entry_date)"),s||d.push("وصف القيد (description)"),o&&Array.isArray(o)&&0!==o.length||d.push("تفاصيل القيد (details) - يجب أن تكون مصفوفة تحتوي على سطر واحد على الأقل"),d.length>0)return a.NextResponse.json({success:!1,error:"البيانات المطلوبة مفقودة",details:`البيانات المفقودة: ${d.join(", ")}`,missingFields:d,receivedData:t},{status:400});let l=[];if(o.forEach((e,t)=>{let r=[];e.account_id||r.push("معرف الحساب (account_id)"),void 0===e.debit_amount&&void 0===e.credit_amount&&r.push("يجب تحديد مبلغ مدين أو دائن"),(e.debit_amount<0||e.credit_amount<0)&&r.push("المبالغ يجب أن تكون موجبة"),r.length>0&&l.push(`السطر ${t+1}: ${r.join(", ")}`)}),l.length>0)return a.NextResponse.json({success:!1,error:"أخطاء في تفاصيل القيد",details:l.join(" | "),detailErrors:l,receivedData:t},{status:400});let _=0,E=0;for(let e of o)_+=parseFloat(e.debit_amount||0),E+=parseFloat(e.credit_amount||0);if(Math.abs(_-E)>.01)return a.NextResponse.json({success:!1,error:"القيد غير متوازن",details:`إجمالي المدين (${_}) لا يساوي إجمالي الدائن (${E})`},{status:400});let p=await c(),R=(await (0,n.P)(`
      INSERT INTO journal_entries (
        entry_number, entry_date, description, 
        total_debit, total_credit, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `,[p,r,s,_,E,u,"النظام"])).rows[0];for(let e=0;e<o.length;e++){let t=o[e],r=t.account_name,s=t.account_code;if((!r||!s)&&t.account_id){let e=await (0,n.P)("SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1",[t.account_id]);e.rows[0]&&(r=r||e.rows[0].account_name||`حساب رقم ${t.account_id}`,s=s||e.rows[0].account_code)}await (0,n.P)(`
        INSERT INTO journal_entry_details (
          journal_entry_id, account_id, account_name, account_code,
          debit_amount, credit_amount, description, line_order
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `,[R.id,t.account_id,r,s,t.debit_amount||0,t.credit_amount||0,t.description,e+1])}return a.NextResponse.json({success:!0,entry:R,message:`تم إنشاء القيد اليومي ${p} بنجاح`})}catch(e){return console.error("❌ خطأ في إنشاء القيد اليومي:",e),a.NextResponse.json({success:!1,error:"فشل في إنشاء القيد اليومي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function l(e){try{let{id:t,...r}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف القيد مطلوب"},{status:400});let s=await (0,n.P)(`
      UPDATE journal_entries 
      SET 
        entry_date = COALESCE($2, entry_date),
        description = COALESCE($3, description),
        status = COALESCE($4, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[t,r.entry_date,r.description,r.status]);if(0===s.rows.length)return a.NextResponse.json({success:!1,error:"القيد غير موجود"},{status:404});return a.NextResponse.json({success:!0,entry:s.rows[0],message:"تم تحديث القيد اليومي بنجاح"})}catch(e){return console.error("❌ خطأ في تحديث القيد اليومي:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث القيد اليومي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}n=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(4698));module.exports=s})();