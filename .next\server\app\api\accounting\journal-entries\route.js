/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/accounting/journal-entries/route";
exports.ids = ["app/api/accounting/journal-entries/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fjournal-entries%2Froute&page=%2Fapi%2Faccounting%2Fjournal-entries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fjournal-entries%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fjournal-entries%2Froute&page=%2Fapi%2Faccounting%2Fjournal-entries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fjournal-entries%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_accounting_journal_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/accounting/journal-entries/route.ts */ \"(rsc)/./src/app/api/accounting/journal-entries/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_accounting_journal_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_accounting_journal_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/accounting/journal-entries/route\",\n        pathname: \"/api/accounting/journal-entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/accounting/journal-entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\accounting\\\\journal-entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_accounting_journal_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fjournal-entries%2Froute&page=%2Fapi%2Faccounting%2Fjournal-entries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fjournal-entries%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/accounting/journal-entries/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/accounting/journal-entries/route.ts ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// إنشاء جدول القيود اليومية إذا لم يكن موجوداً\nasync function ensureJournalEntriesTable() {\n    await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    CREATE TABLE IF NOT EXISTS journal_entries (\n      id SERIAL PRIMARY KEY,\n      entry_number VARCHAR(50) UNIQUE NOT NULL,\n      entry_date DATE NOT NULL,\n      description TEXT NOT NULL,\n      total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,\n      total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,\n      status VARCHAR(20) DEFAULT 'draft',\n      created_by VARCHAR(100) DEFAULT 'النظام',\n      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    CREATE TABLE IF NOT EXISTS journal_entry_details (\n      id SERIAL PRIMARY KEY,\n      journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,\n      account_id INTEGER,\n      account_name VARCHAR(255),\n      debit_amount DECIMAL(15,2) DEFAULT 0,\n      credit_amount DECIMAL(15,2) DEFAULT 0,\n      description TEXT,\n      line_order INTEGER DEFAULT 1\n    )\n  `);\n}\n// توليد رقم قيد جديد\nasync function generateEntryNumber() {\n    const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number\n    FROM journal_entries \n    WHERE entry_number ~ '^JE[0-9]+$'\n  `);\n    const nextNumber = result.rows[0]?.next_number || 1;\n    return `JE${String(nextNumber).padStart(6, '0')}`;\n}\n// GET - جلب جميع القيود اليومية\nasync function GET(request) {\n    try {\n        await ensureJournalEntriesTable();\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const dateFrom = searchParams.get('date_from');\n        const dateTo = searchParams.get('date_to');\n        let sql = `\n      SELECT\n        je.*,\n        COUNT(jed.id) as details_count\n      FROM journal_entries je\n      LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id\n      WHERE (je.entry_type = 'journal' OR je.entry_type IS NULL)\n    `;\n        const params = [];\n        let paramIndex = 1;\n        // تصفية حسب الحالة\n        if (status && status !== 'all') {\n            sql += ` AND je.status = $${paramIndex}`;\n            params.push(status);\n            paramIndex++;\n        }\n        // تصفية حسب التاريخ\n        if (dateFrom) {\n            sql += ` AND je.entry_date >= $${paramIndex}`;\n            params.push(dateFrom);\n            paramIndex++;\n        }\n        if (dateTo) {\n            sql += ` AND je.entry_date <= $${paramIndex}`;\n            params.push(dateTo);\n            paramIndex++;\n        }\n        sql += ` GROUP BY je.id ORDER BY je.entry_date DESC, je.entry_number DESC`;\n        console.log('🔍 SQL Query:', sql);\n        console.log('🔍 Parameters:', params);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(sql, params);\n        // جلب تفاصيل كل قيد\n        const entries = [];\n        for (const entry of result.rows){\n            const detailsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        SELECT\n          jed.*,\n          COALESCE(coa.account_name, jed.account_name) as account_name,\n          coa.account_code\n        FROM journal_entry_details jed\n        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id\n        WHERE jed.journal_entry_id = $1\n        ORDER BY jed.line_order\n      `, [\n                entry.id\n            ]);\n            entries.push({\n                ...entry,\n                details: detailsResult.rows,\n                total_debit: parseFloat(entry.total_debit || 0),\n                total_credit: parseFloat(entry.total_credit || 0)\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            entries,\n            total: entries.length,\n            message: 'تم جلب القيود اليومية بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب القيود اليومية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب القيود اليومية',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة قيد يومي جديد\nasync function POST(request) {\n    try {\n        await ensureJournalEntriesTable();\n        const body = await request.json();\n        console.log('📥 بيانات القيد اليومي المستلمة:', body);\n        const { entry_date, description, details = [], status = 'draft' } = body;\n        // التحقق من البيانات المطلوبة\n        const missingFields = [];\n        if (!entry_date) missingFields.push('تاريخ القيد (entry_date)');\n        if (!description) missingFields.push('وصف القيد (description)');\n        if (!details || !Array.isArray(details) || details.length === 0) {\n            missingFields.push('تفاصيل القيد (details) - يجب أن تكون مصفوفة تحتوي على سطر واحد على الأقل');\n        }\n        if (missingFields.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات المطلوبة مفقودة',\n                details: `البيانات المفقودة: ${missingFields.join(', ')}`,\n                missingFields: missingFields,\n                receivedData: body\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من تفاصيل كل سطر\n        const detailErrors = [];\n        details.forEach((detail, index)=>{\n            const lineErrors = [];\n            if (!detail.account_id) lineErrors.push('معرف الحساب (account_id)');\n            if (detail.debit_amount === undefined && detail.credit_amount === undefined) {\n                lineErrors.push('يجب تحديد مبلغ مدين أو دائن');\n            }\n            if (detail.debit_amount < 0 || detail.credit_amount < 0) {\n                lineErrors.push('المبالغ يجب أن تكون موجبة');\n            }\n            if (lineErrors.length > 0) {\n                detailErrors.push(`السطر ${index + 1}: ${lineErrors.join(', ')}`);\n            }\n        });\n        if (detailErrors.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'أخطاء في تفاصيل القيد',\n                details: detailErrors.join(' | '),\n                detailErrors: detailErrors,\n                receivedData: body\n            }, {\n                status: 400\n            });\n        }\n        // حساب إجمالي المدين والدائن\n        let total_debit = 0;\n        let total_credit = 0;\n        for (const detail of details){\n            total_debit += parseFloat(detail.debit_amount || 0);\n            total_credit += parseFloat(detail.credit_amount || 0);\n        }\n        // التحقق من توازن القيد\n        if (Math.abs(total_debit - total_credit) > 0.01) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القيد غير متوازن',\n                details: `إجمالي المدين (${total_debit}) لا يساوي إجمالي الدائن (${total_credit})`\n            }, {\n                status: 400\n            });\n        }\n        // توليد رقم قيد جديد\n        const entry_number = await generateEntryNumber();\n        // إدراج القيد الرئيسي\n        const entryResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO journal_entries (\n        entry_number, entry_date, description, \n        total_debit, total_credit, status, created_by\n      ) VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    `, [\n            entry_number,\n            entry_date,\n            description,\n            total_debit,\n            total_credit,\n            status,\n            'النظام'\n        ]);\n        const newEntry = entryResult.rows[0];\n        // إدراج تفاصيل القيد\n        for(let i = 0; i < details.length; i++){\n            const detail = details[i];\n            // جلب اسم ورمز الحساب من دليل الحسابات\n            let accountName = detail.account_name;\n            let accountCode = detail.account_code;\n            if ((!accountName || !accountCode) && detail.account_id) {\n                const accountResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1', [\n                    detail.account_id\n                ]);\n                if (accountResult.rows[0]) {\n                    accountName = accountName || accountResult.rows[0].account_name || `حساب رقم ${detail.account_id}`;\n                    accountCode = accountCode || accountResult.rows[0].account_code;\n                }\n            }\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        INSERT INTO journal_entry_details (\n          journal_entry_id, account_id, account_name, account_code,\n          debit_amount, credit_amount, description, line_order\n        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n      `, [\n                newEntry.id,\n                detail.account_id,\n                accountName,\n                accountCode,\n                detail.debit_amount || 0,\n                detail.credit_amount || 0,\n                detail.description,\n                i + 1\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            entry: newEntry,\n            message: `تم إنشاء القيد اليومي ${entry_number} بنجاح`\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إنشاء القيد اليومي:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء القيد اليومي',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث قيد يومي\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, ...updateData } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف القيد مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث القيد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE journal_entries \n      SET \n        entry_date = COALESCE($2, entry_date),\n        description = COALESCE($3, description),\n        status = COALESCE($4, status),\n        updated_at = CURRENT_TIMESTAMP\n      WHERE id = $1\n      RETURNING *\n    `, [\n            id,\n            updateData.entry_date,\n            updateData.description,\n            updateData.status\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القيد غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            entry: result.rows[0],\n            message: 'تم تحديث القيد اليومي بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تحديث القيد اليومي:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث القيد اليومي',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/accounting/journal-entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Faccounting%2Fjournal-entries%2Froute&page=%2Fapi%2Faccounting%2Fjournal-entries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccounting%2Fjournal-entries%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();