/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/conversations/route";
exports.ids = ["app/api/chat/conversations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/conversations/route.ts */ \"(rsc)/./src/app/api/chat/conversations/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/conversations/route\",\n        pathname: \"/api/chat/conversations\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/conversations/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\conversations\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/conversations/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/conversations/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب المحادثات\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userType = searchParams.get('userType');\n        const userId = searchParams.get('userId');\n        if (!userType || !userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نوع المستخدم ومعرف المستخدم مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        let conversationsQuery = '';\n        let queryParams = [];\n        if (userType === 'user') {\n            conversationsQuery = `\n        SELECT \n          c.*,\n          cl.name as client_name,\n          u.username as user_name,\n          (\n            SELECT COUNT(*) \n            FROM messages m \n            WHERE m.conversation_id = c.id \n            AND m.sender_type = 'client' \n            AND m.is_read = false\n          ) as unread_count\n        FROM conversations c\n        LEFT JOIN clients cl ON c.client_id = cl.id\n        LEFT JOIN users u ON c.user_id = u.id\n        WHERE c.user_id = $1\n        ORDER BY c.last_message_at DESC\n      `;\n            queryParams = [\n                userId\n            ];\n        } else {\n            conversationsQuery = `\n        SELECT \n          c.*,\n          cl.name as client_name,\n          u.username as user_name,\n          (\n            SELECT COUNT(*) \n            FROM messages m \n            WHERE m.conversation_id = c.id \n            AND m.sender_type = 'user' \n            AND m.is_read = false\n          ) as unread_count\n        FROM conversations c\n        LEFT JOIN clients cl ON c.client_id = cl.id\n        LEFT JOIN users u ON c.user_id = u.id\n        WHERE c.client_id = $1\n        ORDER BY c.last_message_at DESC\n      `;\n            queryParams = [\n                userId\n            ];\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(conversationsQuery, queryParams);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('Error fetching conversations:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب المحادثات'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إنشاء محادثة جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientId, userId, title } = body;\n        if (!clientId || !userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف العميل ومعرف المستخدم مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود محادثة بين نفس العميل والمستخدم\n        const existingConversation = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT id FROM conversations \n      WHERE client_id = $1 AND user_id = $2\n    `, [\n            clientId,\n            userId\n        ]);\n        if (existingConversation.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: existingConversation.rows[0],\n                message: 'المحادثة موجودة مسبقاً'\n            });\n        }\n        // إنشاء محادثة جديدة\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO conversations (client_id, user_id, title, status)\n      VALUES ($1, $2, $3, 'active')\n      RETURNING *\n    `, [\n            clientId,\n            userId,\n            title || 'محادثة جديدة'\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم إنشاء المحادثة بنجاح'\n        });\n    } catch (error) {\n        console.error('Error creating conversation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء المحادثة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث محادثة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, title, status } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المحادثة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE conversations \n      SET title = COALESCE($2, title),\n          status = COALESCE($3, status),\n          updated_at = CURRENT_TIMESTAMP\n      WHERE id = $1\n      RETURNING *\n    `, [\n            id,\n            title,\n            status\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المحادثة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث المحادثة بنجاح'\n        });\n    } catch (error) {\n        console.error('Error updating conversation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث المحادثة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف محادثة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المحادثة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // حذف المحادثة (سيتم حذف الرسائل تلقائياً بسبب CASCADE)\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM conversations \n      WHERE id = $1\n      RETURNING *\n    `, [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المحادثة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المحادثة بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting conversation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المحادثة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/conversations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg?ce08\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg?ce08":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();