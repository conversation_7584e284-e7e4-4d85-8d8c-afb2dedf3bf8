/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/legal-library/route";
exports.ids = ["app/api/legal-library/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_legal_library_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/legal-library/route.ts */ \"(rsc)/./src/app/api/legal-library/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/legal-library/route\",\n        pathname: \"/api/legal-library\",\n        filename: \"route\",\n        bundlePath: \"app/api/legal-library/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\legal-library\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_legal_library_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/legal-library/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/legal-library/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst LAWS_DIR = '/home/<USER>/Downloads/legal-system/laws';\n// GET - جلب جميع الملفات القانونية\nasync function GET() {\n    try {\n        const files = await getAllLegalFiles(LAWS_DIR);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: files\n        });\n    } catch (error) {\n        console.error('Error fetching legal files:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الملفات القانونية'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getAllLegalFiles(dir) {\n    const files = [];\n    try {\n        const items = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readdir(dir, {\n            withFileTypes: true\n        });\n        for (const item of items){\n            const fullPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(dir, item.name);\n            if (item.isDirectory()) {\n                // إضافة الملفات من المجلدات الفرعية\n                const subFiles = await getAllLegalFiles(fullPath);\n                files.push(...subFiles);\n            } else if (item.isFile() && (item.name.endsWith('.txt') || item.name.endsWith('.pdf'))) {\n                // تنظيف اسم الملف\n                let cleanName = item.name.replace(/^agoyemen\\.net_\\d+_/, '') // إزالة البادئة\n                .replace(/\\.txt$|\\.pdf$/, '') // إزالة الامتداد\n                .replace(/-/g, ' ') // استبدال الشرطات بمسافات\n                .replace(/_/g, ' ') // استبدال الشرطات السفلية بمسافات\n                ;\n                // تحديد الفئة بناءً على المسار\n                let category = 'عام';\n                if (fullPath.includes('الدستور')) category = 'الدستور';\n                else if (fullPath.includes('القوانين')) category = 'القوانين';\n                else if (fullPath.includes('اللوائح')) category = 'اللوائح';\n                else if (fullPath.includes('الاتفاقيات')) category = 'الاتفاقيات';\n                else if (fullPath.includes('التشريعات')) category = 'التشريعات';\n                else if (fullPath.includes('المواثيق')) category = 'المواثيق والإعلانات';\n                else if (fullPath.includes('الأنظمة')) category = 'الأنظمة';\n                else if (fullPath.includes('نصوص عقابية')) category = 'النصوص العقابية';\n                files.push({\n                    id: Buffer.from(fullPath).toString('base64'),\n                    name: cleanName,\n                    originalName: item.name,\n                    path: fullPath,\n                    category,\n                    type: item.name.endsWith('.pdf') ? 'pdf' : 'txt',\n                    size: (await fs__WEBPACK_IMPORTED_MODULE_1__.promises.stat(fullPath)).size\n                });\n            }\n        }\n    } catch (error) {\n        console.error(`Error reading directory ${dir}:`, error);\n    }\n    return files.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n}\n// POST - إضافة ملف قانوني جديد\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const category = formData.get('category') || 'عام';\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لم يتم تحديد ملف'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من نوع الملف\n        if (!file.name.endsWith('.txt') && !file.name.endsWith('.pdf')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نوع الملف غير مدعوم. يجب أن يكون txt أو pdf'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد المجلد المناسب\n        let targetDir = LAWS_DIR;\n        if (category !== 'عام') {\n            targetDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(LAWS_DIR, category);\n            // إنشاء المجلد إذا لم يكن موجوداً\n            try {\n                await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(targetDir);\n            } catch  {\n                await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(targetDir, {\n                    recursive: true\n                });\n            }\n        }\n        // حفظ الملف\n        const buffer = Buffer.from(await file.arrayBuffer());\n        const filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(targetDir, file.name);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(filePath, buffer);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم رفع الملف بنجاح',\n            data: {\n                name: file.name,\n                path: filePath,\n                category,\n                size: buffer.length\n            }\n        });\n    } catch (error) {\n        console.error('Error uploading file:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في رفع الملف'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/legal-library/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();