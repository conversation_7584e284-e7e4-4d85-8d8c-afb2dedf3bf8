/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/legal-library/route";
exports.ids = ["app/api/legal-library/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_legal_library_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/legal-library/route.ts */ \"(rsc)/./src/app/api/legal-library/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/legal-library/route\",\n        pathname: \"/api/legal-library\",\n        filename: \"route\",\n        bundlePath: \"app/api/legal-library/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\legal-library\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_legal_library_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/legal-library/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/legal-library/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// مسار مجلد القوانين\nconst LAWS_DIR = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'laws');\n// GET - جلب جميع الملفات القانونية\nasync function GET() {\n    try {\n        const files = await getAllLegalFiles(LAWS_DIR);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: files\n        });\n    } catch (error) {\n        console.error('Error fetching legal files:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الملفات القانونية'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getAllLegalFiles(dir) {\n    const files = [];\n    try {\n        const items = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readdir(dir, {\n            withFileTypes: true\n        });\n        for (const item of items){\n            const fullPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(dir, item.name);\n            if (item.isDirectory()) {\n                // إضافة الملفات من المجلدات الفرعية\n                const subFiles = await getAllLegalFiles(fullPath);\n                files.push(...subFiles);\n            } else if (item.isFile() && (item.name.endsWith('.txt') || item.name.endsWith('.pdf') || item.name.endsWith('.doc') || item.name.endsWith('.docx'))) {\n                // تنظيف اسم الملف\n                let cleanName = item.name.replace(/^agoyemen\\.net_\\d+_/, '') // إزالة البادئة\n                .replace(/\\.(txt|pdf|doc|docx)$/i, '') // إزالة الامتداد\n                .replace(/-/g, ' ') // استبدال الشرطات بمسافات\n                .replace(/_/g, ' ') // استبدال الشرطات السفلية بمسافات\n                ;\n                // تحديد الفئة بناءً على محتوى اسم الملف\n                let category = 'عام';\n                const fileName = cleanName.toLowerCase();\n                if (fileName.includes('دستور')) {\n                    category = 'الدستور';\n                } else if (fileName.includes('جمارك') || fileName.includes('ضرائب') || fileName.includes('زكاة')) {\n                    category = 'مالي وضريبي';\n                } else if (fileName.includes('عمل') || fileName.includes('نقابات')) {\n                    category = 'عمل ونقابات';\n                } else if (fileName.includes('تجاري') || fileName.includes('شركات') || fileName.includes('غرف تجارية')) {\n                    category = 'تجاري';\n                } else if (fileName.includes('جرائم') || fileName.includes('عقوبات') || fileName.includes('عسكرية')) {\n                    category = 'جنائي';\n                } else if (fileName.includes('طفل') || fileName.includes('حقوق')) {\n                    category = 'حقوق الإنسان';\n                } else if (fileName.includes('ملكية') || fileName.includes('فكري')) {\n                    category = 'ملكية فكرية';\n                } else if (fileName.includes('بنوك') || fileName.includes('مالي')) {\n                    category = 'مصرفي ومالي';\n                } else if (fileName.includes('صحافة') || fileName.includes('مطبوعات')) {\n                    category = 'إعلام وصحافة';\n                } else if (fileName.includes('مياه') || fileName.includes('بيئة')) {\n                    category = 'بيئة وموارد';\n                } else if (fileName.includes('تحكيم')) {\n                    category = 'تحكيم ومنازعات';\n                } else if (fileName.includes('لائحة') || fileName.includes('تنفيذية')) {\n                    category = 'لوائح تنفيذية';\n                }\n                const stats = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.stat(fullPath);\n                const ext = path__WEBPACK_IMPORTED_MODULE_2___default().extname(item.name).toLowerCase();\n                files.push({\n                    id: Buffer.from(fullPath).toString('base64'),\n                    name: cleanName,\n                    originalName: item.name,\n                    fileName: item.name,\n                    path: fullPath,\n                    category,\n                    type: ext.substring(1).toUpperCase(),\n                    fileType: ext.substring(1).toUpperCase(),\n                    size: stats.size,\n                    sizeFormatted: formatFileSize(stats.size),\n                    lastModified: stats.mtime.toISOString(),\n                    downloadUrl: `/api/legal-library/download?file=${encodeURIComponent(item.name)}`\n                });\n            }\n        }\n    } catch (error) {\n        console.error(`Error reading directory ${dir}:`, error);\n    }\n    return files.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n}\n// POST - إضافة ملف قانوني جديد\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const category = formData.get('category') || 'عام';\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لم يتم تحديد ملف'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من نوع الملف\n        if (!file.name.endsWith('.txt') && !file.name.endsWith('.pdf')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نوع الملف غير مدعوم. يجب أن يكون txt أو pdf'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد المجلد المناسب\n        let targetDir = LAWS_DIR;\n        if (category !== 'عام') {\n            targetDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(LAWS_DIR, category);\n            // إنشاء المجلد إذا لم يكن موجوداً\n            try {\n                await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(targetDir);\n            } catch  {\n                await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(targetDir, {\n                    recursive: true\n                });\n            }\n        }\n        // حفظ الملف\n        const buffer = Buffer.from(await file.arrayBuffer());\n        const filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(targetDir, file.name);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(filePath, buffer);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم رفع الملف بنجاح',\n            data: {\n                name: file.name,\n                path: filePath,\n                category,\n                size: buffer.length\n            }\n        });\n    } catch (error) {\n        console.error('Error uploading file:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في رفع الملف'\n        }, {\n            status: 500\n        });\n    }\n}\n// دالة لتنسيق حجم الملف\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/legal-library/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Froute&page=%2Fapi%2Flegal-library%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();