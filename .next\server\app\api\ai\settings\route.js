(()=>{var e={};e.id=2716,e.ids=[2716],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23604:(e,r,s)=>{"use strict";s.a(e,async(e,o)=>{try{s.r(r),s.d(r,{GET:()=>i,POST:()=>_});var t=s(32190),n=s(64939),a=e([n]);if(n=(a.then?(await a)():a)[0],!process.env.DB_PASSWORD)throw Error("DB_PASSWORD environment variable is required");let d=new n.Pool({user:"postgres",host:"localhost",database:"mohammi",password:process.env.DB_PASSWORD||"your_password_here",port:5432});async function i(){try{let e=await d.connect();try{let r,s=await e.query("SELECT * FROM ai_settings WHERE id = 1");if(s.rows.length>0){let e=s.rows[0];r={enabled:e.enabled,model:e.model,delay_seconds:e.delay_seconds,working_hours_only:e.working_hours_only,working_hours_start:e.working_hours_start,working_hours_end:e.working_hours_end,working_days:e.working_days,max_responses_per_conversation:e.max_responses_per_conversation,keywords_trigger:e.keywords_trigger,excluded_keywords:e.excluded_keywords,auto_responses:e.auto_responses}}else r={enabled:!0,model:"groq-llama-8b",delay_seconds:3,working_hours_only:!1,working_hours_start:"00:00",working_hours_end:"23:59",working_days:["الأحد","الاثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"],max_responses_per_conversation:20,keywords_trigger:["مساعدة","استفسار","سؤال","معلومات","خدمة","مرحبا","السلام","أهلا","استشارة","قانوني","محامي"],excluded_keywords:[],auto_responses:{greeting:"مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية!",help:"يمكنني مساعدتك في الاستشارات القانونية.",disclaimer:"للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.",signature:""}},await e.query(`
          INSERT INTO ai_settings (
            id, enabled, model, delay_seconds, working_hours_only,
            working_hours_start, working_hours_end, working_days,
            max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses
          ) VALUES (
            1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
          )
        `,[r.enabled,r.model,r.delay_seconds,r.working_hours_only,r.working_hours_start,r.working_hours_end,r.working_days,r.max_responses_per_conversation,r.keywords_trigger,r.excluded_keywords,r.auto_responses]);return t.NextResponse.json({success:!0,data:r})}finally{e.release()}}catch(e){return console.error("Error fetching AI settings:",e),t.NextResponse.json({success:!1,error:"فشل في جلب إعدادات الذكاء الاصطناعي"},{status:500})}}async function _(e){try{let r=await e.json(),s=await d.connect();try{return await s.query(`
        INSERT INTO ai_settings (
          id, enabled, model, delay_seconds, working_hours_only,
          working_hours_start, working_hours_end, working_days,
          max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses,
          updated_at
        ) VALUES (
          1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
          enabled = $1,
          model = $2,
          delay_seconds = $3,
          working_hours_only = $4,
          working_hours_start = $5,
          working_hours_end = $6,
          working_days = $7,
          max_responses_per_conversation = $8,
          keywords_trigger = $9,
          excluded_keywords = $10,
          auto_responses = $11,
          updated_at = NOW()
      `,[r.enabled,r.model,r.delay_seconds,r.working_hours_only,r.working_hours_start,r.working_hours_end,r.working_days,r.max_responses_per_conversation,r.keywords_trigger,r.excluded_keywords,r.auto_responses]),t.NextResponse.json({success:!0,message:"تم حفظ إعدادات الذكاء الاصطناعي بنجاح"})}finally{s.release()}}catch(e){return console.error("Error saving AI settings:",e),t.NextResponse.json({success:!1,error:"فشل في حفظ إعدادات الذكاء الاصطناعي"},{status:500})}}o()}catch(e){o(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},77540:(e,r,s)=>{"use strict";s.a(e,async(e,o)=>{try{s.r(r),s.d(r,{patchFetch:()=>d,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var t=s(96559),n=s(48088),a=s(37719),i=s(23604),_=e([i]);i=(_.then?(await _)():_)[0];let u=new t.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/settings/route",pathname:"/api/ai/settings",filename:"route",bundlePath:"app/api/ai/settings/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\ai\\settings\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:p}=u;function d(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}o()}catch(e){o(e)}})},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[4447,580],()=>s(77540));module.exports=o})();