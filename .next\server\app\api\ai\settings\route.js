/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/settings/route";
exports.ids = ["app/api/ai/settings/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fsettings%2Froute&page=%2Fapi%2Fai%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fsettings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fsettings%2Froute&page=%2Fapi%2Fai%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fsettings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/settings/route.ts */ \"(rsc)/./src/app/api/ai/settings/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_ai_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_ai_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/settings/route\",\n        pathname: \"/api/ai/settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/settings/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fsettings%2Froute&page=%2Fapi%2Fai%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fsettings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/settings/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/ai/settings/route.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pg */ \"pg?ce08\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_1__]);\npg__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// إعداد قاعدة البيانات\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_1__.Pool({\n    user: 'postgres',\n    host: 'localhost',\n    database: 'mohammi',\n    password: process.env.DB_PASSWORD || 'your_password_here',\n    port: 5432\n});\n// GET - جلب إعدادات الذكاء الاصطناعي\nasync function GET() {\n    try {\n        const client = await pool.connect();\n        try {\n            // البحث عن الإعدادات في قاعدة البيانات\n            const result = await client.query('SELECT * FROM ai_settings WHERE id = 1');\n            let settings;\n            if (result.rows.length > 0) {\n                // إعدادات موجودة في قاعدة البيانات\n                const row = result.rows[0];\n                settings = {\n                    enabled: row.enabled,\n                    model: row.model,\n                    delay_seconds: row.delay_seconds,\n                    working_hours_only: row.working_hours_only,\n                    working_hours_start: row.working_hours_start,\n                    working_hours_end: row.working_hours_end,\n                    working_days: row.working_days,\n                    max_responses_per_conversation: row.max_responses_per_conversation,\n                    keywords_trigger: row.keywords_trigger,\n                    excluded_keywords: row.excluded_keywords,\n                    auto_responses: row.auto_responses\n                };\n            } else {\n                // إعدادات افتراضية - يعمل 24 ساعة عند التفعيل\n                settings = {\n                    enabled: true,\n                    model: 'groq-llama-8b',\n                    delay_seconds: 3,\n                    working_hours_only: false,\n                    working_hours_start: '00:00',\n                    working_hours_end: '23:59',\n                    working_days: [\n                        'الأحد',\n                        'الاثنين',\n                        'الثلاثاء',\n                        'الأربعاء',\n                        'الخميس',\n                        'الجمعة',\n                        'السبت'\n                    ],\n                    max_responses_per_conversation: 20,\n                    keywords_trigger: [\n                        'مساعدة',\n                        'استفسار',\n                        'سؤال',\n                        'معلومات',\n                        'خدمة',\n                        'مرحبا',\n                        'السلام',\n                        'أهلا',\n                        'استشارة',\n                        'قانوني',\n                        'محامي'\n                    ],\n                    excluded_keywords: [],\n                    auto_responses: {\n                        greeting: 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية!',\n                        help: 'يمكنني مساعدتك في الاستشارات القانونية.',\n                        disclaimer: 'للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.',\n                        signature: ''\n                    }\n                };\n                // حفظ الإعدادات الافتراضية في قاعدة البيانات\n                await client.query(`\n          INSERT INTO ai_settings (\n            id, enabled, model, delay_seconds, working_hours_only,\n            working_hours_start, working_hours_end, working_days,\n            max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses\n          ) VALUES (\n            1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11\n          )\n        `, [\n                    settings.enabled,\n                    settings.model,\n                    settings.delay_seconds,\n                    settings.working_hours_only,\n                    settings.working_hours_start,\n                    settings.working_hours_end,\n                    settings.working_days,\n                    settings.max_responses_per_conversation,\n                    settings.keywords_trigger,\n                    settings.excluded_keywords,\n                    settings.auto_responses\n                ]);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: settings\n            });\n        } finally{\n            client.release();\n        }\n    } catch (error) {\n        console.error('Error fetching AI settings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب إعدادات الذكاء الاصطناعي'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - حفظ إعدادات الذكاء الاصطناعي\nasync function POST(request) {\n    try {\n        const settings = await request.json();\n        const client = await pool.connect();\n        try {\n            // تحديث أو إدراج الإعدادات\n            await client.query(`\n        INSERT INTO ai_settings (\n          id, enabled, model, delay_seconds, working_hours_only,\n          working_hours_start, working_hours_end, working_days,\n          max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses,\n          updated_at\n        ) VALUES (\n          1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW()\n        )\n        ON CONFLICT (id) DO UPDATE SET\n          enabled = $1,\n          model = $2,\n          delay_seconds = $3,\n          working_hours_only = $4,\n          working_hours_start = $5,\n          working_hours_end = $6,\n          working_days = $7,\n          max_responses_per_conversation = $8,\n          keywords_trigger = $9,\n          excluded_keywords = $10,\n          auto_responses = $11,\n          updated_at = NOW()\n      `, [\n                settings.enabled,\n                settings.model,\n                settings.delay_seconds,\n                settings.working_hours_only,\n                settings.working_hours_start,\n                settings.working_hours_end,\n                settings.working_days,\n                settings.max_responses_per_conversation,\n                settings.keywords_trigger,\n                settings.excluded_keywords,\n                settings.auto_responses\n            ]);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح'\n            });\n        } finally{\n            client.release();\n        }\n    } catch (error) {\n        console.error('Error saving AI settings:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حفظ إعدادات الذكاء الاصطناعي'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/settings/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg?ce08":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fsettings%2Froute&page=%2Fapi%2Fai%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fsettings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();