"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 h-96 shadow-2xl border-0 transition-all duration-300 \".concat(isMinimized ? 'h-14' : 'h-96'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3 rounded-t-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-white/20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsOpen(false),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-2 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 \".concat(message.type === 'user' ? 'text-blue-100' : 'text-gray-500'),\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            size: \"sm\",\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});