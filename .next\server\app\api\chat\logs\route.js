/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/logs/route";
exports.ids = ["app/api/chat/logs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Flogs%2Froute&page=%2Fapi%2Fchat%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Flogs%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Flogs%2Froute&page=%2Fapi%2Fchat%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Flogs%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/logs/route.ts */ \"(rsc)/./src/app/api/chat/logs/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_chat_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_chat_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/logs/route\",\n        pathname: \"/api/chat/logs\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/logs/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\logs\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Flogs%2Froute&page=%2Fapi%2Fchat%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Flogs%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/logs/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/chat/logs/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب سجلات المحادثة\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const sessionId = searchParams.get('session_id');\n        const responseType = searchParams.get('response_type');\n        const startDate = searchParams.get('start_date');\n        const endDate = searchParams.get('end_date');\n        const offset = (page - 1) * limit;\n        // بناء الاستعلام\n        let whereConditions = [];\n        let queryParams = [];\n        let paramIndex = 1;\n        if (sessionId) {\n            whereConditions.push(`session_id = $${paramIndex}`);\n            queryParams.push(sessionId);\n            paramIndex++;\n        }\n        if (responseType) {\n            whereConditions.push(`response_type = $${paramIndex}`);\n            queryParams.push(responseType);\n            paramIndex++;\n        }\n        if (startDate) {\n            whereConditions.push(`created_at >= $${paramIndex}`);\n            queryParams.push(startDate);\n            paramIndex++;\n        }\n        if (endDate) {\n            whereConditions.push(`created_at <= $${paramIndex}`);\n            queryParams.push(endDate);\n            paramIndex++;\n        }\n        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';\n        // جلب السجلات\n        const logsQuery = `\n      SELECT id, session_id, user_message, bot_response, response_type, user_ip, created_at\n      FROM chat_logs \n      ${whereClause}\n      ORDER BY created_at DESC \n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `;\n        queryParams.push(limit, offset);\n        const logsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(logsQuery, queryParams);\n        // جلب العدد الإجمالي\n        const countQuery = `\n      SELECT COUNT(*) as total \n      FROM chat_logs \n      ${whereClause}\n    `;\n        const countResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(countQuery, queryParams.slice(0, -2)) // إزالة limit و offset\n        ;\n        // إحصائيات إضافية\n        const statsQuery = `\n      SELECT \n        response_type,\n        COUNT(*) as count\n      FROM chat_logs \n      ${whereClause}\n      GROUP BY response_type\n      ORDER BY count DESC\n    `;\n        const statsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(statsQuery, queryParams.slice(0, -2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                logs: logsResult.rows,\n                pagination: {\n                    page,\n                    limit,\n                    total: parseInt(countResult.rows[0].total),\n                    totalPages: Math.ceil(parseInt(countResult.rows[0].total) / limit)\n                },\n                stats: statsResult.rows\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب سجلات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب السجلات'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف سجلات المحادثة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        const sessionId = searchParams.get('session_id');\n        const beforeDate = searchParams.get('before_date');\n        if (action === 'clear_all') {\n            // حذف جميع السجلات\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM chat_logs');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف جميع سجلات المحادثة'\n            });\n        }\n        if (action === 'clear_session' && sessionId) {\n            // حذف سجلات جلسة محددة\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM chat_logs WHERE session_id = $1', [\n                sessionId\n            ]);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف سجلات الجلسة'\n            });\n        }\n        if (action === 'clear_old' && beforeDate) {\n            // حذف السجلات القديمة\n            const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM chat_logs WHERE created_at < $1', [\n                beforeDate\n            ]);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: `تم حذف ${result.rowCount} سجل قديم`\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير صحيح'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('خطأ في حذف سجلات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف السجلات'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/logs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Flogs%2Froute&page=%2Fapi%2Fchat%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Flogs%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();