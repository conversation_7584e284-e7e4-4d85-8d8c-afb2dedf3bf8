'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  ExternalLink,
  Save,
  X,
  Eye,
  Globe
} from 'lucide-react'

interface NavigationPage {
  id: number
  page_title: string
  page_url: string
  page_description: string
  category: string
  keywords: string
  is_active: boolean
  created_date: string
}

export default function NavigationPagesPage() {
  const [pages, setPages] = useState<NavigationPage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showForm, setShowForm] = useState(false)
  const [editingPage, setEditingPage] = useState<NavigationPage | null>(null)
  const [formData, setFormData] = useState({
    page_title: '',
    page_url: '',
    page_description: '',
    category: '',
    keywords: '',
    is_active: true
  })

  // جلب البيانات
  const fetchPages = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('جاري جلب صفحات التنقل...')

      // جلب جميع الصفحات مباشرة
      const response = await fetch('/api/navigation-pages/all')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('استجابة API:', data)

      if (data.success && Array.isArray(data.data)) {
        setPages(data.data)
        setError(null)
        console.log('تم جلب', data.data.length, 'صفحة بنجاح')
      } else {
        const errorMsg = data.error || 'فشل في جلب البيانات'
        console.error('فشل في جلب البيانات:', data)
        setError(errorMsg)
        setPages([])
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'خطأ في الاتصال'
      console.error('خطأ في جلب الصفحات:', error)
      setError(`خطأ في جلب الصفحات: ${errorMsg}`)
      setPages([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPages()
  }, [])

  // تصفية الصفحات
  const filteredPages = Array.isArray(pages) ? pages.filter(page => {
    if (!page || typeof page !== 'object') return false

    const title = page.page_title || ''
    const description = page.page_description || ''
    const keywords = page.keywords || ''
    const category = page.category || ''

    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         keywords.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = selectedCategory === 'all' || category === selectedCategory

    return matchesSearch && matchesCategory
  }) : []

  // الفئات المتاحة
  const categories = ['all', ...Array.from(new Set(
    Array.isArray(pages) ? pages.map(page => page?.category || '').filter(Boolean) : []
  ))]

  // إضافة/تحديث صفحة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const method = editingPage ? 'PUT' : 'POST'
      const url = '/api/search/navigation'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editingPage ? { ...formData, id: editingPage.id } : formData)
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchPages()
        setShowForm(false)
        setEditingPage(null)
        setFormData({
          page_title: '',
          page_url: '',
          page_description: '',
          category: '',
          keywords: '',
          is_active: true
        })
      }
    } catch (error) {
      console.error('Error saving page:', error)
    }
  }

  // تحرير صفحة
  const handleEdit = (page: NavigationPage) => {
    setEditingPage(page)
    setFormData({
      page_title: page.page_title,
      page_url: page.page_url,
      page_description: page.page_description,
      category: page.category,
      keywords: page.keywords,
      is_active: page.is_active
    })
    setShowForm(true)
  }

  // حذف صفحة
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه الصفحة؟')) return
    
    try {
      const response = await fetch(`/api/search/navigation?id=${id}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchPages()
      }
    } catch (error) {
      console.error('Error deleting page:', error)
    }
  }

  // زيارة الصفحة
  const visitPage = (url: string) => {
    if (url.startsWith('http')) {
      window.open(url, '_blank')
    } else {
      window.open(`${window.location.origin}${url}`, '_blank')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة صفحات التنقل</h1>
            <p className="text-gray-600 mt-2">إدارة صفحات النظام للبحث الذكي</p>
          </div>
          <Button 
            onClick={() => setShowForm(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            إضافة صفحة جديدة
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{pages.length}</div>
              <div className="text-sm text-gray-600">إجمالي الصفحات</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">
                {pages.filter(p => p.is_active).length}
              </div>
              <div className="text-sm text-gray-600">صفحات نشطة</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{categories.length - 1}</div>
              <div className="text-sm text-gray-600">الفئات</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">
                {filteredPages.length}
              </div>
              <div className="text-sm text-gray-600">نتائج البحث</div>
            </CardContent>
          </Card>
        </div>

        {/* البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="البحث في الصفحات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="md:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الفئات</option>
                  {categories.slice(1).map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الصفحات */}
        <Card>
          <CardHeader>
            <CardTitle>الصفحات ({filteredPages.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <div className="text-red-500 mb-4">
                  <svg className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-red-600 font-medium mb-2">حدث خطأ في تحميل الصفحات</p>
                <p className="text-gray-600 text-sm mb-4">{error}</p>
                <button
                  onClick={fetchPages}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  إعادة المحاولة
                </button>
              </div>
            ) : filteredPages.length === 0 ? (
              <div className="text-center py-8">
                <Globe className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">لا توجد صفحات مطابقة للبحث</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredPages.map((page) => (
                  <div key={page.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 space-x-reverse mb-2">
                          <h3 className="font-semibold text-gray-900">{page.page_title}</h3>
                          <Badge variant={page.is_active ? "default" : "secondary"}>
                            {page.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                          <Badge variant="outline">{page.category}</Badge>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">{page.page_description}</p>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                          <span className="flex items-center">
                            <Globe className="h-4 w-4 mr-1" />
                            {page.page_url}
                          </span>
                          <span>تاريخ الإنشاء: {page.created_date}</span>
                        </div>
                        {page.keywords && (
                          <div className="mt-2">
                            <span className="text-xs text-gray-500">الكلمات المفتاحية: {page.keywords}</span>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => visitPage(page.page_url)}
                          title="زيارة الصفحة"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(page)}
                          title="تحرير"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(page.id)}
                          className="text-red-600 hover:text-red-700"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج الإضافة/التحرير */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-2xl mx-4">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {editingPage ? 'تحرير الصفحة' : 'إضافة صفحة جديدة'}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowForm(false)
                      setEditingPage(null)
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      عنوان الصفحة *
                    </label>
                    <Input
                      type="text"
                      value={formData.page_title}
                      onChange={(e) => setFormData({...formData, page_title: e.target.value})}
                      required
                      placeholder="مثال: سندات القبض"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رابط الصفحة *
                    </label>
                    <Input
                      type="text"
                      value={formData.page_url}
                      onChange={(e) => setFormData({...formData, page_url: e.target.value})}
                      required
                      placeholder="مثال: /accounting/receipt-vouchers"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      وصف الصفحة
                    </label>
                    <Textarea
                      value={formData.page_description}
                      onChange={(e) => setFormData({...formData, page_description: e.target.value})}
                      placeholder="وصف مختصر للصفحة"
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        الفئة
                      </label>
                      <Input
                        type="text"
                        value={formData.category}
                        onChange={(e) => setFormData({...formData, category: e.target.value})}
                        placeholder="مثال: محاسبة"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        الحالة
                      </label>
                      <select
                        value={formData.is_active ? 'true' : 'false'}
                        onChange={(e) => setFormData({...formData, is_active: e.target.value === 'true'})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="true">نشط</option>
                        <option value="false">غير نشط</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الكلمات المفتاحية
                    </label>
                    <Input
                      type="text"
                      value={formData.keywords}
                      onChange={(e) => setFormData({...formData, keywords: e.target.value})}
                      placeholder="مثال: سندات,قبض,receipt,vouchers"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      افصل الكلمات بفاصلة (,)
                    </p>
                  </div>
                  
                  <div className="flex justify-end space-x-3 space-x-reverse pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setShowForm(false)
                        setEditingPage(null)
                      }}
                    >
                      إلغاء
                    </Button>
                    <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                      <Save className="h-4 w-4 mr-2" />
                      {editingPage ? 'تحديث' : 'إضافة'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
