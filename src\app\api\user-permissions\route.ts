import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب صلاحيات مستخدم معين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    console.log(`🔄 جلب صلاحيات المستخدم: ${userId}`)
    
    const result = await query(`
      SELECT 
        up.permission_key,
        p.permission_name,
        p.category,
        up.is_active,
        up.granted_date
      FROM user_permissions up
      JOIN permissions p ON up.permission_key = p.permission_key
      WHERE up.user_id = $1 AND up.is_active = true
      ORDER BY p.category, p.permission_name
    `, [userId])

    console.log(`📊 المستخدم ${userId} لديه ${result.rows.length} صلاحية`)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('❌ خطأ في جلب صلاحيات المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب صلاحيات المستخدم' },
      { status: 500 }
    )
  }
}

// POST - تحديث صلاحيات مستخدم
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, permissions, grantedBy } = body

    if (!userId || !Array.isArray(permissions)) {
      return NextResponse.json(
        { success: false, error: 'بيانات غير صحيحة' },
        { status: 400 }
      )
    }

    console.log(`🔄 تحديث صلاحيات المستخدم: ${userId}`)

    // بداية المعاملة
    await query('BEGIN')

    try {
      // حذف الصلاحيات الحالية
      await query(`
        DELETE FROM user_permissions 
        WHERE user_id = $1
      `, [userId])

      // إضافة الصلاحيات الجديدة
      for (const permission of permissions) {
        await query(`
          INSERT INTO user_permissions (user_id, permission_key, granted_by)
          VALUES ($1, $2, $3)
        `, [userId, permission, grantedBy || 1])
      }

      // تأكيد المعاملة
      await query('COMMIT')

      console.log(`✅ تم تحديث صلاحيات المستخدم ${userId} - ${permissions.length} صلاحية`)

      return NextResponse.json({
        success: true,
        message: 'تم تحديث الصلاحيات بنجاح',
        data: { userId, permissionsCount: permissions.length }
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('❌ خطأ في تحديث صلاحيات المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الصلاحيات' },
      { status: 500 }
    )
  }
}
