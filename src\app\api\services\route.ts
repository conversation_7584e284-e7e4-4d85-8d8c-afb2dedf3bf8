import { NextRequest, NextResponse } from 'next/server'
import { openDb } from '@/lib/database'

// GET - جلب جميع الخدمات
export async function GET(request: NextRequest) {
  try {
    

    const db = await openDb()

    // إنشاء الجدول إذا لم يكن موجوداً
    await db.exec(`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        lineage_id INTEGER,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // التحقق من وجود بيانات، وإدراج البيانات الافتراضية إذا لم توجد
    const countResult = await db.get('SELECT COUNT(*) as count FROM services')
    if (countResult.count === 0) {
      console.log('📝 إدراج البيانات الافتراضية للخدمات')

      const defaultServices = [
        'الاستشارات القانونية',
        'التمثيل القضائي',
        'صياغة العقود',
        'قضايا الأحوال الشخصية',
        'القضايا التجارية',
        'قضايا العمل'
      ]

      for (const serviceName of defaultServices) {
        await db.run('INSERT INTO services (name, lineage_id) VALUES (?, ?)', [serviceName, 1])
      }
    }

    // جلب الخدمات
    const services = await db.all(`
      SELECT
        s.id,
        s.name,
        s.lineage_id,
        s.created_date,
        l.name as lineage_name
      FROM services s
      LEFT JOIN lineages l ON s.lineage_id = l.id
      ORDER BY s.name ASC
    `)

    

    return NextResponse.json({
      success: true,
      data: services,
      message: `تم جلب ${services.length} خدمة بنجاح`
    })

  } catch (error) {
    console.error('❌ خطأ في جلب الخدمات:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب الخدمات',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة خدمة جديدة
export async function POST(request: NextRequest) {
  try {
    

    const body = await request.json()
    const { name, lineage_id } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const db = await openDb()

    // التحقق من عدم تكرار الاسم
    const existingService = await db.get('SELECT id FROM services WHERE name = ?', [name])
    if (existingService) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة موجود مسبقاً' },
        { status: 400 }
      )
    }

    const result = await db.run(`
      INSERT INTO services (name, lineage_id)
      VALUES (?, ?)
    `, [name, lineage_id || 1])

    const newService = await db.get('SELECT * FROM services WHERE id = ?', [result.lastID])

    

    return NextResponse.json({
      success: true,
      data: newService,
      message: 'تم إضافة الخدمة بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في إضافة الخدمة:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في إضافة الخدمة',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

// PUT - تحديث خدمة
export async function PUT(request: NextRequest) {
  try {
    

    const body = await request.json()
    const { id, name, lineage_id } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const db = await openDb()

    // التحقق من وجود الخدمة
    const existingService = await db.get('SELECT * FROM services WHERE id = ?', [id])
    if (!existingService) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار الاسم (إذا تم تغييره)
    if (name && name !== existingService.name) {
      const duplicateName = await db.get('SELECT id FROM services WHERE name = ? AND id != ?', [name, id])
      if (duplicateName) {
        return NextResponse.json(
          { success: false, error: 'اسم الخدمة موجود مسبقاً' },
          { status: 400 }
        )
      }
    }

    await db.run(`
      UPDATE services
      SET name = ?, lineage_id = ?
      WHERE id = ?
    `, [
      name || existingService.name,
      lineage_id !== undefined ? lineage_id : existingService.lineage_id,
      id
    ])

    const updatedService = await db.get('SELECT * FROM services WHERE id = ?', [id])

    

    return NextResponse.json({
      success: true,
      data: updatedService,
      message: 'تم تحديث الخدمة بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث الخدمة:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في تحديث الخدمة',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

// DELETE - حذف خدمة
export async function DELETE(request: NextRequest) {
  try {
    

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const db = await openDb()

    // التحقق من وجود الخدمة
    const existingService = await db.get('SELECT * FROM services WHERE id = ?', [id])
    if (!existingService) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    await db.run('DELETE FROM services WHERE id = ?', [id])

    

    return NextResponse.json({
      success: true,
      message: 'تم حذف الخدمة بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في حذف الخدمة:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في حذف الخدمة',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}