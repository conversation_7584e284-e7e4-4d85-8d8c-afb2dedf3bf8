(()=>{var e={};e.id=3550,e.ids=[3550],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>p});var o=r(64939),a=r(29021),n=r.n(a),c=r(33873),i=r.n(c),u=e([o]);o=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let E=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],r=d.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new o.Pool(E);async function p(e,t){let r=await l.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16662:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>p,GET:()=>c,POST:()=>i,PUT:()=>u});var o=r(32190),a=r(5069),n=e([a]);async function c(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),n=(r-1)*s,c=await (0,a.P)(`
      SELECT 
        p.*,
        c.name as customer_name,
        cs.title as case_title,
        cs.id as case_id,
        COALESCE(
          (SELECT SUM(qty) FROM timecard WHERE project_id = p.id), 
          0
        ) as total_hours,
        COALESCE(
          (SELECT SUM(qty * sellprice) FROM timecard WHERE project_id = p.id), 
          0
        ) as total_cost,
        COALESCE(
          (SELECT SUM(amount) FROM ar WHERE entity_id = p.customer_id), 
          0
        ) as total_revenue,
        CASE 
          WHEN p.completed >= p.production AND p.production > 0 THEN 'completed'
          WHEN p.completed > 0 THEN 'in_progress'
          ELSE 'not_started'
        END as status
      FROM project p
      LEFT JOIN entity e ON p.customer_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      LEFT JOIN cases cs ON cs.client_id = (
        SELECT client_id FROM clients WHERE entity_id = p.customer_id LIMIT 1
      )
      ORDER BY p.created_date DESC
      LIMIT $1 OFFSET $2
    `,[s,n]),i=await (0,a.P)("SELECT COUNT(*) as total FROM project"),u=parseInt(i.rows[0].total);return o.NextResponse.json({success:!0,data:c.rows.map(e=>({...e,total_hours:parseFloat(e.total_hours||0),total_cost:parseFloat(e.total_cost||0),total_revenue:parseFloat(e.total_revenue||0),profit_margin:e.total_revenue>0?(e.total_revenue-e.total_cost)/e.total_revenue*100:0})),pagination:{page:r,limit:s,total:u,pages:Math.ceil(u/s)}})}catch(e){return console.error("Error fetching projects:",e),o.NextResponse.json({success:!1,error:"فشل في جلب المشاريع"},{status:500})}}async function i(e){try{let{projectnumber:t,description:r,startdate:s,enddate:n,customer_id:c,case_id:i,production:u=0,completed:p=0}=await e.json();if(!t||!r||!s)return o.NextResponse.json({success:!1,error:"رقم المشروع والوصف وتاريخ البداية مطلوبة"},{status:400});if((await (0,a.P)("SELECT id FROM project WHERE projectnumber = $1",[t])).rows.length>0)return o.NextResponse.json({success:!1,error:"رقم المشروع موجود مسبقاً"},{status:400});let d=await (0,a.P)(`
      INSERT INTO project (
        projectnumber, description, startdate, enddate, 
        customer_id, production, completed
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `,[t,r,s,n||null,c||null,u,p]);return i&&await (0,a.P)(`
        UPDATE cases 
        SET project_id = $1 
        WHERE id = $2
      `,[d.rows[0].id,i]),o.NextResponse.json({success:!0,message:"تم إنشاء المشروع بنجاح",data:d.rows[0]})}catch(e){return console.error("Error creating project:",e),o.NextResponse.json({success:!1,error:"فشل في إنشاء المشروع"},{status:500})}}async function u(e){try{let{id:t,projectnumber:r,description:s,startdate:n,enddate:c,customer_id:i,production:u,completed:p}=await e.json();if(!t)return o.NextResponse.json({success:!1,error:"معرف المشروع مطلوب"},{status:400});let d=await (0,a.P)(`
      UPDATE project 
      SET 
        projectnumber = COALESCE($2, projectnumber),
        description = COALESCE($3, description),
        startdate = COALESCE($4, startdate),
        enddate = $5,
        customer_id = COALESCE($6, customer_id),
        production = COALESCE($7, production),
        completed = COALESCE($8, completed)
      WHERE id = $1
      RETURNING *
    `,[t,r,s,n,c,i,u,p]);if(0===d.rows.length)return o.NextResponse.json({success:!1,error:"المشروع غير موجود"},{status:404});return o.NextResponse.json({success:!0,message:"تم تحديث المشروع بنجاح",data:d.rows[0]})}catch(e){return console.error("Error updating project:",e),o.NextResponse.json({success:!1,error:"فشل في تحديث المشروع"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return o.NextResponse.json({success:!1,error:"معرف المشروع مطلوب"},{status:400});let s=await (0,a.P)("SELECT COUNT(*) as count FROM timecard WHERE project_id = $1",[r]);if(parseInt(s.rows[0].count)>0)return o.NextResponse.json({success:!1,error:"لا يمكن حذف مشروع يحتوي على إدخالات وقت"},{status:400});await (0,a.P)("BEGIN");try{await (0,a.P)("UPDATE cases SET project_id = NULL WHERE project_id = $1",[r]);let e=await (0,a.P)("DELETE FROM project WHERE id = $1 RETURNING *",[r]);if(0===e.rows.length)return await (0,a.P)("ROLLBACK"),o.NextResponse.json({success:!1,error:"المشروع غير موجود"},{status:404});return await (0,a.P)("COMMIT"),o.NextResponse.json({success:!0,message:"تم حذف المشروع بنجاح"})}catch(e){throw await (0,a.P)("ROLLBACK"),e}}catch(e){return console.error("Error deleting project:",e),o.NextResponse.json({success:!1,error:"فشل في حذف المشروع"},{status:500})}}a=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},70580:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>E});var o=r(96559),a=r(48088),n=r(37719),c=r(16662),i=e([c]);c=(i.then?(await i)():i)[0];let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/projects/route",pathname:"/api/accounting/projects",filename:"route",bundlePath:"app/api/accounting/projects/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\projects\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:E,serverHooks:l}=p;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:E})}s()}catch(e){s(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(70580));module.exports=s})();