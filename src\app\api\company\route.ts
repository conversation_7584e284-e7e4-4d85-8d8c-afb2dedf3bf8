import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب بيانات الشركة
export async function GET() {
  try {


    const result = await query(`
      SELECT
        id,
        name,
        legal_name,
        registration_number,
        tax_number,
        address,
        city,
        country,
        phone,
        email,
        website,
        logo_url,
        logo_right_text,
        logo_left_text,
        logo_image_url,
        established_date,
        legal_form,
        capital,
        description,
        is_active,
        created_date,
        latitude,
        longitude
      FROM companies
      ORDER BY created_date DESC
    `)



    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('❌ خطأ في جلب بيانات الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الشركة' },
      { status: 500 }
    )
  }
}

// POST - إضافة شركة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📥 بيانات الشركة الجديدة:', body)

    const {
      name,
      legal_name,
      registration_number,
      tax_number,
      address,
      city,
      country,
      phone,
      email,
      website,
      logo_url,
      logo_right_text,
      logo_left_text,
      logo_image_url,
      established_date,
      legal_form,
      capital,
      description,
      is_active
    } = body

    if (!name || !legal_name) {
      return NextResponse.json(
        { success: false, error: 'اسم الشركة والاسم القانوني مطلوبان' },
        { status: 400 }
      )
    }

    // إضافة الشركة لقاعدة البيانات
    const result = await query(`
      INSERT INTO companies (
        name, legal_name, registration_number, tax_number, address, city, country,
        phone, email, website, logo_url, logo_right_text, logo_left_text,
        logo_image_url, established_date, legal_form, capital, description,
        is_active, created_date
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, CURRENT_DATE
      )
      RETURNING *
    `, [
      name, legal_name, registration_number, tax_number, address, city, country,
      phone, email, website, logo_url, logo_right_text, logo_left_text,
      logo_image_url, established_date, legal_form, capital || 0, description,
      is_active !== undefined ? is_active : true
    ])

    const newCompany = result.rows[0]


    return NextResponse.json({
      success: true,
      message: 'تم إضافة الشركة بنجاح',
      data: newCompany
    })
  } catch (error) {
    console.error('❌ خطأ في إضافة الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الشركة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث بيانات الشركة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📝 تحديث بيانات الشركة:', body)

    const {
      id,
      name,
      legal_name,
      registration_number,
      tax_number,
      address,
      city,
      country,
      phone,
      email,
      website,
      logo_url,
      logo_right_text,
      logo_left_text,
      logo_image_url,
      established_date,
      legal_form,
      capital,
      description,
      is_active,
      latitude,
      longitude,
      working_hours
    } = body

    if (!id || !name || !legal_name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الشركة والاسم القانوني مطلوبان' },
        { status: 400 }
      )
    }

    // تحديث الشركة في قاعدة البيانات
    const result = await query(`
      UPDATE companies SET
        name = $1,
        legal_name = $2,
        registration_number = $3,
        tax_number = $4,
        address = $5,
        city = $6,
        country = $7,
        phone = $8,
        email = $9,
        website = $10,
        logo_url = $11,
        logo_right_text = $12,
        logo_left_text = $13,
        logo_image_url = $14,
        established_date = $15,
        legal_form = $16,
        capital = $17,
        description = $18,
        is_active = $19,
        latitude = $20,
        longitude = $21,
        working_hours = $22
      WHERE id = $23
      RETURNING *
    `, [
      name, legal_name, registration_number, tax_number, address, city, country,
      phone, email, website, logo_url, logo_right_text, logo_left_text,
      logo_image_url, established_date || null, legal_form, capital || 0, description,
      is_active !== undefined ? is_active : true, latitude, longitude, working_hours, id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الشركة غير موجودة' },
        { status: 404 }
      )
    }



    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الشركة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('❌ خطأ في تحديث بيانات الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الشركة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف شركة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    console.log('🗑️ حذف الشركة:', id)

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الشركة مطلوب' },
        { status: 400 }
      )
    }

    // حذف الشركة من قاعدة البيانات
    const result = await query(`
      DELETE FROM companies
      WHERE id = $1
      RETURNING id, name
    `, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الشركة غير موجودة' },
        { status: 404 }
      )
    }



    return NextResponse.json({
      success: true,
      message: 'تم حذف الشركة بنجاح'
    })
  } catch (error) {
    console.error('❌ خطأ في حذف الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الشركة' },
      { status: 500 }
    )
  }
}
