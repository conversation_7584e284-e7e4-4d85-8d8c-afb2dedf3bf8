# سكريپت تشغيل النسخة الثانية على المنفذ 8914
# Start Rubaie Server Script

Write-Host "🚀 بدء تشغيل نظام إدارة المحاماة - الربعي" -ForegroundColor Green
Write-Host "📡 المنفذ: 8914" -ForegroundColor Cyan
Write-Host "🗄️ قاعدة البيانات: rubaie" -ForegroundColor Cyan

# التحقق من وجود ملف الإعدادات
if (-not (Test-Path ".env.rubaie")) {
    Write-Host "❌ ملف الإعدادات .env.rubaie غير موجود" -ForegroundColor Red
    exit 1
}

# التحقق من وجود قاعدة البيانات
Write-Host "`n🔍 التحقق من قاعدة البيانات..." -ForegroundColor Yellow
$dbCheck = psql -U postgres -h localhost -p 5432 -lqt | Select-String "rubaie"
if (-not $dbCheck) {
    Write-Host "❌ قاعدة البيانات 'rubaie' غير موجودة" -ForegroundColor Red
    Write-Host "💡 يرجى تشغيل سكريپت copy_database_complete.ps1 أولاً" -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ قاعدة البيانات متوفرة" -ForegroundColor Green

# نسخ ملف الإعدادات
Write-Host "`n📋 إعداد متغيرات البيئة..." -ForegroundColor Yellow
Copy-Item ".env.rubaie" ".env" -Force
Write-Host "✅ تم نسخ إعدادات البيئة" -ForegroundColor Green

# التحقق من Node.js
Write-Host "`n🔍 التحقق من Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js متوفر: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير متوفر" -ForegroundColor Red
    exit 1
}

# التحقق من npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm متوفر: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm غير متوفر" -ForegroundColor Red
    exit 1
}

# التحقق من وجود node_modules
if (-not (Test-Path "node_modules")) {
    Write-Host "`n📦 تثبيت التبعيات..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ تم تثبيت التبعيات" -ForegroundColor Green
}

# عرض معلومات الخادم
Write-Host "`n📊 معلومات الخادم:" -ForegroundColor Cyan
Write-Host "   🌐 الرابط: http://localhost:8914" -ForegroundColor White
Write-Host "   🗄️ قاعدة البيانات: rubaie" -ForegroundColor White
Write-Host "   👤 المستخدم: postgres" -ForegroundColor White
Write-Host "   🏢 النظام: نظام إدارة المحاماة - الربعي" -ForegroundColor White

Write-Host "`n🚀 بدء تشغيل الخادم..." -ForegroundColor Green
Write-Host "⏹️ للإيقاف: اضغط Ctrl+C" -ForegroundColor Yellow

# تشغيل الخادم
npm run dev
