// اختبار الاتصال بـ Groq API (مجاني)
require('dotenv').config({ path: '.env.local' });

async function testGroqConnection() {
  console.log('🔄 اختبار الاتصال بـ Groq API (مجاني)...\n');
  
  // التحقق من وجود API Key
  const apiKey = process.env.GROQ_API_KEY;
  
  if (!apiKey) {
    console.log('❌ GROQ_API_KEY غير موجود في ملف .env.local');
    console.log('📝 يرجى إضافة المفتاح كما يلي:');
    console.log('   GROQ_API_KEY=gsk-your-actual-api-key-here\n');
    console.log('🔗 احصل على مفتاح مجاني من: https://console.groq.com/keys');
    return;
  }
  
  if (apiKey === 'your_groq_api_key_here') {
    console.log('⚠️ يرجى استبدال "your_groq_api_key_here" بالمفتاح الحقيقي');
    console.log('🔗 احصل على المفتاح من: https://console.groq.com/keys\n');
    return;
  }
  
  console.log('✅ تم العثور على GROQ_API_KEY');
  console.log(`🔑 المفتاح يبدأ بـ: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}\n`);
  
  try {
    // اختبار الاتصال بـ Groq API
    console.log('🌐 اختبار الاتصال بـ Groq API...');
    
    const response = await fetch('https://api.groq.com/openai/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ نجح الاتصال بـ Groq API!');
      
      // البحث عن نماذج Llama
      const llamaModels = data.data.filter(model => 
        model.id.includes('llama') || model.id.includes('Llama')
      );
      
      console.log(`\n📊 النماذج المتاحة (${llamaModels.length} من نماذج Llama):`);
      llamaModels.forEach(model => {
        console.log(`   • ${model.id}`);
      });
      
    } else {
      const errorData = await response.text();
      console.log('❌ فشل الاتصال بـ Groq API');
      console.log(`📄 رمز الخطأ: ${response.status}`);
      console.log(`📝 رسالة الخطأ: ${errorData}\n`);
      
      if (response.status === 401) {
        console.log('🔑 المفتاح غير صحيح أو منتهي الصلاحية');
        console.log('📋 تأكد من:');
        console.log('   1. نسخ المفتاح بالكامل من Groq');
        console.log('   2. عدم وجود مسافات إضافية');
        console.log('   3. أن المفتاح لم ينته أو يُحذف');
      }
      
      return;
    }
    
  } catch (error) {
    console.log('❌ خطأ في الشبكة:', error.message);
    console.log('🌐 تأكد من اتصالك بالإنترنت\n');
    return;
  }
  
  // اختبار إرسال رسالة تجريبية
  console.log('\n💬 اختبار إرسال رسالة تجريبية...');
  
  try {
    const testResponse = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'llama-3.1-8b-instant',
        messages: [
          {
            role: 'system',
            content: 'أنت مساعد قانوني ذكي متخصص في القانون اليمني. أجب باللغة العربية.'
          },
          {
            role: 'user',
            content: 'مرحباً، هل يمكنك مساعدتي في استشارة قانونية؟'
          }
        ],
        max_tokens: 150,
        temperature: 0.7
      })
    });
    
    if (testResponse.ok) {
      const testData = await testResponse.json();
      const aiMessage = testData.choices[0].message.content;
      
      console.log('✅ نجح اختبار الرسالة!');
      console.log('🤖 رد Llama 3.1 8B:');
      console.log(`   "${aiMessage}"\n`);
      
      console.log('🎉 النظام جاهز للعمل مع Llama 3.1 8B مجاناً!');
      console.log('📊 معلومات الاستخدام:');
      console.log(`   • النموذج: ${testData.model}`);
      console.log(`   • Tokens المستخدمة: ${testData.usage.total_tokens}`);
      console.log(`   • التكلفة: مجاني تماماً! 🆓`);
      
    } else {
      const errorData = await testResponse.text();
      console.log('❌ فشل اختبار الرسالة');
      console.log(`📄 رمز الخطأ: ${testResponse.status}`);
      console.log(`📝 رسالة الخطأ: ${errorData}`);
      
      if (testResponse.status === 429) {
        console.log('⏰ تم تجاوز حد الاستخدام المجاني');
        console.log('⏳ انتظر قليلاً ثم حاول مرة أخرى');
      }
    }
    
  } catch (error) {
    console.log('❌ خطأ في اختبار الرسالة:', error.message);
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 ملخص الاختبار:');
  console.log('✅ Groq API Key موجود ومُعرَّف');
  console.log('✅ الاتصال بـ Groq API يعمل');
  console.log('✅ Llama 3.1 8B متاح للاستخدام');
  console.log('🆓 مجاني تماماً - بدون تكلفة!');
  console.log('🚀 النظام جاهز للعمل!');
  console.log('='.repeat(50));
}

// تشغيل الاختبار
testGroqConnection().catch(console.error);
