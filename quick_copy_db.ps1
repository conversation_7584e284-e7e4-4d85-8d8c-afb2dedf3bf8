# سكريپت نسخ قاعدة البيانات الكاملة
# Complete Database Copy Script

Write-Host "🔄 نسخ قاعدة البيانات الكاملة من mohammi إلى rubaie" -ForegroundColor Green
Write-Host "🔑 استخدام كلمة المرور: yemen123" -ForegroundColor Cyan

# تعيين متغير البيئة لكلمة المرور
$env:PGPASSWORD = "yemen123"

# التحقق من وجود قاعدة البيانات المصدر
Write-Host "`n🔍 التحقق من قاعدة البيانات المصدر..." -ForegroundColor Yellow
$sourceCheck = psql -U postgres -h localhost -p 5432 -lqt | Select-String "mohammi"
if (-not $sourceCheck) {
    Write-Host "❌ قاعدة البيانات المصدر 'mohammi' غير موجودة" -ForegroundColor Red
    exit 1
}
Write-Host "✅ قاعدة البيانات المصدر موجودة" -ForegroundColor Green

# قطع جميع الاتصالات بقاعدة البيانات الهدف
Write-Host "`n🔌 قطع الاتصالات بقاعدة البيانات الهدف..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'rubaie' AND pid <> pg_backend_pid();" 2>$null

# حذف قاعدة البيانات الهدف إذا كانت موجودة
Write-Host "🗑️ حذف قاعدة البيانات القديمة..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -c "DROP DATABASE IF EXISTS rubaie;" 2>$null

# إنشاء قاعدة البيانات الجديدة
Write-Host "🏗️ إنشاء قاعدة البيانات الجديدة..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -c "CREATE DATABASE rubaie;"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في إنشاء قاعدة البيانات" -ForegroundColor Red
    exit 1
}

# نسخ الهيكل والبيانات كاملة
Write-Host "📋 نسخ الهيكل والبيانات الكاملة..." -ForegroundColor Yellow
Write-Host "   📊 هذا قد يستغرق بضع دقائق..." -ForegroundColor Cyan

# استخدام pg_dump مع جميع الخيارات للنسخ الكامل
pg_dump -U postgres -h localhost -p 5432 -d mohammi --verbose --clean --create --if-exists --no-owner --no-privileges | psql -U postgres -h localhost -p 5432 -d rubaie

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم نسخ قاعدة البيانات بنجاح!" -ForegroundColor Green

    # التحقق من النسخ بمقارنة الإحصائيات
    Write-Host "`n📊 التحقق من صحة النسخ..." -ForegroundColor Yellow

    # إحصائيات قاعدة البيانات المصدر
    Write-Host "📋 قاعدة البيانات المصدر (mohammi):" -ForegroundColor Cyan
    $sourceStats = psql -U postgres -h localhost -p 5432 -d mohammi -t -c "
    SELECT
        'العملاء: ' || COALESCE((SELECT COUNT(*) FROM clients), 0) ||
        ' | القضايا: ' || COALESCE((SELECT COUNT(*) FROM issues), 0) ||
        ' | المحاكم: ' || COALESCE((SELECT COUNT(*) FROM courts), 0) ||
        ' | العملات: ' || COALESCE((SELECT COUNT(*) FROM currencies), 0) ||
        ' | أنواع القضايا: ' || COALESCE((SELECT COUNT(*) FROM issue_types), 0);"
    Write-Host "   $($sourceStats.Trim())" -ForegroundColor White

    # إحصائيات قاعدة البيانات الهدف
    Write-Host "📋 قاعدة البيانات الهدف (rubaie):" -ForegroundColor Cyan
    $targetStats = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "
    SELECT
        'العملاء: ' || COALESCE((SELECT COUNT(*) FROM clients), 0) ||
        ' | القضايا: ' || COALESCE((SELECT COUNT(*) FROM issues), 0) ||
        ' | المحاكم: ' || COALESCE((SELECT COUNT(*) FROM courts), 0) ||
        ' | العملات: ' || COALESCE((SELECT COUNT(*) FROM currencies), 0) ||
        ' | أنواع القضايا: ' || COALESCE((SELECT COUNT(*) FROM issue_types), 0);"
    Write-Host "   $($targetStats.Trim())" -ForegroundColor White

    # مقارنة النتائج
    if ($sourceStats.Trim() -eq $targetStats.Trim()) {
        Write-Host "`n✅ النسخ مكتمل وصحيح!" -ForegroundColor Green
        Write-Host "🎉 جميع البيانات تم نسخها بنجاح" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️ تحذير: هناك اختلاف في الإحصائيات" -ForegroundColor Yellow
        Write-Host "💡 قد يكون هذا طبيعياً إذا كانت هناك بيانات جديدة" -ForegroundColor Cyan
    }

    # عرض معلومات الجداول
    Write-Host "`n📋 جداول قاعدة البيانات المنسوخة:" -ForegroundColor Cyan
    $tables = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name;"

    $tableList = $tables -split "`n" | Where-Object { $_.Trim() -ne "" }
    foreach ($table in $tableList) {
        Write-Host "   📊 $($table.Trim())" -ForegroundColor White
    }

    Write-Host "`n🚀 يمكنك الآن تشغيل النسخة الثانية:" -ForegroundColor Yellow
    Write-Host "   .\start_single_server.ps1 rubaie" -ForegroundColor Cyan
    Write-Host "   أو" -ForegroundColor Gray
    Write-Host "   .\start_both_servers.ps1" -ForegroundColor Cyan

} else {
    Write-Host "❌ فشل في نسخ قاعدة البيانات" -ForegroundColor Red
    Write-Host "💡 تحقق من:" -ForegroundColor Yellow
    Write-Host "   - كلمة مرور قاعدة البيانات صحيحة" -ForegroundColor White
    Write-Host "   - PostgreSQL يعمل بشكل صحيح" -ForegroundColor White
    Write-Host "   - قاعدة البيانات المصدر 'mohammi' موجودة" -ForegroundColor White
}
