"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/hooks/useCompanyData.ts":
/*!*************************************!*\
  !*** ./src/hooks/useCompanyData.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCompanyData: () => (/* binding */ useCompanyData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Hook لإدارة بيانات الشركة والتخزين المحلي\n\nconst STORAGE_KEY = 'company_data';\nconst CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 ساعة\n;\nfunction useCompanyData() {\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // تحميل البيانات من التخزين المحلي\n    const loadFromStorage = ()=>{\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (!stored) return null;\n            const { data, timestamp } = JSON.parse(stored);\n            // التحقق من انتهاء صلاحية البيانات\n            if (Date.now() - timestamp > CACHE_DURATION) {\n                localStorage.removeItem(STORAGE_KEY);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('خطأ في تحميل البيانات من التخزين المحلي:', error);\n            localStorage.removeItem(STORAGE_KEY);\n            return null;\n        }\n    };\n    // حفظ البيانات في التخزين المحلي\n    const saveToStorage = (data)=>{\n        try {\n            const storageData = {\n                data,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(storageData));\n        } catch (error) {\n            console.error('خطأ في حفظ البيانات في التخزين المحلي:', error);\n        }\n    };\n    // جلب البيانات من الخادم\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/company');\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (result.success && result.data && result.data.length > 0) {\n                return result.data[0] // أخذ أول شركة\n                ;\n            } else {\n                throw new Error('لا توجد بيانات شركة متاحة');\n            }\n        } catch (error) {\n            console.error('خطأ في جلب بيانات الشركة:', error);\n            throw error;\n        }\n    };\n    // تحديث البيانات\n    const refreshData = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const data = await fetchCompanyData();\n            if (data) {\n                setCompanyData(data);\n                saveToStorage(data);\n            }\n        } catch (error) {\n            setError(error instanceof Error ? error.message : 'خطأ في تحميل البيانات');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تحميل البيانات عند بدء التشغيل\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCompanyData.useEffect\": ()=>{\n            const initializeData = {\n                \"useCompanyData.useEffect.initializeData\": async ()=>{\n                    // محاولة تحميل البيانات من التخزين المحلي أولاً\n                    const cachedData = loadFromStorage();\n                    if (cachedData) {\n                        // استخدام البيانات المخزنة فوراً بدون loading\n                        setCompanyData(cachedData);\n                        setLoading(false);\n                        // جلب البيانات المحدثة في الخلفية بصمت\n                        try {\n                            const freshData = await fetchCompanyData();\n                            if (freshData && JSON.stringify(freshData) !== JSON.stringify(cachedData)) {\n                                setCompanyData(freshData);\n                                saveToStorage(freshData);\n                            }\n                        } catch (error) {\n                            // في حالة فشل التحديث، نبقي البيانات المخزنة\n                            console.warn('فشل في تحديث البيانات، سيتم استخدام البيانات المخزنة');\n                        }\n                    } else {\n                        // لا توجد بيانات مخزنة، جلب من الخادم مع عرض loading\n                        setLoading(true);\n                        setError(null);\n                        try {\n                            const data = await fetchCompanyData();\n                            if (data) {\n                                setCompanyData(data);\n                                saveToStorage(data);\n                            }\n                        } catch (error) {\n                            setError(error instanceof Error ? error.message : 'خطأ في تحميل البيانات');\n                        } finally{\n                            setLoading(false);\n                        }\n                    }\n                }\n            }[\"useCompanyData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useCompanyData.useEffect\"], []);\n    // مسح البيانات المخزنة\n    const clearCache = ()=>{\n        localStorage.removeItem(STORAGE_KEY);\n        setCompanyData(null);\n    };\n    // الحصول على لون الموضوع\n    const getThemeColor = ()=>{\n        // من متغيرات البيئة (نظام التوجيه)\n        if (true) {\n            const port = window.location.port;\n            if (port === '7443') return '#cca967' // لون مؤسسة الجرافي\n            ;\n            if (port === '8914') return '#2563eb' // لون شركة الربيعي\n            ;\n        }\n        // من بيانات الشركة\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.theme_color) {\n            return companyData.theme_color;\n        }\n        // الافتراضي\n        return '#cca967';\n    };\n    // الحصول على اسم الشركة المناسب\n    const getCompanyName = ()=>{\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.company_name) return companyData.company_name;\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.name) return companyData.name;\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.legal_name) return companyData.legal_name;\n        // من نظام التوجيه\n        if (true) {\n            const port = window.location.port;\n            if (port === '7443') return 'مؤسسة الجرافي للمحاماة';\n            if (port === '8914') return 'شركة الربيعي للمحاماة';\n        }\n        return 'النظام القانوني';\n    };\n    return {\n        companyData,\n        loading,\n        error,\n        refreshData,\n        clearCache,\n        getThemeColor,\n        getCompanyName,\n        isDataAvailable: !!companyData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCompanyData.ts\n"));

/***/ })

});