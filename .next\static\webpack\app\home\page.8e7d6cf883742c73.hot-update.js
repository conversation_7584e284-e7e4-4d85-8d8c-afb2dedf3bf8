"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx":
/*!********************************************************!*\
  !*** ./src/app/home/<USER>/simple-chat-widget.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleChatWidget: () => (/* binding */ SimpleChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ SimpleChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleChatWidget(param) {\n    let { isOpen, onClose, onOpen } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guestId, setGuestId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // إنشاء معرف زائر فريد وجلب بيانات الشركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            let guestIdentifier = localStorage.getItem('guestId');\n            if (!guestIdentifier) {\n                guestIdentifier = \"guest_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                localStorage.setItem('guestId', guestIdentifier);\n            }\n            setGuestId(guestIdentifier);\n            // جلب بيانات الشركة\n            fetchCompanyData();\n        }\n    }[\"SimpleChatWidget.useEffect\"], []);\n    // جلب بيانات الشركة\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/companies');\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && result.data.length > 0) {\n                    const company = result.data[0];\n                    setCompanyData(company);\n                    // إنشاء الرسائل الترحيبية\n                    const welcomeMessages = [\n                        {\n                            id: '1',\n                            content: \"مرحباً بك في \".concat(company.name, \"! \\uD83C\\uDFDB️\"),\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        }\n                    ];\n                    setMessages(welcomeMessages);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching company data:', error);\n            // رسائل افتراضية\n            const defaultMessages = [\n                {\n                    id: '1',\n                    content: 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 🏛️',\n                    sender: 'assistant',\n                    timestamp: new Date(),\n                    status: 'delivered'\n                }\n            ];\n            setMessages(defaultMessages);\n        }\n    };\n    // Auto-scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat is opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!message.trim()) return;\n        console.log('📤 Sending message:', message);\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            sender: 'user',\n            timestamp: new Date(),\n            status: 'delivered'\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setIsTyping(true);\n        try {\n            // إرسال للذكاء الاصطناعي\n            const response = await sendToAI(message);\n            // Add AI response\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message,\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'delivered'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: 'أعتذر، حدث خطأ. يرجى المحاولة مرة أخرى.',\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'error'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            // إعادة التركيز على مربع النص بعد الإرسال\n            setTimeout(()=>{\n                if (inputRef.current) {\n                    inputRef.current.focus();\n                }\n            }, 100);\n        }\n    };\n    const sendToAI = async (userMessage)=>{\n        try {\n            console.log('🤖 Sending message to AI:', userMessage);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage,\n                    model: 'groq-llama-8b',\n                    conversationId: \"guest_\".concat(guestId, \"_\").concat(Date.now()),\n                    context: []\n                })\n            });\n            if (!response.ok) {\n                throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');\n            }\n            const result = await response.json();\n            console.log('🤖 AI Response:', result);\n            if (result.success && result.response) {\n                return {\n                    message: result.response\n                };\n            } else {\n                throw new Error(result.error || 'خطأ في الاستجابة');\n            }\n        } catch (error) {\n            console.error('AI Error:', error);\n            // رد احتياطي\n            const fallbackResponses = [\n                \"أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: \".concat((companyData === null || companyData === void 0 ? void 0 : companyData.phone) || '+967-1-123456'),\n                'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',\n                'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.'\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return {\n                message: randomResponse\n            };\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onOpen,\n                className: \"h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700 flex items-center justify-center\",\n                title: \"المحادثات مع الذكاء الاصطناعي\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-7 w-7 text-white\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-24 left-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b bg-blue-600 text-white rounded-t-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"المحادثات المباشرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-2 w-2 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"AI\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-100 mr-2\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 rounded-full hover:bg-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.sender === 'user' ? 'justify-end' : 'justify-start'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-sm px-4 py-3 rounded-lg \".concat(msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 opacity-70\",\n                                        children: msg.timestamp.toLocaleTimeString('ar-SA', {\n                                            hour: '2-digit',\n                                            minute: '2-digit'\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 text-gray-800 px-4 py-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"المساعد الذكي يكتب...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendMessage,\n                    className: \"flex space-x-2 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ref: inputRef,\n                            type: \"text\",\n                            value: message,\n                            onChange: (e)=>setMessage(e.target.value),\n                            placeholder: \"اكتب رسالتك...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                            disabled: isTyping,\n                            dir: \"rtl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n                            disabled: !message.trim() || isTyping,\n                            children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatWidget, \"+wvRQPGNvkoLiajW7IFDjiYLFZs=\");\n_c = SimpleChatWidget;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\n"));

/***/ })

});