# بيانات تسجيل الدخول للمستخدم Admin

## 👤 بيانات المستخدم الإداري

### 🔑 **بيانات تسجيل الدخول:**
```
اسم المستخدم: admin
كلمة المرور: admin123
البريد الإلكتروني: <EMAIL>
الدور: admin
```

### 🌐 **رابط تسجيل الدخول:**
**http://localhost:7443/login**

## 📊 **تفاصيل المستخدم في قاعدة البيانات:**

- **ID:** 4
- **اسم المستخدم:** admin
- **كلمة المرور المحفوظة:** admin123 (غير مشفرة للاختبار)
- **البريد الإلكتروني:** <EMAIL>
- **الدور:** admin
- **حالة الحساب:** نشط

## 🔧 **كيفية إنشاء المستخدم:**

تم إنشاء المستخدم باستخدام السكريپت التالي:
```sql
DELETE FROM users WHERE username = 'admin';

INSERT INTO users (username, password_hash, email, role)
VALUES ('admin', 'admin123', '<EMAIL>', 'admin');
```

## 🛡️ **الصلاحيات:**

المستخدم `admin` له صلاحيات كاملة في النظام:
- ✅ إدارة المستخدمين
- ✅ إدارة الموظفين
- ✅ إدارة الموكلين
- ✅ إدارة القضايا
- ✅ إدارة الخدمات
- ✅ إدارة النسب المالية
- ✅ الوصول لجميع التقارير
- ✅ إعدادات النظام

## 🔄 **طريقة تسجيل الدخول:**

1. **افتح المتصفح** واذهب إلى: http://localhost:7443/login
2. **أدخل البيانات:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
3. **اختر نوع المستخدم:** مستخدم النظام
4. **اضغط تسجيل الدخول**

## ⚠️ **ملاحظات أمنية:**

### للاختبار والتطوير:
- كلمة المرور غير مشفرة لسهولة الاختبار
- يمكن تغيير كلمة المرور من داخل النظام

### للإنتاج:
- يجب تشفير كلمة المرور باستخدام bcrypt
- يجب تغيير كلمة المرور الافتراضية
- يجب تفعيل المصادقة الثنائية

## 🔧 **تحديث كلمة المرور:**

### لتشفير كلمة المرور:
```javascript
const bcrypt = require('bcrypt');
const hashedPassword = await bcrypt.hash('admin123', 10);

await client.query(`
  UPDATE users 
  SET password_hash = $1 
  WHERE username = 'admin'
`, [hashedPassword]);
```

### لتغيير كلمة المرور:
```sql
UPDATE users 
SET password_hash = 'new_password_here' 
WHERE username = 'admin';
```

## 📋 **معلومات إضافية:**

### قاعدة البيانات:
- **الخادم:** localhost:5432
- **اسم القاعدة:** mohammi
- **المستخدم:** postgres
- **كلمة المرور:** yemen123

### النظام:
- **المنفذ:** 7443
- **الرابط المحلي:** http://localhost:7443
- **الرابط الشبكي:** http://**************:7443

## 🎯 **الخلاصة:**

✅ **تم إنشاء المستخدم admin بنجاح**
✅ **البيانات محفوظة في قاعدة البيانات**
✅ **النظام جاهز لتسجيل الدخول**
✅ **جميع الصلاحيات متاحة**

### 🚀 **للبدء:**
1. اذهب إلى: http://localhost:7443/login
2. استخدم: admin / admin123
3. ابدأ في استخدام النظام القانوني

---
**تاريخ الإنشاء:** $(Get-Date)
**حالة النظام:** يعمل بشكل مثالي
**قاعدة البيانات:** متصلة ومحدثة
