import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {
    console.log('🔧 بدء الإعداد التدريجي...')

    // اختبار الاتصال
    await query('SELECT 1')
    

    const results = []

    // الخطوة 1: إنشاء جدول دليل الحسابات
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS chart_of_accounts (
          id SERIAL PRIMARY KEY,
          accno VARCHAR(20) UNIQUE NOT NULL,
          description TEXT NOT NULL,
          charttype CHAR(1) DEFAULT 'A',
          category CHAR(1),
          contra BOOLEAN DEFAULT FALSE,
          tax BOOLEAN DEFAULT FALSE,
          link TEXT,
          heading INTEGER,
          obsolete BOOLEAN DEFAULT FALSE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)
      results.push('✅ جدول دليل الحسابات')
    } catch (error) {
      results.push(`❌ جدول دليل الحسابات: ${error}`)
    }

    // الخطوة 2: إنشاء جدول القيود العامة
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS gl (
          id SERIAL PRIMARY KEY,
          reference TEXT,
          description TEXT,
          transdate DATE NOT NULL,
          person_id INTEGER,
          notes TEXT,
          approved BOOLEAN DEFAULT FALSE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)
      results.push('✅ جدول القيود العامة')
    } catch (error) {
      results.push(`❌ جدول القيود العامة: ${error}`)
    }

    // الخطوة 3: إنشاء جدول تفاصيل القيود
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS acc_trans (
          entry_id SERIAL PRIMARY KEY,
          trans_id INTEGER REFERENCES gl(id),
          chart_id INTEGER REFERENCES chart_of_accounts(id),
          amount DECIMAL(20,8) NOT NULL,
          transdate DATE NOT NULL,
          source TEXT,
          memo TEXT,
          approved BOOLEAN DEFAULT FALSE
        )
      `)
      results.push('✅ جدول تفاصيل القيود')
    } catch (error) {
      results.push(`❌ جدول تفاصيل القيود: ${error}`)
    }

    // الخطوة 4: إدراج حسابات أساسية
    try {
      const basicAccounts = [
        { accno: '1110', description: 'النقدية في الصندوق', category: 'A' },
        { accno: '1120', description: 'حساب جاري - البنك', category: 'A' },
        { accno: '1210', description: 'حسابات العملاء', category: 'A' },
        { accno: '2110', description: 'حسابات الموردين', category: 'L' },
        { accno: '3100', description: 'رأس المال', category: 'Q' },
        { accno: '4110', description: 'أتعاب استشارات قانونية', category: 'I' },
        { accno: '5110', description: 'رواتب وأجور', category: 'E' }
      ]

      for (const account of basicAccounts) {
        await query(`
          INSERT INTO chart_of_accounts (accno, description, category)
          VALUES ($1, $2, $3)
          ON CONFLICT (accno) DO NOTHING
        `, [account.accno, account.description, account.category])
      }
      results.push('✅ الحسابات الأساسية')
    } catch (error) {
      results.push(`❌ الحسابات الأساسية: ${error}`)
    }

    // الخطوة 5: إحصائيات
    try {
      const accountsCount = await query('SELECT COUNT(*) as count FROM chart_of_accounts')
      const glCount = await query('SELECT COUNT(*) as count FROM gl')
      const transCount = await query('SELECT COUNT(*) as count FROM acc_trans')
      
      results.push(`📊 الحسابات: ${accountsCount.rows[0].count}`)
      results.push(`📊 القيود: ${glCount.rows[0].count}`)
      results.push(`📊 التفاصيل: ${transCount.rows[0].count}`)
    } catch (error) {
      results.push(`❌ الإحصائيات: ${error}`)
    }

    return NextResponse.json({
      success: true,
      message: 'تم الإعداد التدريجي بنجاح',
      data: {
        steps: results,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('خطأ في الإعداد التدريجي:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في الإعداد التدريجي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
