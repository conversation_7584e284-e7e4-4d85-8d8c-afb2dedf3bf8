// اختبار عرض القضايا والإضافة
async function testIssuesDisplay() {
  console.log('🧪 اختبار عرض القضايا والإضافة...\n');

  // 1. اختبار API جلب القضايا
  console.log('1️⃣ اختبار API جلب القضايا:');
  try {
    const response = await fetch('http://localhost:7443/api/issues');
    const result = await response.json();
    
    console.log('   📊 حالة الاستجابة:', response.status);
    console.log('   📋 نجح الطلب:', result.success);
    
    if (result.success) {
      console.log(`   ✅ تم جلب ${result.data.length} قضية`);
      
      if (result.data.length > 0) {
        const firstIssue = result.data[0];
        console.log('   📄 أول قضية:');
        console.log(`      - الرقم: ${firstIssue.case_number}`);
        console.log(`      - العنوان: ${firstIssue.title}`);
        console.log(`      - الموكل: ${firstIssue.client_name}`);
        console.log(`      - الهاتف: ${firstIssue.client_phone || 'غير محدد'}`);
        console.log(`      - المحكمة: ${firstIssue.court_name || 'غير محدد'}`);
        console.log(`      - الحالة: ${firstIssue.status}`);
      } else {
        console.log('   ⚠️ لا توجد قضايا في قاعدة البيانات');
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الشبكة: ${error.message}`);
  }

  console.log('');

  // 2. اختبار إضافة قضية جديدة
  console.log('2️⃣ اختبار إضافة قضية جديدة:');
  try {
    const testIssue = {
      case_number: `TEST-${Date.now()}`,
      title: 'قضية اختبار',
      description: 'هذه قضية للاختبار',
      client_name: 'عميل تجريبي',
      client_phone: '777777777',
      court_name: 'محكمة تجريبية',
      issue_type: 'قضية مدنية',
      status: 'new',
      amount: 1000,
      notes: 'ملاحظات تجريبية',
      contract_method: 'بالجلسة',
      contract_date: new Date().toISOString().split('T')[0]
    };

    console.log('   📤 إرسال بيانات القضية...');
    
    const response = await fetch('http://localhost:7443/api/issues', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testIssue)
    });

    const result = await response.json();
    
    console.log('   📊 حالة الاستجابة:', response.status);
    console.log('   📋 نجح الطلب:', result.success);
    
    if (result.success) {
      console.log('   ✅ تم إضافة القضية بنجاح');
      console.log(`   📄 القضية المضافة: ${result.data.case_number} - ${result.data.title}`);
      
      // حفظ معرف القضية للاختبارات اللاحقة
      global.testIssueId = result.data.id;
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الشبكة: ${error.message}`);
  }

  console.log('');

  // 3. اختبار جلب القضايا مرة أخرى للتأكد من الإضافة
  console.log('3️⃣ اختبار جلب القضايا بعد الإضافة:');
  try {
    const response = await fetch('http://localhost:7443/api/issues');
    const result = await response.json();
    
    if (result.success) {
      console.log(`   ✅ تم جلب ${result.data.length} قضية`);
      
      // البحث عن القضية المضافة
      const addedIssue = result.data.find(issue => issue.id === global.testIssueId);
      if (addedIssue) {
        console.log('   🎯 تم العثور على القضية المضافة:');
        console.log(`      - الرقم: ${addedIssue.case_number}`);
        console.log(`      - العنوان: ${addedIssue.title}`);
        console.log(`      - الموكل: ${addedIssue.client_name}`);
        console.log(`      - الهاتف: ${addedIssue.client_phone}`);
      } else {
        console.log('   ⚠️ لم يتم العثور على القضية المضافة');
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الشبكة: ${error.message}`);
  }

  console.log('');

  // 4. اختبار جلب القضايا غير الموزعة
  console.log('4️⃣ اختبار جلب القضايا غير الموزعة:');
  try {
    const response = await fetch('http://localhost:7443/api/issues?undistributed=true');
    const result = await response.json();
    
    if (result.success) {
      console.log(`   ✅ تم جلب ${result.data.length} قضية غير موزعة`);
      
      if (result.data.length > 0) {
        console.log('   📋 أول قضية غير موزعة:');
        const firstUndistributed = result.data[0];
        console.log(`      - الرقم: ${firstUndistributed.case_number}`);
        console.log(`      - العنوان: ${firstUndistributed.title}`);
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الشبكة: ${error.message}`);
  }

  console.log('\n✅ انتهى اختبار عرض القضايا والإضافة');
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('   1. ✅ إصلاح API جلب القضايا (إزالة خطأ ORDER BY)');
  console.log('   2. ✅ إضافة منطق POST للقضايا الجديدة');
  console.log('   3. ✅ إضافة client_phone في جميع العمليات');
  console.log('   4. ✅ دعم القضايا غير الموزعة');
  console.log('   5. ✅ تحسين معالجة الأخطاء والتسجيل');
}

// تشغيل الاختبار
testIssuesDisplay().catch(console.error);
