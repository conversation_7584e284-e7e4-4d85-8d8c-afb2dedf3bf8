"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    var _currencies_find;\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [dbError, setDbError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currencies, setCurrencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_id: '',\n        court_name: '',\n        issue_type_id: '',\n        issue_type: '',\n        status: 'pending',\n        case_amount: '',\n        currency_id: '1',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n    });\n    // جلب المحاكم من قاعدة البيانات\n    const fetchCourts = async ()=>{\n        try {\n            const response = await fetch('/api/courts');\n            const result = await response.json();\n            if (result.success) {\n                setCourts(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching courts:', error);\n        }\n    };\n    // جلب العملات من قاعدة البيانات\n    const fetchCurrencies = async ()=>{\n        try {\n            const response = await fetch('/api/currencies');\n            const result = await response.json();\n            if (result.success) {\n                setCurrencies(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching currencies:', error);\n        }\n    };\n    // حساب المبلغ بالريال اليمني\n    const calculateAmountYer = (amount, currencyId)=>{\n        const numAmount = parseFloat(amount) || 0;\n        const currency = currencies.find((c)=>c.id.toString() === currencyId);\n        if (!currency) return numAmount;\n        if (currency.is_base_currency) {\n            return numAmount;\n        }\n        return numAmount * currency.exchange_rate;\n    };\n    // جلب البيانات من قاعدة البيانات\n    const fetchIssues = async ()=>{\n        setIsLoading(true);\n        setDbError(null);\n        try {\n            const response = await fetch('/api/issues');\n            const result = await response.json();\n            if (result.success) {\n                setIssues(result.data);\n            } else {\n                setDbError(result.error || 'فشل في جلب بيانات القضايا');\n                setIssues([]);\n            }\n        } catch (error) {\n            console.error('Network error:', error);\n            setDbError('فشل في الاتصال بقاعدة البيانات');\n            setIssues([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            fetchIssues();\n            fetchCourts();\n            fetchCurrencies();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'new':\n                return 'bg-purple-100 text-purple-800';\n            case 'pending':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'new':\n                return 'جديدة';\n            case 'pending':\n                return 'معلقة';\n            case 'in_progress':\n                return 'قيد المعالجة';\n            case 'completed':\n                return 'مكتملة';\n            case 'cancelled':\n                return 'ملغية';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const handleAddNew = ()=>{\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_id: '',\n            court_name: '',\n            issue_type_id: '',\n            issue_type: '',\n            status: 'pending',\n            case_amount: '',\n            currency_id: '1',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n    };\n    const handleClientChange = (clientId, clientData)=>{\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: clientData ? clientData.name : '',\n            client_phone: clientData ? clientData.phone : ''\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                // إضافة رقم المستخدم تلقائياً (يمكن الحصول عليه من الجلسة لاحقاً)\n                const dataToSubmit = {\n                    ...formData,\n                    created_by: 1 // رقم المستخدم الافتراضي - يجب تحديثه ليأخذ من الجلسة\n                };\n                console.log('Data being submitted for new issue:', dataToSubmit);\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                // إرسال جميع البيانات للتحديث\n                const dataToSubmit = {\n                    ...formData\n                };\n                console.log('Sending data to API:', dataToSubmit);\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    const handleViewIssue = (issue)=>{\n        console.log('handleViewIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        // معالجة البيانات الفارغة أو null\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || 'لا يوجد وصف',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || 'غير محدد',\n            court_id: issue.court_id ? issue.court_id.toString() : '',\n            court_name: issue.court_name || 'غير محدد',\n            issue_type_id: issue.issue_type_id ? issue.issue_type_id.toString() : '',\n            issue_type: issue.issue_type || 'عام',\n            status: issue.status || 'new',\n            case_amount: issue.case_amount ? issue.case_amount.toString() : '0',\n            currency_id: issue.currency_id ? issue.currency_id.toString() : '1',\n            notes: issue.notes || 'لا توجد ملاحظات',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleViewIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    const handleEditIssue = (issue)=>{\n        console.log('handleEditIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        // معالجة البيانات الفارغة أو null\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || 'لا يوجد وصف',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || 'غير محدد',\n            court_name: issue.court_name || 'غير محدد',\n            issue_type: issue.issue_type || 'عام',\n            status: issue.status || 'new',\n            amount: issue.amount ? issue.amount.toString() : '0',\n            case_amount: issue.case_amount ? issue.case_amount.toString() : '0',\n            currency_id: issue.currency_id ? issue.currency_id.toString() : '1',\n            start_date: issue.start_date ? new Date(issue.start_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n            notes: issue.notes || 'لا توجد ملاحظات',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleEditIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    const handleDeleteIssue = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه القضية؟')) return;\n        try {\n            const response = await fetch(\"/api/issues/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert('تم حذف القضية بنجاح');\n                fetchIssues();\n            } else {\n                alert(result.error || 'فشل في حذف القضية');\n            }\n        } catch (error) {\n            console.error('Error deleting issue:', error);\n            alert('حدث خطأ في حذف القضية');\n        }\n    };\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-6 bg-white min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إدارة القضايا\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mt-1\",\n                                        children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAddNew,\n                                className: \"bg-emerald-600 hover:bg-emerald-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إضافة قضية جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"إجمالي القضايا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.new\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا جديدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قيد المعالجة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.completed\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا مكتملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"new\",\n                                                    children: \"جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pending\",\n                                                    children: \"معلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"in_progress\",\n                                                    children: \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة القضايا (\",\n                                        filteredIssues.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"رقم القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"الموكل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المبلغ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredIssues.map((issue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium text-blue-600\",\n                                                                children: issue.case_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium\",\n                                                                children: issue.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        issue.client_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: issue.client_phone || 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: issue.court_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-blue-50 text-blue-700\",\n                                                                    children: issue.issue_type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: issue.contract_method === 'بالجلسة' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700',\n                                                                    children: issue.contract_method\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    className: getStatusColor(issue.status),\n                                                                    children: getStatusText(issue.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4 font-medium text-green-600\",\n                                                                children: [\n                                                                    issue.amount ? Math.floor(issue.amount).toLocaleString() : '0',\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleViewIssue(issue),\n                                                                            className: \"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300\",\n                                                                            title: \"عرض تفاصيل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 566,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleEditIssue(issue),\n                                                                            className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300\",\n                                                                            title: \"تعديل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleDeleteIssue(issue.id),\n                                                                            className: \"bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300\",\n                                                                            title: \"حذف القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, issue.id, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto border-t-4 border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this),\n                                                modalType === 'add' && '📋 إضافة قضية جديدة',\n                                                modalType === 'edit' && '✏️ تعديل القضية',\n                                                modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsModalOpen(false),\n                                            className: \"hover:bg-red-50 hover:text-red-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-10 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_number\",\n                                                            className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"case_number\",\n                                                            value: formData.case_number,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    case_number: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل رقم القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-7\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"title\",\n                                                            value: formData.title,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    title: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل عنوان القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"description\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"description\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل وصف مفصل للقضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDC64 الموكل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__.ClientSelect, {\n                                                                value: formData.client_id,\n                                                                onChange: handleClientChange,\n                                                                label: \"\",\n                                                                placeholder: \"اختر الموكل...\",\n                                                                required: true,\n                                                                disabled: modalType === 'view'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"court_name\",\n                                                            className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"court_name\",\n                                                            value: formData.court_name || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    court_name: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المحكمة...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: court.name,\n                                                                        children: [\n                                                                            court.name,\n                                                                            \" - \",\n                                                                            court.governorate_name\n                                                                        ]\n                                                                    }, court.id, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_method\",\n                                                            className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC4 طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"contract_method\",\n                                                            value: formData.contract_method || \"بالجلسة\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_method: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالجلسة\",\n                                                                    children: \"بالجلسة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالعقد\",\n                                                                    children: \"بالعقد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDD04 حالة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            value: formData.status || \"new\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"new\",\n                                                                    children: \"جديدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"pending\",\n                                                                    children: \"معلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in_progress\",\n                                                                    children: \"قيد المعالجة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"completed\",\n                                                                    children: \"مكتملة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cancelled\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"issue_type\",\n                                                            className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"⚖️ نوع القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"issue_type\",\n                                                            value: formData.issue_type || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    issue_type: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر نوع القضية...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"مدنية\",\n                                                                    children: \"مدنية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"تجارية\",\n                                                                    children: \"تجارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"جنائية\",\n                                                                    children: \"جنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"أحوال شخصية\",\n                                                                    children: \"أحوال شخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"عمالية\",\n                                                                    children: \"عمالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"إدارية\",\n                                                                    children: \"إدارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 753,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"start_date\",\n                                                            className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC5 تاريخ بداية القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"start_date\",\n                                                            type: \"date\",\n                                                            value: formData.start_date || new Date().toISOString().split('T')[0],\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    start_date: e.target.value\n                                                                }),\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-purple-50 border-purple-300 focus:border-purple-500 focus:bg-white transition-colors', \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"currency_id\",\n                                                            className: \"text-sm font-semibold text-yellow-700 bg-yellow-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB1 العملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"currency_id\",\n                                                            value: formData.currency_id || '1',\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    currency_id: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-yellow-50 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: currency.id,\n                                                                    children: [\n                                                                        currency.currency_name,\n                                                                        \" (\",\n                                                                        currency.symbol,\n                                                                        \")\",\n                                                                        currency.is_base_currency && ' - الأساسية'\n                                                                    ]\n                                                                }, currency.id, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_date\",\n                                                            className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"contract_date\",\n                                                            type: \"date\",\n                                                            value: formData.contract_date,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_date: e.target.value\n                                                                }),\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors', \" text-sm\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_amount\",\n                                                            className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"case_amount\",\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    min: \"0\",\n                                                                    value: formData.case_amount || '',\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            case_amount: e.target.value\n                                                                        }),\n                                                                    readOnly: modalType === 'view',\n                                                                    className: \"h-10 pr-12 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors', \" text-sm\"),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: ((_currencies_find = currencies.find((c)=>c.id.toString() === (formData.currency_id || '1'))) === null || _currencies_find === void 0 ? void 0 : _currencies_find.symbol) || 'ر.ي'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"amount_yer\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB5 المبلغ بالريال اليمني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"amount_yer\",\n                                                                    type: \"text\",\n                                                                    value: calculateAmountYer(formData.case_amount || '0', formData.currency_id || '1').toLocaleString('ar-YE', {\n                                                                        minimumFractionDigits: 2,\n                                                                        maximumFractionDigits: 2\n                                                                    }),\n                                                                    readOnly: true,\n                                                                    className: \"h-10 pr-12 bg-gray-50 text-gray-700 text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"ر.ي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 848,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-gray-500\",\n                                                            children: \"يُحسب تلقائياً باستخدام سعر الصرف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"notes\",\n                                                        value: formData.notes,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                notes: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                            children: [\n                                                modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setIsModalOpen(false),\n                                                    className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                    children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, \"modal-\".concat(modalType, \"-\").concat((editingIssue === null || editingIssue === void 0 ? void 0 : editingIssue.id) || 'new'), true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 379,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"iKwW0t2JTBl+s85T8uJ6HYAN8m0=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});