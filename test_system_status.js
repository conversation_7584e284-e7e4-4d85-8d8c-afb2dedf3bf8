// اختبار حالة النظام
const { Pool } = require('pg');

async function testSystemStatus() {
  console.log('🔍 اختبار حالة النظام...\n');

  // قواعد البيانات
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 اختبار قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. اختبار الاتصال
      await pool.query('SELECT NOW()');
      console.log('   ✅ الاتصال بقاعدة البيانات يعمل');

      // 2. فحص الجداول الأساسية
      const tables = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `);

      console.log('   📊 الجداول الموجودة:');
      tables.rows.forEach(table => {
        console.log(`      - ${table.table_name}`);
      });

      // 3. فحص البيانات
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as issues_count,
          (SELECT COUNT(*) FROM clients) as clients_count,
          (SELECT COUNT(*) FROM issue_types) as types_count,
          (SELECT COUNT(*) FROM courts) as courts_count,
          (SELECT COUNT(*) FROM hearings) as hearings_count
      `);

      const data = stats.rows[0];
      console.log('   📈 إحصائيات البيانات:');
      console.log(`      - القضايا: ${data.issues_count}`);
      console.log(`      - العملاء: ${data.clients_count}`);
      console.log(`      - أنواع القضايا: ${data.types_count}`);
      console.log(`      - المحاكم: ${data.courts_count}`);
      console.log(`      - الجلسات: ${data.hearings_count}`);

      // 4. فحص العلاقات
      const relations = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
      `);

      console.log('   🔗 العلاقات الخارجية:');
      relations.rows.forEach(rel => {
        console.log(`      - ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}`);
      });

      // 5. فحص الـ triggers
      const triggers = await pool.query(`
        SELECT trigger_name, event_object_table
        FROM information_schema.triggers
        WHERE event_object_table IN ('issues', 'hearings')
      `);

      console.log('   🔄 الـ Triggers:');
      triggers.rows.forEach(trigger => {
        console.log(`      - ${trigger.trigger_name} على ${trigger.event_object_table}`);
      });

    } catch (error) {
      console.error(`   ❌ خطأ في قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(40) + '\n');
  }

  console.log('✅ تم الانتهاء من اختبار حالة النظام');
}

// تشغيل الاختبار
testSystemStatus().catch(console.error);
