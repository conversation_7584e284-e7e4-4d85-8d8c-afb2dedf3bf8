@echo off
chcp 65001 >nul
echo ========================================
echo   استيراد قاعدة البيانات - النظام القانوني
echo ========================================
echo.

echo [1/4] التحقق من PostgreSQL...
psql --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PostgreSQL غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت PostgreSQL 13+ من: https://www.postgresql.org/
    pause
    exit /b 1
)

echo [2/4] إدخال إعدادات قاعدة البيانات...
set /p DB_HOST="عنوان الخادم (افتراضي: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT="المنفذ (افتراضي: 5432): "
if "%DB_PORT%"=="" set DB_PORT=5432

set /p DB_USER="اسم المستخدم (افتراضي: postgres): "
if "%DB_USER%"=="" set DB_USER=postgres

set /p DB_NAME="اسم قاعدة البيانات (افتراضي: legal_system_production): "
if "%DB_NAME%"=="" set DB_NAME=legal_system_production

echo.
echo [3/4] إنشاء قاعدة البيانات...
createdb -h %DB_HOST% -p %DB_PORT% -U %DB_USER% %DB_NAME%
if errorlevel 1 (
    echo تحذير: قد تكون قاعدة البيانات موجودة مسبقاً
)

echo [4/4] استيراد البيانات...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "legal_system_complete_2025-08-27.sql"

if errorlevel 1 (
    echo ERROR: فشل في استيراد قاعدة البيانات
    echo راجع الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ========================================
echo   تم الاستيراد بنجاح!
echo ========================================
echo.
echo الخطوات التالية:
echo 1. انسخ ملفات النظام إلى الخادم
echo 2. أنشئ ملف .env.local مع إعدادات قاعدة البيانات
echo 3. شغل: npm install && npm run build && npm start
echo.
pause