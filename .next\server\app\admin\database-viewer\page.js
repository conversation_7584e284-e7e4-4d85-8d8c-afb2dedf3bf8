(()=>{var e={};e.id=9399,e.ids=[9399],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44949:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\database-viewer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\admin\\database-viewer\\page.tsx","default")},52448:(e,s,t)=>{Promise.resolve().then(t.bind(t,44949))},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},62566:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(60687),r=t(43210),l=t(44493),i=t(29523),n=t(96834),c=t(61611),d=t(78122);let o=(0,t(62688).A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);var m=t(53411),x=t(13861);function h(){let[e,s]=(0,r.useState)([]),[t,h]=(0,r.useState)(""),[p,u]=(0,r.useState)([]),[j,v]=(0,r.useState)([]),[b,f]=(0,r.useState)(!1),[y,g]=(0,r.useState)({}),N=async()=>{f(!0);try{let e=await fetch("/api/database/tables"),t=await e.json();t.success&&s(t.tables)}catch(e){console.error("Error fetching tables:",e)}finally{f(!1)}},w=async()=>{try{let e=await fetch("/api/database/stats"),s=await e.json();s.success&&g(s.stats)}catch(e){console.error("Error fetching stats:",e)}};return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",dir:"rtl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"مستعرض قاعدة البيانات"})]}),(0,a.jsxs)(i.$,{onClick:()=>{N(),w()},disabled:b,children:[(0,a.jsx)(d.A,{className:`h-4 w-4 ml-2 ${b?"animate-spin":""}`}),"تحديث"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي الجداول"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.length})]}),(0,a.jsx)(o,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"حجم قاعدة البيانات"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:y.database_size||"N/A"})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي السجلات"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:y.total_rows||"N/A"})]}),(0,a.jsx)(c.A,{className:"h-8 w-8 text-purple-600"})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"الجدول المحدد"}),(0,a.jsx)("p",{className:"text-lg font-bold",children:t||"لا يوجد"})]}),(0,a.jsx)(x.A,{className:"h-8 w-8 text-orange-600"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(o,{className:"h-5 w-5 ml-2"}),"الجداول (",e.length,")"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:e.map(e=>(0,a.jsxs)("div",{className:`p-3 rounded-lg border cursor-pointer transition-colors ${t===e.table_name?"bg-blue-50 border-blue-200":"hover:bg-gray-50"}`,onClick:()=>h(e.table_name),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:e.table_name}),(0,a.jsxs)(n.E,{variant:"secondary",children:[e.row_count," سجل"]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:[e.column_count," عمود • ",e.table_size]})]},e.table_name))})})]}),(0,a.jsxs)(l.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 ml-2"}),t?`تفاصيل جدول: ${t}`:"اختر جدولاً لعرض التفاصيل"]})}),(0,a.jsx)(l.Wu,{children:t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold mb-2",children:["الأعمدة (",p.length,")"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto",children:p.map(e=>(0,a.jsxs)("div",{className:"p-2 bg-gray-50 rounded",children:[(0,a.jsx)("div",{className:"font-medium",children:e.column_name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[e.data_type," • ","YES"===e.is_nullable?"اختياري":"مطلوب"]})]},e.column_name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"البيانات (أول 50 سجل)"}),(0,a.jsx)("div",{className:"overflow-x-auto max-h-64 overflow-y-auto",children:(0,a.jsxs)("table",{className:"min-w-full border border-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsx)("tr",{children:p.slice(0,5).map(e=>(0,a.jsx)("th",{className:"px-3 py-2 text-right text-xs font-medium text-gray-500 border-b",children:e.column_name},e.column_name))})}),(0,a.jsx)("tbody",{children:j.slice(0,10).map((e,s)=>(0,a.jsx)("tr",{className:"hover:bg-gray-50",children:p.slice(0,5).map(s=>(0,a.jsxs)("td",{className:"px-3 py-2 text-sm border-b",children:[String(e[s.column_name]||"").slice(0,50),String(e[s.column_name]||"").length>50?"...":""]},s.column_name))},s))})]})})]})]}):(0,a.jsx)("div",{className:"text-center text-gray-500 py-8",children:"اختر جدولاً من القائمة لعرض تفاصيله"})})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81824:(e,s,t)=>{Promise.resolve().then(t.bind(t,62566))},83950:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["database-viewer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44949)),"D:\\mohaminew\\src\\app\\admin\\database-viewer\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\admin\\database-viewer\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/database-viewer/page",pathname:"/admin/database-viewer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,8409,7932],()=>t(83950));module.exports=a})();