import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

// تحديد قاعدة البيانات بناءً على المنفذ أو متغير البيئة
const getDatabaseName = () => {
  // إذا كان هناك متغير DOTENV_CONFIG_PATH يشير لـ rubaie
  if (process.env.DOTENV_CONFIG_PATH?.includes('rubaie')) {
    return 'rubaie'
  }
  // أو إذا كان المنفذ 8914
  if (process.env.PORT === '8914') {
    return 'rubaie'
  }
  // أو إذا كان DB_NAME محدد صراحة
  if (process.env.DB_NAME) {
    return process.env.DB_NAME
  }
  // الافتراضي
  return 'mohammi'
}

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: getDatabaseName(),
  password: process.env.DB_PASSWORD || 'yemen123',
  port: parseInt(process.env.DB_PORT || '5432'),
})

export async function query(text: string, params?: any[]) {
  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } finally {
    client.release()
  }
}

export { pool }
