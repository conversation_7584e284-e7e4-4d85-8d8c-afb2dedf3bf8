import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

// تحديد قاعدة البيانات من متغير البيئة (يتم تعيينه بواسطة Instance Manager)
const getDatabaseName = () => {
  // استخدام DB_NAME المحدد من Instance Manager
  if (process.env.DB_NAME) {
    return process.env.DB_NAME
  }

  // الافتراضي إذا لم يتم تحديد قاعدة البيانات
  console.warn('⚠️ لم يتم تحديد DB_NAME، سيتم استخدام قاعدة البيانات الافتراضية: mohammi')
  return 'mohammi'
}

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: getDatabaseName(),
  password: process.env.DB_PASSWORD || 'yemen123',
  port: parseInt(process.env.DB_PORT || '5432'),
})

export async function query(text: string, params?: any[]) {
  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } finally {
    client.release()
  }
}

export { pool }
