import { Pool } from 'pg'
import fs from 'fs'
import path from 'path'

// تحميل ملف التوجيه
let routingConfig: any = null
try {
  const configPath = path.join(process.cwd(), 'routing.config.json')
  const configData = fs.readFileSync(configPath, 'utf8')
  routingConfig = JSON.parse(configData)
} catch (error) {
  console.error('❌ خطأ في تحميل ملف التوجيه:', error)
}

// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {
  throw new Error('DB_PASSWORD environment variable is required')
}

// تحديد قاعدة البيانات بناءً على المنفذ
const getDatabaseConfig = () => {
  const port = process.env.PORT || '7443'

  if (routingConfig && routingConfig.routes[port]) {
    const route = routingConfig.routes[port]
    const defaultConfig = routingConfig.default_config

    return {
      database: route.database,
      user: defaultConfig.db_user,
      host: defaultConfig.db_host,
      password: process.env.DB_PASSWORD || defaultConfig.db_password,
      port: defaultConfig.db_port
    }
  }

  // الافتراضي إذا لم يتم العثور على التوجيه
  console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`)
  return {
    database: 'mohammi',
    user: 'postgres',
    host: 'localhost',
    password: process.env.DB_PASSWORD || 'yemen123',
    port: 5432
  }
}

const dbConfig = getDatabaseConfig()
const pool = new Pool(dbConfig)

export async function query(text: string, params?: any[]) {
  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } finally {
    client.release()
  }
}

export { pool }
