import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الرسائل
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversationId')

    if (!conversationId) {
      return NextResponse.json(
        { success: false, error: 'معرف المحادثة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      SELECT
        m.*,
        CASE
          WHEN m.sender_type = 'user' THEN u.username
          WHEN m.sender_type = 'client' THEN c.name
        END as sender_name
      FROM messages m
      LEFT JOIN users u ON m.sender_type = 'user' AND m.sender_id = u.id
      LEFT JOIN clients c ON m.sender_type = 'client' AND m.sender_id = c.id
      WHERE m.conversation_id = $1
      ORDER BY m.created_at ASC
    `, [conversationId])

    return NextResponse.json({
      success: true,
      data: result.rows
    })

  } catch (error) {
    console.error('Error fetching messages:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الرسائل' },
      { status: 500 }
    )
  }
}

// POST - إرسال رسالة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      conversationId,
      senderType,
      senderId,
      messageText,
      messageType = 'text',
      fileUrl,
      fileName,
      fileSize,
      replyToMessageId
    } = body

    if (!conversationId || !senderType) {
      return NextResponse.json(
        { success: false, error: 'معرف المحادثة ونوع المرسل مطلوبان' },
        { status: 400 }
      )
    }

    // للذكاء الاصطناعي، senderId يكون 0
    const finalSenderId = senderType === 'ai' ? 0 : senderId

    if (senderType !== 'ai' && !finalSenderId) {
      return NextResponse.json(
        { success: false, error: 'معرف المرسل مطلوب' },
        { status: 400 }
      )
    }

    if (!messageText && !fileUrl) {
      return NextResponse.json(
        { success: false, error: 'نص الرسالة أو الملف مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المحادثة
    const conversationCheck = await query(`
      SELECT id FROM conversations WHERE id = $1
    `, [conversationId])

    if (conversationCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحادثة غير موجودة' },
        { status: 404 }
      )
    }

    // إدراج الرسالة الجديدة
    const result = await query(`
      INSERT INTO messages (
        conversation_id,
        sender_type,
        sender_id,
        message_text,
        message_type,
        file_url,
        file_name,
        file_size,
        reply_to_message_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      conversationId,
      senderType,
      finalSenderId,
      messageText,
      messageType,
      fileUrl,
      fileName,
      fileSize,
      replyToMessageId
    ])

    // إنشاء إشعار للطرف الآخر
    const conversation = await query(`
      SELECT client_id, user_id FROM conversations WHERE id = $1
    `, [conversationId])

    if (conversation.rows.length > 0) {
      const conv = conversation.rows[0]
      let recipientType = ''
      let recipientId = 0

      if (senderType === 'user') {
        recipientType = 'client'
        recipientId = conv.client_id
      } else {
        recipientType = 'user'
        recipientId = conv.user_id
      }

      // إدراج الإشعار
      await query(`
        INSERT INTO notifications (
          recipient_type,
          recipient_id,
          sender_type,
          sender_id,
          notification_type,
          title,
          content,
          related_id
        )
        VALUES ($1, $2, $3, $4, 'message', 'رسالة جديدة', $5, $6)
      `, [
        recipientType,
        recipientId,
        senderType,
        senderId,
        messageText?.substring(0, 100) || 'ملف مرفق',
        result.rows[0].id
      ])
    }

    // تشغيل الرد التلقائي للذكاء الاصطناعي (بدون انتظار)
    if (senderType === 'client' && messageText) {
      console.log('🤖 Triggering AI auto-reply for client message:', messageText)

      // تشغيل الرد التلقائي في الخلفية
      setTimeout(async () => {
        try {
          // استدعاء نموذج الذكاء الاصطناعي الحقيقي
          const aiResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:7443'}/api/ai/local-models`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message: messageText,
              model: 'groq-llama-8b', // استخدام Llama 3.1 8B (مجاني وسريع)
              conversationId: conversationId
            })
          })

          console.log('📡 AI Response status:', aiResponse.status)

          if (aiResponse.ok) {
            const aiData = await aiResponse.json()


            if (aiData.success && aiData.response) {
              // حفظ الرد الذكي الحقيقي في قاعدة البيانات
              await query(`
                INSERT INTO messages
                (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
                VALUES ($1, 'ai', 0, $2, 'text', CURRENT_TIMESTAMP)
              `, [conversationId, aiData.response])

               + '...')
              console.log('🎯 AI model used:', aiData.model)
            } else {
              console.error('❌ AI response success=false:', aiData.error)
              throw new Error(`AI response failed: ${aiData.error}`)
            }
          } else {
            const errorText = await aiResponse.text()
            console.error('❌ AI HTTP error:', aiResponse.status, errorText)
            throw new Error(`AI HTTP error: ${aiResponse.status}`)
          }
        } catch (error) {
          console.error('❌ Error with AI response:', error)
          console.log('⚠️ AI failed - no automatic response will be sent. User will need to respond manually.')
          // لا نرسل رد احتياطي - نترك المحامي يرد يدوياً
        }
      }, 3000) // تأخير 3 ثوان لإعطاء وقت للذكاء الاصطناعي
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إرسال الرسالة بنجاح'
    })

  } catch (error) {
    console.error('Error sending message:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إرسال الرسالة', details: error.message },
      { status: 500 }
    )
  }
}

// PUT - تحديث رسالة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, messageText, isRead } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الرسالة مطلوب' },
        { status: 400 }
      )
    }

    let updateFields = []
    let params = []
    let paramIndex = 1

    if (messageText !== undefined) {
      updateFields.push(`message_text = $${paramIndex}`)
      params.push(messageText)
      paramIndex++

      updateFields.push(`is_edited = true`)
    }

    if (isRead !== undefined) {
      updateFields.push(`is_read = $${paramIndex}`)
      params.push(isRead)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا توجد حقول للتحديث' },
        { status: 400 }
      )
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)
    params.push(id)

    const result = await query(`
      UPDATE messages
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `, params)

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرسالة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الرسالة بنجاح'
    })

  } catch (error) {
    console.error('Error updating message:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الرسالة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف رسالة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الرسالة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      DELETE FROM messages
      WHERE id = $1
      RETURNING *
    `, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرسالة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الرسالة بنجاح'
    })

  } catch (error) {
    console.error('Error deleting message:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الرسالة' },
      { status: 500 }
    )
  }
}
