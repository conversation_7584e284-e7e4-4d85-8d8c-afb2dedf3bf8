(()=>{var e={};e.id=5446,e.ids=[5446],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>l});var a=s(64939),n=s(29021),o=s.n(n),c=s(33873),i=s.n(c),u=e([a]);a=(u.then?(await u)():u)[0];let p=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],s=p.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new a.Pool(d);async function l(e,t){let s=await _.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31628:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a=s(96559),n=s(48088),o=s(37719),c=s(72034),i=e([c]);c=(i.then?(await i)():i)[0];let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/chart-of-accounts/link-tables/route",pathname:"/api/chart-of-accounts/link-tables",filename:"route",bundlePath:"app/api/chart-of-accounts/link-tables/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\chart-of-accounts\\link-tables\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:_}=l;function u(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},72034:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>u,GET:()=>c,POST:()=>i});var a=s(32190),n=s(5069),o=e([n]);async function c(){try{let e=await (0,n.P)(`
      SELECT 
        id,
        account_code,
        account_name,
        linked_table
      FROM chart_of_accounts 
      WHERE linked_table IS NOT NULL
      ORDER BY account_code
    `);return a.NextResponse.json({success:!0,data:{available_tables:[{table_name:"clients",display_name:"الموكلين",description:"ربط الحساب بجدول الموكلين",fields:["id","name","phone","email","address"]},{table_name:"employees",display_name:"الموظفين",description:"ربط الحساب بجدول الموظفين",fields:["id","name","position","phone","email"]},{table_name:"cost_centers",display_name:"مراكز التكلفة",description:"ربط الحساب بمراكز التكلفة",fields:["id","name","description"]}],linked_accounts:e.rows}})}catch(e){return console.error("Error fetching linkable tables:",e),a.NextResponse.json({success:!1,error:"فشل في جلب الجداول المتاحة"},{status:500})}}async function i(e){try{let{account_id:t,table_name:s}=await e.json();if(!t||!s)return a.NextResponse.json({success:!1,error:"معرف الحساب واسم الجدول مطلوبان"},{status:400});let r=await (0,n.P)("SELECT * FROM chart_of_accounts WHERE id = $1",[t]);if(0===r.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});if(!["clients","employees","cost_centers"].includes(s))return a.NextResponse.json({success:!1,error:"اسم الجدول غير صحيح"},{status:400});let o=await (0,n.P)(`
      UPDATE chart_of_accounts 
      SET 
        linked_table = $2,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[t,s]);return a.NextResponse.json({success:!0,data:o.rows[0],message:`تم ربط الحساب بجدول ${s} بنجاح`})}catch(e){return console.error("Error linking account to table:",e),a.NextResponse.json({success:!1,error:"فشل في ربط الحساب بالجدول"},{status:500})}}async function u(e){try{let{account_id:t}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});let s=await (0,n.P)(`
      UPDATE chart_of_accounts 
      SET 
        linked_table = NULL,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[t]);if(0===s.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return a.NextResponse.json({success:!0,data:s.rows[0],message:"تم إلغاء ربط الحساب بنجاح"})}catch(e){return console.error("Error unlinking account:",e),a.NextResponse.json({success:!1,error:"فشل في إلغاء ربط الحساب"},{status:500})}}n=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(31628));module.exports=r})();