// إصلاح نظام المبالغ وتحديث البيانات
const { Pool } = require('pg');

async function fixAmountSystem() {
  console.log('🔧 إصلاح نظام المبالغ وتحديث البيانات...\n');

  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    console.log('='.repeat(40));
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. نقل البيانات من amount إلى case_amount
      console.log('\n   📊 نقل البيانات من amount إلى case_amount:');
      
      const updateResult = await pool.query(`
        UPDATE issues 
        SET case_amount = amount
        WHERE case_amount IS NULL AND amount IS NOT NULL
        RETURNING case_number, amount, case_amount
      `);
      
      console.log(`      ✅ تم تحديث ${updateResult.rows.length} قضية`);
      
      if (updateResult.rows.length > 0) {
        console.log('      عينة من التحديثات:');
        updateResult.rows.slice(0, 3).forEach(row => {
          console.log(`         ${row.case_number}: ${row.amount} -> ${row.case_amount}`);
        });
      }

      // 2. تحديث الـ trigger ليعمل مع case_amount
      console.log('\n   🔄 تحديث الـ trigger:');
      
      const updateTriggerFunction = `
        CREATE OR REPLACE FUNCTION update_amount_yer()
        RETURNS TRIGGER AS $$
        DECLARE
          exchange_rate DECIMAL;
        BEGIN
          -- إذا كانت العملة هي الريال اليمني (ID = 1) أو غير محددة
          IF NEW.currency_id = 1 OR NEW.currency_id IS NULL THEN
            NEW.amount_yer := NEW.case_amount;
          ELSE
            -- الحصول على سعر الصرف
            SELECT c.exchange_rate INTO exchange_rate
            FROM currencies c
            WHERE c.id = NEW.currency_id AND c.is_active = TRUE;
            
            IF exchange_rate IS NOT NULL THEN
              NEW.amount_yer := NEW.case_amount * exchange_rate;
            ELSE
              NEW.amount_yer := NEW.case_amount;
            END IF;
          END IF;
          
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `;

      await pool.query(updateTriggerFunction);
      console.log('      ✅ تم تحديث دالة update_amount_yer');

      // 3. إعادة إنشاء الـ trigger
      const recreateTrigger = `
        DROP TRIGGER IF EXISTS trigger_update_amount_yer ON issues;
        CREATE TRIGGER trigger_update_amount_yer
          BEFORE INSERT OR UPDATE OF case_amount, currency_id ON issues
          FOR EACH ROW
          EXECUTE FUNCTION update_amount_yer();
      `;

      await pool.query(recreateTrigger);
      console.log('      ✅ تم إعادة إنشاء trigger_update_amount_yer');

      // 4. تحديث amount_yer لجميع القضايا الموجودة
      console.log('\n   💰 تحديث amount_yer لجميع القضايا:');
      
      const updateAmountYer = await pool.query(`
        UPDATE issues 
        SET amount_yer = CASE 
          WHEN COALESCE(currency_id, 1) = 1 THEN case_amount
          ELSE case_amount * COALESCE((SELECT exchange_rate FROM currencies WHERE id = COALESCE(currency_id, 1)), 1)
        END
        WHERE case_amount IS NOT NULL
        RETURNING case_number, case_amount, currency_id, amount_yer
      `);
      
      console.log(`      ✅ تم تحديث ${updateAmountYer.rows.length} قضية`);
      
      if (updateAmountYer.rows.length > 0) {
        console.log('      عينة من النتائج:');
        updateAmountYer.rows.slice(0, 3).forEach(row => {
          console.log(`         ${row.case_number}: ${row.case_amount} -> ${row.amount_yer} ر.ي`);
        });
      }

      // 5. فحص النتائج النهائية
      console.log('\n   📊 فحص النتائج النهائية:');
      
      const finalCheck = await pool.query(`
        SELECT 
          case_number,
          amount,
          case_amount,
          amount_yer,
          currency_id
        FROM issues 
        ORDER BY id
      `);

      console.log('      جميع القضايا بعد التحديث:');
      finalCheck.rows.forEach(row => {
        console.log(`         ${row.case_number}:`);
        console.log(`            - amount (قديم): ${row.amount}`);
        console.log(`            - case_amount (جديد): ${row.case_amount}`);
        console.log(`            - amount_yer (محسوب): ${row.amount_yer}`);
        console.log(`            - currency_id: ${row.currency_id}`);
      });

      // 6. إحصائيات
      console.log('\n   📈 إحصائيات:');
      
      const stats = await pool.query(`
        SELECT 
          COUNT(*) as total_issues,
          COUNT(CASE WHEN case_amount IS NOT NULL THEN 1 END) as issues_with_case_amount,
          COUNT(CASE WHEN amount_yer IS NOT NULL THEN 1 END) as issues_with_amount_yer,
          SUM(amount_yer) as total_amount_yer
        FROM issues
      `);

      const statistics = stats.rows[0];
      console.log(`      - إجمالي القضايا: ${statistics.total_issues}`);
      console.log(`      - قضايا بـ case_amount: ${statistics.issues_with_case_amount}`);
      console.log(`      - قضايا بـ amount_yer: ${statistics.issues_with_amount_yer}`);
      console.log(`      - إجمالي المبالغ بالريال: ${statistics.total_amount_yer || 0} ر.ي`);

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح نظام المبالغ');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تم نقل البيانات من amount إلى case_amount');
  console.log('2. ✅ تم تحديث الـ trigger ليعمل مع case_amount');
  console.log('3. ✅ تم حساب amount_yer تلقائياً لجميع القضايا');
  console.log('4. ✅ النظام الآن يستخدم:');
  console.log('   - case_amount: المبلغ بالعملة الأصلية');
  console.log('   - amount_yer: المبلغ بالريال اليمني (محسوب تلقائياً)');
  console.log('   - amount: العمود القديم (يمكن حذفه لاحقاً)');
}

// تشغيل الإصلاح
fixAmountSystem().catch(console.error);
