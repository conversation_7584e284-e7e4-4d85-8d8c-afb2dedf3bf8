(()=>{var t={};t.id=2318,t.ids=[2318],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.d(e,{P:()=>i});var n=a(64939),o=a(29021),s=a.n(o),r=a(33873),u=a.n(r),_=t([n]);n=(_.then?(await _)():_)[0];let l=null;try{let t=u().join(process.cwd(),"routing.config.json"),e=s().readFileSync(t,"utf8");l=JSON.parse(e)}catch(t){console.error("❌ خطأ في تحميل ملف التوجيه:",t)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let t=process.env.PORT||"7443";if(l&&l.routes[t]){let e=l.routes[t],a=l.default_config;return{database:e.database,user:a.db_user,host:a.db_host,password:process.env.DB_PASSWORD||a.db_password,port:a.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${t}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new n.Pool(d);async function i(t,e){let a=await p.connect();try{return await a.query(t,e)}finally{a.release()}}c()}catch(t){c(t)}})},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20156:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.r(e),a.d(e,{patchFetch:()=>_,routeModule:()=>i,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var n=a(96559),o=a(48088),s=a(37719),r=a(81342),u=t([r]);r=(u.then?(await u)():u)[0];let i=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/account-linking/create-system-accounts/route",pathname:"/api/accounting/account-linking/create-system-accounts",filename:"route",bundlePath:"app/api/accounting/account-linking/create-system-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\account-linking\\create-system-accounts\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:p}=i;function _(){return(0,s.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}c()}catch(t){c(t)}})},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:t=>{"use strict";t.exports=import("pg")},78335:()=>{},81342:(t,e,a)=>{"use strict";a.a(t,async(t,c)=>{try{a.r(e),a.d(e,{POST:()=>r});var n=a(32190),o=a(5069),s=t([o]);async function r(){try{let t=0,e=0,a=[];for(let c of[{account_code:"4000",account_name:"الإيرادات الرئيسية",account_type:"I",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!0,account_balance:0,linked_table:null,auto_create_sub_accounts:!1},{account_code:"5000",account_name:"المصروفات الرئيسية",account_type:"E",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!0,account_balance:0,linked_table:null,auto_create_sub_accounts:!1},{account_code:"3000",account_name:"رأس المال",account_type:"E",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!0,account_balance:0,linked_table:null,auto_create_sub_accounts:!1},{account_code:"1111",account_name:"الصندوق الرئيسي",account_type:"A",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!0,account_balance:0,linked_table:null,auto_create_sub_accounts:!1},{account_code:"1121",account_name:"حسابات العملاء",account_type:"A",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!1,account_balance:0,linked_table:"clients",auto_create_sub_accounts:!0},{account_code:"1122",account_name:"حسابات الموظفين",account_type:"A",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!1,account_balance:0,linked_table:"employees",auto_create_sub_accounts:!0},{account_code:"2500",account_name:"الحسابات الوسيطة",account_type:"L",account_level:1,parent_id:null,is_active:!0,accepts_transactions:!0,account_balance:0,linked_table:null,auto_create_sub_accounts:!1}])try{let a=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[c.account_code]);0===a.rows.length?(await (0,o.P)(`
            INSERT INTO chart_of_accounts 
            (account_code, account_name, account_type, account_level, parent_id, 
             is_active, accepts_transactions, account_balance, linked_table, auto_create_sub_accounts,
             created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `,[c.account_code,c.account_name,c.account_type,c.account_level,c.parent_id,c.is_active,c.accepts_transactions,c.account_balance,c.linked_table,c.auto_create_sub_accounts]),t++):(await (0,o.P)(`
            UPDATE chart_of_accounts 
            SET account_name = $1, account_type = $2, is_active = $3, 
                accepts_transactions = $4, linked_table = $5, 
                auto_create_sub_accounts = $6, updated_at = CURRENT_TIMESTAMP
            WHERE account_code = $7
          `,[c.account_name,c.account_type,c.is_active,c.accepts_transactions,c.linked_table,c.auto_create_sub_accounts,c.account_code]),e++),c.auto_create_sub_accounts&&c.linked_table&&await u(c.account_code,c.linked_table)}catch(t){console.error(`Error processing account ${c.account_code}:`,t),a.push(`خطأ في معالجة الحساب ${c.account_code}: ${t.message}`)}return n.NextResponse.json({success:!0,message:`تم إنشاء ${t} حساب جديد وتحديث ${e} حساب موجود`,details:{created:t,updated:e,errors:a}})}catch(t){return console.error("Error creating system accounts:",t),n.NextResponse.json({success:!1,error:"فشل في إنشاء الحسابات الأساسية"},{status:500})}}async function u(t,e){try{let a=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[t]);if(0===a.rows.length)throw Error(`Parent account ${t} not found`);let c=a.rows[0].id,n=[];for(let a of("clients"===e?n=(await (0,o.P)("SELECT id, name FROM clients WHERE status = $1",["active"])).rows:"employees"===e&&(n=(await (0,o.P)("SELECT id, name FROM employees WHERE status = $1",["active"])).rows),n)){let n=`${t}-${a.id.toString().padStart(4,"0")}`,s=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);0===s.rows.length&&await (0,o.P)(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, account_level, parent_id, 
           is_active, accepts_transactions, account_balance, linked_table, auto_create_sub_accounts,
           created_at, updated_at)
          VALUES ($1, $2, $3, 2, $4, true, true, 0, $5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `,[n,a.name,"A",c,e])}}catch(t){throw console.error(`Error creating sub-accounts for ${e}:`,t),t}}o=(s.then?(await s)():s)[0],c()}catch(t){c(t)}})},96487:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),c=e.X(0,[4447,580],()=>a(20156));module.exports=c})();