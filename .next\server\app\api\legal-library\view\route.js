/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/legal-library/view/route";
exports.ids = ["app/api/legal-library/view/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fview%2Froute&page=%2Fapi%2Flegal-library%2Fview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fview%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fview%2Froute&page=%2Fapi%2Flegal-library%2Fview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fview%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_legal_library_view_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/legal-library/view/route.ts */ \"(rsc)/./src/app/api/legal-library/view/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/legal-library/view/route\",\n        pathname: \"/api/legal-library/view\",\n        filename: \"route\",\n        bundlePath: \"app/api/legal-library/view/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\legal-library\\\\view\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_legal_library_view_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fview%2Froute&page=%2Fapi%2Flegal-library%2Fview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fview%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/legal-library/view/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/legal-library/view/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const fileId = searchParams.get('id');\n        if (!fileId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الملف مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // فك تشفير مسار الملف\n        const filePath = Buffer.from(fileId, 'base64').toString('utf-8');\n        // التحقق من وجود الملف\n        try {\n            await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(filePath);\n        } catch  {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الملف غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        const fileName = path__WEBPACK_IMPORTED_MODULE_2___default().basename(filePath);\n        const fileExtension = path__WEBPACK_IMPORTED_MODULE_2___default().extname(filePath);\n        // إذا كان ملف PDF، إرجاعه مباشرة للعرض\n        if (fileExtension === '.pdf') {\n            const fileBuffer = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(filePath);\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(fileBuffer, {\n                headers: {\n                    'Content-Type': 'application/pdf',\n                    'Content-Disposition': `inline; filename=\"${encodeURIComponent(fileName)}\"`\n                }\n            });\n        }\n        // إذا كان ملف نصي، إرجاع محتواه كـ HTML للعرض\n        if (fileExtension === '.txt') {\n            const fileContent = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(filePath, 'utf-8');\n            const htmlContent = `\n<!DOCTYPE html>\n<html lang=\"ar\" dir=\"rtl\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${fileName}</title>\n    <style>\n        body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            line-height: 1.6;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            background-color: #f5f5f5;\n            color: #333;\n        }\n        .container {\n            background: white;\n            padding: 30px;\n            border-radius: 10px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            border-bottom: 2px solid #007bff;\n            padding-bottom: 20px;\n            margin-bottom: 30px;\n        }\n        .file-title {\n            color: #007bff;\n            font-size: 24px;\n            font-weight: bold;\n            margin: 0;\n        }\n        .content {\n            white-space: pre-wrap;\n            font-size: 16px;\n            line-height: 1.8;\n        }\n        .download-btn {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            background: #007bff;\n            color: white;\n            padding: 10px 20px;\n            border: none;\n            border-radius: 5px;\n            cursor: pointer;\n            text-decoration: none;\n            font-weight: bold;\n        }\n        .download-btn:hover {\n            background: #0056b3;\n        }\n        @media print {\n            .download-btn { display: none; }\n            body { background: white; }\n            .container { box-shadow: none; }\n        }\n    </style>\n</head>\n<body>\n    <a href=\"/api/legal-library/download?id=${fileId}\" class=\"download-btn\">تحميل الملف</a>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1 class=\"file-title\">${fileName.replace('.txt', '')}</h1>\n        </div>\n        <div class=\"content\">${fileContent}</div>\n    </div>\n</body>\n</html>`;\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(htmlContent, {\n                headers: {\n                    'Content-Type': 'text/html; charset=utf-8'\n                }\n            });\n        }\n        // نوع ملف غير مدعوم للعرض\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'نوع الملف غير مدعوم للعرض'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error viewing file:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في عرض الملف'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/legal-library/view/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fview%2Froute&page=%2Fapi%2Flegal-library%2Fview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fview%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();