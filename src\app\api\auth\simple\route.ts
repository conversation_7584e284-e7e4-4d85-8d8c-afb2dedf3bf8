import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// تسجيل دخول مبسط بدون تشفير (للاختبار فقط)
export async function POST(request: NextRequest) {
  try {
    
    const body = await request.json()
    console.log('📋 البيانات المستلمة:', { username: body.username, hasPassword: !!body.password })
    const { username, password } = body

    // التحقق من البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن المستخدم
    console.log('🔍 البحث عن المستخدم:', username)
    const userResult = await query(`
      SELECT u.*, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.username = $1
    `, [username])
    
    

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم غير موجود' },
        { status: 401 }
      )
    }

    const user = userResult.rows[0]
    console.log('👤 بيانات المستخدم:', { id: user.id, username: user.username, status: user.status })

    // تحقق مبسط من كلمة المرور (للاختبار فقط)
    // في الواقع، يجب استخدام bcrypt
    if (password !== username) { // كلمة المرور = اسم المستخدم للاختبار
      return NextResponse.json(
        { success: false, error: 'كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // التحقق من حالة المستخدم
    if (user.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'حساب المستخدم غير نشط' },
        { status: 403 }
      )
    }

    // تحديث بيانات تسجيل الدخول
    await query(`
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0
      WHERE id = $1
    `, [user.id])

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password_hash, ...userWithoutPassword } = user

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        ...userWithoutPassword,
        name: user.employee_name || user.username
      },
      token: 'simple-token-' + user.id
    })

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الدخول: ' + error.message },
      { status: 500 }
    )
  }
}
