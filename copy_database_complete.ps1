# سكريپت نسخ قاعدة البيانات كاملة من mohammi إلى rubaie
# Complete Database Copy Script

Write-Host "🔄 بدء نسخ قاعدة البيانات كاملة..." -ForegroundColor Green

# متغيرات الاتصال
$sourceDb = "mohammi"
$targetDb = "rubaie"
$pgUser = "postgres"
$pgHost = "localhost"
$pgPort = "5432"
$backupFile = "mohammi_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"

Write-Host "📋 معلومات النسخ:" -ForegroundColor Cyan
Write-Host "   المصدر: $sourceDb" -ForegroundColor White
Write-Host "   الهدف: $targetDb" -ForegroundColor White
Write-Host "   ملف النسخ الاحتياطي: $backupFile" -ForegroundColor White

# التحقق من وجود PostgreSQL
Write-Host "`n🔍 التحقق من PostgreSQL..." -ForegroundColor Yellow
try {
    $pgVersion = pg_dump --version
    Write-Host "✅ PostgreSQL متوفر: $pgVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL غير متوفر. يرجى التأكد من تثبيته وإضافته إلى PATH" -ForegroundColor Red
    exit 1
}

# التحقق من وجود قاعدة البيانات المصدر
Write-Host "`n🔍 التحقق من قاعدة البيانات المصدر..." -ForegroundColor Yellow
$sourceCheck = psql -U $pgUser -h $pgHost -p $pgPort -lqt | Select-String $sourceDb
if (-not $sourceCheck) {
    Write-Host "❌ قاعدة البيانات المصدر '$sourceDb' غير موجودة" -ForegroundColor Red
    exit 1
}
Write-Host "✅ قاعدة البيانات المصدر موجودة" -ForegroundColor Green

# حذف قاعدة البيانات الهدف إذا كانت موجودة
Write-Host "`n🗑️ التحقق من قاعدة البيانات الهدف..." -ForegroundColor Yellow
$targetCheck = psql -U $pgUser -h $pgHost -p $pgPort -lqt | Select-String $targetDb
if ($targetCheck) {
    Write-Host "⚠️ قاعدة البيانات '$targetDb' موجودة. سيتم حذفها وإعادة إنشائها..." -ForegroundColor Yellow
    
    # قطع جميع الاتصالات بقاعدة البيانات الهدف
    psql -U $pgUser -h $pgHost -p $pgPort -d postgres -c "
    SELECT pg_terminate_backend(pid)
    FROM pg_stat_activity
    WHERE datname = '$targetDb' AND pid <> pg_backend_pid();"
    
    # حذف قاعدة البيانات
    dropdb -U $pgUser -h $pgHost -p $pgPort $targetDb
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم حذف قاعدة البيانات القديمة" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في حذف قاعدة البيانات القديمة" -ForegroundColor Red
        exit 1
    }
}

# إنشاء نسخة احتياطية من قاعدة البيانات المصدر
Write-Host "`n💾 إنشاء نسخة احتياطية من قاعدة البيانات المصدر..." -ForegroundColor Yellow
pg_dump -U $pgUser -h $pgHost -p $pgPort -d $sourceDb -f $backupFile --verbose --no-password

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء النسخة الاحتياطية بنجاح" -ForegroundColor Green
    $backupSize = (Get-Item $backupFile).Length / 1MB
    Write-Host "📊 حجم النسخة الاحتياطية: $([math]::Round($backupSize, 2)) MB" -ForegroundColor Cyan
} else {
    Write-Host "❌ فشل في إنشاء النسخة الاحتياطية" -ForegroundColor Red
    exit 1
}

# إنشاء قاعدة البيانات الهدف
Write-Host "`n🏗️ إنشاء قاعدة البيانات الهدف..." -ForegroundColor Yellow
createdb -U $pgUser -h $pgHost -p $pgPort $targetDb

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء قاعدة البيانات '$targetDb'" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء قاعدة البيانات '$targetDb'" -ForegroundColor Red
    exit 1
}

# استعادة البيانات إلى قاعدة البيانات الهدف
Write-Host "`n📥 استعادة البيانات إلى قاعدة البيانات الهدف..." -ForegroundColor Yellow
psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -f $backupFile --quiet

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم استعادة البيانات بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في استعادة البيانات" -ForegroundColor Red
    exit 1
}

# التحقق من النتائج
Write-Host "`n📊 التحقق من النتائج..." -ForegroundColor Yellow

# الحصول على إحصائيات قاعدة البيانات المصدر
Write-Host "📋 إحصائيات قاعدة البيانات المصدر ($sourceDb):" -ForegroundColor Cyan
$sourceStats = psql -U $pgUser -h $pgHost -p $pgPort -d $sourceDb -t -c "
SELECT 
    (SELECT COUNT(*) FROM clients) as clients,
    (SELECT COUNT(*) FROM issues) as issues,
    (SELECT COUNT(*) FROM courts) as courts,
    (SELECT COUNT(*) FROM currencies) as currencies,
    (SELECT COUNT(*) FROM issue_types) as issue_types,
    (SELECT COUNT(*) FROM governorates) as governorates;"

# الحصول على إحصائيات قاعدة البيانات الهدف
Write-Host "📋 إحصائيات قاعدة البيانات الهدف ($targetDb):" -ForegroundColor Cyan
$targetStats = psql -U $pgUser -h $pgHost -p $pgPort -d $targetDb -t -c "
SELECT 
    (SELECT COUNT(*) FROM clients) as clients,
    (SELECT COUNT(*) FROM issues) as issues,
    (SELECT COUNT(*) FROM courts) as courts,
    (SELECT COUNT(*) FROM currencies) as currencies,
    (SELECT COUNT(*) FROM issue_types) as issue_types,
    (SELECT COUNT(*) FROM governorates) as governorates;"

# عرض الإحصائيات
$sourceArray = $sourceStats.Trim() -split '\|'
$targetArray = $targetStats.Trim() -split '\|'

Write-Host "`n📊 مقارنة الإحصائيات:" -ForegroundColor Yellow
Write-Host "الجدول`t`tالمصدر`tالهدف`tالحالة" -ForegroundColor White
Write-Host "────────────────────────────────────────────" -ForegroundColor Gray

$tables = @("العملاء", "القضايا", "المحاكم", "العملات", "أنواع القضايا", "المحافظات")
for ($i = 0; $i -lt $tables.Length; $i++) {
    $sourceCount = $sourceArray[$i].Trim()
    $targetCount = $targetArray[$i].Trim()
    $status = if ($sourceCount -eq $targetCount) { "✅" } else { "❌" }
    Write-Host "$($tables[$i])`t`t$sourceCount`t$targetCount`t$status" -ForegroundColor White
}

# التحقق من نجاح النسخ
$allMatch = $true
for ($i = 0; $i -lt $sourceArray.Length; $i++) {
    if ($sourceArray[$i].Trim() -ne $targetArray[$i].Trim()) {
        $allMatch = $false
        break
    }
}

if ($allMatch) {
    Write-Host "`n🎉 تم نسخ قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "✅ جميع البيانات تم نسخها بشكل صحيح" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ تحذير: هناك اختلاف في عدد السجلات" -ForegroundColor Yellow
    Write-Host "يرجى مراجعة البيانات يدوياً" -ForegroundColor Yellow
}

# تنظيف الملفات المؤقتة
Write-Host "`n🧹 تنظيف الملفات المؤقتة..." -ForegroundColor Yellow
$keepBackup = Read-Host "هل تريد الاحتفاظ بملف النسخة الاحتياطية؟ (y/n)"
if ($keepBackup -eq "n" -or $keepBackup -eq "N") {
    Remove-Item $backupFile -Force
    Write-Host "✅ تم حذف ملف النسخة الاحتياطية" -ForegroundColor Green
} else {
    Write-Host "📁 تم الاحتفاظ بملف النسخة الاحتياطية: $backupFile" -ForegroundColor Cyan
}

Write-Host "`n🚀 يمكنك الآن تشغيل النسخة الثانية على المنفذ 8914" -ForegroundColor Yellow
Write-Host "💡 استخدم الأمر: npm run dev -- --port 8914" -ForegroundColor Cyan
