'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Beneficiary {
  id: number
  name: string
  type: 'client' | 'employee' | 'supplier'
  account_id?: number
  account_code?: string
}

interface SimpleBeneficiarySelectProps {
  value?: string
  onChange: (value: string, beneficiary?: Beneficiary) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export default function SimpleBeneficiarySelect({
  value,
  onChange,
  label,
  placeholder = "اختر المستفيد...",
  required = false,
  className = ""
}: SimpleBeneficiarySelectProps) {
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([])
  const [loading, setLoading] = useState(false)

  const fetchBeneficiaries = async () => {
    try {
      setLoading(true)
      console.log('🔍 SimpleBeneficiarySelect: بدء جلب البيانات...')
      
      // جلب العملاء
      const clientsResponse = await fetch('/api/clients')
      const clientsData = await clientsResponse.json()
      console.log('🔍 SimpleBeneficiarySelect: بيانات العملاء:', clientsData)
      
      const clients = clientsData.success ? clientsData.clients.map((client: any) => ({
        id: client.id,
        name: client.name,
        type: 'client' as const,
        account_id: client.account_id,
        account_code: client.account_code
      })) : []

      // جلب الموظفين
      const employeesResponse = await fetch('/api/employees')
      const employeesData = await employeesResponse.json()
      console.log('🔍 SimpleBeneficiarySelect: بيانات الموظفين:', employeesData)

      const employees = employeesData.success ? employeesData.employees.map((employee: any) => ({
        id: employee.id,
        name: employee.name,
        type: 'employee' as const,
        account_id: employee.account_id,
        account_code: employee.account_code
      })) : []

      // دمج جميع المستفيدين
      const allBeneficiaries = [...clients, ...employees]
      console.log('🔍 SimpleBeneficiarySelect: جميع المستفيدين:', allBeneficiaries)
      setBeneficiaries(allBeneficiaries)

    } catch (error) {
      console.error('❌ SimpleBeneficiarySelect: خطأ في جلب المستفيدين:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBeneficiaries()
  }, [])

  // العثور على المستفيد المحدد
  const selectedBeneficiary = beneficiaries.find(b => 
    `${b.type}_${b.id}` === value
  )

  const handleSelect = (selectedValue: string) => {
    const beneficiary = beneficiaries.find(b => 
      `${b.type}_${b.id}` === selectedValue
    )
    onChange(selectedValue, beneficiary)
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'client': return '👤 عميل'
      case 'employee': return '👨‍💼 موظف'
      case 'supplier': return '🏢 مورد'
      default: return type
    }
  }

  return (
    <div className={className}>
      {label && (
        <Label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </Label>
      )}
      
      <Select value={value || ''} onValueChange={handleSelect}>
        <SelectTrigger className="w-full bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors">
          <SelectValue placeholder={loading ? "جاري التحميل..." : placeholder}>
            {selectedBeneficiary && (
              <div className="flex items-center gap-2">
                <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                  {getTypeLabel(selectedBeneficiary.type)}
                </span>
                <span className="font-medium">{selectedBeneficiary.name}</span>
                {selectedBeneficiary.account_code && (
                  <span className="text-xs text-gray-500">
                    {selectedBeneficiary.account_code}
                  </span>
                )}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="max-h-80">
          {loading ? (
            <SelectItem value="loading" disabled>
              جاري التحميل...
            </SelectItem>
          ) : beneficiaries.length === 0 ? (
            <SelectItem value="no-results" disabled>
              لا توجد مستفيدين (تم جلب {beneficiaries.length} عنصر)
            </SelectItem>
          ) : (
            <>
              <SelectItem value="header" disabled className="font-bold text-blue-600">
                المستفيدين ({beneficiaries.length})
              </SelectItem>
            beneficiaries.map((beneficiary) => (
              <SelectItem 
                key={`${beneficiary.type}_${beneficiary.id}`} 
                value={`${beneficiary.type}_${beneficiary.id}`}
              >
                <div className="flex items-center gap-2 w-full">
                  <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                    {getTypeLabel(beneficiary.type)}
                  </span>
                  <span className="font-medium">{beneficiary.name}</span>
                  {beneficiary.account_code && (
                    <span className="text-xs text-gray-500 mr-auto">
                      {beneficiary.account_code}
                    </span>
                  )}
                </div>
              </SelectItem>
            ))
            </>
          )}
        </SelectContent>
      </Select>
    </div>
  )
}
