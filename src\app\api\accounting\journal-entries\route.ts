import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// إنشاء جدول القيود اليومية إذا لم يكن موجوداً
async function ensureJournalEntriesTable() {
  await query(`
    CREATE TABLE IF NOT EXISTS journal_entries (
      id SERIAL PRIMARY KEY,
      entry_number VARCHAR(50) UNIQUE NOT NULL,
      entry_date DATE NOT NULL,
      description TEXT NOT NULL,
      total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
      total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
      status VARCHAR(20) DEFAULT 'draft',
      created_by VARCHAR(100) DEFAULT 'النظام',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `)
  
  await query(`
    CREATE TABLE IF NOT EXISTS journal_entry_details (
      id SERIAL PRIMARY KEY,
      journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,
      account_id INTEGER,
      account_name VARCHAR(255),
      debit_amount DECIMAL(15,2) DEFAULT 0,
      credit_amount DECIMAL(15,2) DEFAULT 0,
      description TEXT,
      line_order INTEGER DEFAULT 1
    )
  `)
}

// توليد رقم قيد جديد
async function generateEntryNumber() {
  const result = await query(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries 
    WHERE entry_number ~ '^JE[0-9]+$'
  `)
  
  const nextNumber = result.rows[0]?.next_number || 1
  return `JE${String(nextNumber).padStart(6, '0')}`
}

// GET - جلب جميع القيود اليومية
export async function GET(request: NextRequest) {
  try {
    await ensureJournalEntriesTable()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')

    let sql = `
      SELECT
        je.*,
        COUNT(jed.id) as details_count
      FROM journal_entries je
      LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
      WHERE (je.entry_type = 'journal' OR je.entry_type IS NULL)
    `

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      sql += ` AND je.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` GROUP BY je.id ORDER BY je.entry_date DESC, je.entry_number DESC`

    console.log('🔍 SQL Query:', sql)
    console.log('🔍 Parameters:', params)

    const result = await query(sql, params)
    
    // جلب تفاصيل كل قيد
    const entries = []
    for (const entry of result.rows) {
      const detailsResult = await query(`
        SELECT
          jed.*,
          COALESCE(coa.account_name, jed.account_name) as account_name,
          coa.account_code
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE jed.journal_entry_id = $1
        ORDER BY jed.line_order
      `, [entry.id])
      
      entries.push({
        ...entry,
        details: detailsResult.rows,
        total_debit: parseFloat(entry.total_debit || 0),
        total_credit: parseFloat(entry.total_credit || 0)
      })
    }

    

    return NextResponse.json({
      success: true,
      entries,
      total: entries.length,
      message: 'تم جلب القيود اليومية بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في جلب القيود اليومية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب القيود اليومية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة قيد يومي جديد
export async function POST(request: NextRequest) {
  try {
    await ensureJournalEntriesTable()
    
    const body = await request.json()
    console.log('📥 بيانات القيد اليومي المستلمة:', body)
    
    const {
      entry_date,
      description,
      details = [],
      status = 'draft'
    } = body

    // التحقق من البيانات المطلوبة
    const missingFields = []
    if (!entry_date) missingFields.push('تاريخ القيد (entry_date)')
    if (!description) missingFields.push('وصف القيد (description)')
    if (!details || !Array.isArray(details) || details.length === 0) {
      missingFields.push('تفاصيل القيد (details) - يجب أن تكون مصفوفة تحتوي على سطر واحد على الأقل')
    }

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة',
        details: `البيانات المفقودة: ${missingFields.join(', ')}`,
        missingFields: missingFields,
        receivedData: body
      }, { status: 400 })
    }

    // التحقق من تفاصيل كل سطر
    const detailErrors = []
    details.forEach((detail, index) => {
      const lineErrors = []
      if (!detail.account_id) lineErrors.push('معرف الحساب (account_id)')
      if (detail.debit_amount === undefined && detail.credit_amount === undefined) {
        lineErrors.push('يجب تحديد مبلغ مدين أو دائن')
      }
      if (detail.debit_amount < 0 || detail.credit_amount < 0) {
        lineErrors.push('المبالغ يجب أن تكون موجبة')
      }

      if (lineErrors.length > 0) {
        detailErrors.push(`السطر ${index + 1}: ${lineErrors.join(', ')}`)
      }
    })

    if (detailErrors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'أخطاء في تفاصيل القيد',
        details: detailErrors.join(' | '),
        detailErrors: detailErrors,
        receivedData: body
      }, { status: 400 })
    }

    // حساب إجمالي المدين والدائن
    let total_debit = 0
    let total_credit = 0
    
    for (const detail of details) {
      total_debit += parseFloat(detail.debit_amount || 0)
      total_credit += parseFloat(detail.credit_amount || 0)
    }

    // التحقق من توازن القيد
    if (Math.abs(total_debit - total_credit) > 0.01) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير متوازن',
        details: `إجمالي المدين (${total_debit}) لا يساوي إجمالي الدائن (${total_credit})`
      }, { status: 400 })
    }

    // توليد رقم قيد جديد
    const entry_number = await generateEntryNumber()

    // إدراج القيد الرئيسي
    const entryResult = await query(`
      INSERT INTO journal_entries (
        entry_number, entry_date, description, 
        total_debit, total_credit, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      entry_number, entry_date, description,
      total_debit, total_credit, status, 'النظام'
    ])

    const newEntry = entryResult.rows[0]

    // إدراج تفاصيل القيد
    for (let i = 0; i < details.length; i++) {
      const detail = details[i]

      // جلب اسم ورمز الحساب من دليل الحسابات
      let accountName = detail.account_name
      let accountCode = detail.account_code

      if ((!accountName || !accountCode) && detail.account_id) {
        const accountResult = await query(
          'SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1',
          [detail.account_id]
        )
        if (accountResult.rows[0]) {
          accountName = accountName || accountResult.rows[0].account_name || `حساب رقم ${detail.account_id}`
          accountCode = accountCode || accountResult.rows[0].account_code
        }
      }

      await query(`
        INSERT INTO journal_entry_details (
          journal_entry_id, account_id, account_name, account_code,
          debit_amount, credit_amount, description, line_order
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        newEntry.id, detail.account_id, accountName, accountCode,
        detail.debit_amount || 0, detail.credit_amount || 0,
        detail.description, i + 1
      ])
    }

    

    return NextResponse.json({
      success: true,
      entry: newEntry,
      message: `تم إنشاء القيد اليومي ${entry_number} بنجاح`
    })

  } catch (error) {
    console.error('❌ خطأ في إنشاء القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث قيد يومي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف القيد مطلوب'
      }, { status: 400 })
    }

    // تحديث القيد
    const result = await query(`
      UPDATE journal_entries 
      SET 
        entry_date = COALESCE($2, entry_date),
        description = COALESCE($3, description),
        status = COALESCE($4, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      id, updateData.entry_date, updateData.description, updateData.status
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    

    return NextResponse.json({
      success: true,
      entry: result.rows[0],
      message: 'تم تحديث القيد اليومي بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
