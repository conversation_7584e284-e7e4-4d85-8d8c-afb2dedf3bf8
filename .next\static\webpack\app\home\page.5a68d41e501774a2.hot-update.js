"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx":
/*!********************************************************!*\
  !*** ./src/app/home/<USER>/simple-chat-widget.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleChatWidget: () => (/* binding */ SimpleChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ SimpleChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleChatWidget(param) {\n    let { isOpen, onClose, onOpen } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guestId, setGuestId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // إنشاء معرف زائر فريد وجلب بيانات الشركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            let guestIdentifier = localStorage.getItem('guestId');\n            if (!guestIdentifier) {\n                guestIdentifier = \"guest_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                localStorage.setItem('guestId', guestIdentifier);\n            }\n            setGuestId(guestIdentifier);\n            // جلب بيانات الشركة\n            fetchCompanyData();\n        }\n    }[\"SimpleChatWidget.useEffect\"], []);\n    // جلب بيانات الشركة\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/companies');\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && result.data.length > 0) {\n                    const company = result.data[0];\n                    setCompanyData(company);\n                    // إنشاء الرسائل الترحيبية\n                    const welcomeMessages = [\n                        {\n                            id: '1',\n                            content: \"مرحباً بك في \".concat(company.name, \"! \\uD83C\\uDFDB️\"),\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        },\n                        {\n                            id: '2',\n                            content: 'أنا مساعدك القانوني الذكي المدعوم بالذكاء الاصطناعي 🤖 يمكنني مساعدتك في الاستشارات القانونية.',\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        }\n                    ];\n                    setMessages(welcomeMessages);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching company data:', error);\n            // رسائل افتراضية\n            const defaultMessages = [\n                {\n                    id: '1',\n                    content: 'مرحباً! أنا مساعدك القانوني الذكي 🤖',\n                    sender: 'assistant',\n                    timestamp: new Date(),\n                    status: 'delivered'\n                }\n            ];\n            setMessages(defaultMessages);\n        }\n    };\n    // Auto-scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat is opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!message.trim()) return;\n        console.log('📤 Sending message:', message);\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            sender: 'user',\n            timestamp: new Date(),\n            status: 'delivered'\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setIsTyping(true);\n        try {\n            // إرسال للذكاء الاصطناعي\n            const response = await sendToAI(message);\n            // Add AI response\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message,\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'delivered'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: 'أعتذر، حدث خطأ. يرجى المحاولة مرة أخرى.',\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'error'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n        }\n    };\n    const sendToAI = async (userMessage)=>{\n        try {\n            console.log('🤖 Sending message to AI:', userMessage);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage,\n                    model: 'openai-gpt4',\n                    conversationId: \"guest_\".concat(guestId, \"_\").concat(Date.now()),\n                    context: []\n                })\n            });\n            if (!response.ok) {\n                throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');\n            }\n            const result = await response.json();\n            console.log('🤖 AI Response:', result);\n            if (result.success && result.response) {\n                return {\n                    message: result.response\n                };\n            } else {\n                throw new Error(result.error || 'خطأ في الاستجابة');\n            }\n        } catch (error) {\n            console.error('AI Error:', error);\n            // رد احتياطي\n            const fallbackResponses = [\n                \"أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: \".concat((companyData === null || companyData === void 0 ? void 0 : companyData.phone) || '+967-1-123456'),\n                'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',\n                'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.'\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return {\n                message: randomResponse\n            };\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onOpen,\n                className: \"h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700 flex items-center justify-center\",\n                title: \"المحادثات مع الذكاء الاصطناعي\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-7 w-7 text-white\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-24 left-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b bg-blue-600 text-white rounded-t-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"المحادثات المباشرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-2 w-2 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"AI\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-100 mr-2\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 rounded-full hover:bg-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.sender === 'user' ? 'justify-end' : 'justify-start'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-sm px-4 py-3 rounded-lg \".concat(msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 opacity-70\",\n                                        children: msg.timestamp.toLocaleTimeString('ar-SA', {\n                                            hour: '2-digit',\n                                            minute: '2-digit'\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 text-gray-800 px-4 py-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"المساعد الذكي يكتب...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendMessage,\n                    className: \"flex space-x-2 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ref: inputRef,\n                            type: \"text\",\n                            value: message,\n                            onChange: (e)=>setMessage(e.target.value),\n                            placeholder: \"اكتب رسالتك...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                            disabled: isTyping,\n                            dir: \"rtl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n                            disabled: !message.trim() || isTyping,\n                            children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatWidget, \"+wvRQPGNvkoLiajW7IFDjiYLFZs=\");\n_c = SimpleChatWidget;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\n"));

/***/ })

});