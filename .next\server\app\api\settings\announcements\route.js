/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/announcements/route";
exports.ids = ["app/api/settings/announcements/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Fannouncements%2Froute&page=%2Fapi%2Fsettings%2Fannouncements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fannouncements%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Fannouncements%2Froute&page=%2Fapi%2Fsettings%2Fannouncements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fannouncements%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_settings_announcements_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/settings/announcements/route.ts */ \"(rsc)/./src/app/api/settings/announcements/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_settings_announcements_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_settings_announcements_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/announcements/route\",\n        pathname: \"/api/settings/announcements\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/announcements/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\settings\\\\announcements\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_settings_announcements_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Fannouncements%2Froute&page=%2Fapi%2Fsettings%2Fannouncements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fannouncements%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/settings/announcements/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/settings/announcements/route.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب الإعلانات\nasync function GET() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        id,\n        announcement_1,\n        announcement_2,\n        announcement_3,\n        announcement_4,\n        is_active,\n        created_date,\n        updated_at\n      FROM announcements\n      ORDER BY id DESC\n      LIMIT 1\n    `);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows.length > 0 ? result.rows[0] : null,\n            message: 'تم جلب الإعلانات بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في جلب الإعلانات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الإعلانات',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة إعلانات جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { announcement_1 = '', announcement_2 = '', announcement_3 = '', announcement_4 = '', is_active = true } = body;\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO announcements (announcement_1, announcement_2, announcement_3, announcement_4, is_active)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING *\n    `, [\n            announcement_1,\n            announcement_2,\n            announcement_3,\n            announcement_4,\n            is_active\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم إضافة الإعلانات بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة الإعلانات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الإعلانات',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث الإعلانات\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, announcement_1 = '', announcement_2 = '', announcement_3 = '', announcement_4 = '', is_active = true } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الإعلانات مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE announcements\n      SET announcement_1 = $1, announcement_2 = $2, announcement_3 = $3,\n          announcement_4 = $4, is_active = $5, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $6\n      RETURNING *\n    `, [\n            announcement_1,\n            announcement_2,\n            announcement_3,\n            announcement_4,\n            is_active,\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الإعلانات غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث الإعلانات بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث الإعلانات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث الإعلانات',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف الإعلانات\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الإعلانات مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM announcements WHERE id = $1 RETURNING *', [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الإعلانات غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الإعلانات بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف الإعلانات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف الإعلانات',\n            details: error instanceof Error ? error.message : 'خطأ غير معروف'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/settings/announcements/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsettings%2Fannouncements%2Froute&page=%2Fapi%2Fsettings%2Fannouncements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fannouncements%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();