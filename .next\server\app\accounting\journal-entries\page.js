(()=>{var e={};e.id=8855,e.ids=[8855],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7238:(e,t,a)=>{Promise.resolve().then(a.bind(a,88607))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>p,eb:()=>b,gC:()=>h,l6:()=>o,yv:()=>m});var s=a(60687),r=a(43210),i=a(58106),n=a(78272),l=a(3589),c=a(13964),d=a(4780);let o=i.bL;i.YJ;let m=i.WT,p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-700"})})]}));p.displayName=i.l9.displayName;let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=i.PP.displayName;let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let h=r.forwardRef(({className:e,children:t,position:a="popper",...r},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(u,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(x,{})]})}));h.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let b=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:t})]}));b.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36193:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var s=a(60687),r=a(43210),i=a(98254),n=a(44493),l=a(29523),c=a(89667),d=a(80013),o=a(15079),m=a(63503),p=a(96834),u=a(82080),x=a(96474),h=a(99270),b=a(13861),g=a(63143),f=a(88233),_=a(71444),j=a(93613);function v(){let[e,t]=(0,r.useState)([]),[a,v]=(0,r.useState)([]),[y,N]=(0,r.useState)([]),[w,k]=(0,r.useState)([]),[C,D]=(0,r.useState)([]),[$,S]=(0,r.useState)([]),[A,L]=(0,r.useState)([]),[P,q]=(0,r.useState)(!0),[z,R]=(0,r.useState)(!1),[F,I]=(0,r.useState)(null),[M,E]=(0,r.useState)(""),[J,G]=(0,r.useState)({entry_date:new Date().toISOString().split("T")[0],description:"",currency_id:"1",reference_number:""}),[V,B]=(0,r.useState)([{line_number:1,account_id:"",debit_amount:0,credit_amount:0,currency_id:1,exchange_rate:1,description:""},{line_number:2,account_id:"",debit_amount:0,credit_amount:0,currency_id:1,exchange_rate:1,description:""}]),T=async()=>{try{q(!0);let e=await fetch("/api/accounting/journal-entries?include_details=true");if(e.ok){let a=await e.json();t(a.entries||[])}let a=await fetch("/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true");if(a.ok){let e=await a.json();console.log("Accounts data:",e);let t=(e.accounts||[]).sort((e,t)=>{if(e.is_linked_record&&!t.is_linked_record)return 1;if(!e.is_linked_record&&t.is_linked_record)return -1;if(e.is_linked_record&&t.is_linked_record){if("clients"===e.original_table&&"employees"===t.original_table)return -1;if("employees"===e.original_table&&"clients"===t.original_table)return 1}return e.account_name.localeCompare(t.account_name,"ar")});console.log("Sorted accounts:",t),v(t)}else console.error("Failed to fetch accounts:",a.status);let s=await fetch("/api/accounting/currencies");if(s.ok){let e=await s.json();N(e.currencies||[])}let r=await fetch("/api/accounting/payment-methods");if(r.ok){let e=await r.json();k(e.methods||[])}let i=await fetch("/api/cost-centers");if(i.ok){let e=await i.json();D(e.centers||[])}let n=await fetch("/api/issues");if(n.ok){let e=await n.json();S(e.data||e.issues||[])}let l=await fetch("/api/services");if(l.ok){let e=await l.json();L(e.data||[])}}catch(e){console.error("خطأ في جلب البيانات:",e)}finally{q(!1)}},O=()=>({totalDebit:V.reduce((e,t)=>e+(t.debit_amount||0),0),totalCredit:V.reduce((e,t)=>e+(t.credit_amount||0),0)}),U=()=>{let{totalDebit:e,totalCredit:t}=O();return .01>Math.abs(e-t)},Z=e=>{V.length>2&&B(V.filter((t,a)=>a!==e).map((e,t)=>({...e,line_number:t+1})))},W=(e,t,a)=>{let s=[...V];if("account_id"===t)if(""===a||"0"===a)s[e]={...s[e],[t]:""};else{let r=parseInt(a);s[e]={...s[e],[t]:r}}else s[e]={...s[e],[t]:a};"debit_amount"===t&&a>0?s[e].credit_amount=0:"credit_amount"===t&&a>0&&(s[e].debit_amount=0),B(s)},H=async e=>{if(e.preventDefault(),!U())return void alert("القيد غير متوازن! يجب أن تكون إجماليات المدين والدائن متساوية");let t=V.filter(e=>("number"==typeof e.account_id?e.account_id>0:""!==e.account_id)&&(e.debit_amount>0||e.credit_amount>0));if(t.length<2)return void alert("يجب أن يحتوي القيد على سطرين على الأقل بحسابات وأرقام صحيحة");try{let{totalDebit:e,totalCredit:a}=O(),s=F?`/api/accounting/journal-entries/${F.id}`:"/api/accounting/journal-entries",r=F?"PUT":"POST",i=await fetch(s,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify({...J,total_debit:e,total_credit:a,currency_id:parseInt(J.currency_id),cost_center_id:J.cost_center_id?parseInt(J.cost_center_id):null,case_id:J.case_id?parseInt(J.case_id):null,service_id:J.service_id?parseInt(J.service_id):null,details:t.map(e=>({...e,account_id:parseInt(e.account_id.toString()),currency_id:parseInt(e.currency_id.toString()),cost_center_id:e.cost_center_id?parseInt(e.cost_center_id.toString()):null,payment_method_id:e.payment_method_id?parseInt(e.payment_method_id.toString()):null}))})});if(i.ok)await T(),R(!1),I(null),Y();else{let e=await i.json();alert(`خطأ: ${e.error}`)}}catch(e){console.error("خطأ في حفظ القيد:",e),alert("حدث خطأ أثناء حفظ القيد")}},Y=()=>{G({entry_date:new Date().toISOString().split("T")[0],description:"",currency_id:"1",cost_center_id:"",case_id:"",service_id:"",reference_number:""}),B([{line_number:1,account_id:"",debit_amount:0,credit_amount:0,currency_id:1,exchange_rate:1,cost_center_id:0,case_id:0,service_id:0,description:""},{line_number:2,account_id:"",debit_amount:0,credit_amount:0,currency_id:1,exchange_rate:1,cost_center_id:0,case_id:0,service_id:0,description:""}])},K=async e=>{try{let t=await fetch(`/api/accounting/journal-entries/${e.id}?include_details=true`);if(t.ok){let e=(await t.json()).entry;I(e),G({entry_date:e.entry_date,description:e.description,currency_id:e.currency_id?.toString()||"1",cost_center_id:e.cost_center_id?.toString()||"",case_id:e.case_id?.toString()||"",case_number:e.case_number||"",reference_number:e.reference_number||""}),e.details&&e.details.length>0&&B(e.details.map(e=>({line_number:e.line_number,account_id:e.account_id,debit_amount:e.debit_amount||0,credit_amount:e.credit_amount||0,currency_id:e.currency_id||1,exchange_rate:e.exchange_rate||1,cost_center_id:e.cost_center_id,case_id:e.case_id,payment_method_id:e.payment_method_id,description:e.description||"",reference_number:e.reference_number||""}))),R(!0)}}catch(e){console.error("خطأ في جلب تفاصيل القيد:",e),alert("حدث خطأ أثناء جلب تفاصيل القيد")}},X=async e=>{try{let t=await fetch(`/api/accounting/journal-entries/${e.id}?include_details=true`);if(t.ok){let a=(await t.json()).entry,s=window.open("","_blank","width=900,height=700");s&&(s.document.write(`
            <html dir="rtl">
              <head>
                <title>قيد يومي رقم ${e.entry_number}</title>
                <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { text-align: center; margin-bottom: 30px; }
                  .entry-info { border: 2px solid #333; padding: 20px; margin-bottom: 20px; }
                  .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                  .details-table th, .details-table td { border: 1px solid #333; padding: 8px; text-align: right; }
                  .details-table th { background-color: #f5f5f5; font-weight: bold; }
                  .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
                  .label { font-weight: bold; text-align: right; }
                  .totals { margin-top: 20px; font-weight: bold; }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>قيد يومي</h1>
                  <h2>رقم: ${e.entry_number}</h2>
                </div>
                <div class="entry-info">
                  <div class="row">
                    <span class="label">التاريخ:</span>
                    <span>${new Date(e.entry_date).toLocaleDateString("en-GB")}</span>
                  </div>
                  <div class="row">
                    <span class="label">البيان:</span>
                    <span>${e.description}</span>
                  </div>
                  <div class="row">
                    <span class="label">الحالة:</span>
                    <span>${"approved"===e.status?"معتمد":"draft"===e.status?"مسودة":"ملغي"}</span>
                  </div>
                </div>
                <table class="details-table">
                  <thead>
                    <tr>
                      <th>رقم السطر</th>
                      <th>اسم الحساب</th>
                      <th>البيان</th>
                      <th>مدين</th>
                      <th>دائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${a.details?.map(e=>`
                      <tr>
                        <td>${e.line_number}</td>
                        <td>${e.account_name||"غير محدد"}</td>
                        <td>${e.description||""}</td>
                        <td>${e.debit_amount?e.debit_amount.toLocaleString():"-"}</td>
                        <td>${e.credit_amount?e.credit_amount.toLocaleString():"-"}</td>
                      </tr>
                    `).join("")||""}
                  </tbody>
                </table>
                <div class="totals">
                  <div class="row">
                    <span>إجمالي المدين: ${e.total_debit.toLocaleString()} ر.ي</span>
                    <span>إجمالي الدائن: ${e.total_credit.toLocaleString()} ر.ي</span>
                  </div>
                  <div class="row">
                    <span>التوازن: ${e.total_debit===e.total_credit?"متوازن":"غير متوازن"}</span>
                  </div>
                </div>
              </body>
            </html>
          `),s.document.close())}}catch(e){console.error("خطأ في عرض القيد:",e),alert("حدث خطأ أثناء عرض القيد")}},Q=async e=>{if(confirm("هل أنت متأكد من حذف هذا القيد؟ سيتم حذف جميع تفاصيل القيد أيضاً."))try{let t=await fetch(`/api/accounting/journal-entries/${e}`,{method:"DELETE"});if(t.ok)await T(),alert("تم حذف القيد بنجاح");else{let e=await t.json();alert(`خطأ في حذف القيد: ${e.error}`)}}catch(e){console.error("خطأ في حذف القيد:",e),alert("حدث خطأ أثناء حذف القيد")}},ee=async e=>{try{let t=await fetch(`/api/accounting/journal-entries/${e.id}?include_details=true`);if(t.ok){let a=(await t.json()).entry,s=window.open("","_blank","width=900,height=700");s&&(s.document.write(`
            <html dir="rtl">
              <head>
                <title>طباعة قيد يومي رقم ${e.entry_number}</title>
                <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { text-align: center; margin-bottom: 30px; }
                  .entry-info { border: 2px solid #333; padding: 20px; margin-bottom: 20px; }
                  .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                  .details-table th, .details-table td { border: 1px solid #333; padding: 10px; text-align: right; }
                  .details-table th { background-color: #f5f5f5; font-weight: bold; }
                  .row { display: flex; justify-content: space-between; margin: 15px 0; text-align: right; }
                  .label { font-weight: bold; width: 150px; text-align: right; }
                  .value { flex: 1; border-bottom: 1px dotted #333; padding-bottom: 5px; text-align: right; }
                  .totals { margin-top: 30px; font-weight: bold; border-top: 2px solid #333; padding-top: 15px; }
                  .signature { margin-top: 50px; display: flex; justify-content: space-between; }
                  .signature div { text-align: center; width: 200px; }
                  .signature-line { border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; }
                  @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                  }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>قيد يومي</h1>
                  <h2>رقم: ${e.entry_number}</h2>
                </div>
                <div class="entry-info">
                  <div class="row">
                    <span class="label">التاريخ:</span>
                    <span class="value">${new Date(e.entry_date).toLocaleDateString("en-GB")}</span>
                  </div>
                  <div class="row">
                    <span class="label">البيان:</span>
                    <span class="value">${e.description}</span>
                  </div>
                  <div class="row">
                    <span class="label">الحالة:</span>
                    <span class="value">${"approved"===e.status?"معتمد":"draft"===e.status?"مسودة":"ملغي"}</span>
                  </div>
                </div>
                <table class="details-table">
                  <thead>
                    <tr>
                      <th>رقم السطر</th>
                      <th>اسم الحساب</th>
                      <th>البيان</th>
                      <th>مدين</th>
                      <th>دائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${a.details?.map(e=>`
                      <tr>
                        <td>${e.line_number}</td>
                        <td>${e.account_name||"غير محدد"}</td>
                        <td>${e.description||""}</td>
                        <td>${e.debit_amount?e.debit_amount.toLocaleString()+" ر.ي":"-"}</td>
                        <td>${e.credit_amount?e.credit_amount.toLocaleString()+" ر.ي":"-"}</td>
                      </tr>
                    `).join("")||""}
                  </tbody>
                </table>
                <div class="totals">
                  <div class="row">
                    <span>إجمالي المدين: ${e.total_debit.toLocaleString()} ر.ي</span>
                    <span>إجمالي الدائن: ${e.total_credit.toLocaleString()} ر.ي</span>
                  </div>
                  <div class="row">
                    <span>التوازن: ${e.total_debit===e.total_credit?"متوازن ✓":"غير متوازن ✗"}</span>
                  </div>
                </div>
                <div class="signature">
                  <div>
                    <div class="signature-line">المحاسب</div>
                  </div>
                  <div>
                    <div class="signature-line">المراجع</div>
                  </div>
                  <div>
                    <div class="signature-line">المدير المالي</div>
                  </div>
                </div>
                <script>
                  window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                      window.close();
                    }
                  }
                </script>
              </body>
            </html>
          `),s.document.close())}}catch(e){console.error("خطأ في طباعة القيد:",e),alert("حدث خطأ أثناء طباعة القيد")}},et=e.filter(e=>e.entry_number.toLowerCase().includes(M.toLowerCase())||e.description.toLowerCase().includes(M.toLowerCase())),ea=e=>{let t={draft:{label:"مسودة",variant:"secondary"},approved:{label:"معتمد",variant:"default"},cancelled:{label:"ملغي",variant:"destructive"}},a=t[e]||t.draft;return(0,s.jsx)(p.E,{variant:a.variant,children:a.label})},{totalDebit:es,totalCredit:er}=O(),ei=U();return(0,s.jsx)(i.O,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"space-y-6 p-6 bg-white min-h-screen",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"القيود اليومية"}),(0,s.jsx)("p",{className:"text-gray-600",children:"إدارة القيود المحاسبية اليومية"})]})]}),(0,s.jsxs)(l.$,{onClick:()=>R(!0),children:[(0,s.jsx)(x.A,{className:"h-4 w-4 ml-2"}),"قيد يومي جديد"]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,s.jsx)(n.ZB,{children:"القيود اليومية"}),(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(c.p,{placeholder:"البحث في القيود اليومية...",value:M,onChange:e=>E(e.target.value),className:"pr-10"})]})]}),(0,s.jsx)(n.Wu,{children:P?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"جاري تحميل القيود..."})]}):0===et.length?(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"لا توجد قيود يومية"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"رقم القيد"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"التاريخ"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"الحساب الدائن"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"الحساب المدين"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"مدين"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"دائن"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"البيان"}),(0,s.jsx)("th",{className:"text-center p-3 font-semibold text-black",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{children:et.map((e,t)=>e.details&&e.details.length>0?e.details.map((a,r)=>(0,s.jsxs)("tr",{className:`border-b hover:bg-gray-50 ${t%2==0?"bg-white":"bg-gray-25"}`,children:[(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"font-mono text-black font-medium",children:e.entry_number}),0===r&&ea(e.status)]})}),(0,s.jsx)("td",{className:"p-3 text-black",children:new Date(e.entry_date).toLocaleDateString("en-GB")}),(0,s.jsx)("td",{className:"p-3 text-black",children:a.credit_amount>0?(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"font-medium",children:a.account_name})}):"-"}),(0,s.jsx)("td",{className:"p-3 text-black",children:a.debit_amount>0?(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"font-medium",children:a.account_name})}):"-"}),(0,s.jsx)("td",{className:"p-3 text-right",children:(0,s.jsx)("span",{className:"font-bold text-black",children:a.debit_amount>0?parseFloat(a.debit_amount).toLocaleString()+" ر.ي":"-"})}),(0,s.jsx)("td",{className:"p-3 text-right",children:(0,s.jsx)("span",{className:"font-bold text-black",children:a.credit_amount>0?parseFloat(a.credit_amount).toLocaleString()+" ر.ي":"-"})}),(0,s.jsx)("td",{className:"p-3 text-black max-w-xs truncate",title:a.description||e.description,children:a.description||e.description}),(0,s.jsx)("td",{className:"p-3",children:0===r&&(0,s.jsxs)("div",{className:"flex justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>X(e),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",title:"مشاهدة",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>K(e),className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",title:"تعديل",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>Q(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"حذف",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ee(e),className:"text-gray-600 hover:text-gray-700 hover:bg-gray-50",title:"طباعة",children:(0,s.jsx)(_.A,{className:"h-4 w-4"})})]})})]},`${e.id}-${a.id||r}`)):(0,s.jsxs)("tr",{className:`border-b hover:bg-gray-50 ${t%2==0?"bg-white":"bg-gray-25"}`,children:[(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"font-mono text-blue-600 font-medium",children:e.entry_number}),ea(e.status)]})}),(0,s.jsx)("td",{className:"p-3 text-gray-600",children:new Date(e.entry_date).toLocaleDateString("en-GB")}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("span",{className:"font-bold text-blue-600",children:[e.total_debit.toLocaleString()," ر.ي"]})}),(0,s.jsx)("td",{className:"p-3 text-gray-600",children:"-"}),(0,s.jsx)("td",{className:"p-3 text-gray-600",children:"-"}),(0,s.jsx)("td",{className:"p-3 text-gray-600 max-w-xs truncate",title:e.description,children:e.description}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>X(e),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",title:"مشاهدة",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>K(e),className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",title:"تعديل",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>Q(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"حذف",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ee(e),className:"text-gray-600 hover:text-gray-700 hover:bg-gray-50",title:"طباعة",children:(0,s.jsx)(_.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,s.jsx)(m.lG,{open:z,onOpenChange:R,children:(0,s.jsx)(m.Cf,{className:"max-w-7xl max-h-[95vh] overflow-y-auto p-4",children:(0,s.jsxs)("form",{onSubmit:H,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-8 gap-3",children:[(0,s.jsxs)("div",{className:"col-span-1",children:[(0,s.jsx)(d.J,{htmlFor:"entry_date",className:"text-xs font-medium text-black mb-1 block",children:"\uD83D\uDCC5 التاريخ"}),(0,s.jsx)(c.p,{id:"entry_date",type:"date",value:J.entry_date,onChange:e=>G({...J,entry_date:e.target.value}),required:!0,className:"h-8 text-sm"})]}),(0,s.jsxs)("div",{className:"col-span-1",children:[(0,s.jsx)(d.J,{htmlFor:"currency_id",className:"text-xs font-medium text-yellow-700 mb-1 block",children:"\uD83D\uDCB1 العملة"}),(0,s.jsxs)(o.l6,{value:J.currency_id,onValueChange:e=>G({...J,currency_id:e}),children:[(0,s.jsx)(o.bq,{className:"h-8 text-sm",children:(0,s.jsx)(o.yv,{placeholder:"اختر العملة"})}),(0,s.jsx)(o.gC,{children:y.map(e=>(0,s.jsxs)(o.eb,{value:e.id.toString(),children:[e.currency_name," (",e.symbol,")"]},e.id))})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(d.J,{htmlFor:"description",className:"text-xs font-medium text-black mb-1 block",children:"\uD83D\uDCDD البيان"}),(0,s.jsx)(c.p,{id:"description",value:J.description,onChange:e=>G({...J,description:e.target.value}),required:!0,placeholder:"وصف مختصر للقيد...",className:"h-8 text-sm"})]}),(0,s.jsxs)("div",{className:"col-span-1",children:[(0,s.jsx)(d.J,{htmlFor:"cost_center_id",className:"text-xs font-medium text-black mb-1 block",children:"\uD83C\uDFE2 م.التكلفة"}),(0,s.jsxs)(o.l6,{value:J.cost_center_id,onValueChange:e=>G({...J,cost_center_id:e}),children:[(0,s.jsx)(o.bq,{className:"h-8 text-sm",children:(0,s.jsx)(o.yv,{placeholder:"اختر مركز التكلفة"})}),(0,s.jsxs)(o.gC,{className:"max-h-60",children:[(0,s.jsx)(o.eb,{value:"0",children:"بدون مركز تكلفة"}),C.map(e=>(0,s.jsx)(o.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.center_name})},e.id))]})]})]}),(0,s.jsxs)("div",{className:"col-span-1",children:[(0,s.jsx)(d.J,{htmlFor:"service_id",className:"text-xs font-medium text-teal-700 mb-1 block",children:"\uD83D\uDD27 الخدمة"}),(0,s.jsxs)(o.l6,{value:J.service_id,onValueChange:e=>G({...J,service_id:e}),children:[(0,s.jsx)(o.bq,{className:"h-8 text-sm",children:(0,s.jsx)(o.yv,{placeholder:"اختر الخدمة"})}),(0,s.jsxs)(o.gC,{className:"max-h-60",children:[(0,s.jsx)(o.eb,{value:"0",children:"بدون خدمة"}),A.map(e=>(0,s.jsx)(o.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.name})},e.id))]})]})]}),(0,s.jsxs)("div",{className:"col-span-1",children:[(0,s.jsx)(d.J,{htmlFor:"reference_number",className:"text-xs font-medium text-blue-700 mb-1 block",children:"\uD83D\uDCC4 المرجع"}),(0,s.jsx)(c.p,{id:"reference_number",value:J.reference_number,onChange:e=>G({...J,reference_number:e.target.value}),placeholder:"رقم المرجع",className:"h-8 text-sm"})]})]}),(0,s.jsxs)(n.Zp,{className:"shadow-md",children:[(0,s.jsx)(n.aR,{className:"bg-gradient-to-r from-green-50 to-green-100 border-b",children:(0,s.jsx)(n.ZB,{className:"flex items-center text-black",children:"\uD83D\uDCCA تفاصيل القيد"})}),(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-2 bg-gray-50 p-3 rounded-lg font-semibold text-sm text-black",children:[(0,s.jsx)("div",{className:"col-span-3",children:"الحساب"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"مدين"}),(0,s.jsx)("div",{className:"col-span-2 text-center",children:"دائن"}),(0,s.jsx)("div",{className:"col-span-2",children:"القضية"}),(0,s.jsx)("div",{className:"col-span-2",children:"البيان"}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:"إجراءات"})]}),V.map((e,t)=>(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-center p-2 border rounded-lg",children:[(0,s.jsx)("div",{className:"col-span-3",children:(0,s.jsxs)("select",{value:e.account_id?.toString()||"",onChange:e=>W(t,"account_id",e.target.value),className:"w-full h-8 text-sm p-2 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500",style:{color:"black"},children:[(0,s.jsx)("option",{value:"",style:{color:"black"},children:"اختر الحساب..."}),(0,s.jsx)("optgroup",{label:"\uD83D\uDCB0 الأصول",children:a.filter(e=>"أصول"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))}),(0,s.jsx)("optgroup",{label:"\uD83D\uDCCA الخصوم",children:a.filter(e=>"خصوم"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))}),(0,s.jsx)("optgroup",{label:"\uD83D\uDCBC حقوق الملكية",children:a.filter(e=>"حقوق الملكية"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))}),(0,s.jsx)("optgroup",{label:"\uD83D\uDCC8 الإيرادات",children:a.filter(e=>"إيرادات"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))}),(0,s.jsx)("optgroup",{label:"\uD83D\uDCC9 المصروفات",children:a.filter(e=>"مصروفات"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))})]})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)(c.p,{type:"number",step:"0.01",value:e.debit_amount||"",onChange:e=>W(t,"debit_amount",parseFloat(e.target.value)||0),placeholder:"0.00",className:"h-8 text-sm text-center"})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)(c.p,{type:"number",step:"0.01",value:e.credit_amount||"",onChange:e=>W(t,"credit_amount",parseFloat(e.target.value)||0),placeholder:"0.00",className:"h-8 text-sm text-center"})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsxs)(o.l6,{value:e.case_id?.toString()||"0",onValueChange:e=>W(t,"case_id",parseInt(e)),children:[(0,s.jsx)(o.bq,{className:"h-8 text-sm",children:(0,s.jsx)(o.yv,{placeholder:"اختر القضية"})}),(0,s.jsxs)(o.gC,{className:"max-h-60",children:[(0,s.jsx)(o.eb,{value:"0",children:"بدون قضية"}),$.map(e=>(0,s.jsx)(o.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.title})},e.id))]})]})}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)(c.p,{value:e.description||"",onChange:e=>W(t,"description",e.target.value),placeholder:"بيان الحساب...",className:"h-8 text-sm"})}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:(0,s.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>Z(t),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})})]},t)),(0,s.jsxs)(l.$,{type:"button",variant:"outline",onClick:()=>{let e={line_number:V.length+1,account_id:"",debit_amount:0,credit_amount:0,currency_id:1,exchange_rate:1,description:""};B([...V,e])},className:"w-full border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"إضافة صف جديد"]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm text-black",children:"إجمالي المدين"}),(0,s.jsxs)("div",{className:"text-lg font-bold text-black",children:[es.toLocaleString()," ر.ي"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm text-black",children:"إجمالي الدائن"}),(0,s.jsxs)("div",{className:"text-lg font-bold text-black",children:[er.toLocaleString()," ر.ي"]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm text-black",children:"الفرق"}),(0,s.jsxs)("div",{className:`text-lg font-bold ${ei?"text-black":"text-red-600"}`,children:[Math.abs(es-er).toLocaleString()," ر.ي"]})]})]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t",children:[(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>R(!1),children:"إلغاء"}),(0,s.jsx)("div",{className:"flex space-x-2 space-x-reverse",children:(0,s.jsx)(l.$,{type:"submit",disabled:!ei,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400",children:F?"تحديث القيد":"حفظ القيد"})})]}),!ei&&(0,s.jsx)(n.Zp,{className:"border-red-200 bg-red-50",children:(0,s.jsx)(n.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center text-red-700",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:"⚠️ يجب أن يكون مجموع المدين مساوياً لمجموع الدائن لحفظ القيد"})]})})})]})})})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>u,L3:()=>x,c7:()=>p,lG:()=>c,rr:()=>h});var s=a(60687),r=a(43210),i=a(37908),n=a(11860),l=a(4780);let c=i.bL;i.l9;let d=i.ZL;i.bm;let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.hJ,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));o.displayName=i.hJ.displayName;let m=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(d,{children:[(0,s.jsx)(o,{}),(0,s.jsxs)(i.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.UC.displayName;let p=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let u=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});u.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.hE,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=i.hE.displayName;let h=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.VY,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=i.VY.displayName},71444:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},76974:(e,t,a)=>{Promise.resolve().then(a.bind(a,36193))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("label",{ref:a,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName="Label"},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88607:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\journal-entries\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\journal-entries\\page.tsx","default")},92484:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>d});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let d={children:["",{children:["accounting",{children:["journal-entries",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,88607)),"D:\\mohaminew\\src\\app\\accounting\\journal-entries\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\journal-entries\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/accounting/journal-entries/page",pathname:"/accounting/journal-entries",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93613:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,8409,4036,8106,2614,7932],()=>a(92484));module.exports=s})();