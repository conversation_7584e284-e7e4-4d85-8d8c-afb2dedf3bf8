import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

export async function GET() {
  let pool: Pool | null = null
  
  try {
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: process.env.DB_PASSWORD || 'your_password_here',
      connectionTimeoutMillis: 5000
    }

    pool = new Pool(dbConfig)
    const client = await pool.connect()
    
    // التحقق من الجداول الموجودة
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `)
    
    client.release()
    
    const existingTables = result.rows.map(row => row.table_name)
    const requiredTables = [
      'clients', 'employees', 'issues', 'issue_types', 
      'lineages', 'follows', 'movements', 'users'
    ]
    
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))
    
    if (missingTables.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'جميع الجداول المطلوبة موجودة',
        details: `الجداول الموجودة: ${existingTables.join(', ')}`
      })
    } else {
      return NextResponse.json({
        success: false,
        message: `يوجد ${missingTables.length} جداول مفقودة`,
        details: `الجداول المفقودة: ${missingTables.join(', ')}\nالجداول الموجودة: ${existingTables.join(', ')}`
      })
    }
    
  } catch (error: any) {
    console.error('خطأ في فحص الجداول:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في فحص الجداول',
      details: `رمز الخطأ: ${error.code || 'غير محدد'}\nالرسالة: ${error.message}`
    }, { status: 500 })
    
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
