// إصلاح إعدادات الذكاء الاصطناعي لقاعدة بيانات rubaie
const { Pool } = require('pg');

async function fixRubaieAISettings() {
  console.log('🔧 إصلاح إعدادات الذكاء الاصطناعي لقاعدة بيانات rubaie...\n');

  const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    // حذف البيانات الموجودة وإدراج جديدة
    console.log('🗑️ حذف البيانات الموجودة...');
    await pool.query('DELETE FROM ai_settings');

    console.log('➕ إدراج بيانات جديدة...');
    
    const welcomeMessage = 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

    await pool.query(`
      INSERT INTO ai_settings (
        enabled, 
        is_enabled,
        welcome_message, 
        default_response, 
        auto_respond,
        keywords_trigger,
        model,
        delay_seconds
      ) VALUES (
        true,
        true,
        $1,
        'شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك من قبل فريقنا المختص في أقرب وقت ممكن.',
        true,
        ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم'],
        'gpt-3.5-turbo',
        2
      )
    `, [welcomeMessage]);

    console.log('✅ تم إدراج البيانات الجديدة بنجاح');

    // التحقق من النتيجة
    const result = await pool.query('SELECT * FROM ai_settings LIMIT 1');
    if (result.rows.length > 0) {
      const settings = result.rows[0];
      console.log('📋 الحالة النهائية:');
      console.log(`   - enabled: ${settings.enabled}`);
      console.log(`   - is_enabled: ${settings.is_enabled}`);
      console.log(`   - auto_respond: ${settings.auto_respond}`);
      console.log(`   - رسالة الترحيب: ${settings.welcome_message.substring(0, 50)}...`);
    }

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error.message);
  } finally {
    await pool.end();
  }

  console.log('\n✅ تم الانتهاء من إصلاح قاعدة بيانات rubaie');
}

// تشغيل الإصلاح
fixRubaieAISettings().catch(console.error);
