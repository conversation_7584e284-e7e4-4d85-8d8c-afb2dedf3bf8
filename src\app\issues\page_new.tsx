'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ClientSelect } from '@/components/ui/client-select'
import MainLayout from '@/components/layout/main-layout'
import { Scale, Plus, Edit, Eye, Trash2, Search, Save, X } from 'lucide-react'

interface Issue {
  id: number
  case_number: string
  title: string
  description: string
  client_id: number
  client_name: string
  client_phone: string
  court_id: number
  court_name: string
  issue_type: string
  status: string
  case_amount: number
  currency_id: number
  currency_symbol: string
  amount_yer: number
  notes: string
  contract_method: string
  contract_date: string
  created_date: string
}

interface Court {
  id: number
  name: string
  governorate_name: string
}

interface Currency {
  id: number
  currency_name: string
  symbol: string
  exchange_rate: number
  is_base_currency: boolean
}

interface Status {
  value: string
  label: string
}

interface ContractMethod {
  value: string
  label: string
}

interface IssueType {
  id: number
  name: string
}

export default function IssuesPage() {
  const [issues, setIssues] = useState<Issue[]>([])
  const [courts, setCourts] = useState<Court[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [statuses, setStatuses] = useState<Status[]>([])
  const [contractMethods, setContractMethods] = useState<ContractMethod[]>([])
  const [issueTypes, setIssueTypes] = useState<IssueType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingIssue, setEditingIssue] = useState<Issue | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_id: '',
    client_name: '',
    client_phone: '',
    court_id: '',
    court_name: '',
    issue_type: '',
    status: 'new',
    case_amount: '',
    currency_id: '1',
    notes: '',
    contract_method: 'بالجلسة',
    contract_date: new Date().toISOString().split('T')[0]
  })

  // جلب القضايا
  const fetchIssues = async () => {
    try {
      const response = await fetch('/api/issues')
      const result = await response.json()
      if (result.success) {
        setIssues(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching issues:', error)
    }
  }

  // جلب المحاكم
  const fetchCourts = async () => {
    try {
      const response = await fetch('/api/courts')
      const result = await response.json()
      if (result.success) {
        setCourts(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching courts:', error)
    }
  }

  // جلب العملات
  const fetchCurrencies = async () => {
    try {
      const response = await fetch('/api/currencies')
      const result = await response.json()
      if (result.success) {
        setCurrencies(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching currencies:', error)
    }
  }

  // جلب حالات القضايا
  const fetchStatuses = async () => {
    try {
      const response = await fetch('/api/issue-statuses')
      const result = await response.json()
      if (result.success) {
        setStatuses(result.data || [])
      } else {
        // بيانات افتراضية في حالة عدم وجود API
        setStatuses([
          { value: 'new', label: 'جديدة' },
          { value: 'pending', label: 'معلقة' },
          { value: 'in_progress', label: 'قيد المعالجة' },
          { value: 'completed', label: 'مكتملة' },
          { value: 'cancelled', label: 'ملغية' }
        ])
      }
    } catch (error) {
      console.error('Error fetching statuses:', error)
      // بيانات افتراضية في حالة الخطأ
      setStatuses([
        { value: 'new', label: 'جديدة' },
        { value: 'pending', label: 'معلقة' },
        { value: 'in_progress', label: 'قيد المعالجة' },
        { value: 'completed', label: 'مكتملة' },
        { value: 'cancelled', label: 'ملغية' }
      ])
    }
  }

  // جلب طرق التعاقد
  const fetchContractMethods = async () => {
    try {
      const response = await fetch('/api/contract-methods')
      const result = await response.json()
      if (result.success) {
        setContractMethods(result.data || [])
      } else {
        // بيانات افتراضية في حالة عدم وجود API
        setContractMethods([
          { value: 'بالجلسة', label: 'بالجلسة' },
          { value: 'بالعقد', label: 'بالعقد' }
        ])
      }
    } catch (error) {
      console.error('Error fetching contract methods:', error)
      // بيانات افتراضية في حالة الخطأ
      setContractMethods([
        { value: 'بالجلسة', label: 'بالجلسة' },
        { value: 'بالعقد', label: 'بالعقد' }
      ])
    }
  }

  // جلب أنواع القضايا
  const fetchIssueTypes = async () => {
    try {
      const response = await fetch('/api/issue-types')
      const result = await response.json()
      if (result.success) {
        setIssueTypes(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching issue types:', error)
    }
  }

  // حساب المبلغ بالريال اليمني
  const calculateAmountYer = (amount: string, currencyId: string) => {
    const numAmount = parseFloat(amount) || 0
    const currency = currencies.find(c => c.id.toString() === currencyId)

    if (!currency) return numAmount
    if (currency.is_base_currency) return numAmount

    return numAmount * currency.exchange_rate
  }

  // معالجة تغيير العميل
  const handleClientChange = (clientId: string, clientData?: any) => {
    setFormData({
      ...formData,
      client_id: clientId,
      client_name: clientData?.name || '',
      client_phone: clientData?.phone || ''
    })
  }

  // إضافة قضية جديدة
  const handleAddNew = () => {
    setEditingIssue(null)
    setFormData({
      case_number: '',
      title: '',
      description: '',
      client_id: '',
      client_name: '',
      client_phone: '',
      court_id: '',
      court_name: '',
      issue_type: '',
      status: 'new',
      case_amount: '',
      currency_id: '1',
      notes: '',
      contract_method: 'بالجلسة',
      contract_date: new Date().toISOString().split('T')[0]
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  // تعديل قضية
  const handleEdit = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      description: issue.description,
      client_id: issue.client_id.toString(),
      client_name: issue.client_name,
      client_phone: issue.client_phone,
      court_id: issue.court_id?.toString() || '',
      court_name: issue.court_name || '',
      issue_type: issue.issue_type || '',
      status: issue.status,
      case_amount: issue.case_amount?.toString() || '',
      currency_id: issue.currency_id?.toString() || '1',
      notes: issue.notes || '',
      contract_method: issue.contract_method || 'بالجلسة',
      contract_date: issue.contract_date || new Date().toISOString().split('T')[0]
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  // عرض قضية
  const handleView = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      description: issue.description,
      client_id: issue.client_id.toString(),
      client_name: issue.client_name,
      client_phone: issue.client_phone,
      court_id: issue.court_id?.toString() || '',
      court_name: issue.court_name || '',
      issue_type: issue.issue_type || '',
      status: issue.status,
      case_amount: issue.case_amount?.toString() || '',
      currency_id: issue.currency_id?.toString() || '1',
      notes: issue.notes || '',
      contract_method: issue.contract_method || 'بالجلسة',
      contract_date: issue.contract_date || new Date().toISOString().split('T')[0]
    })
    setModalType('view')
    setIsModalOpen(true)
  }

  // حفظ القضية
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/issues', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, created_by: 1 })
        })

        const result = await response.json()
        if (result.success) {
          alert('تم إضافة القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في إضافة القضية')
          return
        }
      } else if (modalType === 'edit' && editingIssue) {
        const response = await fetch(`/api/issues/${editingIssue.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()
        if (result.success) {
          alert('تم تحديث القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في تحديث القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingIssue(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  // حذف قضية
  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه القضية؟')) {
      try {
        const response = await fetch(`/api/issues/${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()
        if (result.success) {
          alert('تم حذف القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في حذف القضية')
        }
      } catch (error) {
        console.error('Error deleting issue:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await Promise.all([
        fetchIssues(),
        fetchCourts(),
        fetchCurrencies(),
        fetchStatuses(),
        fetchContractMethods(),
        fetchIssueTypes()
      ])
      setIsLoading(false)
    }
    loadData()
  }, [])

  // تصفية القضايا
  const filteredIssues = issues.filter(issue => {
    const matchesSearch =
      issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.client_name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || issue.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // إحصائيات
  const stats = {
    total: issues.length,
    new: issues.filter(i => i.status === 'new').length,
    pending: issues.filter(i => i.status === 'pending').length,
    in_progress: issues.filter(i => i.status === 'in_progress').length,
    completed: issues.filter(i => i.status === 'completed').length
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* العنوان والزر */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Scale className="h-8 w-8 mr-3 text-emerald-600" />
              إدارة القضايا
            </h1>
            <p className="text-gray-700 mt-1">إدارة ومتابعة جميع القضايا القانونية</p>
          </div>
          <Button onClick={handleAddNew} className="bg-emerald-600 hover:bg-emerald-700 text-white">
            <Plus className="h-4 w-4 mr-2 text-white" />
            إضافة قضية جديدة
          </Button>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-gray-600">إجمالي القضايا</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.new}</div>
                <div className="text-sm text-gray-600">جديدة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
                <div className="text-sm text-gray-600">معلقة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.in_progress}</div>
                <div className="text-sm text-gray-600">قيد المعالجة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">{stats.completed}</div>
                <div className="text-sm text-gray-600">مكتملة</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* البحث والفلترة */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4" />
                <Input
                  placeholder="البحث برقم القضية، العنوان، أو اسم الموكل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 text-black"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                >
                  <option value="all">جميع الحالات</option>
                  {statuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول القضايا */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة القضايا ({filteredIssues.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredIssues.length === 0 ? (
              <div className="text-center py-8">
                <Scale className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد قضايا</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">رقم القضية</th>
                      <th className="text-right p-3 font-semibold">العنوان</th>
                      <th className="text-right p-3 font-semibold">الموكل</th>
                      <th className="text-center p-3 font-semibold">المحكمة</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">المبلغ</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredIssues.map((issue) => (
                      <tr key={issue.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium text-blue-600">{issue.case_number}</td>
                        <td className="p-3">{issue.title}</td>
                        <td className="p-3">
                          <div>
                            <div className="font-medium">{issue.client_name}</div>
                            <div className="text-sm text-gray-500">{issue.client_phone}</div>
                          </div>
                        </td>
                        <td className="p-3 text-center">{issue.court_name || '-'}</td>
                        <td className="p-3 text-center">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            issue.status === 'new' ? 'bg-green-100 text-green-800' :
                            issue.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            issue.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            issue.status === 'completed' ? 'bg-emerald-100 text-emerald-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {statuses.find(s => s.value === issue.status)?.label || issue.status}
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <div className="text-sm">
                            <div>{(issue.case_amount || 0).toLocaleString()} {issue.currency_symbol}</div>
                            <div className="text-gray-500">{(issue.amount_yer || 0).toLocaleString()} ر.ي</div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(issue)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(issue)}
                              className="text-green-600 hover:text-green-800"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(issue.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
