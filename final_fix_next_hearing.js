// إصلاح نهائي للعمود next_hearing مع Triggers
const { Pool } = require('pg');

async function finalFixNextHearing() {
  console.log('🔧 إصلاح نهائي للعمود next_hearing مع Triggers...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص الأعمدة الموجودة
      console.log('\n   🔍 فحص الأعمدة الموجودة:');
      
      const columns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name LIKE '%hearing%'
        ORDER BY column_name
      `);

      console.log('      الأعمدة الموجودة:');
      columns.rows.forEach(col => {
        console.log(`         - ${col.column_name}: ${col.data_type}`);
      });

      // 2. حذف العمود المكرر إذا وجد
      const nextHearingDateExists = columns.rows.some(col => col.column_name === 'next_hearing_date');
      if (nextHearingDateExists) {
        console.log('\n   🗑️ حذف العمود المكرر next_hearing_date:');
        await pool.query(`ALTER TABLE issues DROP COLUMN IF EXISTS next_hearing_date`);
        console.log('      ✅ تم حذف العمود next_hearing_date');
      }

      // 3. التأكد من وجود العمود next_hearing
      const nextHearingExists = columns.rows.some(col => col.column_name === 'next_hearing');
      if (!nextHearingExists) {
        console.log('\n   ➕ إضافة العمود next_hearing:');
        await pool.query(`ALTER TABLE issues ADD COLUMN next_hearing TIMESTAMP`);
        console.log('      ✅ تم إضافة العمود next_hearing');
      } else {
        console.log('\n   ✅ العمود next_hearing موجود');
      }

      // 4. تنظيف جدول الجلسات
      console.log('\n   🧹 تنظيف جدول الجلسات:');
      
      await pool.query(`DROP TABLE IF EXISTS hearings CASCADE`);
      console.log('      ✅ تم حذف جدول الجلسات القديم');

      // 5. إنشاء جدول الجلسات الجديد
      console.log('\n   📅 إنشاء جدول الجلسات الجديد:');
      
      await pool.query(`
        CREATE TABLE hearings (
          id SERIAL PRIMARY KEY,
          issue_id INTEGER NOT NULL,
          hearing_date TIMESTAMP NOT NULL,
          hearing_type VARCHAR(100) DEFAULT 'مرافعة',
          status VARCHAR(50) DEFAULT 'scheduled',
          notes TEXT,
          judge_name VARCHAR(255),
          location VARCHAR(255),
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT fk_hearings_issue_id 
            FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE
        )
      `);
      console.log('      ✅ تم إنشاء جدول الجلسات الجديد');

      // 6. إنشاء الفهارس
      await pool.query(`
        CREATE INDEX idx_hearings_issue_id ON hearings(issue_id);
        CREATE INDEX idx_hearings_date ON hearings(hearing_date);
        CREATE INDEX idx_hearings_status ON hearings(status);
      `);
      console.log('      ✅ تم إنشاء الفهارس');

      // 7. إنشاء دالة تحديث next_hearing
      console.log('\n   🔄 إنشاء دالة تحديث next_hearing:');
      
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_next_hearing_for_issue(p_issue_id INTEGER)
        RETURNS VOID AS $$
        DECLARE
          next_date TIMESTAMP;
        BEGIN
          -- البحث عن أقرب جلسة مستقبلية
          SELECT hearing_date INTO next_date
          FROM hearings 
          WHERE issue_id = p_issue_id 
          AND hearing_date > CURRENT_TIMESTAMP
          AND status IN ('scheduled', 'postponed')
          ORDER BY hearing_date ASC
          LIMIT 1;
          
          -- تحديث العمود next_hearing
          UPDATE issues 
          SET next_hearing = next_date,
              updated_date = CURRENT_TIMESTAMP
          WHERE id = p_issue_id;
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('      ✅ تم إنشاء دالة update_next_hearing_for_issue');

      // 8. إنشاء triggers
      console.log('\n   🔄 إنشاء triggers:');
      
      // Trigger للإدراج
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_hearings_insert()
        RETURNS TRIGGER AS $$
        BEGIN
          PERFORM update_next_hearing_for_issue(NEW.issue_id);
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS tr_hearings_insert ON hearings;
        CREATE TRIGGER tr_hearings_insert
          AFTER INSERT ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_hearings_insert();
      `);
      console.log('      ✅ تم إنشاء trigger للإدراج');

      // Trigger للتحديث
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_hearings_update()
        RETURNS TRIGGER AS $$
        BEGIN
          -- تحديث القضية القديمة إذا تغير issue_id
          IF NEW.issue_id != OLD.issue_id THEN
            PERFORM update_next_hearing_for_issue(OLD.issue_id);
          END IF;
          
          -- تحديث القضية الجديدة
          PERFORM update_next_hearing_for_issue(NEW.issue_id);
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS tr_hearings_update ON hearings;
        CREATE TRIGGER tr_hearings_update
          AFTER UPDATE ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_hearings_update();
      `);
      console.log('      ✅ تم إنشاء trigger للتحديث');

      // Trigger للحذف
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_hearings_delete()
        RETURNS TRIGGER AS $$
        BEGIN
          PERFORM update_next_hearing_for_issue(OLD.issue_id);
          RETURN OLD;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS tr_hearings_delete ON hearings;
        CREATE TRIGGER tr_hearings_delete
          AFTER DELETE ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_hearings_delete();
      `);
      console.log('      ✅ تم إنشاء trigger للحذف');

      // 9. إدراج بيانات تجريبية
      console.log('\n   📅 إدراج بيانات جلسات تجريبية:');
      
      const issues = await pool.query(`SELECT id, case_number FROM issues ORDER BY id`);
      
      for (const issue of issues.rows) {
        try {
          // جلسة ماضية (مكتملة)
          const pastDate = new Date();
          pastDate.setDate(pastDate.getDate() - 5);
          
          await pool.query(`
            INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes, judge_name)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            issue.id, 
            pastDate.toISOString(), 
            'مرافعة أولى', 
            'completed', 
            'جلسة مرافعة أولى مكتملة', 
            'القاضي أحمد محمد'
          ]);

          // جلسة قادمة
          const futureDate = new Date();
          futureDate.setDate(futureDate.getDate() + (Math.floor(Math.random() * 20) + 5)); // بين 5-25 يوم
          
          await pool.query(`
            INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes, judge_name)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            issue.id, 
            futureDate.toISOString(), 
            'مرافعة ثانية', 
            'scheduled', 
            'جلسة مرافعة ثانية', 
            'القاضي أحمد محمد'
          ]);

          console.log(`      ✅ ${issue.case_number}: تم إدراج جلستين`);
        } catch (error) {
          console.log(`      ❌ ${issue.case_number}: ${error.message}`);
        }
      }

      // 10. اختبار الـ triggers
      console.log('\n   🧪 اختبار الـ triggers:');
      
      if (issues.rows.length > 0) {
        const testIssue = issues.rows[0];
        console.log(`      🧪 اختبار إدراج جلسة جديدة للقضية ${testIssue.case_number}...`);
        
        // إدراج جلسة جديدة أقرب
        const nearDate = new Date();
        nearDate.setDate(nearDate.getDate() + 3);
        
        const insertResult = await pool.query(`
          INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, hearing_date
        `, [
          testIssue.id, 
          nearDate.toISOString(), 
          'جلسة عاجلة', 
          'scheduled', 
          'جلسة عاجلة للاختبار'
        ]);

        if (insertResult.rows.length > 0) {
          console.log(`      ✅ تم إدراج جلسة جديدة: ${insertResult.rows[0].hearing_date}`);
          
          // فحص تحديث next_hearing
          const updatedIssue = await pool.query(`
            SELECT next_hearing FROM issues WHERE id = $1
          `, [testIssue.id]);
          
          if (updatedIssue.rows.length > 0) {
            console.log(`      ✅ تم تحديث next_hearing: ${updatedIssue.rows[0].next_hearing}`);
          }
          
          // حذف الجلسة التجريبية
          await pool.query(`DELETE FROM hearings WHERE id = $1`, [insertResult.rows[0].id]);
          console.log(`      🗑️ تم حذف الجلسة التجريبية`);
        }
      }

      // 11. عرض النتائج النهائية
      console.log('\n   📊 النتائج النهائية:');
      
      const finalResults = await pool.query(`
        SELECT 
          i.case_number,
          i.next_hearing,
          COUNT(h.id) as total_hearings,
          COUNT(CASE WHEN h.hearing_date > CURRENT_TIMESTAMP AND h.status IN ('scheduled', 'postponed') THEN 1 END) as future_hearings
        FROM issues i
        LEFT JOIN hearings h ON i.id = h.issue_id
        GROUP BY i.id, i.case_number, i.next_hearing
        ORDER BY i.case_number
      `);

      console.log('      📋 القضايا وجلساتها:');
      finalResults.rows.forEach(row => {
        console.log(`         📄 ${row.case_number}:`);
        console.log(`            - الجلسة القادمة: ${row.next_hearing ? new Date(row.next_hearing).toLocaleDateString('ar-EG') : 'لا توجد'}`);
        console.log(`            - إجمالي الجلسات: ${row.total_hearings}`);
        console.log(`            - جلسات مستقبلية: ${row.future_hearings}`);
      });

      // 12. إحصائيات نهائية
      console.log('\n   📈 إحصائيات نهائية:');
      
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM hearings) as total_hearings,
          (SELECT COUNT(*) FROM issues WHERE next_hearing IS NOT NULL) as issues_with_next_hearing,
          (SELECT COUNT(*) FROM hearings WHERE hearing_date > CURRENT_TIMESTAMP AND status IN ('scheduled', 'postponed')) as future_hearings
      `);

      const finalStats = stats.rows[0];
      console.log(`      - إجمالي القضايا: ${finalStats.total_issues}`);
      console.log(`      - إجمالي الجلسات: ${finalStats.total_hearings}`);
      console.log(`      - قضايا بها جلسة قادمة: ${finalStats.issues_with_next_hearing}`);
      console.log(`      - جلسات مستقبلية: ${finalStats.future_hearings}`);

    } catch (error) {
      console.error(`   ❌ خطأ في قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من الإصلاح النهائي');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تنظيف الأعمدة المكررة');
  console.log('2. ✅ إنشاء جدول hearings مع العلاقة الصحيحة');
  console.log('3. ✅ إضافة العمود next_hearing');
  console.log('4. ✅ إنشاء دالة update_next_hearing_for_issue');
  console.log('5. ✅ إنشاء triggers للتحديث التلقائي');
  console.log('6. ✅ إدراج بيانات جلسات تجريبية');
  console.log('7. ✅ اختبار الـ triggers');
  console.log('8. ✅ العمود next_hearing يُحدث تلقائياً');
}

// تشغيل الإصلاح
finalFixNextHearing().catch(console.error);
