{"name": "legal-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 7443", "dev:rubaie": "cross-env NODE_ENV=development DOTENV_CONFIG_PATH=.env.rubaie next dev -p 8914", "build": "next build", "build:rubaie": "cross-env DOTENV_CONFIG_PATH=.env.rubaie next build", "start": "next start -p 7443", "start:rubaie": "cross-env DOTENV_CONFIG_PATH=.env.rubaie next start -p 8914", "start:dev": "next dev -p 7443", "start:prod": "next build && next start -p 7443"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.15", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.5", "node-fetch": "^3.3.2", "pg": "^8.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.6.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "5.9.2"}}