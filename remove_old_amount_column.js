// حذف العمود القديم amount من جدول القضايا
const { Pool } = require('pg');

async function removeOldAmountColumn() {
  console.log('🗑️ حذف العمود القديم amount من جدول القضايا...\n');

  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    console.log('='.repeat(40));
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص وجود العمود amount
      console.log('\n   🔍 فحص وجود العمود amount:');
      
      const columnExists = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'amount'
      `);

      if (columnExists.rows.length === 0) {
        console.log('      ✅ العمود amount غير موجود (تم حذفه مسبقاً)');
        continue;
      }

      console.log('      📊 العمود amount موجود');

      // 2. فحص البيانات في العمود amount
      console.log('\n   📊 فحص البيانات في العمود amount:');
      
      const amountData = await pool.query(`
        SELECT 
          COUNT(*) as total_records,
          COUNT(amount) as records_with_amount,
          COUNT(case_amount) as records_with_case_amount,
          SUM(amount) as total_amount,
          SUM(case_amount) as total_case_amount
        FROM issues
      `);

      const stats = amountData.rows[0];
      console.log(`      - إجمالي السجلات: ${stats.total_records}`);
      console.log(`      - سجلات بـ amount: ${stats.records_with_amount}`);
      console.log(`      - سجلات بـ case_amount: ${stats.records_with_case_amount}`);
      console.log(`      - مجموع amount: ${stats.total_amount || 0}`);
      console.log(`      - مجموع case_amount: ${stats.total_case_amount || 0}`);

      // 3. التحقق من أن جميع البيانات تم نقلها
      console.log('\n   🔄 التحقق من نقل البيانات:');
      
      const dataCheck = await pool.query(`
        SELECT 
          case_number,
          amount,
          case_amount
        FROM issues 
        WHERE amount IS NOT NULL AND case_amount IS NULL
        ORDER BY case_number
      `);

      if (dataCheck.rows.length > 0) {
        console.log(`      ⚠️ يوجد ${dataCheck.rows.length} سجل لم يتم نقل بياناته:`);
        dataCheck.rows.forEach(row => {
          console.log(`         ${row.case_number}: amount=${row.amount}, case_amount=${row.case_amount}`);
        });
        
        // نقل البيانات المتبقية
        console.log('\n   📋 نقل البيانات المتبقية:');
        const transferResult = await pool.query(`
          UPDATE issues 
          SET case_amount = amount
          WHERE amount IS NOT NULL AND case_amount IS NULL
          RETURNING case_number, amount, case_amount
        `);
        
        console.log(`      ✅ تم نقل ${transferResult.rows.length} سجل`);
      } else {
        console.log('      ✅ جميع البيانات تم نقلها بنجاح');
      }

      // 4. فحص الـ triggers والدوال المرتبطة بالعمود amount
      console.log('\n   🔄 فحص الـ triggers المرتبطة بالعمود amount:');
      
      const triggersCheck = await pool.query(`
        SELECT 
          trigger_name,
          event_manipulation,
          action_statement
        FROM information_schema.triggers
        WHERE event_object_table = 'issues'
        AND action_statement LIKE '%amount%'
        AND action_statement NOT LIKE '%case_amount%'
        AND action_statement NOT LIKE '%amount_yer%'
      `);

      if (triggersCheck.rows.length > 0) {
        console.log(`      ⚠️ يوجد ${triggersCheck.rows.length} trigger مرتبط بالعمود amount:`);
        triggersCheck.rows.forEach(trigger => {
          console.log(`         - ${trigger.trigger_name}: ${trigger.event_manipulation}`);
        });
        console.log('      ⚠️ يجب تحديث هذه الـ triggers قبل حذف العمود');
      } else {
        console.log('      ✅ لا توجد triggers مرتبطة بالعمود amount');
      }

      // 5. حذف العمود amount
      console.log('\n   🗑️ حذف العمود amount:');
      
      if (triggersCheck.rows.length === 0) {
        await pool.query('ALTER TABLE issues DROP COLUMN amount');
        console.log('      ✅ تم حذف العمود amount بنجاح');
      } else {
        console.log('      ⚠️ تم تخطي حذف العمود بسبب وجود triggers مرتبطة');
      }

      // 6. التحقق النهائي
      console.log('\n   ✅ التحقق النهائي:');
      
      const finalCheck = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name IN ('amount', 'case_amount', 'amount_yer')
        ORDER BY column_name
      `);

      console.log('      الأعمدة المتبقية:');
      finalCheck.rows.forEach(col => {
        console.log(`         - ${col.column_name}`);
      });

      // 7. فحص البيانات النهائية
      const finalDataCheck = await pool.query(`
        SELECT 
          COUNT(*) as total_issues,
          COUNT(case_amount) as issues_with_case_amount,
          COUNT(amount_yer) as issues_with_amount_yer,
          SUM(case_amount) as total_case_amount,
          SUM(amount_yer) as total_amount_yer
        FROM issues
      `);

      const finalStats = finalDataCheck.rows[0];
      console.log('\n      📊 الإحصائيات النهائية:');
      console.log(`         - إجمالي القضايا: ${finalStats.total_issues}`);
      console.log(`         - قضايا بـ case_amount: ${finalStats.issues_with_case_amount}`);
      console.log(`         - قضايا بـ amount_yer: ${finalStats.issues_with_amount_yer}`);
      console.log(`         - إجمالي case_amount: ${finalStats.total_case_amount || 0}`);
      console.log(`         - إجمالي amount_yer: ${finalStats.total_amount_yer || 0}`);

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من معالجة العمود amount');
  
  console.log('\n📋 ملخص العملية:');
  console.log('1. ✅ تم فحص وجود العمود amount');
  console.log('2. ✅ تم التحقق من نقل البيانات إلى case_amount');
  console.log('3. ✅ تم فحص الـ triggers المرتبطة');
  console.log('4. ✅ تم حذف العمود amount (إذا لم تكن هناك triggers مرتبطة)');
  console.log('5. ✅ تم التحقق من سلامة البيانات النهائية');
  
  console.log('\n💡 النظام الآن يستخدم:');
  console.log('   - case_amount: المبلغ بالعملة الأصلية');
  console.log('   - amount_yer: المبلغ بالريال اليمني (محسوب تلقائياً)');
}

// تشغيل العملية
removeOldAmountColumn().catch(console.error);
