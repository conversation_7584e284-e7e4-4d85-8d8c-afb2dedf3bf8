/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/system-settings/route";
exports.ids = ["app/api/system-settings/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-settings%2Froute&page=%2Fapi%2Fsystem-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-settings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-settings%2Froute&page=%2Fapi%2Fsystem-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-settings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_system_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/system-settings/route.ts */ \"(rsc)/./src/app/api/system-settings/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_system_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_system_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/system-settings/route\",\n        pathname: \"/api/system-settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/system-settings/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\system-settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_system_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzeXN0ZW0tc2V0dGluZ3MlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnN5c3RlbS1zZXR0aW5ncyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnN5c3RlbS1zZXR0aW5ncyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbW9oYW1pbmV3JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbW9oYW1pbmV3JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNPO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3lzdGVtLXNldHRpbmdzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zeXN0ZW0tc2V0dGluZ3Mvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zeXN0ZW0tc2V0dGluZ3NcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N5c3RlbS1zZXR0aW5ncy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXG1vaGFtaW5ld1xcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxzeXN0ZW0tc2V0dGluZ3NcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-settings%2Froute&page=%2Fapi%2Fsystem-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-settings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/system-settings/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/system-settings/route.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع إعدادات النظام أو إعداد محدد\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const key = searchParams.get('key');\n        let result;\n        if (key) {\n            // جلب إعداد محدد\n            result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM system_settings WHERE setting_key = $1', [\n                key\n            ]);\n        } else {\n            // جلب جميع الإعدادات\n            result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM system_settings ORDER BY setting_key');\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: key ? result.rows[0] || null : result.rows\n        });\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات النظام:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الإعدادات'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة إعداد جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { setting_key, setting_value, setting_type, description, is_editable } = body;\n        if (!setting_key || setting_value === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مفتاح الإعداد والقيمة مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING *\n    `, [\n            setting_key,\n            setting_value,\n            setting_type || 'string',\n            description || '',\n            is_editable !== false\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم إضافة الإعداد بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة إعداد النظام:', error);\n        if (error.code === '23505') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مفتاح الإعداد موجود بالفعل'\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في إضافة الإعداد'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث إعداد موجود\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { setting_key, setting_value, setting_type, description } = body;\n        if (!setting_key || setting_value === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مفتاح الإعداد والقيمة مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود الإعداد وإمكانية تعديله\n        const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM system_settings WHERE setting_key = $1', [\n            setting_key\n        ]);\n        if (existingResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الإعداد غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        if (!existingResult.rows[0].is_editable) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'هذا الإعداد غير قابل للتعديل'\n            }, {\n                status: 403\n            });\n        }\n        // تحديث الإعداد\n        const updateFields = [\n            'setting_value = $2'\n        ];\n        const updateValues = [\n            setting_key,\n            setting_value\n        ];\n        let paramIndex = 3;\n        if (setting_type) {\n            updateFields.push(`setting_type = $${paramIndex}`);\n            updateValues.push(setting_type);\n            paramIndex++;\n        }\n        if (description !== undefined) {\n            updateFields.push(`description = $${paramIndex}`);\n            updateValues.push(description);\n            paramIndex++;\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE system_settings \n      SET ${updateFields.join(', ')}\n      WHERE setting_key = $1\n      RETURNING *\n    `, updateValues);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث الإعداد بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث إعداد النظام:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في تحديث الإعداد'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف إعداد\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const key = searchParams.get('key');\n        if (!key) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مفتاح الإعداد مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود الإعداد وإمكانية حذفه\n        const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM system_settings WHERE setting_key = $1', [\n            key\n        ]);\n        if (existingResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الإعداد غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        if (!existingResult.rows[0].is_editable) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'هذا الإعداد غير قابل للحذف'\n            }, {\n                status: 403\n            });\n        }\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM system_settings WHERE setting_key = $1', [\n            key\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الإعداد بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف إعداد النظام:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف الإعداد'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/system-settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-settings%2Froute&page=%2Fapi%2Fsystem-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-settings%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();