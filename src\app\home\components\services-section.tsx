'use client';

import { useState, useEffect } from 'react';
import { Search, Gavel, FileText, Building, Shield, Briefcase, UserCheck, BookOpen, TrendingUp, CheckCircle, Library, Archive } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

type Service = {
  id: number;
  title: string;
  slug: string;
  description: string;
  content?: string;
  icon_name: string;
  icon_color: string;
  image_url?: string;
  is_active: boolean;
  sort_order: number;
  meta_title?: string;
  meta_description?: string;
  created_date: string;
  updated_date: string;
};

// خريطة الأيقونات
const iconMap: { [key: string]: any } = {
  Gavel,
  FileText,
  Building,
  Shield,
  Briefcase,
  UserCheck,
  BookOpen,
  TrendingUp,
  CheckCircle,
  Search,
  Library,
  Archive
};

interface ServicesSectionProps {
  searchQuery: string;
  onSearch: (query: string) => void;
}

export function ServicesSection({ searchQuery, onSearch }: ServicesSectionProps) {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب الخدمات من قاعدة البيانات
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        // استخدام نفس المنفذ والخادم
        const response = await fetch('/api/serviceslow?active=true');
        const data = await response.json();

        if (data.success) {
          setServices(data.data || []);
        } else {
          setError('فشل في جلب الخدمات');
        }
      } catch (err) {
        console.error('خطأ في جلب الخدمات:', err);
        setError('حدث خطأ في جلب الخدمات');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // الخدمات الافتراضية في حالة عدم وجود بيانات
  const defaultServices: Service[] = [
    {
      id: 1,
      title: 'التقاضي والمرافعات',
      slug: 'litigation-advocacy',
      description: 'تمثيل قضائي احترافي أمام جميع المحاكم والدرجات القضائية مع ضمان أفضل النتائج',
      icon_name: 'Gavel',
      icon_color: '#2563eb',
      is_active: true,
      sort_order: 1,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    },
    {
      id: 2,
      title: 'صياغة العقود والاتفاقيات',
      slug: 'contracts-agreements',
      description: 'إعداد ومراجعة العقود القانونية بأعلى معايير الدقة والاحترافية لحماية مصالحك',
      icon_name: 'FileText',
      icon_color: '#2563eb',
      is_active: true,
      sort_order: 2,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    },
    {
      id: 3,
      title: 'قانون الشركات والاستثمار',
      slug: 'corporate-investment-law',
      description: 'استشارات قانونية متخصصة في تأسيس الشركات والاستثمار والامتثال التنظيمي',
      icon_name: 'Building',
      icon_color: '#2563eb',
      is_active: true,
      sort_order: 3,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    },
    {
      id: 4,
      title: 'القانون الجنائي والدفاع',
      slug: 'criminal-law-defense',
      description: 'دفاع قوي ومتخصص في القضايا الجنائية مع فريق من أمهر المحامين الجنائيين',
      icon_name: 'Shield',
      icon_color: '#2563eb',
      is_active: true,
      sort_order: 4,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    }
  ];

  // استخدام البيانات من قاعدة البيانات أو الافتراضية
  const displayServices = services.length > 0 ? services : defaultServices;

  // تصفية الخدمات حسب البحث
  const filteredServices = searchQuery
    ? displayServices.filter(service =>
        service.title.includes(searchQuery) ||
        service.description.includes(searchQuery)
      )
    : displayServices;

  // عرض أول 6 خدمات فقط
  const visibleServices = filteredServices.slice(0, 6);

  return (
    <section id="services" className="py-20" style={{ background: 'linear-gradient(135deg, #333333 0%, #171717 100%)' }}>
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header - بتصميم تفاهم */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-gradient-to-r from-yellow-600/20 to-yellow-500/10 text-yellow-400 text-sm font-semibold px-6 py-3 rounded-full mb-6 border border-yellow-600/30 tafahum-glow">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
            خدماتنا القانونية المتميزة
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            <span className="block">حلول قانونية</span>
            <span className="block" style={{
              background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>متكاملة واحترافية</span>
          </h2>
          <div className="w-24 h-1 rounded-full mx-auto my-8" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}></div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            نقدم مجموعة شاملة من الخدمات القانونية المتخصصة بأعلى معايير الجودة والاحترافية
            لضمان حماية حقوقك وتحقيق أهدافك القانونية
          </p>
          <div className="flex justify-center mt-8">
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full"></div>
          </div>
        </div>

        {/* Enhanced Search Bar */}
        <div className="max-w-2xl mx-auto mb-16">
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
              <Search className="w-5 h-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="ابحث عن الخدمة التي تحتاجها..."
              className="w-full pr-12 pl-6 py-4 text-lg border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 text-right bg-white shadow-lg transition-all duration-300"
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
            />
          </div>
        </div>
        
        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
            <p className="text-gray-300 mt-4">جاري تحميل الخدمات...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-400 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              إعادة المحاولة
            </Button>
          </div>
        )}



        {/* Services Grid */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {visibleServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        )}

        {/* Show More Button */}
        {!loading && !error && filteredServices.length > 6 && (
          <div className="text-center mt-12">
            <Button
              className="bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300"
            >
              عرض المزيد من الخدمات
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}

function ServiceCard({ service }: { service: Service }) {
  const Icon = iconMap[service.icon_name] || iconMap['Briefcase'];

  return (
    <div className="group h-full bg-white text-gray-900 p-8 rounded-lg text-center transition-all duration-300 hover:transform hover:-translate-y-2 shadow-lg hover:shadow-xl">
      <div className="w-16 h-16 mx-auto mb-6 rounded-lg flex items-center justify-center text-white" style={{
        backgroundColor: service.icon_color || '#cca967'
      }}>
        <Icon className="w-8 h-8" />
      </div>
      <h3 className="text-xl font-bold leading-tight mb-4 text-gray-900">
        {service.title}
      </h3>
      <p className="leading-relaxed mb-6 text-gray-600">
        {service.description}
      </p>
      <Link href={`/serviceslow/${service.slug}`}>
        <button className="text-sm px-6 py-2 mt-auto rounded-full border-2 transition-all duration-300 hover:transform hover:-translate-y-1" style={{
          borderColor: '#cca967',
          color: '#cca967'
        }}>
          <span className="flex items-center">
            تفاصيل الخدمة
            <svg className="w-4 h-4 mr-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </span>
        </button>
      </Link>
    </div>
  );
}
