#!/usr/bin/env node

const { Client } = require('pg')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function createAdminUser() {
  console.log('👑 إنشاء المستخدم الأول (مدير النظام)...')
  
  try {
    // جمع بيانات المستخدم
    const username = await question('اسم المستخدم: ')
    const password = await question('كلمة المرور: ')
    const name = await question('الاسم الكامل: ')
    
    if (!username || !password || !name) {
      console.error('❌ جميع البيانات مطلوبة')
      process.exit(1)
    }
    
    // الاتصال بقاعدة البيانات
    const client = new Client({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'legal_system_production',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD
    })
    
    await client.connect()
    console.log('✅ تم الاتصال بقاعدة البيانات')
    
    // إنشاء المستخدم
    const result = await client.query(`
      INSERT INTO users (username, password_hash, name, role, user_type, permissions, status)
      VALUES ($1, $2, $3, 'admin', 'admin', ARRAY['system_admin'], 'active')
      RETURNING id, username, name
    `, [username, password, name])
    
    const newUser = result.rows[0]
    
    // منح جميع الصلاحيات
    const permissions = await client.query('SELECT permission_key FROM permissions')
    for (const perm of permissions.rows) {
      await client.query(`
        INSERT INTO user_permissions (user_id, permission_key, granted_by)
        VALUES ($1, $2, $1)
        ON CONFLICT (user_id, permission_key) DO NOTHING
      `, [newUser.id, perm.permission_key])
    }
    
    console.log('✅ تم إنشاء المستخدم الأول بنجاح!')
    console.log(`👤 المستخدم: ${newUser.username}`)
    console.log(`📛 الاسم: ${newUser.name}`)
    console.log(`🔑 الدور: مدير النظام`)
    
    await client.end()
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم:', error)
    process.exit(1)
  } finally {
    rl.close()
  }
}

createAdminUser()