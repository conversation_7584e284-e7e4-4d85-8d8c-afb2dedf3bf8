import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Facebook, Twitter, Linkedin, Instagram, Youtube, MapPin, Phone, Mail, Clock } from 'lucide-react';

type CompanyData = {
  name: string;
  description: string;
  logo_url?: string;
  address: string;
  city?: string;
  phone: string;
  email: string;
  working_hours?: string;
};

interface FooterLink {
  id: number;
  category: string;
  title: string;
  url: string;
  sort_order: number;
  is_active: boolean;
}

interface FooterProps {
  companyData: CompanyData;
}

export function Footer({ companyData }: FooterProps) {
  const currentYear = new Date().getFullYear();
  const [footerLinks, setFooterLinks] = useState<FooterLink[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFooterLinks();
  }, []);

  const fetchFooterLinks = async () => {
    try {
      const response = await fetch('/api/footer-links');
      const data = await response.json();

      if (data.success) {
        setFooterLinks(data.data.filter((link: FooterLink) => link.is_active));
      }
    } catch (error) {
      console.error('Error fetching footer links:', error);
    } finally {
      setLoading(false);
    }
  };

  // تجميع الروابط حسب الفئة
  const groupedLinks = footerLinks.reduce((acc, link) => {
    if (!acc[link.category]) {
      acc[link.category] = [];
    }
    acc[link.category].push(link);
    return acc;
  }, {} as Record<string, FooterLink[]>);

  // ترتيب الروابط داخل كل فئة
  Object.keys(groupedLinks).forEach(category => {
    groupedLinks[category].sort((a, b) => a.sort_order - b.sort_order);
  });

  const socialLinks = [
    { name: 'فيسبوك', icon: Facebook, href: '#' },
    { name: 'تويتر', icon: Twitter, href: '#' },
    { name: 'لينكد إن', icon: Linkedin, href: '#' },
    { name: 'إنستغرام', icon: Instagram, href: '#' },
    { name: 'يوتيوب', icon: Youtube, href: '#' },
  ];

  return (
    <footer className="pt-20 pb-8" style={{ background: 'linear-gradient(135deg, #222222 0%, #**********%)' }}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
          {/* Company Info - بتصميم تفاهم */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg flex items-center justify-center text-gray-900 mr-3 shadow-lg" style={{
                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'
              }}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-6 h-6"
                >
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  <polyline points="14 2 14 8 20 8" />
                </svg>
              </div>
              <h3 className="text-xl font-bold" style={{ color: '#cca967' }}>{companyData.name}</h3>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {companyData.description}
            </p>
            <div className="flex space-x-4 space-x-reverse">
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 rounded-full flex items-center justify-center text-gray-300 hover:text-gray-900 transition-all duration-300 shadow-lg hover:shadow-xl"
                    style={{
                      background: 'linear-gradient(135deg, #444444 0%, #**********%)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, #444444 0%, #**********%)';
                    }}
                    aria-label={social.name}
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(groupedLinks).map(([category, links]) => (
            <div key={category}>
              <h4 className="text-lg font-semibold mb-6" style={{ color: '#cca967' }}>
                {category}
              </h4>
              <ul className="space-y-3">
                {links.map((link) => (
                  <li key={link.id}>
                    <Link
                      href={link.url || '#'}
                      className="tafahum-footer-widget text-gray-300 hover:text-yellow-400 transition-colors flex items-center group"
                    >
                      <span className="w-1 h-1 bg-yellow-600 rounded-full ml-2 group-hover:bg-yellow-400 transition-colors"></span>
                      {link.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Copyright and Bottom Links */}
        <div className="border-t pt-8 flex flex-col md:flex-row justify-between items-center" style={{ borderColor: 'rgba(204, 169, 103, 0.2)' }}>
          <div className="text-gray-300 text-sm mb-4 md:mb-0">
            © {currentYear} <span style={{ color: '#cca967' }}>{companyData.name}</span>. جميع الحقوق محفوظة.
          </div>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
              سياسة الخصوصية
            </Link>
            <span className="text-gray-600">|</span>
            <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
              الشروط والأحكام
            </Link>
            <span className="text-gray-600">|</span>
            <Link href="/sitemap" className="text-gray-400 hover:text-white text-sm transition-colors">
              خريطة الموقع
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
