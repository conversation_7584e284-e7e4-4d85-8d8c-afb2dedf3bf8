import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: process.env.DB_PASSWORD || 'your_password_here',
})

export async function GET() {
  try {
    const client = await pool.connect()
    
    const query = `
      SELECT 
        t.table_name,
        COUNT(c.column_name) as column_count,
        COALESCE(s.n_live_tup, 0) as row_count,
        pg_size_pretty(pg_total_relation_size(quote_ident(t.table_name)::regclass)) as table_size
      FROM information_schema.tables t
      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
      LEFT JOIN pg_stat_user_tables s ON t.table_name = s.relname
      WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
      GROUP BY t.table_name, s.n_live_tup
      ORDER BY row_count DESC, t.table_name;
    `
    
    const result = await client.query(query)
    client.release()
    
    return NextResponse.json({
      success: true,
      tables: result.rows
    })
    
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب قائمة الجداول'
    }, { status: 500 })
  }
}
