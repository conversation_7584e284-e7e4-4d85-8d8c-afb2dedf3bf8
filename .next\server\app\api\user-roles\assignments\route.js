/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-roles/assignments/route";
exports.ids = ["app/api/user-roles/assignments/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-roles%2Fassignments%2Froute&page=%2Fapi%2Fuser-roles%2Fassignments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-roles%2Fassignments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-roles%2Fassignments%2Froute&page=%2Fapi%2Fuser-roles%2Fassignments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-roles%2Fassignments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_user_roles_assignments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-roles/assignments/route.ts */ \"(rsc)/./src/app/api/user-roles/assignments/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_user_roles_assignments_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_user_roles_assignments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-roles/assignments/route\",\n        pathname: \"/api/user-roles/assignments\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-roles/assignments/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\user-roles\\\\assignments\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_user_roles_assignments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-roles%2Fassignments%2Froute&page=%2Fapi%2Fuser-roles%2Fassignments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-roles%2Fassignments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-roles/assignments/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/user-roles/assignments/route.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب أدوار مستخدم معين\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🔄 جلب أدوار المستخدم: ${userId}`);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT \n        ura.role_name,\n        ur.display_name,\n        ur.description,\n        ura.assigned_date,\n        ura.is_active\n      FROM user_role_assignments ura\n      JOIN user_roles ur ON ura.role_name = ur.role_name\n      WHERE ura.user_id = $1 AND ura.is_active = true\n      ORDER BY ura.assigned_date DESC\n    `, [\n            userId\n        ]);\n        console.log(`📊 المستخدم ${userId} لديه ${result.rows.length} دور`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب أدوار المستخدم:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب أدوار المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - تحديث أدوار مستخدم\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { userId, roles, assignedBy } = body;\n        if (!userId || !Array.isArray(roles)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'بيانات غير صحيحة'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🔄 تحديث أدوار المستخدم: ${userId}`);\n        // بداية المعاملة\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('BEGIN');\n        try {\n            // حذف الأدوار الحالية\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        DELETE FROM user_role_assignments \n        WHERE user_id = $1\n      `, [\n                userId\n            ]);\n            // إضافة الأدوار الجديدة\n            for (const roleName of roles){\n                await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n          INSERT INTO user_role_assignments (user_id, role_name, assigned_by)\n          VALUES ($1, $2, $3)\n        `, [\n                    userId,\n                    roleName,\n                    assignedBy || 1\n                ]);\n            }\n            // تحديث الصلاحيات المجمعة للمستخدم\n            const combinedPermissions = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        SELECT get_user_combined_permissions($1) as permissions\n      `, [\n                userId\n            ]);\n            // تحديث جدول user_permissions بالصلاحيات المجمعة\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        DELETE FROM user_permissions WHERE user_id = $1\n      `, [\n                userId\n            ]);\n            if (combinedPermissions.rows[0]?.permissions) {\n                for (const permission of combinedPermissions.rows[0].permissions){\n                    await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n            INSERT INTO user_permissions (user_id, permission_key, granted_by)\n            VALUES ($1, $2, $3)\n            ON CONFLICT (user_id, permission_key) DO NOTHING\n          `, [\n                        userId,\n                        permission,\n                        assignedBy || 1\n                    ]);\n                }\n            }\n            // تأكيد المعاملة\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('COMMIT');\n            console.log(`✅ تم تحديث أدوار المستخدم ${userId} - ${roles.length} دور`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم تحديث الأدوار بنجاح',\n                data: {\n                    userId,\n                    rolesCount: roles.length,\n                    permissionsCount: combinedPermissions.rows[0]?.permissions?.length || 0\n                }\n            });\n        } catch (error) {\n            // إلغاء المعاملة في حالة الخطأ\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('ROLLBACK');\n            throw error;\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحديث أدوار المستخدم:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث الأدوار'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-roles/assignments/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-roles%2Fassignments%2Froute&page=%2Fapi%2Fuser-roles%2Fassignments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-roles%2Fassignments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();