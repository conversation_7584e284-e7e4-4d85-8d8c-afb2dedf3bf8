// إعداد جدول إعدادات النظام
const { Pool } = require('pg');

async function setupSystemSettings() {
  console.log('🔧 إعداد جدول إعدادات النظام...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // إنشاء جدول إعدادات النظام
      console.log('   🔨 إنشاء جدول system_settings...');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS system_settings (
          id SERIAL PRIMARY KEY,
          setting_key VARCHAR(100) UNIQUE NOT NULL,
          setting_value TEXT,
          setting_type VARCHAR(50) DEFAULT 'string',
          description TEXT,
          is_editable BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء فهرس
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key)
      `);

      // إنشاء دالة التحديث
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_system_settings_timestamp()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql
      `);

      // إنشاء المحفز
      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_update_system_settings_timestamp ON system_settings
      `);
      
      await pool.query(`
        CREATE TRIGGER trigger_update_system_settings_timestamp
          BEFORE UPDATE ON system_settings
          FOR EACH ROW
          EXECUTE FUNCTION update_system_settings_timestamp()
      `);

      console.log('   ✅ تم إنشاء الجدول والمحفزات');

      // إدراج الإعدادات الافتراضية
      console.log('   📝 إدراج الإعدادات الافتراضية...');
      
      const defaultSettings = [
        {
          key: 'legal_library_path',
          value: '/home/<USER>/Downloads/legal-system/laws',
          type: 'path',
          description: 'مسار مجلد المكتبة القانونية'
        },
        {
          key: 'legal_library_max_file_size',
          value: '50',
          type: 'number',
          description: 'الحد الأقصى لحجم الملف بالميجابايت'
        },
        {
          key: 'legal_library_allowed_extensions',
          value: '.pdf,.doc,.docx,.txt',
          type: 'string',
          description: 'امتدادات الملفات المسموحة'
        },
        {
          key: 'legal_library_auto_scan',
          value: 'true',
          type: 'boolean',
          description: 'فحص المجلد تلقائياً للملفات الجديدة'
        },
        {
          key: 'legal_library_backup_enabled',
          value: 'false',
          type: 'boolean',
          description: 'تفعيل النسخ الاحتياطي للملفات'
        }
      ];

      for (const setting of defaultSettings) {
        await pool.query(`
          INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) 
          VALUES ($1, $2, $3, $4, true)
          ON CONFLICT (setting_key) DO NOTHING
        `, [setting.key, setting.value, setting.type, setting.description]);
      }

      console.log(`   ✅ تم إدراج ${defaultSettings.length} إعداد افتراضي`);

      // التحقق من النتائج
      const result = await pool.query('SELECT COUNT(*) as count FROM system_settings');
      console.log(`   📊 إجمالي الإعدادات: ${result.rows[0].count}`);

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من إعداد جدول إعدادات النظام');
}

// تشغيل الإعداد
setupSystemSettings().catch(console.error);
