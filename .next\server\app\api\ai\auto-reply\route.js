(()=>{var e={};e.id=3485,e.ids=[3485],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(s,{P:()=>d});var o=r(64939),a=r(29021),n=r.n(a),i=r(33873),c=r.n(i),u=e([o]);o=(u.then?(await u)():u)[0];let p=null;try{let e=c().join(process.cwd(),"routing.config.json"),s=n().readFileSync(e,"utf8");p=JSON.parse(s)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let s=p.routes[e],r=p.default_config;return{database:s.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new o.Pool(l);async function d(e,s){let r=await _.connect();try{return await r.query(e,s)}finally{r.release()}}t()}catch(e){t(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},91708:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var o=r(96559),a=r(48088),n=r(37719),i=r(94327),c=e([i]);i=(c.then?(await c)():c)[0];let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai/auto-reply/route",pathname:"/api/ai/auto-reply",filename:"route",bundlePath:"app/api/ai/auto-reply/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\ai\\auto-reply\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:_}=d;function u(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}t()}catch(e){t(e)}})},94327:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{GET:()=>i,POST:()=>c,PUT:()=>u});var o=r(32190),a=r(5069),n=e([a]);async function i(){try{try{let e=await (0,a.P)("SELECT * FROM ai_settings WHERE id = 1");if(e.rows.length>0){let s=e.rows[0],r={enabled:s.enabled,model:s.model,delay_seconds:s.delay_seconds,working_hours_only:s.working_hours_only,working_hours_start:s.working_hours_start,working_hours_end:s.working_hours_end,working_days:s.working_days,max_responses_per_conversation:s.max_responses_per_conversation,keywords_trigger:s.keywords_trigger,excluded_keywords:s.excluded_keywords,auto_responses:s.auto_responses};return o.NextResponse.json({success:!0,data:r})}}catch(e){console.error("Database error:",e)}return o.NextResponse.json({success:!0,data:{enabled:!0,model:"groq-llama-8b",delay_seconds:2,working_hours_only:!1,working_hours_start:"00:00",working_hours_end:"23:59",working_days:["الأحد","الاثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"],max_responses_per_conversation:10,keywords_trigger:["مساعدة","استفسار","سؤال","معلومات","خدمة","مرحبا","السلام","أهلا"],excluded_keywords:["عاجل","طارئ","مهم جداً"],auto_responses:{greeting:"مرحباً! كيف يمكنني مساعدتك؟",help:"يمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب\n• توجيهك للمحامي المناسب\n• تحليل القضايا القانونية",disclaimer:"للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.",signature:""}}})}catch(e){return console.error("Error fetching auto-reply settings:",e),o.NextResponse.json({success:!1,error:"فشل في جلب إعدادات الرد التلقائي"},{status:500})}}async function c(e){try{let s=await e.json();return o.NextResponse.json({success:!0,message:"تم حفظ إعدادات الرد التلقائي بنجاح",data:s})}catch(e){return console.error("Error saving auto-reply settings:",e),o.NextResponse.json({success:!1,error:"فشل في حفظ إعدادات الرد التلقائي"},{status:500})}}async function u(e){try{let{conversationId:s,messageText:r,senderType:t}=await e.json();if("user"===t)return o.NextResponse.json({success:!0,shouldReply:!1,reason:"message_from_admin"});let n=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:7443"}/api/ai/auto-reply`,{method:"GET"}),i=(await n.json()).data;if(!i.enabled)return o.NextResponse.json({success:!0,shouldReply:!1,reason:"auto_reply_disabled"});if(i.excluded_keywords.some(e=>r.toLowerCase().includes(e.toLowerCase())))return o.NextResponse.json({success:!0,shouldReply:!1,reason:"excluded_keywords_found"});if(!(i.keywords_trigger.some(e=>r.toLowerCase().includes(e.toLowerCase()))||r.trim().length>0))return o.NextResponse.json({success:!0,shouldReply:!1,reason:"no_trigger_keywords"});let c=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:7443"}/api/ai/local-models`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:r,model:i.model,conversationId:s})}),u=await c.json();if(!u.success)return o.NextResponse.json({success:!0,shouldReply:!1,reason:"ai_error",error:u.error});try{await (0,a.P)(`
          INSERT INTO messages
          (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
          VALUES ($1, 'ai', 0, $2, 'text', CURRENT_TIMESTAMP)
        `,[s,u.data.response])}catch(e){console.error("Error saving AI message to database:",e)}return o.NextResponse.json({success:!0,shouldReply:!0,response:u.data.response,model:u.data.model,delay:i.delay_seconds})}catch(e){return console.error("Error processing auto-reply:",e),o.NextResponse.json({success:!1,error:"فشل في معالجة الرد التلقائي"},{status:500})}}a=(n.then?(await n)():n)[0],t()}catch(e){t(e)}})},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,580],()=>r(91708));module.exports=t})();