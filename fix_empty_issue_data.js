// إصلاح البيانات الفارغة في جدول القضايا
const { Pool } = require('pg');

async function fixEmptyIssueData() {
  console.log('🔧 إصلاح البيانات الفارغة في جدول القضايا...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // جلب جميع القضايا للفحص
      const allIssues = await pool.query('SELECT * FROM issues ORDER BY id');
      
      console.log(`   📊 عدد القضايا الموجودة: ${allIssues.rows.length}`);

      for (const issue of allIssues.rows) {
        console.log(`\n   🔍 فحص القضية: ${issue.case_number}`);
        
        let needsUpdate = false;
        let updateFields = [];
        let updateValues = [];
        let paramIndex = 1;

        // فحص وإصلاح البيانات الفارغة
        if (!issue.client_phone || issue.client_phone.trim() === '') {
          console.log('      📞 رقم الهاتف فارغ - سيتم تعيين قيمة افتراضية');
          updateFields.push(`client_phone = $${paramIndex++}`);
          updateValues.push('غير محدد');
          needsUpdate = true;
        }

        if (!issue.status || issue.status.trim() === '') {
          console.log('      📊 الحالة فارغة - سيتم تعيين "جديدة"');
          updateFields.push(`status = $${paramIndex++}`);
          updateValues.push('new');
          needsUpdate = true;
        }

        if (!issue.court_name || issue.court_name.trim() === '') {
          console.log('      🏛️ المحكمة فارغة - سيتم تعيين قيمة افتراضية');
          updateFields.push(`court_name = $${paramIndex++}`);
          updateValues.push('غير محدد');
          needsUpdate = true;
        }

        if (!issue.issue_type || issue.issue_type.trim() === '') {
          console.log('      📋 نوع القضية فارغ - سيتم تعيين قيمة افتراضية');
          updateFields.push(`issue_type = $${paramIndex++}`);
          updateValues.push('عام');
          needsUpdate = true;
        }

        if (!issue.contract_method || issue.contract_method.trim() === '') {
          console.log('      📄 طريقة التعاقد فارغة - سيتم تعيين "بالجلسة"');
          updateFields.push(`contract_method = $${paramIndex++}`);
          updateValues.push('بالجلسة');
          needsUpdate = true;
        }

        if (issue.amount === null || issue.amount === undefined) {
          console.log('      💰 المبلغ فارغ - سيتم تعيين 0');
          updateFields.push(`amount = $${paramIndex++}`);
          updateValues.push(0);
          needsUpdate = true;
        }

        if (!issue.description || issue.description.trim() === '') {
          console.log('      📝 الوصف فارغ - سيتم تعيين قيمة افتراضية');
          updateFields.push(`description = $${paramIndex++}`);
          updateValues.push('لا يوجد وصف');
          needsUpdate = true;
        }

        if (!issue.notes || issue.notes.trim() === '') {
          console.log('      📋 الملاحظات فارغة - سيتم تعيين قيمة افتراضية');
          updateFields.push(`notes = $${paramIndex++}`);
          updateValues.push('لا توجد ملاحظات');
          needsUpdate = true;
        }

        // تحديث البيانات إذا لزم الأمر
        if (needsUpdate) {
          updateValues.push(issue.id); // إضافة ID في النهاية
          
          const updateQuery = `
            UPDATE issues 
            SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramIndex}
          `;

          console.log(`      🔄 تحديث القضية...`);
          await pool.query(updateQuery, updateValues);
          console.log(`      ✅ تم تحديث القضية بنجاح`);
        } else {
          console.log(`      ✅ القضية لا تحتاج تحديث`);
        }
      }

      // عرض ملخص بعد التحديث
      console.log(`\n   📊 ملخص قاعدة البيانات ${dbName}:`);
      
      const summary = await pool.query(`
        SELECT 
          COUNT(*) as total_issues,
          COUNT(CASE WHEN client_phone IS NULL OR client_phone = '' THEN 1 END) as empty_phone,
          COUNT(CASE WHEN status IS NULL OR status = '' THEN 1 END) as empty_status,
          COUNT(CASE WHEN court_name IS NULL OR court_name = '' THEN 1 END) as empty_court,
          COUNT(CASE WHEN issue_type IS NULL OR issue_type = '' THEN 1 END) as empty_type
        FROM issues
      `);

      const stats = summary.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - هواتف فارغة: ${stats.empty_phone}`);
      console.log(`      - حالات فارغة: ${stats.empty_status}`);
      console.log(`      - محاكم فارغة: ${stats.empty_court}`);
      console.log(`      - أنواع فارغة: ${stats.empty_type}`);

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من إصلاح البيانات الفارغة');
}

// تشغيل الإصلاح
fixEmptyIssueData().catch(console.error);
