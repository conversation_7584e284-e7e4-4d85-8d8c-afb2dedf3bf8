'use client'

import { useState, useRef, useEffect } from 'react'
import { MessageCircle, X, Send, Bot, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface Message {
  id: string
  content: string
  sender: 'user' | 'bot'
  timestamp: Date
}

export function GuestChat() {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'مرحباً بك في مكتب المحاماة والاستشارات القانونية. كيف يمكنني مساعدتك اليوم؟',
      sender: 'bot',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsTyping(true)

    // محاكاة رد الذكاء الاصطناعي
    setTimeout(() => {
      const botResponse = getBotResponse(inputMessage)
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: botResponse,
        sender: 'bot',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, botMessage])
      setIsTyping(false)
    }, 1500)
  }

  const getBotResponse = (userInput: string): string => {
    const input = userInput.toLowerCase()
    
    if (input.includes('خدمات') || input.includes('خدمة')) {
      return 'نقدم خدمات قانونية متنوعة تشمل: تنفيذ الأحكام القضائية، إدارة العقود، قانون الشركات، الامتثال واللوائح، القوانين العمالية، الأمن السيبراني، حماية الملكية الفكرية، حل النزاعات والتحكيم، والاستشارات الضريبية. هل تريد معرفة المزيد عن خدمة معينة؟'
    }
    
    if (input.includes('أسعار') || input.includes('تكلفة') || input.includes('رسوم')) {
      return 'أسعارنا تختلف حسب نوع الخدمة وتعقيد القضية. يمكنك حجز استشارة مجانية لمناقشة قضيتك والحصول على عرض سعر مفصل. للتواصل المباشر، يرجى الاتصال على الرقم الموجود في الموقع.'
    }
    
    if (input.includes('موعد') || input.includes('استشارة') || input.includes('لقاء')) {
      return 'يمكنك حجز موعد للاستشارة من خلال الاتصال المباشر أو زيارة مكتبنا. نوفر استشارات مجانية أولية لتقييم قضيتك. ساعات العمل من الأحد إلى الخميس من 9 صباحاً حتى 6 مساءً.'
    }
    
    if (input.includes('عنوان') || input.includes('موقع') || input.includes('مكان')) {
      return 'يمكنك العثور على عنوان مكتبنا ومعلومات الاتصال في أسفل الصفحة. نحن متواجدون في موقع مركزي لسهولة الوصول إلينا.'
    }
    
    if (input.includes('شكر') || input.includes('شكراً')) {
      return 'العفو! نحن هنا لخدمتك دائماً. لا تتردد في التواصل معنا إذا كان لديك أي استفسارات أخرى.'
    }
    
    return 'شكراً لك على تواصلك معنا. فريقنا القانوني المتخصص جاهز لمساعدتك في جميع الأمور القانونية. للحصول على استشارة مفصلة، يرجى التواصل معنا مباشرة أو زيارة مكتبنا.'
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <>
      {/* زر فتح الشات */}
      {!isOpen && (
        <Button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 left-6 z-50 w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
          size="icon"
        >
          <MessageCircle className="h-6 w-6 text-white" />
        </Button>
      )}

      {/* نافذة الشات */}
      {isOpen && (
        <div className="fixed bottom-6 left-6 z-50 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col">
          {/* رأس الشات */}
          <div className="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Bot className="h-5 w-5" />
              <span className="font-semibold">المساعد القانوني</span>
            </div>
            <Button
              onClick={() => setIsOpen(false)}
              variant="ghost"
              size="icon"
              className="text-white hover:bg-blue-700 h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* منطقة الرسائل */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="flex items-start space-x-2 space-x-reverse">
                    {message.sender === 'bot' && <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                    {message.sender === 'user' && <User className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                    <p className="text-sm leading-relaxed">{message.content}</p>
                  </div>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString('ar-SA', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 p-3 rounded-lg max-w-[80%]">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Bot className="h-4 w-4" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* منطقة الإدخال */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2 space-x-reverse">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="اكتب رسالتك هنا..."
                className="flex-1"
                disabled={isTyping}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isTyping}
                size="icon"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}