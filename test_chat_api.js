// اختبار API المحادثة
async function testChatAPI() {
  console.log('🧪 اختبار API المحادثة...\n');

  const testMessages = [
    'احتاج الى استشارة قانونية',
    'ما هي خدماتكم؟',
    'كم تكلفة الاستشارة؟',
    'اريد حجز موعد',
    'لدي قضية جنائية',
    'مرحبا'
  ];

  for (const message of testMessages) {
    console.log(`📤 إرسال: "${message}"`);
    
    try {
      const response = await fetch('http://localhost:7443/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: message,
          sessionId: 'test_session'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`📥 الرد (${result.data.type}): ${result.data.message.substring(0, 100)}...`);
      } else {
        console.log(`❌ خطأ: ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ خطأ في الطلب: ${error.message}`);
    }
    
    console.log('---');
  }

  console.log('✅ انتهى الاختبار');
}

// تشغيل الاختبار
testChatAPI().catch(console.error);
