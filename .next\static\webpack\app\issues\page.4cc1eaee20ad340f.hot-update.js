"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,FileText,Plus,Save,Scale,Search,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [dbError, setDbError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_name: '',\n        issue_type: '',\n        status: 'pending',\n        amount: '',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n    });\n    // جلب المحاكم من قاعدة البيانات\n    const fetchCourts = async ()=>{\n        try {\n            const response = await fetch('/api/courts');\n            const result = await response.json();\n            if (result.success) {\n                setCourts(result.data);\n            }\n        } catch (error) {\n            console.error('Error fetching courts:', error);\n        }\n    };\n    // جلب البيانات من قاعدة البيانات\n    const fetchIssues = async ()=>{\n        setIsLoading(true);\n        setDbError(null);\n        try {\n            const response = await fetch('/api/issues');\n            const result = await response.json();\n            if (result.success) {\n                setIssues(result.data);\n            } else {\n                setDbError(result.error || 'فشل في جلب بيانات القضايا');\n                setIssues([]);\n            }\n        } catch (error) {\n            console.error('Network error:', error);\n            setDbError('فشل في الاتصال بقاعدة البيانات');\n            setIssues([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            fetchIssues();\n            fetchCourts();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'new':\n                return 'bg-purple-100 text-purple-800';\n            case 'pending':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'new':\n                return 'جديدة';\n            case 'pending':\n                return 'معلقة';\n            case 'in_progress':\n                return 'قيد المعالجة';\n            case 'completed':\n                return 'مكتملة';\n            case 'cancelled':\n                return 'ملغية';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const handleAddNew = ()=>{\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_name: '',\n            issue_type: '',\n            status: 'pending',\n            amount: '',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0] // تاريخ اليوم كافتراضي\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n    };\n    const handleClientChange = (clientId, clientData)=>{\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: clientData ? clientData.name : '',\n            client_phone: clientData ? clientData.phone : ''\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                // إضافة رقم المستخدم تلقائياً (يمكن الحصول عليه من الجلسة لاحقاً)\n                const dataToSubmit = {\n                    ...formData,\n                    created_by: 1 // رقم المستخدم الافتراضي - يجب تحديثه ليأخذ من الجلسة\n                };\n                console.log('Data being submitted for new issue:', dataToSubmit);\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                // إرسال جميع البيانات للتحديث\n                const dataToSubmit = {\n                    ...formData\n                };\n                console.log('Sending data to API:', dataToSubmit);\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(dataToSubmit)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    const handleViewIssue = (issue)=>{\n        console.log('handleViewIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || '',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status || 'new',\n            amount: issue.amount ? issue.amount.toString() : '0',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleViewIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    const handleEditIssue = (issue)=>{\n        console.log('handleEditIssue: Original issue data:', issue);\n        setEditingIssue(issue);\n        // معالجة البيانات الفارغة أو null\n        const formDataToSet = {\n            case_number: issue.case_number || '',\n            title: issue.title || '',\n            description: issue.description || 'لا يوجد وصف',\n            client_id: issue.client_id ? issue.client_id.toString() : '',\n            client_name: issue.client_name || '',\n            client_phone: issue.client_phone || 'غير محدد',\n            court_name: issue.court_name || 'غير محدد',\n            issue_type: issue.issue_type || 'عام',\n            status: issue.status || 'new',\n            amount: issue.amount ? issue.amount.toString() : '0',\n            notes: issue.notes || 'لا توجد ملاحظات',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date ? new Date(issue.contract_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n        };\n        console.log('handleEditIssue: Form data to set:', formDataToSet);\n        setFormData(formDataToSet);\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    const handleDeleteIssue = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه القضية؟')) return;\n        try {\n            const response = await fetch(\"/api/issues/\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert('تم حذف القضية بنجاح');\n                fetchIssues();\n            } else {\n                alert(result.error || 'فشل في حذف القضية');\n            }\n        } catch (error) {\n            console.error('Error deleting issue:', error);\n            alert('حدث خطأ في حذف القضية');\n        }\n    };\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-6 bg-white min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إدارة القضايا\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mt-1\",\n                                        children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAddNew,\n                                className: \"bg-emerald-600 hover:bg-emerald-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إضافة قضية جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-emerald-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"إجمالي القضايا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.new\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا جديدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قيد المعالجة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: stats.completed\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"قضايا مكتملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"new\",\n                                                    children: \"جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pending\",\n                                                    children: \"معلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"in_progress\",\n                                                    children: \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة القضايا (\",\n                                        filteredIssues.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"رقم القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right p-4 font-semibold text-lg\",\n                                                            children: \"الموكل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"المبلغ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center p-4 font-semibold text-lg\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredIssues.map((issue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium text-blue-600\",\n                                                                children: issue.case_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 font-medium\",\n                                                                children: issue.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        issue.client_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: issue.client_phone || 'غير محدد'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 487,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: issue.court_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-blue-50 text-blue-700\",\n                                                                    children: issue.issue_type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: issue.contract_method === 'بالجلسة' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700',\n                                                                    children: issue.contract_method\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                    className: getStatusColor(issue.status),\n                                                                    children: getStatusText(issue.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4 font-medium text-green-600\",\n                                                                children: [\n                                                                    issue.amount ? Math.floor(issue.amount).toLocaleString() : '0',\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleViewIssue(issue),\n                                                                            className: \"bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300\",\n                                                                            title: \"عرض تفاصيل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleEditIssue(issue),\n                                                                            className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300\",\n                                                                            title: \"تعديل القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"outline\",\n                                                                            onClick: ()=>handleDeleteIssue(issue.id),\n                                                                            className: \"bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300\",\n                                                                            title: \"حذف القضية\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, issue.id, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto border-t-4 border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                modalType === 'add' && '📋 إضافة قضية جديدة',\n                                                modalType === 'edit' && '✏️ تعديل القضية',\n                                                modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsModalOpen(false),\n                                            className: \"hover:bg-red-50 hover:text-red-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-10 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"case_number\",\n                                                            className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"case_number\",\n                                                            value: formData.case_number,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    case_number: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل رقم القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-7\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"title\",\n                                                            value: formData.title,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    title: e.target.value\n                                                                }),\n                                                            required: modalType !== 'view',\n                                                            readOnly: modalType === 'view',\n                                                            className: \"h-10 \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors'),\n                                                            placeholder: \"أدخل عنوان القضية...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"description\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"description\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل وصف مفصل للقضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDC64 الموكل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_8__.ClientSelect, {\n                                                                value: formData.client_id,\n                                                                onChange: handleClientChange,\n                                                                label: \"\",\n                                                                placeholder: \"اختر الموكل...\",\n                                                                required: true,\n                                                                disabled: modalType === 'view'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"court_name\",\n                                                            className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"court_name\",\n                                                            value: formData.court_name || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    court_name: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المحكمة...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: court.name,\n                                                                        children: [\n                                                                            court.name,\n                                                                            \" - \",\n                                                                            court.governorate_name\n                                                                        ]\n                                                                    }, court.id, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_method\",\n                                                            className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC4 طريقة التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"contract_method\",\n                                                            value: formData.contract_method || \"بالجلسة\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_method: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالجلسة\",\n                                                                    children: \"بالجلسة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"بالعقد\",\n                                                                    children: \"بالعقد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDD04 حالة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"status\",\n                                                            value: formData.status || \"new\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"new\",\n                                                                    children: \"جديدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"pending\",\n                                                                    children: \"معلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in_progress\",\n                                                                    children: \"قيد المعالجة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"completed\",\n                                                                    children: \"مكتملة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cancelled\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"issue_type\",\n                                                            className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"⚖️ نوع القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"issue_type\",\n                                                            value: formData.issue_type || \"\",\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    issue_type: e.target.value\n                                                                }),\n                                                            className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm\",\n                                                            disabled: modalType === 'view',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر نوع القضية...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"مدنية\",\n                                                                    children: \"مدنية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"تجارية\",\n                                                                    children: \"تجارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"جنائية\",\n                                                                    children: \"جنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"أحوال شخصية\",\n                                                                    children: \"أحوال شخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"عمالية\",\n                                                                    children: \"عمالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"إدارية\",\n                                                                    children: \"إدارية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"amount\",\n                                                            className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"amount\",\n                                                            type: \"number\",\n                                                            step: \"1\",\n                                                            min: \"0\",\n                                                            value: formData.amount,\n                                                            onChange: (e)=>{\n                                                                const value = e.target.value;\n                                                                // التأكد من أن القيمة رقم صحيح\n                                                                if (value === '' || /^\\d+$/.test(value)) {\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        amount: value\n                                                                    });\n                                                                }\n                                                            },\n                                                            className: \"h-10 bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none\",\n                                                            placeholder: \"أدخل قيمة القضية (أرقام صحيحة فقط)...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"contract_date\",\n                                                            className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                            children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"contract_date\",\n                                                            type: \"date\",\n                                                            value: formData.contract_date,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    contract_date: e.target.value\n                                                                }),\n                                                            className: \"h-10 bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"notes\",\n                                                        value: formData.notes,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                notes: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-20 px-3 py-2 border rounded-md resize-none text-sm \".concat(modalType === 'view' ? 'bg-gray-50' : 'bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors'),\n                                                        placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                            children: [\n                                                modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_FileText_Plus_Save_Scale_Search_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setIsModalOpen(false),\n                                                    className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                    children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 334,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"uTrsIW0hAcwsvd1ZHzCgc+3ou90=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});