# Database Configuration للنسخة الثانية - <PERSON><PERSON>ie
DB_HOST=localhost
DB_PORT=5432
DB_NAME=rubaie
DB_USER=postgres
DB_PASSWORD=yemen123

# JWT Secret
JWT_SECRET=your-secret-key-here

# Next.js Configuration للنسخة الثانية
NEXT_PUBLIC_API_URL=http://localhost:8914
PORT=8914

# AI Models API Configuration
# نفس المفاتيح للنسختين

# OpenAI API (GPT-4o) - مدفوع
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Groq API (Llama 3.1) - مجاني
# Get free API key from: https://console.groq.com/keys
GROQ_API_KEY=********************************************************

# Hugging Face API (Qwen 2.5) - مجاني
# Get free API key from: https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=*************************************
