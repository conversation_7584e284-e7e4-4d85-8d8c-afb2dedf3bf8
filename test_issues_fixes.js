// اختبار إصلاحات صفحة القضايا
async function testIssuesFixes() {
  console.log('🧪 اختبار إصلاحات صفحة القضايا...\n');

  // 1. اختبار جلب القضايا العادية
  console.log('1️⃣ اختبار جلب القضايا العادية:');
  try {
    const response = await fetch('http://localhost:7443/api/issues');
    const result = await response.json();
    
    if (result.success) {
      console.log(`   ✅ تم جلب ${result.data.length} قضية`);
      if (result.data.length > 0) {
        console.log(`   📋 أول قضية: ${result.data[0].case_number} - ${result.data[0].title}`);
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ: ${error.message}`);
  }

  console.log('');

  // 2. اختبار جلب القضايا غير الموزعة
  console.log('2️⃣ اختبار جلب القضايا غير الموزعة:');
  try {
    const response = await fetch('http://localhost:7443/api/issues?undistributed=true');
    const result = await response.json();
    
    if (result.success) {
      console.log(`   ✅ تم جلب ${result.data.length} قضية غير موزعة`);
      if (result.data.length > 0) {
        console.log(`   📋 أول قضية غير موزعة: ${result.data[0].case_number} - ${result.data[0].title}`);
      }
    } else {
      console.log(`   ❌ فشل: ${result.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ: ${error.message}`);
  }

  console.log('');

  // 3. اختبار جلب قضية واحدة
  console.log('3️⃣ اختبار جلب قضية واحدة:');
  try {
    // جلب أول قضية متاحة
    const issuesResponse = await fetch('http://localhost:7443/api/issues');
    const issuesResult = await issuesResponse.json();
    
    if (issuesResult.success && issuesResult.data.length > 0) {
      const firstIssueId = issuesResult.data[0].id;
      
      const response = await fetch(`http://localhost:7443/api/issues/${firstIssueId}`);
      const result = await response.json();
      
      if (result.success) {
        console.log(`   ✅ تم جلب القضية: ${result.data.case_number}`);
        console.log(`   📋 البيانات: العنوان=${result.data.title}, الموكل=${result.data.client_name}, الهاتف=${result.data.client_phone}`);
      } else {
        console.log(`   ❌ فشل: ${result.error}`);
      }
    } else {
      console.log('   ⚠️ لا توجد قضايا للاختبار');
    }
  } catch (error) {
    console.log(`   ❌ خطأ: ${error.message}`);
  }

  console.log('');

  // 4. اختبار تحديث قضية (محاكاة)
  console.log('4️⃣ اختبار تحديث قضية:');
  try {
    // جلب أول قضية متاحة
    const issuesResponse = await fetch('http://localhost:7443/api/issues');
    const issuesResult = await issuesResponse.json();
    
    if (issuesResult.success && issuesResult.data.length > 0) {
      const firstIssue = issuesResult.data[0];
      
      // تحديث بيانات القضية
      const updateData = {
        case_number: firstIssue.case_number,
        title: firstIssue.title + ' (محدث)',
        description: firstIssue.description || 'وصف محدث',
        client_id: firstIssue.client_id,
        client_name: firstIssue.client_name,
        client_phone: firstIssue.client_phone || '777777777',
        court_name: firstIssue.court_name || 'محكمة تجريبية',
        issue_type: firstIssue.issue_type || 'قضية تجريبية',
        status: firstIssue.status,
        amount: firstIssue.amount || 1000,
        notes: 'ملاحظات محدثة',
        contract_method: firstIssue.contract_method || 'بالجلسة',
        contract_date: new Date().toISOString().split('T')[0]
      };
      
      const response = await fetch(`http://localhost:7443/api/issues/${firstIssue.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log(`   ✅ تم تحديث القضية: ${result.data.case_number}`);
        console.log(`   📋 العنوان الجديد: ${result.data.title}`);
        console.log(`   📞 رقم الهاتف: ${result.data.client_phone}`);
      } else {
        console.log(`   ❌ فشل: ${result.error}`);
      }
    } else {
      console.log('   ⚠️ لا توجد قضايا للاختبار');
    }
  } catch (error) {
    console.log(`   ❌ خطأ: ${error.message}`);
  }

  console.log('');

  // 5. اختبار حذف قضية (تحذير: سيحذف قضية حقيقية!)
  console.log('5️⃣ اختبار حذف قضية (محاكاة فقط):');
  console.log('   ⚠️ لن يتم تنفيذ الحذف الفعلي لحماية البيانات');
  console.log('   📝 لاختبار الحذف، استخدم الواجهة مع قضية تجريبية');

  console.log('\n✅ انتهى اختبار إصلاحات القضايا');
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('   1. ✅ إصلاح عرض بيانات القضية في نافذة التعديل');
  console.log('   2. ✅ إضافة client_phone في API التحديث');
  console.log('   3. ✅ تحسين API الحذف مع تفاصيل أكثر');
  console.log('   4. ✅ إصلاح API القضايا لدعم القضايا غير الموزعة');
  console.log('   5. ✅ إضافة تحقق من التوزيعات قبل الحذف');
}

// تشغيل الاختبار
testIssuesFixes().catch(console.error);
