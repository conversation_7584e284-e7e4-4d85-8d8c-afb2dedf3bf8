'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Globe,
  Settings,
  Upload,
  Save,
  Eye,
  Edit,
  Trash2,
  Plus,
  FileText,
  Megaphone,
  BookOpen,
  Building,
  Scale,
  Users,
  Shield,
  Gavel,
  Link as LinkIcon,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  MessageCircle,
  Download,
  X,
  EyeOff,
  ArrowUp,
  ArrowDown,
  Palette,
  ExternalLink
} from 'lucide-react'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  icon_name: string
  icon_color: string
  image_url?: string
  is_active: boolean
  sort_order: number
  meta_title?: string
  meta_description?: string
}

interface Announcement {
  id: number
  title: string
  content: string
  type: string
  is_active: boolean
  created_date: string
}

interface CompanyData {
  id: number
  name: string
  legal_name: string
  description: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  logo_url: string
  logo_image_url: string
  established_date: string
  registration_number: string
  legal_form: string
  capital: number
  tax_number: string
  is_active: boolean
  working_hours?: string
  latitude?: number
  longitude?: number
}

// خريطة الأيقونات المتاحة
const availableIcons = [
  { name: 'Scale', icon: Scale, label: 'ميزان العدالة' },
  { name: 'Users', icon: Users, label: 'المستخدمين' },
  { name: 'FileText', icon: FileText, label: 'المستندات' },
  { name: 'Shield', icon: Shield, label: 'الحماية' },
  { name: 'Building', icon: Building, label: 'المباني' },
  { name: 'Gavel', icon: Gavel, label: 'المطرقة' },
  { name: 'BookOpen', icon: BookOpen, label: 'الكتاب' },
  { name: 'Award', icon: Award, label: 'الجائزة' },
  { name: 'Phone', icon: Phone, label: 'الهاتف' },
  { name: 'Mail', icon: Mail, label: 'البريد' },
  { name: 'MapPin', icon: MapPin, label: 'الموقع' },
  { name: 'Star', icon: Star, label: 'النجمة' },
  { name: 'TrendingUp', icon: TrendingUp, label: 'النمو' },
  { name: 'CheckCircle', icon: CheckCircle, label: 'التحقق' },
  { name: 'MessageCircle', icon: MessageCircle, label: 'الرسائل' }
]

// الألوان المتاحة
const availableColors = [
  '#2563eb', '#dc2626', '#059669', '#7c3aed', '#ea580c', '#0891b2',
  '#be123c', '#9333ea', '#c2410c', '#0369a1', '#15803d', '#a21caf'
]

export default function WebsiteAdminPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('company')

  // Services state
  const [services, setServices] = useState<Service[]>([])
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [isAddingNew, setIsAddingNew] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    content: '',
    icon_name: 'Scale',
    icon_color: '#2563eb',
    image_url: '',
    is_active: true,
    sort_order: 0,
    meta_title: '',
    meta_description: ''
  })

  // Announcements state
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    content: '',
    type: 'public_1'
  })

  // Company data state
  const [companyData, setCompanyData] = useState<CompanyData>({
    id: 1,
    name: '',
    legal_name: '',
    description: '',
    address: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    logo_url: '',
    logo_image_url: '',
    established_date: '',
    registration_number: '',
    legal_form: '',
    capital: 0,
    tax_number: '',
    is_active: true,
    working_hours: '',
    latitude: undefined,
    longitude: undefined
  })

  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // جلب الخدمات من جدول serviceslow
      const servicesResponse = await fetch('/api/serviceslow')
      const servicesResult = await servicesResponse.json()
      if (servicesResult.success) {
        setServices(servicesResult.data)
      }

      // جلب الإعلانات
      const announcementsRes = await fetch('/api/settings/public-announcements')
      const announcementsData = await announcementsRes.json()

      if (announcementsData.success) {
        setAnnouncements(announcementsData.data)
      }

      // جلب بيانات الشركة
      const companyResponse = await fetch('/api/company')
      const companyResult = await companyResponse.json()
      if (companyResult.success && companyResult.data && companyResult.data.length > 0) {
        const company = companyResult.data[0]
        setCompanyData({
          ...company,
          latitude: company.latitude ? Number(company.latitude) : undefined,
          longitude: company.longitude ? Number(company.longitude) : undefined
        })
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  // دوال إدارة الخدمات
  const handleEditService = (service: Service) => {
    setEditingService(service)
    setFormData({
      title: service.title,
      slug: service.slug,
      description: service.description || '',
      content: service.content || '',
      icon_name: service.icon_name,
      icon_color: service.icon_color,
      image_url: service.image_url || '',
      is_active: service.is_active,
      sort_order: service.sort_order,
      meta_title: service.meta_title || '',
      meta_description: service.meta_description || ''
    })
    setIsAddingNew(false)
  }

  const handleAddNewService = () => {
    setIsAddingNew(true)
    setEditingService(null)
    setFormData({
      title: '',
      slug: '',
      description: '',
      content: '',
      icon_name: 'Scale',
      icon_color: '#2563eb',
      image_url: '',
      is_active: true,
      sort_order: services.length + 1,
      meta_title: '',
      meta_description: ''
    })
  }

  const handleCancelService = () => {
    setEditingService(null)
    setIsAddingNew(false)
    setFormData({
      title: '',
      slug: '',
      description: '',
      content: '',
      icon_name: 'Scale',
      icon_color: '#2563eb',
      image_url: '',
      is_active: true,
      sort_order: 0,
      meta_title: '',
      meta_description: ''
    })
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[أ-ي]/g, (match) => {
        const arabicToEnglish: { [key: string]: string } = {
          'أ': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
          'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
          'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
          'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y'
        }
        return arabicToEnglish[match] || match
      })
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title),
      meta_title: title
    }))
  }

  const handleSaveService = async () => {
    try {
      const url = isAddingNew ? '/api/serviceslow' : `/api/serviceslow/${editingService?.id}`
      const method = isAddingNew ? 'POST' : 'PUT'
      const body = formData

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const result = await response.json()
      if (result.success) {
        await fetchData()
        handleCancelService()
        setMessage('تم حفظ الخدمة بنجاح')
        setTimeout(() => setMessage(''), 3000)
      } else {
        alert(result.error || 'حدث خطأ')
      }
    } catch (error) {
      console.error('Error saving service:', error)
      alert('حدث خطأ في الحفظ')
    }
  }

  const handleDeleteService = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه الخدمة؟')) return

    try {
      const response = await fetch(`/api/serviceslow/${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      if (result.success) {
        await fetchData()
        setMessage('تم حذف الخدمة بنجاح')
        setTimeout(() => setMessage(''), 3000)
      } else {
        alert(result.error || 'حدث خطأ في الحذف')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      alert('حدث خطأ في الحذف')
    }
  }

  const handleToggleServiceActive = async (service: Service) => {
    try {
      const response = await fetch(`/api/serviceslow/${service.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...service,
          is_active: !service.is_active
        })
      })

      const result = await response.json()
      if (result.success) {
        await fetchData()
      }
    } catch (error) {
      console.error('Error toggling service:', error)
    }
  }

  const moveService = async (service: Service, direction: 'up' | 'down') => {
    const currentIndex = services.findIndex(s => s.id === service.id)
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1

    if (targetIndex < 0 || targetIndex >= services.length) return

    const targetService = services[targetIndex]

    try {
      await fetch(`/api/serviceslow/${service.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...service,
          sort_order: targetService.sort_order
        })
      })

      await fetch(`/api/serviceslow/${targetService.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...targetService,
          sort_order: service.sort_order
        })
      })

      await fetchData()
    } catch (error) {
      console.error('Error moving service:', error)
    }
  }

  const getIconComponent = (iconName: string) => {
    const iconData = availableIcons.find(icon => icon.name === iconName)
    return iconData ? iconData.icon : Scale
  }

  const handleSaveAnnouncement = async () => {
    if (!newAnnouncement.title || !newAnnouncement.content) {
      setMessage('يرجى ملء جميع حقول الإعلان')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/public-announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAnnouncement),
      })

      const result = await response.json()
      
      if (result.success) {
        setMessage('تم إضافة الإعلان بنجاح')
        setNewAnnouncement({ title: '', content: '', type: 'public_1' })
        fetchData() // إعادة تحميل البيانات
      } else {
        setMessage('فشل في إضافة الإعلان: ' + result.error)
      }
    } catch (error) {
      setMessage('حدث خطأ أثناء إضافة الإعلان')
    } finally {
      setLoading(false)
      setTimeout(() => setMessage(''), 3000)
    }
  }

  const handleDeleteAnnouncement = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الإعلان؟')) return

    try {
      const response = await fetch(`/api/settings/public-announcements/${id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        setMessage('تم حذف الإعلان بنجاح')
        fetchData()
      } else {
        setMessage('فشل في حذف الإعلان')
      }
    } catch (error) {
      setMessage('حدث خطأ أثناء حذف الإعلان')
    } finally {
      setTimeout(() => setMessage(''), 3000)
    }
  }

  // دالة حفظ بيانات الشركة
  const handleSaveCompany = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/company', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(companyData),
      })

      const result = await response.json()

      if (result.success) {
        setMessage('تم حفظ بيانات الشركة بنجاح')
        fetchData()
      } else {
        setMessage('فشل في حفظ بيانات الشركة: ' + result.error)
      }
    } catch (error) {
      setMessage('حدث خطأ أثناء حفظ بيانات الشركة')
    } finally {
      setLoading(false)
      setTimeout(() => setMessage(''), 3000)
    }
  }

  const tabs = [
    { id: 'company', label: 'بيانات الشركة', icon: Building },
    { id: 'services', label: 'إدارة الخدمات', icon: Settings },
    { id: 'announcements', label: 'الإعلانات العامة', icon: Megaphone },
    { id: 'footer-links', label: 'روابط التذييل', icon: LinkIcon },
    { id: 'library', label: 'المكتبة القانونية', icon: BookOpen },
    { id: 'preview', label: 'معاينة الموقع', icon: Eye }
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Globe className="h-8 w-8 ml-3 text-blue-600" />
            لوحة تحكم الموقع الرئيسي
          </h1>
          <div className="flex gap-2">
            <Button
              onClick={() => window.open('/company', '_blank')}
              variant="outline"
              className="flex items-center"
            >
              <Building className="h-4 w-4 ml-2" />
              بيانات الشركة
            </Button>
            <Button
              onClick={() => window.open('/home', '_blank')}
              variant="outline"
              className="flex items-center"
            >
              <Eye className="h-4 w-4 ml-2" />
              معاينة الموقع
            </Button>
          </div>
        </div>

        {message && (
          <div className={`p-4 rounded-lg ${message.includes('فشل') || message.includes('خطأ') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message}
          </div>
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 ml-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Company Data Tab */}
        {activeTab === 'services' && (
          <div className="space-y-6">
            {/* نموذج إضافة/تعديل الخدمة */}
            {(editingService || isAddingNew) && (
              <Card className="shadow-xl border-0 bg-white rounded-2xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 border-b border-purple-100 pb-6">
                  <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                    <Settings className="h-6 w-6 mr-3 text-purple-600" />
                    {isAddingNew ? 'إضافة خدمة جديدة' : 'تعديل الخدمة'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* المعلومات الأساسية */}
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        المعلومات الأساسية
                      </h3>

                      <div className="space-y-2">
                        <Label htmlFor="title">عنوان الخدمة *</Label>
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => handleTitleChange(e.target.value)}
                          placeholder="أدخل عنوان الخدمة"
                          className="border-2 border-gray-200 rounded-xl focus:border-purple-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="slug">الرابط (Slug) *</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                          placeholder="service-url"
                          className="border-2 border-gray-200 rounded-xl focus:border-purple-500"
                        />
                        <p className="text-sm text-gray-500">
                          سيكون الرابط: /serviceslow/{formData.slug}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description">الوصف المختصر *</Label>
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="وصف مختصر للخدمة"
                          rows={3}
                          className="border-2 border-gray-200 rounded-xl focus:border-purple-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="content">المحتوى التفصيلي</Label>
                        <Textarea
                          id="content"
                          value={formData.content}
                          onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                          placeholder="المحتوى التفصيلي للخدمة (يمكن استخدام HTML)"
                          rows={8}
                          className="border-2 border-gray-200 rounded-xl focus:border-purple-500"
                        />
                        <p className="text-sm text-gray-500">
                          يمكنك استخدام HTML لتنسيق المحتوى
                        </p>
                      </div>
                    </div>

                    {/* التصميم والإعدادات */}
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        التصميم والإعدادات
                      </h3>

                      {/* اختيار الأيقونة */}
                      <div className="space-y-2">
                        <Label>الأيقونة</Label>
                        <div className="grid grid-cols-5 gap-2">
                          {availableIcons.map((iconData) => {
                            const IconComponent = iconData.icon
                            return (
                              <button
                                key={iconData.name}
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, icon_name: iconData.name }))}
                                className={`p-3 rounded-lg border-2 transition-all ${
                                  formData.icon_name === iconData.name
                                    ? 'border-purple-500 bg-purple-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                                title={iconData.label}
                              >
                                <IconComponent className="h-5 w-5 mx-auto" />
                              </button>
                            )
                          })}
                        </div>
                      </div>

                      {/* اختيار اللون */}
                      <div className="space-y-2">
                        <Label>لون الأيقونة</Label>
                        <div className="grid grid-cols-6 gap-2">
                          {availableColors.map((color) => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => setFormData(prev => ({ ...prev, icon_color: color }))}
                              className={`w-10 h-10 rounded-lg border-2 transition-all ${
                                formData.icon_color === color
                                  ? 'border-gray-800 scale-110'
                                  : 'border-gray-300 hover:scale-105'
                              }`}
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Input
                            type="color"
                            value={formData.icon_color}
                            onChange={(e) => setFormData(prev => ({ ...prev, icon_color: e.target.value }))}
                            className="w-12 h-8 border-0 rounded"
                          />
                          <span className="text-sm text-gray-600">أو اختر لون مخصص</span>
                        </div>
                      </div>

                      {/* معاينة */}
                      <div className="space-y-2">
                        <Label>معاينة</Label>
                        <div className="p-4 border-2 border-gray-200 rounded-xl bg-gray-50">
                          <div className="flex items-center mb-3">
                            <div
                              className="w-12 h-12 rounded-lg flex items-center justify-center mr-4"
                              style={{ backgroundColor: `${formData.icon_color}20` }}
                            >
                              {(() => {
                                const IconComponent = getIconComponent(formData.icon_name)
                                return <IconComponent className="h-6 w-6" style={{ color: formData.icon_color }} />
                              })()}
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">{formData.title || 'عنوان الخدمة'}</h4>
                          </div>
                          <p className="text-gray-600 text-sm">{formData.description || 'وصف الخدمة'}</p>
                        </div>
                      </div>

                      {/* إعدادات إضافية */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="is_active">نشط</Label>
                          <Switch
                            id="is_active"
                            checked={formData.is_active}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="sort_order">ترتيب العرض</Label>
                          <Input
                            id="sort_order"
                            type="number"
                            value={formData.sort_order}
                            onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                            className="border-2 border-gray-200 rounded-xl focus:border-purple-500"
                          />
                        </div>
                      </div>

                      {/* أزرار الحفظ والإلغاء */}
                      <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleCancelService}
                          className="px-6 py-3 rounded-xl border-2 border-gray-300 hover:border-gray-400"
                        >
                          <X className="h-4 w-4 mr-2" />
                          إلغاء
                        </Button>
                        <Button
                          onClick={handleSaveService}
                          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <Save className="h-4 w-4 mr-2" />
                          حفظ
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* قائمة الخدمات */}
            <Card className="shadow-xl border-0 bg-white rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-blue-100 pb-6">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                    <Globe className="h-6 w-6 mr-3 text-blue-600" />
                    الخدمات المتاحة ({services.length})
                  </CardTitle>
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Link
                      href="/serviceslow"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button
                        variant="outline"
                        className="bg-white hover:bg-gray-50 text-gray-700 border-gray-300 px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        معاينة جميع الخدمات
                      </Button>
                    </Link>
                    <Button
                      onClick={handleAddNewService}
                      className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Plus className="h-5 w-5 mr-2" />
                      إضافة خدمة جديدة
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {loading ? (
                  <div className="p-8 text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-600 mt-4">جاري التحميل...</p>
                  </div>
                ) : services.length === 0 ? (
                  <div className="p-8 text-center">
                    <Globe className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد خدمات</h3>
                    <p className="text-gray-600 mb-4">ابدأ بإضافة خدمة جديدة</p>
                    <Button onClick={handleAddNewService} className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      إضافة خدمة
                    </Button>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200">
                    {services.map((service, index) => {
                      const IconComponent = getIconComponent(service.icon_name)
                      return (
                        <div key={service.id} className="p-6 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1">
                              <Link
                                href={`/serviceslow/${service.slug}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="mr-4"
                              >
                                <div
                                  className="w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200 hover:shadow-md"
                                  style={{ backgroundColor: `${service.icon_color}20` }}
                                  title={`عرض صفحة ${service.title}`}
                                >
                                  <IconComponent
                                    className="h-6 w-6"
                                    style={{ color: service.icon_color }}
                                  />
                                </div>
                              </Link>

                              <div className="flex-1">
                                <div className="flex items-center mb-1">
                                  <Link
                                    href={`/serviceslow/${service.slug}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="mr-3"
                                  >
                                    <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer transition-colors duration-200">
                                      {service.title}
                                    </h3>
                                  </Link>
                                  <Badge variant={service.is_active ? "default" : "secondary"}>
                                    {service.is_active ? 'نشط' : 'غير نشط'}
                                  </Badge>
                                </div>
                                <p className="text-gray-600 text-sm mb-2">{service.description}</p>
                                <p className="text-xs text-gray-500">
                                  الرابط:
                                  <Link
                                    href={`/serviceslow/${service.slug}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 underline mx-1"
                                  >
                                    /serviceslow/{service.slug}
                                  </Link>
                                  | الترتيب: {service.sort_order}
                                </p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2 space-x-reverse">
                              {/* زر عرض الصفحة */}
                              <Link
                                href={`/serviceslow/${service.slug}`}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
                                  title="عرض صفحة الخدمة"
                                >
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              </Link>

                              {/* أزرار الترتيب */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => moveService(service, 'up')}
                                disabled={index === 0}
                                className="p-2"
                                title="نقل للأعلى"
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => moveService(service, 'down')}
                                disabled={index === services.length - 1}
                                className="p-2"
                                title="نقل للأسفل"
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>

                              {/* زر تفعيل/إلغاء تفعيل */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleServiceActive(service)}
                                className="p-2"
                                title={service.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                              >
                                {service.is_active ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>

                              {/* زر التعديل */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditService(service)}
                                className="p-2"
                                title="تعديل الخدمة"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>

                              {/* زر الحذف */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteService(service.id)}
                                className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="حذف الخدمة"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Company Data Tab */}
        {activeTab === 'company' && (
          <Card className="shadow-xl border-0 bg-white rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-blue-100 pb-6">
              <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                <Building className="h-6 w-6 mr-3 text-blue-600" />
                إدارة بيانات الشركة
              </CardTitle>
              <p className="text-gray-600 mt-2">
                إدارة المعلومات الأساسية للشركة التي تظهر في الموقع الرئيسي
              </p>
            </CardHeader>
            <CardContent className="p-8">
              <div className="space-y-8">
                {/* المعلومات الأساسية */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    المعلومات الأساسية
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">اسم الشركة *</Label>
                      <Input
                        id="name"
                        value={companyData.name}
                        onChange={(e) => setCompanyData({...companyData, name: e.target.value})}
                        placeholder="اسم الشركة"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="legal_name">الاسم القانوني *</Label>
                      <Input
                        id="legal_name"
                        value={companyData.legal_name}
                        onChange={(e) => setCompanyData({...companyData, legal_name: e.target.value})}
                        placeholder="الاسم القانوني للشركة"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="legal_form">الشكل القانوني</Label>
                      <Input
                        id="legal_form"
                        value={companyData.legal_form}
                        onChange={(e) => setCompanyData({...companyData, legal_form: e.target.value})}
                        placeholder="مثل: شركة محدودة المسؤولية"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="registration_number">رقم التسجيل</Label>
                      <Input
                        id="registration_number"
                        value={companyData.registration_number}
                        onChange={(e) => setCompanyData({...companyData, registration_number: e.target.value})}
                        placeholder="رقم التسجيل التجاري"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="tax_number">الرقم الضريبي</Label>
                      <Input
                        id="tax_number"
                        value={companyData.tax_number}
                        onChange={(e) => setCompanyData({...companyData, tax_number: e.target.value})}
                        placeholder="الرقم الضريبي"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="established_date">تاريخ التأسيس</Label>
                      <Input
                        id="established_date"
                        type="date"
                        value={companyData.established_date}
                        onChange={(e) => setCompanyData({...companyData, established_date: e.target.value})}
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* معلومات الاتصال */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    معلومات الاتصال
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف *</Label>
                      <Input
                        id="phone"
                        value={companyData.phone}
                        onChange={(e) => setCompanyData({...companyData, phone: e.target.value})}
                        placeholder="+966 50 000 0000"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">البريد الإلكتروني *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={companyData.email}
                        onChange={(e) => setCompanyData({...companyData, email: e.target.value})}
                        placeholder="<EMAIL>"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website">الموقع الإلكتروني</Label>
                      <Input
                        id="website"
                        value={companyData.website}
                        onChange={(e) => setCompanyData({...companyData, website: e.target.value})}
                        placeholder="www.company.com"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="working_hours">ساعات العمل</Label>
                      <Input
                        id="working_hours"
                        value={companyData.working_hours || ''}
                        onChange={(e) => setCompanyData({...companyData, working_hours: e.target.value})}
                        placeholder="الأحد - الخميس: 8 صباحاً - 6 مساءً"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* العنوان والموقع */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    العنوان والموقع
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="city">المدينة *</Label>
                      <Input
                        id="city"
                        value={companyData.city}
                        onChange={(e) => setCompanyData({...companyData, city: e.target.value})}
                        placeholder="الرياض"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">الدولة *</Label>
                      <Input
                        id="country"
                        value={companyData.country}
                        onChange={(e) => setCompanyData({...companyData, country: e.target.value})}
                        placeholder="المملكة العربية السعودية"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">العنوان الكامل *</Label>
                    <Input
                      id="address"
                      value={companyData.address}
                      onChange={(e) => setCompanyData({...companyData, address: e.target.value})}
                      placeholder="شارع الملك فهد، الرياض، المملكة العربية السعودية"
                      className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                    />
                  </div>

                  {/* إحداثيات الموقع */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="latitude" className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-green-600" />
                        خط العرض (Latitude)
                      </Label>
                      <Input
                        id="latitude"
                        type="number"
                        step="any"
                        value={companyData.latitude || ''}
                        onChange={(e) => setCompanyData({...companyData, latitude: e.target.value ? parseFloat(e.target.value) : undefined})}
                        placeholder="24.7136"
                        className="border-2 border-gray-200 rounded-xl focus:border-green-500"
                      />
                      <p className="text-xs text-gray-500">
                        مثال: 24.7136 (للرياض)
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="longitude" className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-green-600" />
                        خط الطول (Longitude)
                      </Label>
                      <Input
                        id="longitude"
                        type="number"
                        step="any"
                        value={companyData.longitude || ''}
                        onChange={(e) => setCompanyData({...companyData, longitude: e.target.value ? parseFloat(e.target.value) : undefined})}
                        placeholder="46.6753"
                        className="border-2 border-gray-200 rounded-xl focus:border-green-500"
                      />
                      <p className="text-xs text-gray-500">
                        مثال: 46.6753 (للرياض)
                      </p>
                    </div>
                  </div>

                  {/* معاينة الموقع */}
                  {companyData.latitude && companyData.longitude && !isNaN(Number(companyData.latitude)) && !isNaN(Number(companyData.longitude)) && (
                    <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <MapPin className="h-5 w-5 text-green-600 mr-2" />
                          <span className="text-green-800 font-medium">
                            الموقع محفوظ: {Number(companyData.latitude).toFixed(6)}, {Number(companyData.longitude).toFixed(6)}
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://www.google.com/maps?q=${companyData.latitude},${companyData.longitude}`, '_blank')}
                          className="text-green-700 border-green-300 hover:bg-green-100"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          عرض في الخرائط
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* الشعار والوصف */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    الشعار والوصف
                  </h3>
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="logo_image_url">رابط شعار الشركة</Label>
                      <Input
                        id="logo_image_url"
                        value={companyData.logo_image_url}
                        onChange={(e) => setCompanyData({...companyData, logo_image_url: e.target.value})}
                        placeholder="/images/logo.png"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                      {companyData.logo_image_url && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600 mb-2">معاينة الشعار:</p>
                          <img
                            src={companyData.logo_image_url}
                            alt="شعار الشركة"
                            className="w-20 h-20 object-contain border border-gray-200 rounded-lg"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">وصف الشركة *</Label>
                      <Textarea
                        id="description"
                        value={companyData.description}
                        onChange={(e) => setCompanyData({...companyData, description: e.target.value})}
                        placeholder="نقدم خدمات قانونية متميزة بأعلى معايير الجودة والاحترافية"
                        rows={4}
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* معلومات إضافية */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    معلومات إضافية
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="capital">رأس المال</Label>
                      <Input
                        id="capital"
                        type="number"
                        value={companyData.capital}
                        onChange={(e) => setCompanyData({...companyData, capital: parseInt(e.target.value) || 0})}
                        placeholder="2000000"
                        className="border-2 border-gray-200 rounded-xl focus:border-blue-500"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="is_active">الشركة نشطة</Label>
                      <Switch
                        id="is_active"
                        checked={companyData.is_active}
                        onCheckedChange={(checked) => setCompanyData({...companyData, is_active: checked})}
                      />
                    </div>
                  </div>
                </div>

                {/* زر الحفظ */}
                <div className="flex justify-end pt-6 border-t border-gray-200">
                  <Button
                    onClick={handleSaveCompany}
                    disabled={loading}
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Save className="h-5 w-5 mr-2" />
                    {loading ? 'جاري الحفظ...' : 'حفظ بيانات الشركة'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Announcements Tab */}
        {activeTab === 'announcements' && (
          <div className="space-y-6">
            {/* Add New Announcement */}
            <Card>
              <CardHeader>
                <CardTitle>إضافة إعلان جديد</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="announcement_type">نوع الإعلان</Label>
                  <select
                    id="announcement_type"
                    value={newAnnouncement.type}
                    onChange={(e) => setNewAnnouncement({...newAnnouncement, type: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="public_1">إعلان رقم 1</option>
                    <option value="public_2">إعلان رقم 2</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="announcement_title">عنوان الإعلان</Label>
                  <Input
                    id="announcement_title"
                    value={newAnnouncement.title}
                    onChange={(e) => setNewAnnouncement({...newAnnouncement, title: e.target.value})}
                    placeholder="عنوان الإعلان"
                  />
                </div>
                <div>
                  <Label htmlFor="announcement_content">محتوى الإعلان</Label>
                  <Textarea
                    id="announcement_content"
                    value={newAnnouncement.content}
                    onChange={(e) => setNewAnnouncement({...newAnnouncement, content: e.target.value})}
                    placeholder="محتوى الإعلان"
                    rows={3}
                  />
                </div>
                <Button onClick={handleSaveAnnouncement} disabled={loading}>
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة الإعلان
                </Button>
              </CardContent>
            </Card>

            {/* Existing Announcements */}
            <Card>
              <CardHeader>
                <CardTitle>الإعلانات الحالية</CardTitle>
              </CardHeader>
              <CardContent>
                {announcements.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">لا توجد إعلانات</p>
                ) : (
                  <div className="space-y-4">
                    {announcements.map((announcement) => (
                      <div key={announcement.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-2">
                              <h4 className="font-semibold">{announcement.title}</h4>
                              <Badge variant={announcement.type === 'public_1' ? 'default' : 'secondary'}>
                                {announcement.type === 'public_1' ? 'إعلان رقم 1' : 'إعلان رقم 2'}
                              </Badge>
                              <Badge variant={announcement.is_active ? 'default' : 'secondary'}>
                                {announcement.is_active ? 'نشط' : 'غير نشط'}
                              </Badge>
                            </div>
                            <p className="text-gray-600">{announcement.content}</p>
                            <p className="text-sm text-gray-400 mt-2">
                              تاريخ الإنشاء: {new Date(announcement.created_date).toLocaleDateString('ar-SA')}
                            </p>
                          </div>
                          <Button
                            onClick={() => handleDeleteAnnouncement(announcement.id)}
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Legal Library Tab */}
        {activeTab === 'library' && (
          <Card>
            <CardHeader>
              <CardTitle>إدارة المكتبة القانونية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">إدارة ملفات المكتبة القانونية</p>
                <p className="text-sm text-gray-400">
                  يمكن إضافة الملفات مباشرة إلى مجلد: /home/<USER>/Downloads/legal-system/laws
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer Links Tab */}
        {activeTab === 'footer-links' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <LinkIcon className="h-6 w-6 mr-3 text-blue-600" />
                إدارة روابط التذييل
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <LinkIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">إدارة الروابط التي تظهر في أسفل الموقع الرئيسي</p>
                <Button
                  onClick={() => window.open('/settings/footer-links', '_blank')}
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Settings className="h-5 w-5 ml-2" />
                  إدارة روابط التذييل
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Preview Tab */}
        {activeTab === 'preview' && (
          <Card>
            <CardHeader>
              <CardTitle>معاينة الموقع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Globe className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">معاينة الموقع الرئيسي</p>
                <Button
                  onClick={() => window.open('/home', '_blank')}
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Eye className="h-5 w-5 ml-2" />
                  فتح الموقع في نافذة جديدة
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}