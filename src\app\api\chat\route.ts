import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// دالة للحصول على إعدادات الذكاء الاصطناعي
async function getAISettings() {
  try {
    const result = await query(`
      SELECT * FROM ai_settings 
      WHERE id = 1
    `)
    
    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error)
    return null
  }
}

// دالة للحصول على بيانات الشركة
async function getCompanyData() {
  try {
    const result = await query(`
      SELECT * FROM company_data 
      WHERE id = 1
    `)
    
    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في جلب بيانات الشركة:', error)
    return null
  }
}

// دالة للحصول على الخدمات
async function getServices() {
  try {
    const result = await query(`
      SELECT title, description, slug FROM serviceslow 
      WHERE is_active = true 
      ORDER BY sort_order ASC
    `)
    
    return result.rows || []
  } catch (error) {
    console.error('خطأ في جلب الخدمات:', error)
    return []
  }
}

// دالة للحصول على المكتبة القانونية
async function getLegalLibrary() {
  try {
    const result = await query(`
      SELECT title, description, category FROM legal_library 
      WHERE is_active = true 
      ORDER BY created_date DESC 
      LIMIT 10
    `)
    
    return result.rows || []
  } catch (error) {
    console.error('خطأ في جلب المكتبة القانونية:', error)
    return []
  }
}

// دالة لمعالجة الرسالة وإنشاء الرد
async function processMessage(message: string, companyData: any, aiSettings: any, services: any[], legalLibrary: any[]) {
  const lowerMessage = message.toLowerCase().trim()
  
  // التحقق من الكلمات المحفزة
  const triggerKeywords = aiSettings?.keywords_trigger || ['مساعدة', 'استفسار', 'سؤال', 'معلومات']
  const shouldRespond = triggerKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()))
  
  if (!shouldRespond && !aiSettings?.auto_respond) {
    return {
      type: 'no_response',
      message: 'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.'
    }
  }

  // رسائل الترحيب
  const greetings = ['مرحبا', 'السلام عليكم', 'أهلا', 'صباح الخير', 'مساء الخير', 'هلا', 'اهلين']
  if (greetings.some(greeting => lowerMessage.includes(greeting))) {
    return {
      type: 'greeting',
      message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`
    }
  }

  // أسئلة حول الخدمات
  const serviceKeywords = ['خدمات', 'خدمة', 'تخصص', 'مجال', 'عمل', 'قانوني', 'محاماة', 'استشارة']
  if (serviceKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `نحن نقدم الخدمات القانونية التالية:\n\n`
    services.forEach((service, index) => {
      response += `${index + 1}. **${service.title}**\n${service.description}\n\n`
    })
    response += `للمزيد من التفاصيل، يمكنك التواصل معنا على:\n📞 ${companyData?.phone || 'غير متوفر'}`
    
    return {
      type: 'services',
      message: response
    }
  }

  // أسئلة حول التواصل
  const contactKeywords = ['تواصل', 'رقم', 'هاتف', 'عنوان', 'موقع', 'مكان', 'اتصال', 'واتساب']
  if (contactKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `يمكنك التواصل معنا من خلال:\n\n`
    
    if (companyData?.phone) {
      response += `📞 **الهاتف:** ${companyData.phone}\n`
    }
    
    if (companyData?.email) {
      response += `📧 **البريد الإلكتروني:** ${companyData.email}\n`
    }
    
    if (companyData?.address) {
      response += `📍 **العنوان:** ${companyData.address}\n`
    }
    
    if (companyData?.working_hours) {
      response += `🕐 **ساعات العمل:** ${companyData.working_hours}\n`
    }
    
    response += `\nنحن في خدمتك دائماً!`
    
    return {
      type: 'contact',
      message: response
    }
  }

  // أسئلة حول المكتبة القانونية
  const libraryKeywords = ['قانون', 'قوانين', 'مكتبة', 'وثائق', 'مراجع', 'نصوص', 'تشريع']
  if (libraryKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `لدينا مكتبة قانونية شاملة تحتوي على:\n\n`
    
    if (legalLibrary.length > 0) {
      legalLibrary.forEach((doc, index) => {
        response += `${index + 1}. **${doc.title}** (${doc.category})\n`
      })
      response += `\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`
    } else {
      response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`
    }
    
    return {
      type: 'library',
      message: response
    }
  }

  // أسئلة حول الشركة
  const companyKeywords = ['من أنتم', 'عنكم', 'تعريف', 'شركة', 'مكتب', 'تأسيس', 'خبرة']
  if (companyKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `**${companyData?.name || 'مكتبنا القانوني'}**\n\n`
    
    if (companyData?.description) {
      response += `${companyData.description}\n\n`
    }
    
    if (companyData?.established_date) {
      const establishedYear = new Date(companyData.established_date).getFullYear()
      const currentYear = new Date().getFullYear()
      const experience = currentYear - establishedYear
      response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\n`
    }
    
    if (companyData?.legal_form) {
      response += `📋 **الشكل القانوني:** ${companyData.legal_form}\n`
    }
    
    response += `\nنحن هنا لخدمتك بأفضل الحلول القانونية.`
    
    return {
      type: 'company',
      message: response
    }
  }

  // رد افتراضي
  return {
    type: 'default',
    message: aiSettings?.default_response || `شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك في أقرب وقت ممكن.\n\nللتواصل المباشر:\n📞 ${companyData?.phone || 'غير متوفر'}\n📧 ${companyData?.email || 'غير متوفر'}`
  }
}

// POST - معالجة رسالة المحادثة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, sessionId } = body

    if (!message || !message.trim()) {
      return NextResponse.json(
        { success: false, error: 'الرسالة مطلوبة' },
        { status: 400 }
      )
    }

    // جلب البيانات المطلوبة
    const [aiSettings, companyData, services, legalLibrary] = await Promise.all([
      getAISettings(),
      getCompanyData(),
      getServices(),
      getLegalLibrary()
    ])

    if (!aiSettings) {
      return NextResponse.json(
        { success: false, error: 'إعدادات الذكاء الاصطناعي غير متوفرة' },
        { status: 500 }
      )
    }

    // التحقق من تفعيل النظام
    if (!aiSettings.is_enabled) {
      return NextResponse.json({
        success: true,
        data: {
          type: 'disabled',
          message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',
          contact: {
            phone: companyData?.phone,
            email: companyData?.email
          }
        }
      })
    }

    // معالجة الرسالة
    const response = await processMessage(message, companyData, aiSettings, services, legalLibrary)

    // حفظ المحادثة في قاعدة البيانات (اختياري)
    try {
      await query(`
        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)
        VALUES ($1, $2, $3, $4, NOW())
      `, [sessionId || 'anonymous', message, response.message, response.type])
    } catch (logError) {
      console.error('خطأ في حفظ سجل المحادثة:', logError)
      // لا نوقف العملية إذا فشل الحفظ
    }

    return NextResponse.json({
      success: true,
      data: {
        ...response,
        timestamp: new Date().toISOString(),
        companyInfo: {
          name: companyData?.name,
          phone: companyData?.phone,
          email: companyData?.email
        }
      }
    })

  } catch (error) {
    console.error('خطأ في معالجة رسالة المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في معالجة الرسالة' },
      { status: 500 }
    )
  }
}

// GET - جلب رسالة الترحيب
export async function GET(request: NextRequest) {
  try {
    const [aiSettings, companyData] = await Promise.all([
      getAISettings(),
      getCompanyData()
    ])

    if (!aiSettings || !aiSettings.is_enabled) {
      return NextResponse.json({
        success: false,
        error: 'خدمة المحادثة غير متوفرة'
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,
        isEnabled: aiSettings.is_enabled,
        companyInfo: {
          name: companyData?.name,
          phone: companyData?.phone,
          email: companyData?.email
        }
      }
    })

  } catch (error) {
    console.error('خطأ في جلب إعدادات المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب الإعدادات' },
      { status: 500 }
    )
  }
}
