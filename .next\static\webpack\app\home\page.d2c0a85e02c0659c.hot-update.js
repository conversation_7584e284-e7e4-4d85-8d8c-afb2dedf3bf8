"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                console.log('🔄 تحميل رسالة الترحيب...');\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            console.log('📡 جاري الاتصال بـ /api/chat...');\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            console.log('📨 استجابة API:', result);\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                console.log('✅ تم إضافة رسالة الترحيب:', welcomeMsg);\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            console.log('🔄 استخدام رسالة ترحيب افتراضية');\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse flex items-center justify-center\",\n                size: \"lg\",\n                style: {\n                    zIndex: 9999\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\",\n                    strokeWidth: 2\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n        style: {\n            zIndex: 9999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 shadow-2xl border transition-all duration-300 \".concat(isMinimized ? 'h-auto' : 'h-96', \" bg-white overflow-hidden\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 \".concat(isMinimized ? 'rounded-t-lg' : 'rounded-t-lg'),\n                    style: {\n                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                        color: 'white',\n                        borderTopLeftRadius: '0.5rem',\n                        borderTopRightRadius: '0.5rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-full flex items-center justify-center\",\n                                        style: {\n                                            backgroundColor: 'rgba(255, 255, 255, 0.2)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium\",\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0\n                                                },\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs\",\n                                                style: {\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    margin: 0\n                                                },\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        className: \"h-6 w-6 p-0 rounded hover:bg-white/20 transition-colors flex items-center justify-center\",\n                                        style: {\n                                            color: 'white',\n                                            background: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer'\n                                        },\n                                        title: isMinimized ? 'توسيع' : 'تصغير',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"h-6 w-6 p-0 rounded hover:bg-white/20 transition-colors flex items-center justify-center\",\n                                        style: {\n                                            color: 'white',\n                                            background: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer'\n                                        },\n                                        title: \"إغلاق\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-3 max-w-xs\",\n                                                    style: {\n                                                        backgroundColor: message.type === 'user' ? '#3b82f6' : '#f3f4f6',\n                                                        color: message.type === 'user' ? 'white' : '#374151'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            style: {\n                                                                color: message.type === 'user' ? 'white' : '#374151',\n                                                                lineHeight: '1.5'\n                                                            },\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1\",\n                                                            style: {\n                                                                color: message.type === 'user' ? 'rgba(255, 255, 255, 0.7)' : '#6b7280'\n                                                            },\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t bg-gray-50 p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500\",\n                                            style: {\n                                                backgroundColor: 'white',\n                                                color: '#374151'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            className: \"px-3 py-2 rounded-md text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            style: {\n                                                backgroundColor: !inputMessage.trim() || isLoading ? '#9ca3af' : '#3b82f6',\n                                                border: 'none',\n                                                cursor: !inputMessage.trim() || isLoading ? 'not-allowed' : 'pointer'\n                                            },\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});