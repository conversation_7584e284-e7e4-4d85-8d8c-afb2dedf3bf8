/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/employees/route";
exports.ids = ["app/api/employees/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femployees%2Froute&page=%2Fapi%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femployees%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femployees%2Froute&page=%2Fapi%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femployees%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/employees/route.ts */ \"(rsc)/./src/app/api/employees/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/employees/route\",\n        pathname: \"/api/employees\",\n        filename: \"route\",\n        bundlePath: \"app/api/employees/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\employees\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femployees%2Froute&page=%2Fapi%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femployees%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/employees/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/employees/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع الموظفين من قاعدة البيانات\nasync function GET() {\n    try {\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM employees ORDER BY id');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('Error fetching الموظفين:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات الموظفين',\n            message: 'تأكد من وجود الجدول في قاعدة البيانات'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة الموظفين جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // هنا يجب إضافة منطق الإدراج حسب كل جدول\n        // سيتم تحديثه لاحقاً حسب هيكل كل جدول\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة الموظفين بنجاح'\n        });\n    } catch (error) {\n        console.error('Error creating الموظفين:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الموظفين'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث الموظفين\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        // هنا يجب إضافة منطق التحديث حسب كل جدول\n        // سيتم تحديثه لاحقاً حسب هيكل كل جدول\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث الموظفين بنجاح'\n        });\n    } catch (error) {\n        console.error('Error updating الموظفين:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث الموظفين'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف الموظفين\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الموظفين مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM employees WHERE id = $1', [\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الموظفين بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting الموظفين:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف الموظفين'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/employees/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeTables: () => (/* binding */ initializeTables),\n/* harmony export */   openDb: () => (/* binding */ openDb),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Database configuration from mohammi.txt\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst dbConfig = {\n    host: 'localhost',\n    port: 5432,\n    database: 'mohammi',\n    user: 'postgres',\n    password: process.env.DB_PASSWORD || 'your_password_here',\n    ssl: false,\n    connectionTimeoutMillis: 5000,\n    idleTimeoutMillis: 30000,\n    max: 20\n};\n// Create connection pool\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n// Test database connection\nasync function testConnection() {\n    try {\n        const client = await pool.connect();\n        const result = await client.query('SELECT NOW()');\n        client.release();\n        return true;\n    } catch (error) {\n        console.error('❌ Database connection failed:', error);\n        return false;\n    }\n}\n// SQLite-like interface for compatibility\nasync function openDb() {\n    const client = await pool.connect();\n    return {\n        async exec (sql) {\n            try {\n                // Split multiple statements and execute them one by one\n                const statements = sql.split(';').filter((stmt)=>stmt.trim());\n                for (const statement of statements){\n                    if (statement.trim()) {\n                        await client.query(statement.trim());\n                    }\n                }\n            } catch (error) {\n                console.error('Database exec error:', error);\n                throw error;\n            }\n        },\n        async get (sql, params = []) {\n            try {\n                const result = await client.query(sql, params);\n                return result.rows[0] || null;\n            } catch (error) {\n                console.error('Database get error:', error);\n                throw error;\n            }\n        },\n        async all (sql, params = []) {\n            try {\n                const result = await client.query(sql, params);\n                return result.rows;\n            } catch (error) {\n                console.error('Database all error:', error);\n                throw error;\n            }\n        },\n        async run (sql, params = []) {\n            try {\n                // For INSERT statements, add RETURNING id to get the inserted ID\n                if (sql.trim().toUpperCase().startsWith('INSERT')) {\n                    const modifiedSql = sql.includes('RETURNING') ? sql : sql + ' RETURNING id';\n                    const result = await client.query(modifiedSql, params);\n                    return {\n                        lastID: result.rows[0]?.id || null,\n                        changes: result.rowCount || 0\n                    };\n                } else {\n                    const result = await client.query(sql, params);\n                    return {\n                        lastID: null,\n                        changes: result.rowCount || 0\n                    };\n                }\n            } catch (error) {\n                console.error('Database run error:', error);\n                throw error;\n            }\n        },\n        close () {\n            client.release();\n        }\n    };\n}\n// Generic query function\nasync function query(text, params) {\n    let client;\n    try {\n        client = await pool.connect();\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error('Database query error:', error);\n        console.error('Query:', text);\n        console.error('Params:', params);\n        throw error;\n    } finally{\n        if (client) {\n            client.release();\n        }\n    }\n}\n// Initialize database tables\nasync function initializeTables() {\n    try {\n        // Create clients table\n        await query(`\n      CREATE TABLE IF NOT EXISTS clients (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        status VARCHAR(20) DEFAULT 'active',\n        cases_count INTEGER DEFAULT 0,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create employees table\n        await query(`\n      CREATE TABLE IF NOT EXISTS employees (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        position VARCHAR(255),\n        department VARCHAR(255),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        salary DECIMAL(10,2),\n        hire_date DATE,\n        status VARCHAR(20) DEFAULT 'active',\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issue_types table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issue_types (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        color VARCHAR(50),\n        cases_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issues table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issues (\n        id SERIAL PRIMARY KEY,\n        case_number VARCHAR(50) UNIQUE NOT NULL,\n        title VARCHAR(255) NOT NULL,\n        description TEXT,\n        client_id INTEGER REFERENCES clients(id),\n        client_name VARCHAR(255),\n        court_name VARCHAR(255),\n        issue_type_id INTEGER REFERENCES issue_types(id),\n        issue_type VARCHAR(255),\n        status VARCHAR(50) DEFAULT 'pending',\n        amount DECIMAL(12,2),\n        next_hearing DATE,\n        notes TEXT,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create lineages table (النسب المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS lineages (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        management_share DECIMAL(5,2) DEFAULT 0.00,\n        court_share DECIMAL(5,2) DEFAULT 0.00,\n        commission_share DECIMAL(5,2) DEFAULT 0.00,\n        other_share DECIMAL(5,2) DEFAULT 0.00,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create follows table (المتابعات)\n        await query(`\n      CREATE TABLE IF NOT EXISTS follows (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        client_name VARCHAR(255),\n        follow_type VARCHAR(50),\n        description TEXT,\n        due_date DATE,\n        status VARCHAR(50) DEFAULT 'pending',\n        priority VARCHAR(20) DEFAULT 'medium',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create movements table (الحركات المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS movements (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        movement_type VARCHAR(20) NOT NULL, -- 'income' or 'expense'\n        category VARCHAR(255),\n        amount DECIMAL(12,2) NOT NULL,\n        description TEXT,\n        date DATE DEFAULT CURRENT_DATE,\n        reference_number VARCHAR(100),\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create users table\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        role VARCHAR(50) DEFAULT 'user',\n        status VARCHAR(20) DEFAULT 'active',\n        last_login TIMESTAMP,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create companies table\n        await query(`\n      CREATE TABLE IF NOT EXISTS companies (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        legal_name VARCHAR(255),\n        registration_number VARCHAR(100),\n        address TEXT,\n        city VARCHAR(100),\n        country VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        tax_number VARCHAR(50),\n        commercial_register VARCHAR(50),\n        logo_url VARCHAR(500),\n        logo_right_text TEXT,\n        logo_left_text TEXT,\n        logo_image_url VARCHAR(500),\n        established_date DATE,\n        legal_form VARCHAR(100),\n        capital DECIMAL(15,2),\n        description TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create journal_entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        description TEXT NOT NULL,\n        date DATE DEFAULT CURRENT_DATE,\n        total_debit DECIMAL(12,2) DEFAULT 0.00,\n        total_credit DECIMAL(12,2) DEFAULT 0.00,\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create governorates table\n        await query(`\n      CREATE TABLE IF NOT EXISTS governorates (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        code VARCHAR(10),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create branches table\n        await query(`\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        manager_name VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create courts table\n        await query(`\n      CREATE TABLE IF NOT EXISTS courts (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        type VARCHAR(100),\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create announcements table\n        await query(`\n      CREATE TABLE IF NOT EXISTS announcements (\n        id SERIAL PRIMARY KEY,\n        announcement_1 TEXT,\n        announcement_2 TEXT,\n        announcement_3 TEXT,\n        announcement_4 TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create opening_balances table\n        await query(`\n      CREATE TABLE IF NOT EXISTS opening_balances (\n        id SERIAL PRIMARY KEY,\n        account_id INTEGER NOT NULL,\n        debit_balance DECIMAL(15,2) DEFAULT 0,\n        credit_balance DECIMAL(15,2) DEFAULT 0,\n        balance_date DATE NOT NULL,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create navigation_pages table for smart search\n        await query(`\n      CREATE TABLE IF NOT EXISTS navigation_pages (\n        id SERIAL PRIMARY KEY,\n        page_title VARCHAR(255) NOT NULL,\n        page_url VARCHAR(500) NOT NULL,\n        page_description TEXT,\n        category VARCHAR(100),\n        keywords TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create serviceslow table for website services\n        await query(`\n      CREATE TABLE IF NOT EXISTS serviceslow (\n        id SERIAL PRIMARY KEY,\n        title VARCHAR(255) NOT NULL,\n        slug VARCHAR(255) UNIQUE NOT NULL,\n        description TEXT,\n        content TEXT,\n        icon_name VARCHAR(100) DEFAULT 'Scale',\n        icon_color VARCHAR(50) DEFAULT '#2563eb',\n        image_url VARCHAR(500),\n        is_active BOOLEAN DEFAULT true,\n        sort_order INTEGER DEFAULT 0,\n        meta_title VARCHAR(255),\n        meta_description TEXT,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Insert default navigation pages\n        await query(`\n      INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords) VALUES\n      ('لوحة التحكم', '/', 'الصفحة الرئيسية للنظام', 'رئيسي', 'لوحة,تحكم,رئيسي,dashboard'),\n      ('العملاء', '/clients', 'إدارة بيانات العملاء', 'إدارة', 'عملاء,clients,زبائن'),\n      ('الموظفين', '/employees', 'إدارة بيانات الموظفين', 'إدارة', 'موظفين,employees,عمال'),\n      ('أنواع القضايا', '/issue-types', 'إدارة أنواع القضايا', 'قضايا', 'أنواع,قضايا,types'),\n      ('القضايا', '/issues', 'إدارة القضايا والدعاوى', 'قضايا', 'قضايا,دعاوى,issues,cases'),\n      ('المتابعات', '/follows', 'متابعة القضايا والمهام', 'قضايا', 'متابعات,follows,مهام'),\n      ('حركة القضايا', '/movements', 'إدارة حركة القضايا', 'مالية', 'حركات,مالية,movements'),\n      ('دليل الحسابات', '/accounting/chart-of-accounts', 'دليل الحسابات المحاسبي', 'محاسبة', 'دليل,حسابات,chart,accounts'),\n      ('سندات الصرف', '/accounting/payment-vouchers', 'إدارة سندات الصرف', 'محاسبة', 'سندات,صرف,payment,vouchers'),\n      ('سندات القبض', '/accounting/receipt-vouchers', 'إدارة سندات القبض', 'محاسبة', 'سندات,قبض,receipt,vouchers'),\n      ('القيود اليومية', '/accounting/journal-entries', 'إدارة القيود اليومية', 'محاسبة', 'قيود,يومية,journal,entries'),\n      ('الأرصدة الافتتاحية', '/accounting/opening-balances', 'إدارة الأرصدة الافتتاحية', 'محاسبة', 'أرصدة,افتتاحية,opening,balances'),\n      ('التقارير المحاسبية', '/accounting/reports', 'التقارير المحاسبية', 'تقارير', 'تقارير,محاسبية,reports'),\n      ('كشف حساب', '/accounting/reports/account-statement', 'كشف حساب تفصيلي', 'تقارير', 'كشف,حساب,statement'),\n      ('تقارير القضايا', '/case-reports', 'تقارير القضايا والدعاوى', 'تقارير', 'تقارير,قضايا,cases'),\n      ('التقارير المالية', '/financial-reports', 'التقارير المالية', 'تقارير', 'تقارير,مالية,financial'),\n      ('تقارير الموظفين', '/employee-reports', 'تقارير الموظفين', 'تقارير', 'تقارير,موظفين,employees'),\n      ('بيانات الشركة', '/company', 'إدارة بيانات الشركة', 'إعدادات', 'شركة,company,بيانات'),\n      ('مراكز التكلفة', '/settings/cost-centers', 'إدارة مراكز التكلفة', 'إعدادات', 'مراكز,تكلفة,cost,centers'),\n      ('الإعلانات', '/settings/announcements', 'إدارة الإعلانات', 'إعدادات', 'إعلانات,announcements'),\n      ('المحافظات', '/governorates', 'إدارة المحافظات', 'إدارة', 'محافظات,governorates'),\n      ('الفروع', '/branches', 'إدارة الفروع', 'إدارة', 'فروع,branches'),\n      ('المحاكم', '/courts', 'إدارة المحاكم', 'إدارة', 'محاكم,courts'),\n      ('خدمات الموقع', '/admin/serviceslow', 'إدارة خدمات الموقع الرئيسي', 'إدارة', 'خدمات,موقع,serviceslow')\n      ON CONFLICT DO NOTHING\n    `);\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database tables:', error);\n        throw error;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femployees%2Froute&page=%2Fapi%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femployees%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();