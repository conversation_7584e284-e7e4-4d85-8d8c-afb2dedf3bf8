/* تصميم حيوي واحترافي للصفحة الرئيسية */

:root {
  /* الألوان الأساسية الحيوية */
  --vibrant-primary: #1e40af;      /* أزرق ملكي */
  --vibrant-primary-light: #3b82f6; /* أزرق فاتح */
  --vibrant-primary-dark: #1e3a8a;  /* أزرق داكن */
  
  --vibrant-secondary: #059669;     /* أخضر زمردي */
  --vibrant-secondary-light: #10b981; /* أخضر فاتح */
  --vibrant-secondary-dark: #047857; /* أخضر داكن */
  
  --vibrant-accent: #dc2626;        /* أحمر حيوي */
  --vibrant-accent-light: #ef4444;  /* أحمر فاتح */
  --vibrant-accent-dark: #b91c1c;   /* أحمر داكن */
  
  --vibrant-gold: #f59e0b;          /* ذهبي حيوي */
  --vibrant-gold-light: #fbbf24;    /* ذهبي فاتح */
  --vibrant-gold-dark: #d97706;     /* ذهبي داكن */
  
  /* ألوان النص الحيوية */
  --vibrant-text-primary: #111827;  /* نص أساسي داكن */
  --vibrant-text-secondary: #374151; /* نص ثانوي */
  --vibrant-text-muted: #6b7280;    /* نص خفيف */
  --vibrant-text-white: #ffffff;    /* نص أبيض */
  
  /* ألوان الخلفية الحيوية */
  --vibrant-bg-primary: #ffffff;    /* خلفية بيضاء */
  --vibrant-bg-secondary: #f9fafb;  /* خلفية رمادية فاتحة */
  --vibrant-bg-dark: #1f2937;       /* خلفية داكنة */
  --vibrant-bg-darker: #111827;     /* خلفية أكثر قتامة */
  
  /* التدرجات الحيوية */
  --vibrant-gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  --vibrant-gradient-secondary: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --vibrant-gradient-accent: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  --vibrant-gradient-gold: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  --vibrant-gradient-hero: linear-gradient(135deg, #1e40af 0%, #059669 50%, #f59e0b 100%);
  
  /* الظلال الحيوية */
  --vibrant-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --vibrant-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --vibrant-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* الظلال الملونة */
  --vibrant-shadow-primary: 0 10px 25px -5px rgba(30, 64, 175, 0.3);
  --vibrant-shadow-secondary: 0 10px 25px -5px rgba(5, 150, 105, 0.3);
  --vibrant-shadow-accent: 0 10px 25px -5px rgba(220, 38, 38, 0.3);
  --vibrant-shadow-gold: 0 10px 25px -5px rgba(245, 158, 11, 0.3);
  
  /* الحواف المدورة */
  --vibrant-radius-sm: 0.375rem;
  --vibrant-radius-md: 0.5rem;
  --vibrant-radius-lg: 0.75rem;
  --vibrant-radius-xl: 1rem;
  --vibrant-radius-2xl: 1.5rem;
  --vibrant-radius-full: 9999px;
}

/* إعادة تعيين الألوان الباهتة */
* {
  color: inherit;
}

body {
  background: var(--vibrant-bg-primary) !important;
  color: var(--vibrant-text-primary) !important;
}

/* البطاقات الحيوية */
.vibrant-card {
  background: var(--vibrant-bg-primary);
  border-radius: var(--vibrant-radius-xl);
  padding: 2rem;
  box-shadow: var(--vibrant-shadow-lg);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vibrant-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--vibrant-shadow-2xl);
  border-color: var(--vibrant-primary-light);
}

/* الأزرار الحيوية */
.vibrant-btn-primary {
  background: var(--vibrant-gradient-primary);
  color: var(--vibrant-text-white);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-primary);
  cursor: pointer;
}

.vibrant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

.vibrant-btn-secondary {
  background: var(--vibrant-gradient-secondary);
  color: var(--vibrant-text-white);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-secondary);
  cursor: pointer;
}

.vibrant-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #047857 0%, #059669 100%);
}

.vibrant-btn-gold {
  background: var(--vibrant-gradient-gold);
  color: var(--vibrant-text-primary);
  border: none;
  border-radius: var(--vibrant-radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--vibrant-shadow-gold);
  cursor: pointer;
}

.vibrant-btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: var(--vibrant-shadow-xl);
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

/* النصوص الحيوية */
.vibrant-text-primary {
  color: var(--vibrant-text-primary) !important;
}

.vibrant-text-secondary {
  color: var(--vibrant-text-secondary) !important;
}

.vibrant-text-muted {
  color: var(--vibrant-text-muted) !important;
}

.vibrant-text-white {
  color: var(--vibrant-text-white) !important;
}

/* العناوين الحيوية */
.vibrant-heading {
  background: var(--vibrant-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.vibrant-heading-secondary {
  background: var(--vibrant-gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.vibrant-heading-gold {
  background: var(--vibrant-gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

/* الأيقونات الحيوية */
.vibrant-icon-primary {
  color: var(--vibrant-primary);
}

.vibrant-icon-secondary {
  color: var(--vibrant-secondary);
}

.vibrant-icon-accent {
  color: var(--vibrant-accent);
}

.vibrant-icon-gold {
  color: var(--vibrant-gold);
}

/* خلفيات الأقسام */
.vibrant-section-primary {
  background: var(--vibrant-gradient-primary);
  color: var(--vibrant-text-white);
}

.vibrant-section-secondary {
  background: var(--vibrant-gradient-secondary);
  color: var(--vibrant-text-white);
}

.vibrant-section-light {
  background: var(--vibrant-bg-secondary);
  color: var(--vibrant-text-primary);
}

.vibrant-section-white {
  background: var(--vibrant-bg-primary);
  color: var(--vibrant-text-primary);
}

/* تأثيرات الحركة */
.vibrant-animate-float {
  animation: vibrant-float 6s ease-in-out infinite;
}

@keyframes vibrant-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.vibrant-animate-pulse {
  animation: vibrant-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes vibrant-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.vibrant-animate-bounce {
  animation: vibrant-bounce 1s infinite;
}

@keyframes vibrant-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .vibrant-card {
    padding: 1.5rem;
  }
  
  .vibrant-btn-primary,
  .vibrant-btn-secondary,
  .vibrant-btn-gold {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* إزالة الألوان الباهتة من جميع العناصر */
.text-gray-500,
.text-gray-400,
.text-gray-600 {
  color: var(--vibrant-text-secondary) !important;
}

.text-gray-300 {
  color: var(--vibrant-text-muted) !important;
}

.bg-gray-100,
.bg-gray-50 {
  background: var(--vibrant-bg-secondary) !important;
}

.bg-gray-900,
.bg-gray-800 {
  background: var(--vibrant-bg-dark) !important;
}
