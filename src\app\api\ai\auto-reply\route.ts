import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب إعدادات الرد التلقائي
export async function GET() {
  try {
    // جلب الإعدادات من قاعدة البيانات
    try {
      const settingsResult = await query('SELECT * FROM ai_settings WHERE id = 1')

      if (settingsResult.rows.length > 0) {
        const row = settingsResult.rows[0]
        const settings = {
          enabled: row.enabled,
          model: row.model,
          delay_seconds: row.delay_seconds,
          working_hours_only: row.working_hours_only,
          working_hours_start: row.working_hours_start,
          working_hours_end: row.working_hours_end,
          working_days: row.working_days,
          max_responses_per_conversation: row.max_responses_per_conversation,
          keywords_trigger: row.keywords_trigger,
          excluded_keywords: row.excluded_keywords,
          auto_responses: row.auto_responses
        }

        return NextResponse.json({
          success: true,
          data: settings
        })
      }
    } catch (dbError) {
      console.error('Database error:', dbError)
    }

    // إعدادات افتراضية في حالة عدم وجود بيانات
    const defaultSettings = {
      enabled: true,
      model: 'openai-gpt4',
      delay_seconds: 2,
      working_hours_only: false,
      working_hours_start: '00:00',
      working_hours_end: '23:59',
      working_days: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
      max_responses_per_conversation: 10,
      keywords_trigger: ['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'مرحبا', 'السلام', 'أهلا'],
      excluded_keywords: ['عاجل', 'طارئ', 'مهم جداً'],
      auto_responses: {
        greeting: 'مرحباً! أنا المساعد الذكي للمكتب مدعوم بـ GPT-4.',
        help: 'يمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب\n• توجيهك للمحامي المناسب\n• تحليل القضايا القانونية',
        disclaimer: 'للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.',
        signature: '🤖 المساعد الذكي مدعوم بـ GPT-4'
      }
    }

    return NextResponse.json({
      success: true,
      data: defaultSettings
    })
  } catch (error) {
    console.error('Error fetching auto-reply settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب إعدادات الرد التلقائي' },
      { status: 500 }
    )
  }
}

// POST - تحديث إعدادات الرد التلقائي
export async function POST(request: NextRequest) {
  try {
    const settings = await request.json()

    // في الوقت الحالي، نحفظ الإعدادات في الذاكرة فقط
    // يمكن تطوير هذا لاحقاً لحفظها في قاعدة البيانات

    return NextResponse.json({
      success: true,
      message: 'تم حفظ إعدادات الرد التلقائي بنجاح',
      data: settings
    })
  } catch (error) {
    console.error('Error saving auto-reply settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ إعدادات الرد التلقائي' },
      { status: 500 }
    )
  }
}

// PUT - معالجة رسالة جديدة للرد التلقائي
export async function PUT(request: NextRequest) {
  try {
    const { conversationId, messageText, senderType } = await request.json()

    // تجاهل الرسائل من المستخدمين (الإدارة)
    if (senderType === 'user') {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'message_from_admin'
      })
    }

    // جلب إعدادات الرد التلقائي
    const settingsResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:7443'}/api/ai/auto-reply`, {
      method: 'GET'
    })
    const settingsData = await settingsResponse.json()
    const settings = settingsData.data

    if (!settings.enabled) {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'auto_reply_disabled'
      })
    }

    // تم إزالة فحص ساعات العمل - النظام يعمل 24 ساعة عند التفعيل
    // إذا كان النظام مفعل، فهو يعمل دائماً

    // فحص عدد الردود السابقة (مبسط للاختبار)
    // يمكن تطوير هذا لاحقاً للتحقق من قاعدة البيانات
    // const aiMessagesCount = await query(`
    //   SELECT COUNT(*) as count
    //   FROM messages
    //   WHERE conversation_id = $1
    //     AND sender_type = 'ai'
    //     AND created_at > NOW() - INTERVAL '24 hours'
    // `, [conversationId])

    // if (parseInt(aiMessagesCount.rows[0].count) >= settings.max_responses_per_conversation) {
    //   return NextResponse.json({
    //     success: true,
    //     shouldReply: false,
    //     reason: 'max_responses_reached'
    //   })
    // }

    // فحص الكلمات المستبعدة
    const hasExcludedKeywords = settings.excluded_keywords.some(keyword =>
      messageText.toLowerCase().includes(keyword.toLowerCase())
    )

    if (hasExcludedKeywords) {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'excluded_keywords_found'
      })
    }

    // فحص الكلمات المحفزة (مبسط - يرد على أي رسالة تقريباً)
    const hasTriggerKeywords = settings.keywords_trigger.some(keyword =>
      messageText.toLowerCase().includes(keyword.toLowerCase())
    ) || messageText.trim().length > 0 // يرد على أي رسالة غير فارغة

    if (!hasTriggerKeywords) {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'no_trigger_keywords'
      })
    }

    // إرسال الرسالة للذكاء الاصطناعي
    const aiResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:7443'}/api/ai/local-models`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: messageText,
        model: settings.model,
        conversationId
      })
    })

    const aiData = await aiResponse.json()

    if (aiData.success) {
      // حفظ رد الذكاء الاصطناعي في قاعدة البيانات
      try {
        await query(`
          INSERT INTO messages
          (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
          VALUES ($1, 'ai', 0, $2, 'text', CURRENT_TIMESTAMP)
        `, [conversationId, aiData.data.response])
      } catch (dbError) {
        console.error('Error saving AI message to database:', dbError)
        // نستمر حتى لو فشل حفظ الرسالة في قاعدة البيانات
      }

      return NextResponse.json({
        success: true,
        shouldReply: true,
        response: aiData.data.response,
        model: aiData.data.model,
        delay: settings.delay_seconds
      })
    } else {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'ai_error',
        error: aiData.error
      })
    }

  } catch (error) {
    console.error('Error processing auto-reply:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في معالجة الرد التلقائي' },
      { status: 500 }
    )
  }
}