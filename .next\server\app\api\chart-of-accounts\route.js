(()=>{var e={};e.id=761,e.ids=[761],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>p});var a=s(64939),o=s(29021),n=s.n(o),c=s(33873),u=s.n(c),i=e([a]);a=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],s=d.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new a.Pool(l);async function p(e,t){let s=await E.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56263:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>p,GET:()=>c,POST:()=>u,PUT:()=>i});var a=s(32190),o=s(5069),n=e([o]);async function c(e){try{let{searchParams:t}=new URL(e.url),s=t.get("type"),r=t.get("category"),n=t.get("active"),c="WHERE 1=1",u=[];"accounts"===s?(c+=" AND charttype = $"+(u.length+1),u.push("A")):"headings"===s&&(c+=" AND charttype = $"+(u.length+1),u.push("H")),r&&(c+=" AND category = $"+(u.length+1),u.push(r)),"true"===n&&(c+=" AND obsolete = false");let i=await (0,o.P)(`
      SELECT 
        coa.*,
        COALESCE(
          (SELECT SUM(amount) 
           FROM acc_trans at 
           WHERE at.chart_id = coa.id 
           AND at.approved = true), 
          0
        ) as balance,
        h.description as heading_name
      FROM chart_of_accounts coa
      LEFT JOIN chart_of_accounts h ON coa.heading = h.id
      ${c}
      ORDER BY coa.accno
    `,u);return a.NextResponse.json({success:!0,data:i.rows})}catch(e){return console.error("Error fetching chart of accounts:",e),a.NextResponse.json({success:!1,error:"فشل في جلب دليل الحسابات"},{status:500})}}async function u(e){try{let{accno:t,description:s,charttype:r="A",category:n,contra:c=!1,tax:u=!1,link:i,heading:p}=await e.json();if(!t||!s||!n)return a.NextResponse.json({success:!1,error:"رقم الحساب والوصف والفئة مطلوبة"},{status:400});if((await (0,o.P)("SELECT id FROM chart_of_accounts WHERE accno = $1",[t])).rows.length>0)return a.NextResponse.json({success:!1,error:"رقم الحساب موجود مسبقاً"},{status:400});let d=await (0,o.P)(`
      INSERT INTO chart_of_accounts (
        accno, description, charttype, category, 
        contra, tax, link, heading
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `,[t,s,r,n,c,u,i,p]);return a.NextResponse.json({success:!0,message:"تم إنشاء الحساب بنجاح",data:d.rows[0]})}catch(e){return console.error("Error creating account:",e),a.NextResponse.json({success:!1,error:"فشل في إنشاء الحساب"},{status:500})}}async function i(e){try{let{id:t,accno:s,description:r,charttype:n,category:c,contra:u,tax:i,link:p,heading:d,obsolete:l}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});let E=await (0,o.P)(`
      UPDATE chart_of_accounts 
      SET 
        accno = COALESCE($2, accno),
        description = COALESCE($3, description),
        charttype = COALESCE($4, charttype),
        category = COALESCE($5, category),
        contra = COALESCE($6, contra),
        tax = COALESCE($7, tax),
        link = COALESCE($8, link),
        heading = $9,
        obsolete = COALESCE($10, obsolete)
      WHERE id = $1
      RETURNING *
    `,[t,s,r,n,c,u,i,p,d,l]);if(0===E.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return a.NextResponse.json({success:!0,message:"تم تحديث الحساب بنجاح",data:E.rows[0]})}catch(e){return console.error("Error updating account:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث الحساب"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),s=t.get("id");if(!s)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});let r=await (0,o.P)("SELECT COUNT(*) as count FROM acc_trans WHERE chart_id = $1",[s]);if(parseInt(r.rows[0].count)>0)return await (0,o.P)("UPDATE chart_of_accounts SET obsolete = true WHERE id = $1",[s]),a.NextResponse.json({success:!0,message:"تم إلغاء الحساب (يحتوي على معاملات)"});{let e=await (0,o.P)("DELETE FROM chart_of_accounts WHERE id = $1 RETURNING *",[s]);if(0===e.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return a.NextResponse.json({success:!0,message:"تم حذف الحساب بنجاح"})}}catch(e){return console.error("Error deleting account:",e),a.NextResponse.json({success:!1,error:"فشل في حذف الحساب"},{status:500})}}o=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},94144:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>i,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a=s(96559),o=s(48088),n=s(37719),c=s(56263),u=e([c]);c=(u.then?(await u)():u)[0];let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/chart-of-accounts/route",pathname:"/api/chart-of-accounts",filename:"route",bundlePath:"app/api/chart-of-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\chart-of-accounts\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:E}=p;function i(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}r()}catch(e){r(e)}})},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(94144));module.exports=r})();