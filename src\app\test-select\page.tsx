'use client'

export default function TestSelectPage() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">🧪 اختبار القوائم المنسدلة</h1>
      
      <div className="max-w-2xl mx-auto space-y-8">
        
        {/* اختبار 1: قائمة HTML بسيطة */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-4 text-blue-600">1️⃣ قائمة HTML بسيطة</h2>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              👤 اختر العميل
            </label>
            <select className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
              <option value="">اختر العميل...</option>
              <optgroup label="👤 العملاء">
                <option value="client_1">أحمد محمد - 12010001</option>
                <option value="client_2">فاطمة علي - 12010002</option>
                <option value="client_3">محمد حسن - 12010003</option>
                <option value="client_4">سارة أحمد - 12010004</option>
              </optgroup>
              <optgroup label="👨‍💼 الموظفين">
                <option value="employee_1">علي محمود - 12020001</option>
                <option value="employee_2">نور الدين - 12020002</option>
                <option value="employee_3">ليلى حسام - 12020003</option>
              </optgroup>
            </select>
          </div>
        </div>

        {/* اختبار 2: قائمة الحسابات */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-4 text-green-600">2️⃣ قائمة الحسابات</h2>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              🏦 اختر الحساب
            </label>
            <select className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
              <option value="">اختر الحساب...</option>
              <optgroup label="💰 الصناديق">
                <option value="1">صندوق الرئيسي - 1001</option>
                <option value="2">صندوق فرعي - 1002</option>
              </optgroup>
              <optgroup label="🏦 البنوك">
                <option value="3">البنك الأهلي - 1101</option>
                <option value="4">بنك مصر - 1102</option>
              </optgroup>
              <optgroup label="👤 العملاء">
                <option value="5">العملاء - 1201</option>
              </optgroup>
              <optgroup label="📦 المخزون">
                <option value="6">المخزون - 1301</option>
              </optgroup>
            </select>
          </div>
        </div>

        {/* اختبار 3: قائمة مع JavaScript */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-4 text-purple-600">3️⃣ قائمة مع JavaScript</h2>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              ⚡ قائمة تفاعلية
            </label>
            <select 
              className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              onChange={(e) => {
                console.log('تم اختيار:', e.target.value)
                alert(`تم اختيار: ${e.target.options[e.target.selectedIndex].text}`)
              }}
            >
              <option value="">اختر الخيار...</option>
              <optgroup label="🎯 خيارات الاختبار">
                <option value="test1">اختبار 1</option>
                <option value="test2">اختبار 2</option>
                <option value="test3">اختبار 3</option>
              </optgroup>
            </select>
          </div>
        </div>

        {/* رسالة النجاح */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="text-2xl">✅</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                إذا كنت ترى هذه القوائم وتعمل بشكل صحيح، فهذا يعني أن المشكلة تم حلها!
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>جرب اختيار خيارات مختلفة من القوائم أعلاه للتأكد من عملها.</p>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  )
}
