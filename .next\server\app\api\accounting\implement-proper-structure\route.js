(()=>{var e={};e.id=8194,e.ids=[8194],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,c)=>{"use strict";c.a(e,async(e,a)=>{try{c.d(t,{P:()=>_});var o=c(64939),n=c(29021),r=c.n(n),s=c(33873),u=c.n(s),i=e([o]);o=(i.then?(await i)():i)[0];let l=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=r().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],c=l.default_config;return{database:t.database,user:c.db_user,host:c.db_host,password:process.env.DB_PASSWORD||c.db_password,port:c.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new o.Pool(d);async function _(e,t){let c=await E.connect();try{return await c.query(e,t)}finally{c.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50600:(e,t,c)=>{"use strict";c.a(e,async(e,a)=>{try{c.r(t),c.d(t,{patchFetch:()=>i,routeModule:()=>_,serverHooks:()=>E,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var o=c(96559),n=c(48088),r=c(37719),s=c(72934),u=e([s]);s=(u.then?(await u)():u)[0];let _=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/implement-proper-structure/route",pathname:"/api/accounting/implement-proper-structure",filename:"route",bundlePath:"app/api/accounting/implement-proper-structure/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\implement-proper-structure\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:E}=_;function i(){return(0,r.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},72934:(e,t,c)=>{"use strict";c.a(e,async(e,a)=>{try{c.r(t),c.d(t,{POST:()=>s});var o=c(32190),n=c(5069),r=e([n]);async function s(e){try{console.log("\uD83D\uDE80 بدء تطبيق التصميم المحاسبي الصحيح...");let e=await (0,n.P)(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'العملاء', 'أصول', 3,
        true, true, 'clients',
        true, 'مدين', false
      ) 
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `),t=await (0,n.P)(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموظفين', 'خصوم', 3,
        true, true, 'employees',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `),c=await (0,n.P)(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموردين', 'خصوم', 3,
        true, true, 'suppliers',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `);console.log("\uD83D\uDD27 التأكد من هيكل الجداول...");try{await (0,n.P)(`
        ALTER TABLE clients 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `)}catch(e){console.log("عمود account_id موجود بالفعل في جدول clients")}try{await (0,n.P)(`
        ALTER TABLE employees 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `)}catch(e){console.log("عمود account_id موجود بالفعل في جدول employees")}await (0,n.P)(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        tax_number VARCHAR(50),
        supplier_type VARCHAR(100),
        account_id INTEGER REFERENCES chart_of_accounts(id),
        status VARCHAR(20) DEFAULT 'active',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `),console.log("\uD83D\uDC65 إنشاء حسابات فرعية للعملاء...");let a=await (0,n.P)("SELECT * FROM clients WHERE account_id IS NULL"),r=e.rows[0].id,s=0;for(let e of a.rows){let t=`12010${String(e.id).padStart(3,"0")}`,c=`العميل - ${e.name}`,a=await (0,n.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'أصول', 4,
          $3, true, 'مدين', true,
          'clients', $4
        ) 
        ON CONFLICT (account_code) DO UPDATE SET
          account_name = EXCLUDED.account_name,
          parent_id = EXCLUDED.parent_id,
          linked_record_id = EXCLUDED.linked_record_id
        RETURNING id
      `,[t,c,r,e.id]);await (0,n.P)("UPDATE clients SET account_id = $1 WHERE id = $2",[a.rows[0].id,e.id]),s++}console.log("\uD83D\uDC68‍\uD83D\uDCBC إنشاء حسابات فرعية للموظفين...");let u=await (0,n.P)("SELECT * FROM employees WHERE account_id IS NULL"),i=t.rows[0].id,_=0;for(let e of u.rows){let t=`21010${String(e.id).padStart(3,"0")}`,c=`الموظف - ${e.name}`,a=await (0,n.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'خصوم', 4,
          $3, true, 'دائن', true,
          'employees', $4
        )
        ON CONFLICT (account_code) DO UPDATE SET
          account_name = EXCLUDED.account_name,
          parent_id = EXCLUDED.parent_id,
          linked_record_id = EXCLUDED.linked_record_id
        RETURNING id
      `,[t,c,i,e.id]);await (0,n.P)("UPDATE employees SET account_id = $1 WHERE id = $2",[a.rows[0].id,e.id]),_++}let l={clientsMainAccountId:e.rows[0].id,employeesMainAccountId:t.rows[0].id,suppliersMainAccountId:c.rows[0].id,clientsLinked:s,employeesLinked:_,message:"تم تطبيق التصميم المحاسبي الصحيح بنجاح"};return console.log("\uD83C\uDF89 تم تطبيق التصميم المحاسبي الصحيح بنجاح!"),o.NextResponse.json({success:!0,message:"تم تطبيق التصميم المحاسبي الصحيح بنجاح",data:l})}catch(e){return console.error("❌ خطأ في تطبيق التصميم المحاسبي:",e),o.NextResponse.json({success:!1,message:"فشل في تطبيق التصميم المحاسبي",error:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}n=(r.then?(await r)():r)[0],a()}catch(e){a(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var c=e=>t(t.s=e),a=t.X(0,[4447,580],()=>c(50600));module.exports=a})();