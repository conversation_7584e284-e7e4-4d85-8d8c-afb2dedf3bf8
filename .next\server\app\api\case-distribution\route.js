/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/case-distribution/route";
exports.ids = ["app/api/case-distribution/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcase-distribution%2Froute&page=%2Fapi%2Fcase-distribution%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcase-distribution%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcase-distribution%2Froute&page=%2Fapi%2Fcase-distribution%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcase-distribution%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_case_distribution_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/case-distribution/route.ts */ \"(rsc)/./src/app/api/case-distribution/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_case_distribution_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_case_distribution_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/case-distribution/route\",\n        pathname: \"/api/case-distribution\",\n        filename: \"route\",\n        bundlePath: \"app/api/case-distribution/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\case-distribution\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_case_distribution_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcase-distribution%2Froute&page=%2Fapi%2Fcase-distribution%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcase-distribution%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/case-distribution/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/case-distribution/route.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب توزيعات القضايا\nasync function GET() {\n    try {\n        // جلب توزيعات القضايا من قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        cd.id,\n        cd.issue_id,\n        i.title as issue_title,\n        i.case_number,\n        i.amount as case_amount,\n        cd.admin_amount,\n        cd.remaining_amount,\n        cd.created_date\n      FROM case_distribution cd\n      JOIN issues i ON cd.issue_id = i.id\n      ORDER BY cd.created_date DESC\n    `);\n        // جلب تفاصيل الخدمات لكل توزيع\n        const distributions = [];\n        for (const dist of result.rows){\n            const servicesResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        SELECT\n          sd.service_id,\n          s.name as service_name,\n          sd.percentage,\n          sd.amount,\n          sd.lawyer_id,\n          e.name as lawyer_name\n        FROM service_distributions sd\n        JOIN services s ON sd.service_id = s.id\n        LEFT JOIN employees e ON sd.lawyer_id = e.id\n        WHERE sd.case_distribution_id = $1\n        ORDER BY sd.service_id\n      `, [\n                dist.id\n            ]);\n            distributions.push({\n                ...dist,\n                case_amount: parseFloat(dist.case_amount || 0),\n                admin_amount: parseFloat(dist.admin_amount || 0),\n                remaining_amount: parseFloat(dist.remaining_amount || 0),\n                lineage_id: null,\n                lineage_name: 'غير محدد',\n                admin_percentage: 0,\n                commission_percentage: 0,\n                service_distributions: servicesResult.rows.map((service)=>({\n                        ...service,\n                        percentage: parseFloat(service.percentage || 0),\n                        amount: parseFloat(service.amount || 0)\n                    }))\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: distributions\n        });\n    } catch (error) {\n        console.error('Error fetching case distributions:', error);\n        console.error('Error details:', error.message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في جلب بيانات توزيع القضايا: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة توزيع قضية جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { issue_id, lineage_id, service_distributions } = body;\n        if (!issue_id || !lineage_id || !service_distributions) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية ومجموعة النسب وتوزيع الخدمات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من أن مجموع نسب الخدمات لا يزيد عن 100%\n        const totalPercentage = service_distributions.reduce((sum, dist)=>sum + (dist.percentage || 0), 0);\n        if (totalPercentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${totalPercentage}%`\n            }, {\n                status: 400\n            });\n        }\n        // جلب بيانات القضية\n        const issueResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT amount FROM issues WHERE id = $1\n    `, [\n            issue_id\n        ]);\n        if (issueResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية المحددة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const caseAmount = parseFloat(issueResult.rows[0].amount || 0);\n        // جلب بيانات النسب المالية\n        const lineageResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1\n    `, [\n            lineage_id\n        ]);\n        if (lineageResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مجموعة النسب المحددة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const lineage = lineageResult.rows[0];\n        const adminPercentage = parseFloat(lineage.admin_percentage || 0);\n        const adminAmount = caseAmount * adminPercentage / 100;\n        const remainingAmount = caseAmount - adminAmount;\n        // حساب المبالغ للخدمات\n        const totalServicePercentage = service_distributions.reduce((sum, dist)=>sum + (dist.percentage || 0), 0);\n        const serviceDistributionsWithAmounts = service_distributions.map((dist)=>({\n                ...dist,\n                amount: remainingAmount * (dist.percentage || 0) / 100\n            }));\n        // إدراج توزيع القضية\n        const distributionResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO case_distribution (issue_id, lineage_id, admin_amount, remaining_amount)\n      VALUES ($1, $2, $3, $4)\n      RETURNING id\n    `, [\n            issue_id,\n            lineage_id,\n            adminAmount,\n            remainingAmount\n        ]);\n        const distributionId = distributionResult.rows[0].id;\n        // إدراج تفاصيل توزيع الخدمات\n        for (const serviceDist of serviceDistributionsWithAmounts){\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)\n        VALUES ($1, $2, $3, $4, $5)\n      `, [\n                distributionId,\n                serviceDist.service_id,\n                serviceDist.percentage,\n                serviceDist.amount,\n                serviceDist.lawyer_id\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة توزيع القضية بنجاح',\n            data: {\n                id: distributionId,\n                issue_id,\n                lineage_id,\n                admin_amount: adminAmount,\n                remaining_amount: remainingAmount,\n                service_distributions: serviceDistributionsWithAmounts\n            }\n        });\n    } catch (error) {\n        console.error('Error creating case distribution:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة توزيع القضية'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث توزيع قضية\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, issue_id, lineage_id, service_distributions } = body;\n        if (!id || !issue_id || !lineage_id || !service_distributions) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف والقضية ومجموعة النسب وتوزيع الخدمات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من أن مجموع نسب الخدمات لا يزيد عن 100%\n        const totalPercentage = service_distributions.reduce((sum, dist)=>sum + (dist.percentage || 0), 0);\n        if (totalPercentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${totalPercentage}%`\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود التوزيع\n        const existingDistribution = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT * FROM case_distribution WHERE id = $1\n    `, [\n            id\n        ]);\n        if (existingDistribution.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'التوزيع المحدد غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // جلب بيانات القضية\n        const issueResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT amount FROM issues WHERE id = $1\n    `, [\n            issue_id\n        ]);\n        if (issueResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية المحددة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const caseAmount = parseFloat(issueResult.rows[0].amount || 0);\n        // جلب بيانات النسب المالية\n        const lineageResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1\n    `, [\n            lineage_id\n        ]);\n        if (lineageResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مجموعة النسب المحددة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const lineage = lineageResult.rows[0];\n        const adminPercentage = parseFloat(lineage.admin_percentage || 0);\n        const adminAmount = caseAmount * adminPercentage / 100;\n        const remainingAmount = caseAmount - adminAmount;\n        // حساب المبالغ للخدمات\n        const serviceDistributionsWithAmounts = service_distributions.map((dist)=>({\n                ...dist,\n                amount: remainingAmount * (dist.percentage || 0) / 100\n            }));\n        // تحديث توزيع القضية\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE case_distribution \n      SET issue_id = $1, lineage_id = $2, admin_amount = $3, remaining_amount = $4\n      WHERE id = $5\n    `, [\n            issue_id,\n            lineage_id,\n            adminAmount,\n            remainingAmount,\n            id\n        ]);\n        // حذف تفاصيل الخدمات القديمة\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM service_distributions WHERE case_distribution_id = $1\n    `, [\n            id\n        ]);\n        // إدراج تفاصيل توزيع الخدمات الجديدة\n        for (const serviceDist of serviceDistributionsWithAmounts){\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)\n        VALUES ($1, $2, $3, $4, $5)\n      `, [\n                id,\n                serviceDist.service_id,\n                serviceDist.percentage,\n                serviceDist.amount,\n                serviceDist.lawyer_id\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث توزيع القضية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error updating case distribution:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث توزيع القضية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف توزيع قضية\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف توزيع القضية مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود التوزيع\n        const existingDistribution = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT * FROM case_distribution WHERE id = $1\n    `, [\n            id\n        ]);\n        if (existingDistribution.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'التوزيع المحدد غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // حذف تفاصيل توزيع الخدمات أولاً (CASCADE سيتولى هذا تلقائياً)\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM case_distribution WHERE id = $1\n    `, [\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف توزيع القضية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting case distribution:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف توزيع القضية'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/case-distribution/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQUd6QiwwQ0FBMEM7QUFDMUMsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLENBQUNDLFdBQVcsRUFBRTtJQUM1QixNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxNQUFNQyxPQUFPLElBQUlMLG9DQUFJQSxDQUFDO0lBQ3BCTSxNQUFNTCxRQUFRQyxHQUFHLENBQUNLLE9BQU8sSUFBSTtJQUM3QkMsTUFBTVAsUUFBUUMsR0FBRyxDQUFDTyxPQUFPLElBQUk7SUFDN0JDLFVBQVVULFFBQVFDLEdBQUcsQ0FBQ1MsT0FBTyxJQUFJO0lBQ2pDQyxVQUFVWCxRQUFRQyxHQUFHLENBQUNDLFdBQVcsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7SUFDaEVVLE1BQU1DLFNBQVNiLFFBQVFDLEdBQUcsQ0FBQ2EsT0FBTyxJQUFJO0FBQ3hDO0FBRU8sZUFBZUMsTUFBTUMsSUFBWSxFQUFFQyxNQUFjO0lBQ3RELE1BQU1DLFNBQVMsTUFBTWQsS0FBS2UsT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNRixPQUFPSCxLQUFLLENBQUNDLE1BQU1DO1FBQ3hDLE9BQU9HO0lBQ1QsU0FBVTtRQUNSRixPQUFPRyxPQUFPO0lBQ2hCO0FBQ0Y7QUFFZSIsInNvdXJjZXMiOlsiRDpcXG1vaGFtaW5ld1xcc3JjXFxsaWJcXGRiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcblxuXG4vLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZg9mE2YXYqSDZhdix2YjYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbmlmICghcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdEQl9QQVNTV09SRCBlbnZpcm9ubWVudCB2YXJpYWJsZSBpcyByZXF1aXJlZCcpXG59XG5cbmNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gIHVzZXI6IHByb2Nlc3MuZW52LkRCX1VTRVIgfHwgJ3Bvc3RncmVzJyxcbiAgaG9zdDogcHJvY2Vzcy5lbnYuREJfSE9TVCB8fCAnbG9jYWxob3N0JyxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ21vaGFtbWknLFxuICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgJ3lvdXJfcGFzc3dvcmRfaGVyZScsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbn0pXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeSh0ZXh0OiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKSB7XG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbmV4cG9ydCB7IHBvb2wgfVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJwcm9jZXNzIiwiZW52IiwiREJfUEFTU1dPUkQiLCJFcnJvciIsInBvb2wiLCJ1c2VyIiwiREJfVVNFUiIsImhvc3QiLCJEQl9IT1NUIiwiZGF0YWJhc2UiLCJEQl9OQU1FIiwicGFzc3dvcmQiLCJwb3J0IiwicGFyc2VJbnQiLCJEQl9QT1JUIiwicXVlcnkiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsInJlbGVhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcase-distribution%2Froute&page=%2Fapi%2Fcase-distribution%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcase-distribution%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();