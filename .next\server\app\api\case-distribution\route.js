(()=>{var e={};e.id=5929,e.ids=[5929],e.modules={691:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{DELETE:()=>d,GET:()=>o,POST:()=>c,PUT:()=>u});var a=t(32190),i=t(5069),n=e([i]);async function o(){try{let e=await (0,i.P)(`
      SELECT
        cd.id,
        cd.issue_id,
        i.title as issue_title,
        i.case_number,
        i.amount as case_amount,
        cd.admin_amount,
        cd.remaining_amount,
        cd.created_date
      FROM case_distribution cd
      JOIN issues i ON cd.issue_id = i.id
      ORDER BY cd.created_date DESC
    `),s=[];for(let t of e.rows){let e=await (0,i.P)(`
        SELECT
          sd.service_id,
          s.name as service_name,
          sd.percentage,
          sd.amount,
          sd.lawyer_id,
          e.name as lawyer_name
        FROM service_distributions sd
        JOIN services s ON sd.service_id = s.id
        LEFT JOIN employees e ON sd.lawyer_id = e.id
        WHERE sd.case_distribution_id = $1
        ORDER BY sd.service_id
      `,[t.id]);s.push({...t,case_amount:parseFloat(t.case_amount||0),admin_amount:parseFloat(t.admin_amount||0),remaining_amount:parseFloat(t.remaining_amount||0),lineage_id:null,lineage_name:"غير محدد",admin_percentage:0,commission_percentage:0,service_distributions:e.rows.map(e=>({...e,percentage:parseFloat(e.percentage||0),amount:parseFloat(e.amount||0)}))})}return a.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error fetching case distributions:",e),console.error("Error details:",e.message),a.NextResponse.json({success:!1,error:`فشل في جلب بيانات توزيع القضايا: ${e.message}`},{status:500})}}async function c(e){try{let{issue_id:s,lineage_id:t,service_distributions:r}=await e.json();if(!s||!t||!r)return a.NextResponse.json({success:!1,error:"القضية ومجموعة النسب وتوزيع الخدمات مطلوبة"},{status:400});let n=r.reduce((e,s)=>e+(s.percentage||0),0);if(n>100)return a.NextResponse.json({success:!1,error:`مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${n}%`},{status:400});let o=await (0,i.P)(`
      SELECT amount FROM issues WHERE id = $1
    `,[s]);if(0===o.rows.length)return a.NextResponse.json({success:!1,error:"القضية المحددة غير موجودة"},{status:404});let c=parseFloat(o.rows[0].amount||0),u=await (0,i.P)(`
      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1
    `,[t]);if(0===u.rows.length)return a.NextResponse.json({success:!1,error:"مجموعة النسب المحددة غير موجودة"},{status:404});let d=u.rows[0],p=parseFloat(d.admin_percentage||0),l=c*p/100,m=c-l;r.reduce((e,s)=>e+(s.percentage||0),0);let _=r.map(e=>({...e,amount:m*(e.percentage||0)/100})),E=(await (0,i.P)(`
      INSERT INTO case_distribution (issue_id, lineage_id, admin_amount, remaining_amount)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `,[s,t,l,m])).rows[0].id;for(let e of _)await (0,i.P)(`
        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)
        VALUES ($1, $2, $3, $4, $5)
      `,[E,e.service_id,e.percentage,e.amount,e.lawyer_id]);return a.NextResponse.json({success:!0,message:"تم إضافة توزيع القضية بنجاح",data:{id:E,issue_id:s,lineage_id:t,admin_amount:l,remaining_amount:m,service_distributions:_}})}catch(e){return console.error("Error creating case distribution:",e),a.NextResponse.json({success:!1,error:"فشل في إضافة توزيع القضية"},{status:500})}}async function u(e){try{let{id:s,issue_id:t,lineage_id:r,service_distributions:n}=await e.json();if(!s||!t||!r||!n)return a.NextResponse.json({success:!1,error:"المعرف والقضية ومجموعة النسب وتوزيع الخدمات مطلوبة"},{status:400});let o=n.reduce((e,s)=>e+(s.percentage||0),0);if(o>100)return a.NextResponse.json({success:!1,error:`مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${o}%`},{status:400});let c=await (0,i.P)(`
      SELECT * FROM case_distribution WHERE id = $1
    `,[s]);if(0===c.rows.length)return a.NextResponse.json({success:!1,error:"التوزيع المحدد غير موجود"},{status:404});let u=await (0,i.P)(`
      SELECT amount FROM issues WHERE id = $1
    `,[t]);if(0===u.rows.length)return a.NextResponse.json({success:!1,error:"القضية المحددة غير موجودة"},{status:404});let d=parseFloat(u.rows[0].amount||0),p=await (0,i.P)(`
      SELECT admin_percentage, commission_percentage FROM lineages WHERE id = $1
    `,[r]);if(0===p.rows.length)return a.NextResponse.json({success:!1,error:"مجموعة النسب المحددة غير موجودة"},{status:404});let l=p.rows[0],m=parseFloat(l.admin_percentage||0),_=d*m/100,E=d-_,g=n.map(e=>({...e,amount:E*(e.percentage||0)/100}));for(let e of(await (0,i.P)(`
      UPDATE case_distribution 
      SET issue_id = $1, lineage_id = $2, admin_amount = $3, remaining_amount = $4
      WHERE id = $5
    `,[t,r,_,E,s]),await (0,i.P)(`
      DELETE FROM service_distributions WHERE case_distribution_id = $1
    `,[s]),g))await (0,i.P)(`
        INSERT INTO service_distributions (case_distribution_id, service_id, percentage, amount, lawyer_id)
        VALUES ($1, $2, $3, $4, $5)
      `,[s,e.service_id,e.percentage,e.amount,e.lawyer_id]);return a.NextResponse.json({success:!0,message:"تم تحديث توزيع القضية بنجاح"})}catch(e){return console.error("Error updating case distribution:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث توزيع القضية"},{status:500})}}async function d(e){try{let{searchParams:s}=new URL(e.url),t=s.get("id");if(!t)return a.NextResponse.json({success:!1,error:"معرف توزيع القضية مطلوب"},{status:400});let r=await (0,i.P)(`
      SELECT * FROM case_distribution WHERE id = $1
    `,[t]);if(0===r.rows.length)return a.NextResponse.json({success:!1,error:"التوزيع المحدد غير موجود"},{status:404});return await (0,i.P)(`
      DELETE FROM case_distribution WHERE id = $1
    `,[t]),a.NextResponse.json({success:!0,message:"تم حذف توزيع القضية بنجاح"})}catch(e){return console.error("Error deleting case distribution:",e),a.NextResponse.json({success:!1,error:"فشل في حذف توزيع القضية"},{status:500})}}i=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.d(s,{P:()=>d});var a=t(64939),i=t(29021),n=t.n(i),o=t(33873),c=t.n(o),u=e([a]);a=(u.then?(await u)():u)[0];let p=null;try{let e=c().join(process.cwd(),"routing.config.json"),s=n().readFileSync(e,"utf8");p=JSON.parse(s)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let s=p.routes[e],t=p.default_config;return{database:s.database,user:t.db_user,host:t.db_host,password:process.env.DB_PASSWORD||t.db_password,port:t.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),m=new a.Pool(l);async function d(e,s){let t=await m.connect();try{return await t.query(e,s)}finally{t.release()}}r()}catch(e){r(e)}})},5300:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var a=t(96559),i=t(48088),n=t(37719),o=t(691),c=e([o]);o=(c.then?(await c)():c)[0];let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/case-distribution/route",pathname:"/api/case-distribution",filename:"route",bundlePath:"app/api/case-distribution/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\case-distribution\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:m}=d;function u(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580],()=>t(5300));module.exports=r})();