import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

export async function GET() {
  let pool: Pool | null = null
  
  try {
    // الاتصال بقاعدة postgres الافتراضية للتحقق من وجود قاعدة mohammi
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'postgres', // الاتصال بقاعدة postgres الافتراضية
      user: 'postgres',
      password: process.env.DB_PASSWORD || 'your_password_here',
      connectionTimeoutMillis: 5000
    }

    pool = new Pool(dbConfig)
    const client = await pool.connect()
    
    // التحقق من وجود قاعدة البيانات mohammi
    const result = await client.query(
      "SELECT datname FROM pg_database WHERE datname = 'mohammi'"
    )
    
    client.release()
    
    if (result.rows.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'قاعدة البيانات "mohammi" موجودة',
        details: 'تم العثور على قاعدة البيانات بنجاح'
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'قاعدة البيانات "mohammi" غير موجودة',
        details: 'يجب إنشاء قاعدة بيانات بالاسم "mohammi" أولاً'
      })
    }
    
  } catch (error: any) {
    console.error('خطأ في التحقق من قاعدة البيانات:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في التحقق من وجود قاعدة البيانات',
      details: `رمز الخطأ: ${error.code || 'غير محدد'}\nالرسالة: ${error.message}`
    }, { status: 500 })
    
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
