/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/percentages/route";
exports.ids = ["app/api/percentages/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpercentages%2Froute&page=%2Fapi%2Fpercentages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpercentages%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpercentages%2Froute&page=%2Fapi%2Fpercentages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpercentages%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_percentages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/percentages/route.ts */ \"(rsc)/./src/app/api/percentages/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_percentages_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_percentages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/percentages/route\",\n        pathname: \"/api/percentages\",\n        filename: \"route\",\n        bundlePath: \"app/api/percentages/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\percentages\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_percentages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZwZXJjZW50YWdlcyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGcGVyY2VudGFnZXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZwZXJjZW50YWdlcyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbW9oYW1pbmV3JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbW9oYW1pbmV3JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNHO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccGVyY2VudGFnZXNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3BlcmNlbnRhZ2VzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcGVyY2VudGFnZXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3BlcmNlbnRhZ2VzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxcbW9oYW1pbmV3XFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHBlcmNlbnRhZ2VzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpercentages%2Froute&page=%2Fapi%2Fpercentages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpercentages%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/percentages/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/percentages/route.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع النسب المالية\nasync function GET() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        id,\n        name,\n        admin_percentage,\n        commission_percentage,\n        created_date\n      FROM lineages\n      ORDER BY created_date DESC\n    `);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('Error fetching percentages:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات النسب المالية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة نسبة مالية جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, admin_percentage, commission_percentage } = body;\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم النسبة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%'\n            }, {\n                status: 400\n            });\n        }\n        if (commission_percentage === undefined || commission_percentage < 0 || commission_percentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نسبة العمولة يجب أن تكون بين 0% و 100%'\n            }, {\n                status: 400\n            });\n        }\n        // إضافة النسبة الجديدة لقاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO lineages (name, admin_percentage, commission_percentage, created_date)\n      VALUES ($1, $2, $3, CURRENT_DATE)\n      RETURNING id, name, admin_percentage, commission_percentage, created_date\n    `, [\n            name,\n            Number(admin_percentage),\n            Number(commission_percentage)\n        ]);\n        const newPercentage = result.rows[0];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة النسبة المالية بنجاح',\n            data: newPercentage\n        });\n    } catch (error) {\n        console.error('Error creating percentage:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة النسبة المالية'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث نسبة مالية\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, name, admin_percentage, commission_percentage } = body;\n        if (!id || !name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف واسم النسبة مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%'\n            }, {\n                status: 400\n            });\n        }\n        if (commission_percentage === undefined || commission_percentage < 0 || commission_percentage > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'نسبة العمولة يجب أن تكون بين 0% و 100%'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث النسبة في قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE lineages\n      SET name = $1, admin_percentage = $2, commission_percentage = $3\n      WHERE id = $4\n      RETURNING id, name, admin_percentage, commission_percentage, created_date\n    `, [\n            name,\n            Number(admin_percentage),\n            Number(commission_percentage),\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النسبة المالية غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث النسبة المالية بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error updating percentage:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث النسبة المالية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف نسبة مالية\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف النسبة المالية مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود النسبة قبل الحذف\n        const checkResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT id FROM lineages WHERE id = $1\n    `, [\n            id\n        ]);\n        if (checkResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النسبة المالية غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        // حذف النسبة من قاعدة البيانات\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM lineages WHERE id = $1\n    `, [\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف النسبة المالية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting percentage:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف النسبة المالية'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/percentages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBeUI7QUFDTjtBQUNJO0FBRXZCLG9CQUFvQjtBQUNwQixJQUFJRyxnQkFBcUI7QUFDekIsSUFBSTtJQUNGLE1BQU1DLGFBQWFGLGdEQUFTLENBQUNJLFFBQVFDLEdBQUcsSUFBSTtJQUM1QyxNQUFNQyxhQUFhUCxzREFBZSxDQUFDRyxZQUFZO0lBQy9DRCxnQkFBZ0JPLEtBQUtDLEtBQUssQ0FBQ0g7QUFDN0IsRUFBRSxPQUFPSSxPQUFPO0lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO0FBQy9DO0FBRUEsMENBQTBDO0FBQzFDLElBQUksQ0FBQ04sUUFBUVEsR0FBRyxDQUFDQyxXQUFXLElBQUksQ0FBQ1osZUFBZWEsZ0JBQWdCQyxhQUFhO0lBQzNFLE1BQU0sSUFBSUMsTUFBTTtBQUNsQjtBQUVBLHdDQUF3QztBQUN4QyxNQUFNQyxvQkFBb0I7SUFDeEIsTUFBTUMsT0FBT2QsUUFBUVEsR0FBRyxDQUFDTyxJQUFJLElBQUk7SUFFakMsSUFBSWxCLGlCQUFpQkEsY0FBY21CLE1BQU0sQ0FBQ0YsS0FBSyxFQUFFO1FBQy9DLE1BQU1HLFFBQVFwQixjQUFjbUIsTUFBTSxDQUFDRixLQUFLO1FBQ3hDLE1BQU1JLGdCQUFnQnJCLGNBQWNhLGNBQWM7UUFFbEQsT0FBTztZQUNMUyxVQUFVRixNQUFNRSxRQUFRO1lBQ3hCQyxNQUFNRixjQUFjRyxPQUFPO1lBQzNCQyxNQUFNSixjQUFjSyxPQUFPO1lBQzNCQyxVQUFVeEIsUUFBUVEsR0FBRyxDQUFDQyxXQUFXLElBQUlTLGNBQWNQLFdBQVc7WUFDOURHLE1BQU1JLGNBQWNPLE9BQU87UUFDN0I7SUFDRjtJQUVBLDBDQUEwQztJQUMxQ2xCLFFBQVFtQixJQUFJLENBQUMsQ0FBQyxrQ0FBa0MsRUFBRVosS0FBSyxtQ0FBbUMsQ0FBQztJQUMzRixPQUFPO1FBQ0xLLFVBQVU7UUFDVkMsTUFBTTtRQUNORSxNQUFNO1FBQ05FLFVBQVV4QixRQUFRUSxHQUFHLENBQUNDLFdBQVcsSUFBSTtRQUNyQ0ssTUFBTTtJQUNSO0FBQ0Y7QUFFQSxNQUFNYSxXQUFXZDtBQUNqQixNQUFNZSxPQUFPLElBQUlsQyxvQ0FBSUEsQ0FBQ2lDO0FBRWYsZUFBZUUsTUFBTUMsSUFBWSxFQUFFQyxNQUFjO0lBQ3RELE1BQU1DLFNBQVMsTUFBTUosS0FBS0ssT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNRixPQUFPSCxLQUFLLENBQUNDLE1BQU1DO1FBQ3hDLE9BQU9HO0lBQ1QsU0FBVTtRQUNSRixPQUFPRyxPQUFPO0lBQ2hCO0FBQ0Y7QUFFZSIsInNvdXJjZXMiOlsiRDpcXG1vaGFtaW5ld1xcc3JjXFxsaWJcXGRiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcbmltcG9ydCBmcyBmcm9tICdmcydcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnXG5cbi8vINiq2K3ZhdmK2YQg2YXZhNmBINin2YTYqtmI2KzZitmHXG5sZXQgcm91dGluZ0NvbmZpZzogYW55ID0gbnVsbFxudHJ5IHtcbiAgY29uc3QgY29uZmlnUGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAncm91dGluZy5jb25maWcuanNvbicpXG4gIGNvbnN0IGNvbmZpZ0RhdGEgPSBmcy5yZWFkRmlsZVN5bmMoY29uZmlnUGF0aCwgJ3V0ZjgnKVxuICByb3V0aW5nQ29uZmlnID0gSlNPTi5wYXJzZShjb25maWdEYXRhKVxufSBjYXRjaCAoZXJyb3IpIHtcbiAgY29uc29sZS5lcnJvcign4p2MINiu2LfYoyDZgdmKINiq2K3ZhdmK2YQg2YXZhNmBINin2YTYqtmI2KzZitmHOicsIGVycm9yKVxufVxuXG4vLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZg9mE2YXYqSDZhdix2YjYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbmlmICghcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgJiYgIXJvdXRpbmdDb25maWc/LmRlZmF1bHRfY29uZmlnPy5kYl9wYXNzd29yZCkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ0RCX1BBU1NXT1JEIGVudmlyb25tZW50IHZhcmlhYmxlIGlzIHJlcXVpcmVkJylcbn1cblxuLy8g2KrYrdiv2YrYryDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Kog2KjZhtin2KHZiyDYudmE2Ykg2KfZhNmF2YbZgdiwXG5jb25zdCBnZXREYXRhYmFzZUNvbmZpZyA9ICgpID0+IHtcbiAgY29uc3QgcG9ydCA9IHByb2Nlc3MuZW52LlBPUlQgfHwgJzc0NDMnXG5cbiAgaWYgKHJvdXRpbmdDb25maWcgJiYgcm91dGluZ0NvbmZpZy5yb3V0ZXNbcG9ydF0pIHtcbiAgICBjb25zdCByb3V0ZSA9IHJvdXRpbmdDb25maWcucm91dGVzW3BvcnRdXG4gICAgY29uc3QgZGVmYXVsdENvbmZpZyA9IHJvdXRpbmdDb25maWcuZGVmYXVsdF9jb25maWdcblxuICAgIHJldHVybiB7XG4gICAgICBkYXRhYmFzZTogcm91dGUuZGF0YWJhc2UsXG4gICAgICB1c2VyOiBkZWZhdWx0Q29uZmlnLmRiX3VzZXIsXG4gICAgICBob3N0OiBkZWZhdWx0Q29uZmlnLmRiX2hvc3QsXG4gICAgICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgZGVmYXVsdENvbmZpZy5kYl9wYXNzd29yZCxcbiAgICAgIHBvcnQ6IGRlZmF1bHRDb25maWcuZGJfcG9ydFxuICAgIH1cbiAgfVxuXG4gIC8vINin2YTYp9mB2KrYsdin2LbZiiDYpdiw2Kcg2YTZhSDZitiq2YUg2KfZhNi52KvZiNixINi52YTZiSDYp9mE2KrZiNis2YrZh1xuICBjb25zb2xlLndhcm4oYOKaoO+4jyDZhNmFINmK2KrZhSDYp9mE2LnYq9mI2LEg2LnZhNmJINiq2YjYrNmK2Ycg2YTZhNmF2YbZgdiwICR7cG9ydH3YjCDYs9mK2KrZhSDYp9iz2KrYrtiv2KfZhSDYp9mE2KXYudiv2KfYr9in2Kog2KfZhNin2YHYqtix2KfYttmK2KlgKVxuICByZXR1cm4ge1xuICAgIGRhdGFiYXNlOiAnbW9oYW1taScsXG4gICAgdXNlcjogJ3Bvc3RncmVzJyxcbiAgICBob3N0OiAnbG9jYWxob3N0JyxcbiAgICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgJ3llbWVuMTIzJyxcbiAgICBwb3J0OiA1NDMyXG4gIH1cbn1cblxuY29uc3QgZGJDb25maWcgPSBnZXREYXRhYmFzZUNvbmZpZygpXG5jb25zdCBwb29sID0gbmV3IFBvb2woZGJDb25maWcpXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeSh0ZXh0OiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKSB7XG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbmV4cG9ydCB7IHBvb2wgfVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJmcyIsInBhdGgiLCJyb3V0aW5nQ29uZmlnIiwiY29uZmlnUGF0aCIsImpvaW4iLCJwcm9jZXNzIiwiY3dkIiwiY29uZmlnRGF0YSIsInJlYWRGaWxlU3luYyIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsImVudiIsIkRCX1BBU1NXT1JEIiwiZGVmYXVsdF9jb25maWciLCJkYl9wYXNzd29yZCIsIkVycm9yIiwiZ2V0RGF0YWJhc2VDb25maWciLCJwb3J0IiwiUE9SVCIsInJvdXRlcyIsInJvdXRlIiwiZGVmYXVsdENvbmZpZyIsImRhdGFiYXNlIiwidXNlciIsImRiX3VzZXIiLCJob3N0IiwiZGJfaG9zdCIsInBhc3N3b3JkIiwiZGJfcG9ydCIsIndhcm4iLCJkYkNvbmZpZyIsInBvb2wiLCJxdWVyeSIsInRleHQiLCJwYXJhbXMiLCJjbGllbnQiLCJjb25uZWN0IiwicmVzdWx0IiwicmVsZWFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpercentages%2Froute&page=%2Fapi%2Fpercentages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpercentages%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();