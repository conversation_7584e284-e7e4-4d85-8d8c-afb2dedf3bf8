"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/simple-chat-widget */ \"(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCompanyData */ \"(app-pages-browser)/./src/hooks/useCompanyData.ts\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst defaultCompanyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // استخدام hook بيانات الشركة مع التخزين المحلي\n    const { companyData, loading: companyLoading, error: companyError, getThemeColor, getCompanyName, isDataAvailable } = (0,_hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData)();\n    // استخدام البيانات المحملة أو الافتراضية\n    const companyDataState = companyData || defaultCompanyData;\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // مؤشر التحميل للبيانات الأساسية\n    if (companyLoading && !isDataAvailable) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-yellow-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mt-2\",\n                        children: \"يتم حفظ البيانات محلياً لتسريع التصفح مستقبلاً\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            companyError && !isDataAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-600 text-white px-4 py-2 text-center text-sm\",\n                children: \"⚠️ تعذر تحميل أحدث البيانات، يتم عرض البيانات المحفوظة محلياً\"\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__.SimpleChatWidget, {\n                isOpen: isChatWidgetOpen,\n                onClose: ()=>setIsChatWidgetOpen(false),\n                onOpen: ()=>setIsChatWidgetOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"O/ceB+Iu6CvIuCsB3XkHEZGk0w4=\", false, function() {\n    return [\n        _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData\n    ];\n});\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ })

});