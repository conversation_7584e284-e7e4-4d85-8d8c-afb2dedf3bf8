import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع القيود اليومية من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM journal_entries ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching القيود اليومية:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات القيود اليومية',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة القيود اليومية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة القيود اليومية بنجاح'
    })
  } catch (error) {
    console.error('Error creating القيود اليومية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة القيود اليومية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث القيود اليومية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث القيود اليومية بنجاح'
    })
  } catch (error) {
    console.error('Error updating القيود اليومية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث القيود اليومية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف القيود اليومية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف القيود اليومية مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM journal_entries WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف القيود اليومية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting القيود اليومية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القيود اليومية' },
      { status: 500 }
    )
  }
}