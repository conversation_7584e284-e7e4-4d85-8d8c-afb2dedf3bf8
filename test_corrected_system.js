// اختبار النظام المصحح
require('dotenv').config({ path: '.env.local' });

async function testCorrectedSystem() {
  console.log('🔄 اختبار النظام المصحح...\n');
  
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY غير متوفر');
    return;
  }
  
  // أسئلة للاختبار
  const testQuestions = [
    {
      question: 'ما هي حقوق العامل في قانون العمل اليمني؟',
      expected: 'يجب أن يجيب مباشرة عن حقوق العامل'
    },
    {
      question: 'كيف أطلق زوجتي حسب القانون اليمني؟',
      expected: 'يجب أن يجيب مباشرة عن إجراءات الطلاق'
    },
    {
      question: 'ما هو قانون الفضاء الخارجي في المريخ؟',
      expected: 'يجب أن يوجه للتواصل مع محامي المؤسسة'
    },
    {
      question: 'أحتاج استشارة معقدة جداً في قضية دولية',
      expected: 'يجب أن يوجه للتواصل مع محامي المؤسسة'
    }
  ];
  
  console.log(`📋 سيتم اختبار ${testQuestions.length} أسئلة مختلفة...\n`);
  
  for (let i = 0; i < testQuestions.length; i++) {
    const test = testQuestions[i];
    
    console.log(`${'='.repeat(60)}`);
    console.log(`🧪 اختبار ${i + 1}: "${test.question}"`);
    console.log(`🎯 المتوقع: ${test.expected}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: `أنت مساعد ذكي لمؤسسة الجرافي للمحاماة والاستشارات القانونية.

قدم استشارات قانونية مفيدة ومباشرة. أجب على الأسئلة بوضوح ودون مقدمات أو ترحيب.

لا تقل "مرحباً" أو "أتشرف" أو أي ترحيب. ابدأ مباشرة بالإجابة القانونية.

إذا لم تكن متأكداً من الإجابة أو كان السؤال معقداً جداً، قل فقط: "أنصح بالتواصل مع أحد محامينا للحصول على استشارة مفصلة."

أجب بشكل مهني ومختصر (30-100 كلمة فقط).`
            },
            {
              role: 'user',
              content: test.question
            }
          ],
          max_tokens: 200,
          temperature: 0.7
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        console.log('✅ نجح الاختبار!');
        console.log('🤖 رد GPT-4o:');
        console.log(`"${aiResponse}"`);
        
        // تحليل الرد
        const hasGreeting = aiResponse.includes('مرحباً') || aiResponse.includes('أتشرف');
        const refersToLawyers = aiResponse.includes('أنصح بالتواصل مع أحد محامينا');
        const hasDirectAnswer = !refersToLawyers && (
          aiResponse.includes('حقوق') || 
          aiResponse.includes('طلاق') || 
          aiResponse.includes('إجراءات') ||
          aiResponse.includes('قانون')
        );
        
        console.log('\n📊 تحليل الرد:');
        console.log(`   • يحتوي على ترحيب: ${hasGreeting ? '❌ نعم (غير مرغوب)' : '✅ لا (جيد)'}`);
        console.log(`   • يوجه لمحامي المؤسسة: ${refersToLawyers ? '✅ نعم' : '❌ لا'}`);
        console.log(`   • يعطي إجابة مباشرة: ${hasDirectAnswer ? '✅ نعم' : '❌ لا'}`);
        
        // تقييم الرد
        let evaluation = '';
        if (test.question.includes('المريخ') || test.question.includes('معقدة جداً')) {
          // أسئلة يجب أن توجه للمحامي
          if (refersToLawyers && !hasGreeting) {
            evaluation = '✅ ممتاز - يوجه للمحامي بدون ترحيب';
          } else if (refersToLawyers && hasGreeting) {
            evaluation = '⚠️ جيد لكن يحتوي على ترحيب';
          } else {
            evaluation = '❌ خطأ - يجب أن يوجه للمحامي';
          }
        } else {
          // أسئلة قانونية عادية
          if (hasDirectAnswer && !hasGreeting) {
            evaluation = '✅ ممتاز - إجابة مباشرة بدون ترحيب';
          } else if (hasDirectAnswer && hasGreeting) {
            evaluation = '⚠️ جيد لكن يحتوي على ترحيب';
          } else {
            evaluation = '❌ خطأ - يجب أن يجيب مباشرة';
          }
        }
        
        console.log(`   • التقييم: ${evaluation}`);
        
      } else {
        const errorData = await response.text();
        console.log('❌ فشل الاختبار');
        console.log(`📄 رمز الخطأ: ${response.status}`);
        console.log(`📝 رسالة الخطأ: ${errorData.substring(0, 200)}`);
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
    }
    
    console.log('\n');
    
    // انتظار قصير بين الاختبارات
    if (i < testQuestions.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log(`${'='.repeat(60)}`);
  console.log('📋 ملخص الاختبار:');
  console.log('🎯 النظام المصحح يجب أن:');
  console.log('   ✅ يجيب مباشرة على الأسئلة القانونية العادية');
  console.log('   ✅ يوجه للمحامي للأسئلة المعقدة أو غير القانونية');
  console.log('   ✅ لا يحتوي على ترحيب في الردود');
  console.log('   ✅ يذكر "محامينا" وليس "مركز آخر"');
  console.log(`${'='.repeat(60)}`);
}

// تشغيل الاختبار
testCorrectedSystem().catch(console.error);
