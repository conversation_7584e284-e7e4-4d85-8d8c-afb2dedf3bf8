"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-settings/page",{

/***/ "(app-pages-browser)/./src/app/admin/ai-settings/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/ai-settings/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AISettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bot,CheckCircle,Clock,MessageSquare,RefreshCw,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AISettingsPage() {\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: true,\n        model: 'openai-gpt4',\n        delay_seconds: 3,\n        working_hours_only: false,\n        working_hours_start: '00:00',\n        working_hours_end: '23:59',\n        working_days: [\n            'الأحد',\n            'الاثنين',\n            'الثلاثاء',\n            'الأربعاء',\n            'الخميس',\n            'الجمعة',\n            'السبت'\n        ],\n        max_responses_per_conversation: 20,\n        keywords_trigger: [\n            'مساعدة',\n            'استفسار',\n            'سؤال',\n            'معلومات',\n            'خدمة',\n            'مرحبا',\n            'السلام',\n            'أهلا',\n            'استشارة',\n            'قانوني',\n            'محامي'\n        ],\n        excluded_keywords: [],\n        auto_responses: {\n            greeting: 'مرحباً! أنا المساعد الذكي للمكتب مدعوم بـ GPT-4. كيف يمكنني مساعدتك؟',\n            working_hours: 'أنا متاح للمساعدة على مدار 24 ساعة مدعوم بـ GPT-4. كيف يمكنني مساعدتك؟',\n            max_reached: 'تم الوصول للحد الأقصى من الردود التلقائية. سيقوم أحد المحامين بالرد عليك قريباً.'\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testMessage, setTestMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('مرحبا، أحتاج استشارة قانونية');\n    const [testResponse, setTestResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AISettingsPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"AISettingsPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        setLoading(true);\n        try {\n            // جلب النماذج المتاحة\n            const modelsResponse = await fetch('/api/ai/local-models');\n            const modelsData = await modelsResponse.json();\n            // جلب الإعدادات من API الجديد\n            const settingsResponse = await fetch('/api/ai/settings');\n            const settingsData = await settingsResponse.json();\n            if (modelsData.success) {\n                setModels(modelsData.data.models || []);\n            }\n            if (settingsData.success) {\n                setSettings(settingsData.data);\n            }\n        } catch (error) {\n            console.error('Error fetching data:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في جلب البيانات'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage(null);\n        try {\n            const response = await fetch('/api/ai/settings', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMessage({\n                    type: 'success',\n                    text: 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح'\n                });\n                // إعادة جلب الإعدادات للتأكد من الحفظ\n                setTimeout(()=>{\n                    fetchData();\n                }, 1000);\n            } else {\n                setMessage({\n                    type: 'error',\n                    text: result.error || 'فشل في حفظ الإعدادات'\n                });\n            }\n        } catch (error) {\n            console.error('Error saving settings:', error);\n            setMessage({\n                type: 'error',\n                text: 'حدث خطأ في حفظ الإعدادات'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleTestAI = async ()=>{\n        if (!testMessage.trim()) {\n            setMessage({\n                type: 'error',\n                text: 'يرجى إدخال رسالة للاختبار'\n            });\n            return;\n        }\n        setTesting(true);\n        setTestResponse('');\n        setMessage(null);\n        try {\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: testMessage,\n                    model: settings.model,\n                    conversationId: 'test-' + Date.now()\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.response) {\n                setTestResponse(data.response);\n                setMessage({\n                    type: 'success',\n                    text: 'تم اختبار الذكاء الاصطناعي بنجاح!'\n                });\n            } else {\n                throw new Error(data.error || 'لم يتم الحصول على رد من النموذج');\n            }\n        } catch (error) {\n            console.error('Error testing AI:', error);\n            setMessage({\n                type: 'error',\n                text: 'فشل في اختبار الذكاء الاصطناعي: ' + error.message\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const testAI = async ()=>{\n        setTesting(true);\n        setMessage(null);\n        try {\n            console.log('🧪 بدء اختبار الذكاء الاصطناعي...');\n            console.log('النموذج المحدد:', settings.model);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: 'مرحبا، هذا اختبار للنظام',\n                    model: settings.model,\n                    conversationId: 'quick-test-' + Date.now()\n                })\n            });\n            console.log('📡 استجابة الخادم:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP Error: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (result.success && result.response) {\n                setMessage({\n                    type: 'success',\n                    text: \"تم اختبار النموذج بنجاح! الرد: \".concat(result.response.substring(0, 100), \"...\")\n                });\n            } else {\n                setMessage({\n                    type: 'error',\n                    text: result.error || 'لم يتم الحصول على رد من النموذج'\n                });\n            }\n        } catch (error) {\n            console.error('❌ خطأ في اختبار الذكاء الاصطناعي:', error);\n            setMessage({\n                type: 'error',\n                text: \"فشل في الاتصال بالنموذج المحلي: \".concat(error.message)\n            });\n        } finally{\n            setTesting(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'available':\n                return 'bg-green-100 text-green-800';\n            case 'not_found':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'service_unavailable':\n                return 'bg-orange-100 text-orange-800';\n            case 'offline':\n                return 'bg-red-100 text-red-800';\n            case 'api_key_required':\n                return 'bg-blue-100 text-blue-800';\n            case 'api_error':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'available':\n                return 'متاح';\n            case 'not_found':\n                return 'غير موجود';\n            case 'service_unavailable':\n                return 'الخدمة غير متاحة';\n            case 'offline':\n                return 'غير متصل';\n            case 'api_key_required':\n                return 'يحتاج API Key';\n            case 'api_error':\n                return 'خطأ في API';\n            default:\n                return 'غير معروف';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"إعدادات الذكاء الاصطناعي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"إدارة النماذج المحلية والرد التلقائي للعملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: fetchData,\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: testAI,\n                                    variant: \"outline\",\n                                    disabled: testing || !settings.enabled,\n                                    children: [\n                                        testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"اختبار النموذج\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleSave,\n                                    disabled: saving,\n                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                    children: [\n                                        saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"حفظ الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                    className: message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50',\n                    children: [\n                        message.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                            className: message.type === 'success' ? 'text-green-800' : 'text-red-800',\n                            children: message.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حالة النماذج المحلية والخارجية\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\",\n                                                children: \"محسن للسرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: fetchData,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2 \".concat(loading ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: model.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                            className: getStatusColor(model.status),\n                                                            children: getStatusText(model.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                    children: model.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"النموذج: \",\n                                                                model.model\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"آخر فحص: \",\n                                                                new Date(model.lastChecked).toLocaleString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        model.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 mt-1\",\n                                                            children: [\n                                                                \"خطأ: \",\n                                                                model.error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, model.key, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الإعدادات العامة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-base font-medium\",\n                                                    children: \"تفعيل الرد التلقائي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"تشغيل أو إيقاف المساعد الذكي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                            checked: settings.enabled,\n                                            onCheckedChange: (checked)=>setSettings({\n                                                    ...settings,\n                                                    enabled: checked\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"النموذج المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: settings.model,\n                                                    onValueChange: (value)=>setSettings({\n                                                            ...settings,\n                                                            model: value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: models.filter((m)=>m.status === 'available').map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: model.key,\n                                                                    children: model.name\n                                                                }, model.key, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"تأخير الرد (بالثواني)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"number\",\n                                                    value: settings.delay_seconds,\n                                                    onChange: (e)=>setSettings({\n                                                            ...settings,\n                                                            delay_seconds: parseInt(e.target.value)\n                                                        }),\n                                                    min: \"5\",\n                                                    max: \"300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الحد الأقصى للردود لكل محادثة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            type: \"number\",\n                                            value: settings.max_responses_per_conversation,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    max_responses_per_conversation: parseInt(e.target.value)\n                                                }),\n                                            min: \"1\",\n                                            max: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"ساعات العمل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-base font-medium\",\n                                                    children: \"حالة التشغيل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"النظام يعمل 24 ساعة طالما مفتاح التشغيل مفعل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"متاح 24/7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                            children: \"تم تعطيل قيود ساعات العمل. المساعد الذكي متاح على مدار 24 ساعة طالما أن مفتاح التشغيل مفعل.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"ساعات العمل الافتراضية (للعرض فقط)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"time\",\n                                                    value: \"00:00\",\n                                                    disabled: true,\n                                                    className: \"bg-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"بداية: منتصف الليل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"نهاية ساعات العمل (للعرض فقط)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"time\",\n                                                    value: \"23:59\",\n                                                    disabled: true,\n                                                    className: \"bg-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"نهاية: قبل منتصف الليل بدقيقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الكلمات المفتاحية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الكلمات المحفزة للرد (مفصولة بفاصلة)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.keywords_trigger.join(', '),\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    keywords_trigger: e.target.value.split(',').map((k)=>k.trim()).filter((k)=>k)\n                                                }),\n                                            placeholder: \"مساعدة, استفسار, سؤال, معلومات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"الكلمات المستبعدة (مفصولة بفاصلة)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.excluded_keywords.join(', '),\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    excluded_keywords: e.target.value.split(',').map((k)=>k.trim()).filter((k)=>k)\n                                                }),\n                                            placeholder: \"عاجل, طارئ, مهم جداً, محامي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الردود التلقائية المحددة مسبقاً\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الترحيب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.greeting,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        greeting: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة خارج ساعات العمل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.working_hours,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        working_hours: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الوصول للحد الأقصى\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: settings.auto_responses.max_reached,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    auto_responses: {\n                                                        ...settings.auto_responses,\n                                                        max_reached: e.target.value\n                                                    }\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"اختبار الذكاء الاصطناعي\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رسالة الاختبار\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            value: testMessage,\n                                            onChange: (e)=>setTestMessage(e.target.value),\n                                            placeholder: \"اكتب رسالة لاختبار الذكاء الاصطناعي...\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleTestAI,\n                                    disabled: testing || !testMessage.trim(),\n                                    className: \"w-full\",\n                                    children: testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"جاري الاختبار...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bot_CheckCircle_Clock_MessageSquare_RefreshCw_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"اختبار الذكاء الاصطناعي\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                testResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"رد الذكاء الاصطناعي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"whitespace-pre-wrap text-sm\",\n                                                children: testResponse\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\admin\\\\ai-settings\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(AISettingsPage, \"H8ELR9b9NhMXZJktur0OmRerFFQ=\");\n_c = AISettingsPage;\nvar _c;\n$RefreshReg$(_c, \"AISettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vYWktc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2lCO0FBQ21CO0FBQ2hDO0FBQ0Y7QUFDQTtBQUNFO0FBQ3VEO0FBQ25EO0FBQ047QUFDa0I7QUFhMUM7QUErQk4sU0FBUytCOztJQUN0QixNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2pDLCtDQUFRQSxDQUFZLEVBQUU7SUFDbEQsTUFBTSxDQUFDa0MsVUFBVUMsWUFBWSxHQUFHbkMsK0NBQVFBLENBQWE7UUFDbkRvQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxvQkFBb0I7UUFDcEJDLHFCQUFxQjtRQUNyQkMsbUJBQW1CO1FBQ25CQyxjQUFjO1lBQUM7WUFBUztZQUFXO1lBQVk7WUFBWTtZQUFVO1lBQVU7U0FBUTtRQUN2RkMsZ0NBQWdDO1FBQ2hDQyxrQkFBa0I7WUFBQztZQUFVO1lBQVc7WUFBUTtZQUFXO1lBQVE7WUFBUztZQUFVO1lBQVE7WUFBVztZQUFVO1NBQVE7UUFDM0hDLG1CQUFtQixFQUFFO1FBQ3JCQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWQyxlQUFlO1lBQ2ZDLGFBQWE7UUFDZjtJQUNGO0lBQ0EsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUduRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNvRCxRQUFRQyxVQUFVLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNzRCxTQUFTQyxXQUFXLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN3RCxhQUFhQyxlQUFlLEdBQUd6RCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMwRCxjQUFjQyxnQkFBZ0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRELFNBQVNDLFdBQVcsR0FBRzdELCtDQUFRQSxDQUFxRDtJQUUzRkMsZ0RBQVNBO29DQUFDO1lBQ1I2RDtRQUNGO21DQUFHLEVBQUU7SUFFTCxNQUFNQSxZQUFZO1FBQ2hCWCxXQUFXO1FBQ1gsSUFBSTtZQUNGLHNCQUFzQjtZQUN0QixNQUFNWSxpQkFBaUIsTUFBTUMsTUFBTTtZQUNuQyxNQUFNQyxhQUFhLE1BQU1GLGVBQWVHLElBQUk7WUFFNUMsOEJBQThCO1lBQzlCLE1BQU1DLG1CQUFtQixNQUFNSCxNQUFNO1lBQ3JDLE1BQU1JLGVBQWUsTUFBTUQsaUJBQWlCRCxJQUFJO1lBRWhELElBQUlELFdBQVdJLE9BQU8sRUFBRTtnQkFDdEJwQyxVQUFVZ0MsV0FBV0ssSUFBSSxDQUFDdEMsTUFBTSxJQUFJLEVBQUU7WUFDeEM7WUFFQSxJQUFJb0MsYUFBYUMsT0FBTyxFQUFFO2dCQUN4QmxDLFlBQVlpQyxhQUFhRSxJQUFJO1lBQy9CO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3RDVixXQUFXO2dCQUNUWSxNQUFNO2dCQUNOQyxNQUFNO1lBQ1I7UUFDRixTQUFVO1lBQ1J2QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU13QixhQUFhO1FBQ2pCdEIsVUFBVTtRQUNWUSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU1lLFdBQVcsTUFBTVosTUFBTSxvQkFBb0I7Z0JBQy9DYSxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUMvQztZQUN2QjtZQUVBLE1BQU1nRCxTQUFTLE1BQU1OLFNBQVNWLElBQUk7WUFFbEMsSUFBSWdCLE9BQU9iLE9BQU8sRUFBRTtnQkFDbEJSLFdBQVc7b0JBQ1RZLE1BQU07b0JBQ05DLE1BQU07Z0JBQ1I7Z0JBRUEsc0NBQXNDO2dCQUN0Q1MsV0FBVztvQkFDVHJCO2dCQUNGLEdBQUc7WUFDTCxPQUFPO2dCQUNMRCxXQUFXO29CQUNUWSxNQUFNO29CQUNOQyxNQUFNUSxPQUFPWCxLQUFLLElBQUk7Z0JBQ3hCO1lBQ0Y7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeENWLFdBQVc7Z0JBQ1RZLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtRQUNGLFNBQVU7WUFDUnJCLFVBQVU7UUFDWjtJQUNGO0lBRUEsTUFBTStCLGVBQWU7UUFDbkIsSUFBSSxDQUFDNUIsWUFBWTZCLElBQUksSUFBSTtZQUN2QnhCLFdBQVc7Z0JBQ1RZLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQW5CLFdBQVc7UUFDWEksZ0JBQWdCO1FBQ2hCRSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU1lLFdBQVcsTUFBTVosTUFBTSx3QkFBd0I7Z0JBQ25EYSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJyQixTQUFTSjtvQkFDVG5CLE9BQU9ILFNBQVNHLEtBQUs7b0JBQ3JCaUQsZ0JBQWdCLFVBQVVDLEtBQUtDLEdBQUc7Z0JBQ3BDO1lBQ0Y7WUFFQSxNQUFNbEIsT0FBTyxNQUFNTSxTQUFTVixJQUFJO1lBR2hDLElBQUlJLEtBQUtELE9BQU8sSUFBSUMsS0FBS00sUUFBUSxFQUFFO2dCQUNqQ2pCLGdCQUFnQlcsS0FBS00sUUFBUTtnQkFDN0JmLFdBQVc7b0JBQ1RZLE1BQU07b0JBQ05DLE1BQU07Z0JBQ1I7WUFDRixPQUFPO2dCQUNMLE1BQU0sSUFBSWUsTUFBTW5CLEtBQUtDLEtBQUssSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMscUJBQXFCQTtZQUNuQ1YsV0FBVztnQkFDVFksTUFBTTtnQkFDTkMsTUFBTSxxQ0FBcUNILE1BQU1YLE9BQU87WUFDMUQ7UUFDRixTQUFVO1lBQ1JMLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTW1DLFNBQVM7UUFDYm5DLFdBQVc7UUFDWE0sV0FBVztRQUVYLElBQUk7WUFDRlcsUUFBUW1CLEdBQUcsQ0FBQztZQUNabkIsUUFBUW1CLEdBQUcsQ0FBQyxtQkFBbUJ6RCxTQUFTRyxLQUFLO1lBRTdDLE1BQU11QyxXQUFXLE1BQU1aLE1BQU0sd0JBQXdCO2dCQUNuRGEsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQnJCLFNBQVM7b0JBQ1R2QixPQUFPSCxTQUFTRyxLQUFLO29CQUNyQmlELGdCQUFnQixnQkFBZ0JDLEtBQUtDLEdBQUc7Z0JBQzFDO1lBQ0Y7WUFFQWhCLFFBQVFtQixHQUFHLENBQUMsc0JBQXNCZixTQUFTZ0IsTUFBTTtZQUVqRCxJQUFJLENBQUNoQixTQUFTaUIsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlKLE1BQU0sZUFBK0IsT0FBaEJiLFNBQVNnQixNQUFNO1lBQ2hEO1lBRUEsTUFBTVYsU0FBUyxNQUFNTixTQUFTVixJQUFJO1lBR2xDLElBQUlnQixPQUFPYixPQUFPLElBQUlhLE9BQU9OLFFBQVEsRUFBRTtnQkFDckNmLFdBQVc7b0JBQ1RZLE1BQU07b0JBQ05DLE1BQU0sa0NBQW9FLE9BQWxDUSxPQUFPTixRQUFRLENBQUNrQixTQUFTLENBQUMsR0FBRyxNQUFLO2dCQUM1RTtZQUNGLE9BQU87Z0JBQ0xqQyxXQUFXO29CQUNUWSxNQUFNO29CQUNOQyxNQUFNUSxPQUFPWCxLQUFLLElBQUk7Z0JBQ3hCO1lBQ0Y7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkRWLFdBQVc7Z0JBQ1RZLE1BQU07Z0JBQ05DLE1BQU0sbUNBQWlELE9BQWRILE1BQU1YLE9BQU87WUFDeEQ7UUFDRixTQUFVO1lBQ1JMLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXdDLGlCQUFpQixDQUFDSDtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBdUIsT0FBTztZQUNuQyxLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBb0IsT0FBTztZQUNoQyxLQUFLO2dCQUFhLE9BQU87WUFDekI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTUksZ0JBQWdCLENBQUNKO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUF1QixPQUFPO1lBQ25DLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFvQixPQUFPO1lBQ2hDLEtBQUs7Z0JBQWEsT0FBTztZQUN6QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJMUMsU0FBUztRQUNYLHFCQUNFLDhEQUFDaEQsc0VBQVVBO3NCQUNULDRFQUFDK0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDM0Usd0tBQVNBO3dCQUFDMkUsV0FBVTs7Ozs7O2tDQUNyQiw4REFBQ0M7d0JBQUtELFdBQVU7a0NBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSS9CO0lBRUEscUJBQ0UsOERBQUNoRyxzRUFBVUE7a0JBQ1QsNEVBQUMrRjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM5RSx3S0FBR0E7b0NBQUM4RSxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEOztzREFDQyw4REFBQ0c7NENBQUdGLFdBQVU7c0RBQW1DOzs7Ozs7c0RBQ2pELDhEQUFDRzs0Q0FBRUgsV0FBVTtzREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzNGLHlEQUFNQTtvQ0FDTCtGLFNBQVN4QztvQ0FDVHlDLFNBQVE7b0NBQ1JDLFVBQVV0RDs7c0RBRVYsOERBQUMzQix3S0FBU0E7NENBQUMyRSxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUd4Qyw4REFBQzNGLHlEQUFNQTtvQ0FDTCtGLFNBQVNaO29DQUNUYSxTQUFRO29DQUNSQyxVQUFVbEQsV0FBVyxDQUFDcEIsU0FBU0UsT0FBTzs7d0NBRXJDa0Isd0JBQ0MsOERBQUMvQix3S0FBU0E7NENBQUMyRSxXQUFVOzs7OztpRUFFckIsOERBQUN4RSx3S0FBR0E7NENBQUN3RSxXQUFVOzs7Ozs7d0NBQ2Y7Ozs7Ozs7OENBR0osOERBQUMzRix5REFBTUE7b0NBQ0wrRixTQUFTM0I7b0NBQ1Q2QixVQUFVcEQ7b0NBQ1Y4QyxXQUFVOzt3Q0FFVDlDLHVCQUNDLDhEQUFDN0Isd0tBQVNBOzRDQUFDMkUsV0FBVTs7Ozs7aUVBRXJCLDhEQUFDNUUsd0tBQUlBOzRDQUFDNEUsV0FBVTs7Ozs7O3dDQUNoQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFPUHRDLHlCQUNDLDhEQUFDMUMsd0RBQUtBO29CQUFDZ0YsV0FBV3RDLFFBQVFhLElBQUksS0FBSyxZQUFZLGlDQUFpQzs7d0JBQzdFYixRQUFRYSxJQUFJLEtBQUssMEJBQ2hCLDhEQUFDakQsd0tBQVdBOzRCQUFDMEUsV0FBVTs7Ozs7aURBRXZCLDhEQUFDekUsd0tBQVdBOzRCQUFDeUUsV0FBVTs7Ozs7O3NDQUV6Qiw4REFBQy9FLG1FQUFnQkE7NEJBQUMrRSxXQUFXdEMsUUFBUWEsSUFBSSxLQUFLLFlBQVksbUJBQW1CO3NDQUMxRWIsUUFBUWMsSUFBSTs7Ozs7Ozs7Ozs7OzhCQU1uQiw4REFBQ3ZFLHFEQUFJQTs7c0NBQ0gsOERBQUNFLDJEQUFVQTtzQ0FDVCw0RUFBQ0MsMERBQVNBO2dDQUFDNEYsV0FBVTs7a0RBQ25CLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNwRSx3S0FBUUE7Z0RBQUNvRSxXQUFVOzs7Ozs7NENBQWlCOzBEQUVyQyw4REFBQ0M7Z0RBQUtELFdBQVU7MERBQWtFOzs7Ozs7Ozs7Ozs7a0RBSXBGLDhEQUFDM0YseURBQU1BO3dDQUNMZ0csU0FBUTt3Q0FDUkUsTUFBSzt3Q0FDTEgsU0FBU3hDO3dDQUNUMEMsVUFBVXREOzswREFFViw4REFBQzNCLHdLQUFTQTtnREFBQzJFLFdBQVcsZ0JBQThDLE9BQTlCaEQsVUFBVSxpQkFBaUI7Ozs7Ozs0Q0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUsvRSw4REFBQzlDLDREQUFXQTtzQ0FDViw0RUFBQzZGO2dDQUFJQyxXQUFVOzBDQUNabEUsT0FBTzBFLEdBQUcsQ0FBQyxDQUFDckUsc0JBQ1gsOERBQUNsQyxxREFBSUE7d0NBQWlCK0YsV0FBVTtrREFDOUIsNEVBQUM5Riw0REFBV0E7NENBQUM4RixXQUFVOzs4REFDckIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ1M7NERBQUdULFdBQVU7c0VBQWU3RCxNQUFNdUUsSUFBSTs7Ozs7O3NFQUN2Qyw4REFBQzNGLHdEQUFLQTs0REFBQ2lGLFdBQVdILGVBQWUxRCxNQUFNdUQsTUFBTTtzRUFDMUNJLGNBQWMzRCxNQUFNdUQsTUFBTTs7Ozs7Ozs7Ozs7OzhEQUcvQiw4REFBQ1M7b0RBQUVILFdBQVU7OERBQThCN0QsTUFBTXdFLFdBQVc7Ozs7Ozs4REFDNUQsOERBQUNaO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0c7O2dFQUFFO2dFQUFVaEUsTUFBTUEsS0FBSzs7Ozs7OztzRUFDeEIsOERBQUNnRTs7Z0VBQUU7Z0VBQVUsSUFBSWQsS0FBS2xELE1BQU15RSxXQUFXLEVBQUVDLGNBQWMsQ0FBQzs7Ozs7Ozt3REFDdkQxRSxNQUFNa0MsS0FBSyxrQkFDViw4REFBQzhCOzREQUFFSCxXQUFVOztnRUFBb0I7Z0VBQU03RCxNQUFNa0MsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FiL0NsQyxNQUFNMkUsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQXdCNUIsOERBQUM3RyxxREFBSUE7O3NDQUNILDhEQUFDRSwyREFBVUE7c0NBQ1QsNEVBQUNDLDBEQUFTQTtnQ0FBQzRGLFdBQVU7O2tEQUNuQiw4REFBQzdFLHdLQUFRQTt3Q0FBQzZFLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7OztzQ0FJekMsOERBQUM5Riw0REFBV0E7NEJBQUM4RixXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDeEYsdURBQUtBO29EQUFDeUYsV0FBVTs4REFBd0I7Ozs7Ozs4REFDekMsOERBQUNHO29EQUFFSCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUV2Qyw4REFBQ3hGLHlEQUFNQTs0Q0FDTHVHLFNBQVMvRSxTQUFTRSxPQUFPOzRDQUN6QjhFLGlCQUFpQixDQUFDRCxVQUFZOUUsWUFBWTtvREFBQyxHQUFHRCxRQUFRO29EQUFFRSxTQUFTNkU7Z0RBQU87Ozs7Ozs7Ozs7Ozs4Q0FJNUUsOERBQUNoQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzs4REFDQyw4REFBQ3hGLHVEQUFLQTs4REFBQzs7Ozs7OzhEQUNQLDhEQUFDRSx5REFBTUE7b0RBQ0x3RyxPQUFPakYsU0FBU0csS0FBSztvREFDckIrRSxlQUFlLENBQUNELFFBQVVoRixZQUFZOzREQUFDLEdBQUdELFFBQVE7NERBQUVHLE9BQU84RTt3REFBSzs7c0VBRWhFLDhEQUFDckcsZ0VBQWFBO3NFQUNaLDRFQUFDQyw4REFBV0E7Ozs7Ozs7Ozs7c0VBRWQsOERBQUNILGdFQUFhQTtzRUFDWG9CLE9BQU9xRixNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUUxQixNQUFNLEtBQUssYUFBYWMsR0FBRyxDQUFDLENBQUNyRSxzQkFDakQsOERBQUN4Qiw2REFBVUE7b0VBQWlCc0csT0FBTzlFLE1BQU0yRSxHQUFHOzhFQUN6QzNFLE1BQU11RSxJQUFJO21FQURJdkUsTUFBTTJFLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBUWxDLDhEQUFDZjs7OERBQ0MsOERBQUN4Rix1REFBS0E7OERBQUM7Ozs7Ozs4REFDUCw4REFBQ0QsdURBQUtBO29EQUNKaUUsTUFBSztvREFDTDBDLE9BQU9qRixTQUFTSSxhQUFhO29EQUM3QmlGLFVBQVUsQ0FBQ0MsSUFBTXJGLFlBQVk7NERBQUMsR0FBR0QsUUFBUTs0REFBRUksZUFBZW1GLFNBQVNELEVBQUVFLE1BQU0sQ0FBQ1AsS0FBSzt3REFBQztvREFDbEZRLEtBQUk7b0RBQ0pDLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLViw4REFBQzNCOztzREFDQyw4REFBQ3hGLHVEQUFLQTtzREFBQzs7Ozs7O3NEQUNQLDhEQUFDRCx1REFBS0E7NENBQ0ppRSxNQUFLOzRDQUNMMEMsT0FBT2pGLFNBQVNTLDhCQUE4Qjs0Q0FDOUM0RSxVQUFVLENBQUNDLElBQU1yRixZQUFZO29EQUFDLEdBQUdELFFBQVE7b0RBQUVTLGdDQUFnQzhFLFNBQVNELEVBQUVFLE1BQU0sQ0FBQ1AsS0FBSztnREFBQzs0Q0FDbkdRLEtBQUk7NENBQ0pDLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPWiw4REFBQ3pILHFEQUFJQTs7c0NBQ0gsOERBQUNFLDJEQUFVQTtzQ0FDVCw0RUFBQ0MsMERBQVNBO2dDQUFDNEYsV0FBVTs7a0RBQ25CLDhEQUFDdkUsd0tBQUtBO3dDQUFDdUUsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7O3NDQUl0Qyw4REFBQzlGLDREQUFXQTs0QkFBQzhGLFdBQVU7OzhDQUNyQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUN4Rix1REFBS0E7b0RBQUN5RixXQUFVOzhEQUF3Qjs7Ozs7OzhEQUN6Qyw4REFBQ0c7b0RBQUVILFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7c0RBRXZDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNDO29EQUFLRCxXQUFVOzhEQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUl6RCw4REFBQ2hGLHdEQUFLQTs7c0RBQ0osOERBQUNNLHdLQUFXQTs0Q0FBQzBFLFdBQVU7Ozs7OztzREFDdkIsOERBQUMvRSxtRUFBZ0JBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBTXBCLDhEQUFDOEU7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUN4Rix1REFBS0E7OERBQUM7Ozs7Ozs4REFDUCw4REFBQ0QsdURBQUtBO29EQUNKaUUsTUFBSztvREFDTDBDLE9BQU07b0RBQ05YLFFBQVE7b0RBQ1JOLFdBQVU7Ozs7Ozs4REFFWiw4REFBQ0c7b0RBQUVILFdBQVU7OERBQTZCOzs7Ozs7Ozs7Ozs7c0RBRzVDLDhEQUFDRDs7OERBQ0MsOERBQUN4Rix1REFBS0E7OERBQUM7Ozs7Ozs4REFDUCw4REFBQ0QsdURBQUtBO29EQUNKaUUsTUFBSztvREFDTDBDLE9BQU07b0RBQ05YLFFBQVE7b0RBQ1JOLFdBQVU7Ozs7Ozs4REFFWiw4REFBQ0c7b0RBQUVILFdBQVU7OERBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2xELDhEQUFDL0YscURBQUlBOztzQ0FDSCw4REFBQ0UsMkRBQVVBO3NDQUNULDRFQUFDQywwREFBU0E7Z0NBQUM0RixXQUFVOztrREFDbkIsOERBQUN0RSx3S0FBYUE7d0NBQUNzRSxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7c0NBSTlDLDhEQUFDOUYsNERBQVdBOzRCQUFDOEYsV0FBVTs7OENBQ3JCLDhEQUFDRDs7c0RBQ0MsOERBQUN4Rix1REFBS0E7c0RBQUM7Ozs7OztzREFDUCw4REFBQ08sNkRBQVFBOzRDQUNQbUcsT0FBT2pGLFNBQVNVLGdCQUFnQixDQUFDaUYsSUFBSSxDQUFDOzRDQUN0Q04sVUFBVSxDQUFDQyxJQUFNckYsWUFBWTtvREFDM0IsR0FBR0QsUUFBUTtvREFDWFUsa0JBQWtCNEUsRUFBRUUsTUFBTSxDQUFDUCxLQUFLLENBQUNXLEtBQUssQ0FBQyxLQUFLcEIsR0FBRyxDQUFDcUIsQ0FBQUEsSUFBS0EsRUFBRTFDLElBQUksSUFBSWdDLE1BQU0sQ0FBQ1UsQ0FBQUEsSUFBS0E7Z0RBQzdFOzRDQUNBQyxhQUFZOzs7Ozs7Ozs7Ozs7OENBSWhCLDhEQUFDL0I7O3NEQUNDLDhEQUFDeEYsdURBQUtBO3NEQUFDOzs7Ozs7c0RBQ1AsOERBQUNPLDZEQUFRQTs0Q0FDUG1HLE9BQU9qRixTQUFTVyxpQkFBaUIsQ0FBQ2dGLElBQUksQ0FBQzs0Q0FDdkNOLFVBQVUsQ0FBQ0MsSUFBTXJGLFlBQVk7b0RBQzNCLEdBQUdELFFBQVE7b0RBQ1hXLG1CQUFtQjJFLEVBQUVFLE1BQU0sQ0FBQ1AsS0FBSyxDQUFDVyxLQUFLLENBQUMsS0FBS3BCLEdBQUcsQ0FBQ3FCLENBQUFBLElBQUtBLEVBQUUxQyxJQUFJLElBQUlnQyxNQUFNLENBQUNVLENBQUFBLElBQUtBO2dEQUM5RTs0Q0FDQUMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9wQiw4REFBQzdILHFEQUFJQTs7c0NBQ0gsOERBQUNFLDJEQUFVQTtzQ0FDVCw0RUFBQ0MsMERBQVNBO2dDQUFDNEYsV0FBVTs7a0RBQ25CLDhEQUFDckUsd0tBQU1BO3dDQUFDcUUsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7O3NDQUl2Qyw4REFBQzlGLDREQUFXQTs0QkFBQzhGLFdBQVU7OzhDQUNyQiw4REFBQ0Q7O3NEQUNDLDhEQUFDeEYsdURBQUtBO3NEQUFDOzs7Ozs7c0RBQ1AsOERBQUNPLDZEQUFRQTs0Q0FDUG1HLE9BQU9qRixTQUFTWSxjQUFjLENBQUNDLFFBQVE7NENBQ3ZDd0UsVUFBVSxDQUFDQyxJQUFNckYsWUFBWTtvREFDM0IsR0FBR0QsUUFBUTtvREFDWFksZ0JBQWdCO3dEQUFDLEdBQUdaLFNBQVNZLGNBQWM7d0RBQUVDLFVBQVV5RSxFQUFFRSxNQUFNLENBQUNQLEtBQUs7b0RBQUE7Z0RBQ3ZFOzs7Ozs7Ozs7Ozs7OENBSUosOERBQUNsQjs7c0RBQ0MsOERBQUN4Rix1REFBS0E7c0RBQUM7Ozs7OztzREFDUCw4REFBQ08sNkRBQVFBOzRDQUNQbUcsT0FBT2pGLFNBQVNZLGNBQWMsQ0FBQ0UsYUFBYTs0Q0FDNUN1RSxVQUFVLENBQUNDLElBQU1yRixZQUFZO29EQUMzQixHQUFHRCxRQUFRO29EQUNYWSxnQkFBZ0I7d0RBQUMsR0FBR1osU0FBU1ksY0FBYzt3REFBRUUsZUFBZXdFLEVBQUVFLE1BQU0sQ0FBQ1AsS0FBSztvREFBQTtnREFDNUU7Ozs7Ozs7Ozs7Ozs4Q0FJSiw4REFBQ2xCOztzREFDQyw4REFBQ3hGLHVEQUFLQTtzREFBQzs7Ozs7O3NEQUNQLDhEQUFDTyw2REFBUUE7NENBQ1BtRyxPQUFPakYsU0FBU1ksY0FBYyxDQUFDRyxXQUFXOzRDQUMxQ3NFLFVBQVUsQ0FBQ0MsSUFBTXJGLFlBQVk7b0RBQzNCLEdBQUdELFFBQVE7b0RBQ1hZLGdCQUFnQjt3REFBQyxHQUFHWixTQUFTWSxjQUFjO3dEQUFFRyxhQUFhdUUsRUFBRUUsTUFBTSxDQUFDUCxLQUFLO29EQUFBO2dEQUMxRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9SLDhEQUFDaEgscURBQUlBOztzQ0FDSCw4REFBQ0UsMkRBQVVBO3NDQUNULDRFQUFDQywwREFBU0E7Z0NBQUM0RixXQUFVOztrREFDbkIsOERBQUNwRSx3S0FBUUE7d0NBQUNvRSxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7c0NBSXpDLDhEQUFDOUYsNERBQVdBOzRCQUFDOEYsV0FBVTs7OENBQ3JCLDhEQUFDRDs7c0RBQ0MsOERBQUN4Rix1REFBS0E7c0RBQUM7Ozs7OztzREFDUCw4REFBQ08sNkRBQVFBOzRDQUNQbUcsT0FBTzNEOzRDQUNQK0QsVUFBVSxDQUFDQyxJQUFNL0QsZUFBZStELEVBQUVFLE1BQU0sQ0FBQ1AsS0FBSzs0Q0FDOUNhLGFBQVk7NENBQ1pDLE1BQU07Ozs7Ozs7Ozs7Ozs4Q0FJViw4REFBQzFILHlEQUFNQTtvQ0FDTCtGLFNBQVNsQjtvQ0FDVG9CLFVBQVVsRCxXQUFXLENBQUNFLFlBQVk2QixJQUFJO29DQUN0Q2EsV0FBVTs4Q0FFVDVDLHdCQUNDOzswREFDRSw4REFBQy9CLHdLQUFTQTtnREFBQzJFLFdBQVU7Ozs7Ozs0Q0FBOEI7O3FFQUlyRDs7MERBQ0UsOERBQUNwRSx3S0FBUUE7Z0RBQUNvRSxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7OztnQ0FNMUN4Qyw4QkFDQyw4REFBQ3VDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3pGLHVEQUFLQTtzREFBQzs7Ozs7O3NEQUNQLDhEQUFDd0Y7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOzBEQUNaeEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVckI7R0FobEJ3QjNCO0tBQUFBIiwic291cmNlcyI6WyJEOlxcbW9oYW1pbmV3XFxzcmNcXGFwcFxcYWRtaW5cXGFpLXNldHRpbmdzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTWFpbkxheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvbWFpbi1sYXlvdXQnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7IFN3aXRjaCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zd2l0Y2gnXG5pbXBvcnQgeyBTZWxlY3QsIFNlbGVjdENvbnRlbnQsIFNlbGVjdEl0ZW0sIFNlbGVjdFRyaWdnZXIsIFNlbGVjdFZhbHVlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlbGVjdCdcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RleHRhcmVhJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hbGVydCdcbmltcG9ydCB7XG4gIEJvdCxcbiAgU2V0dGluZ3MsXG4gIFNhdmUsXG4gIFJlZnJlc2hDdyxcbiAgQ2hlY2tDaXJjbGUsXG4gIEFsZXJ0Q2lyY2xlLFxuICBaYXAsXG4gIENsb2NrLFxuICBNZXNzYWdlU3F1YXJlLFxuICBTaGllbGQsXG4gIEFjdGl2aXR5XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIEFJTW9kZWwge1xuICBrZXk6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgZW5kcG9pbnQ6IHN0cmluZ1xuICBtb2RlbDogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgc3RhdHVzOiAnYXZhaWxhYmxlJyB8ICdub3RfZm91bmQnIHwgJ3NlcnZpY2VfdW5hdmFpbGFibGUnIHwgJ29mZmxpbmUnIHwgJ2FwaV9rZXlfcmVxdWlyZWQnIHwgJ2FwaV9lcnJvcidcbiAgbGFzdENoZWNrZWQ6IHN0cmluZ1xuICBlcnJvcj86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgQUlTZXR0aW5ncyB7XG4gIGVuYWJsZWQ6IGJvb2xlYW5cbiAgbW9kZWw6IHN0cmluZ1xuICBkZWxheV9zZWNvbmRzOiBudW1iZXJcbiAgd29ya2luZ19ob3Vyc19vbmx5OiBib29sZWFuXG4gIHdvcmtpbmdfaG91cnNfc3RhcnQ6IHN0cmluZ1xuICB3b3JraW5nX2hvdXJzX2VuZDogc3RyaW5nXG4gIHdvcmtpbmdfZGF5czogc3RyaW5nW11cbiAgbWF4X3Jlc3BvbnNlc19wZXJfY29udmVyc2F0aW9uOiBudW1iZXJcbiAga2V5d29yZHNfdHJpZ2dlcjogc3RyaW5nW11cbiAgZXhjbHVkZWRfa2V5d29yZHM6IHN0cmluZ1tdXG4gIGF1dG9fcmVzcG9uc2VzOiB7XG4gICAgZ3JlZXRpbmc6IHN0cmluZ1xuICAgIHdvcmtpbmdfaG91cnM6IHN0cmluZ1xuICAgIG1heF9yZWFjaGVkOiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBSVNldHRpbmdzUGFnZSgpIHtcbiAgY29uc3QgW21vZGVscywgc2V0TW9kZWxzXSA9IHVzZVN0YXRlPEFJTW9kZWxbXT4oW10pXG4gIGNvbnN0IFtzZXR0aW5ncywgc2V0U2V0dGluZ3NdID0gdXNlU3RhdGU8QUlTZXR0aW5ncz4oe1xuICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgbW9kZWw6ICdvcGVuYWktZ3B0NCcsXG4gICAgZGVsYXlfc2Vjb25kczogMyxcbiAgICB3b3JraW5nX2hvdXJzX29ubHk6IGZhbHNlLCAvLyDYr9in2KbZhdin2YsgZmFsc2UgLSDZiti52YXZhCAyNCDYs9in2LnYqVxuICAgIHdvcmtpbmdfaG91cnNfc3RhcnQ6ICcwMDowMCcsXG4gICAgd29ya2luZ19ob3Vyc19lbmQ6ICcyMzo1OScsXG4gICAgd29ya2luZ19kYXlzOiBbJ9in2YTYo9it2K8nLCAn2KfZhNin2KvZhtmK2YYnLCAn2KfZhNir2YTYp9ir2KfYoScsICfYp9mE2KPYsdio2LnYp9ihJywgJ9in2YTYrtmF2YrYsycsICfYp9mE2KzZhdi52KknLCAn2KfZhNiz2KjYqiddLFxuICAgIG1heF9yZXNwb25zZXNfcGVyX2NvbnZlcnNhdGlvbjogMjAsXG4gICAga2V5d29yZHNfdHJpZ2dlcjogWyfZhdiz2KfYudiv2KknLCAn2KfYs9iq2YHYs9in2LEnLCAn2LPYpNin2YQnLCAn2YXYudmE2YjZhdin2KonLCAn2K7Yr9mF2KknLCAn2YXYsdit2KjYpycsICfYp9mE2LPZhNin2YUnLCAn2KPZh9mE2KcnLCAn2KfYs9iq2LTYp9ix2KknLCAn2YLYp9mG2YjZhtmKJywgJ9mF2K3Yp9mF2YonXSxcbiAgICBleGNsdWRlZF9rZXl3b3JkczogW10sXG4gICAgYXV0b19yZXNwb25zZXM6IHtcbiAgICAgIGdyZWV0aW5nOiAn2YXYsdit2KjYp9mLISDYo9mG2Kcg2KfZhNmF2LPYp9i52K8g2KfZhNiw2YPZiiDZhNmE2YXZg9iq2Kgg2YXYr9i52YjZhSDYqNmAIEdQVC00LiDZg9mK2YEg2YrZhdmD2YbZhtmKINmF2LPYp9i52K/YqtmD2J8nLFxuICAgICAgd29ya2luZ19ob3VyczogJ9ij2YbYpyDZhdiq2KfYrSDZhNmE2YXYs9in2LnYr9ipINi52YTZiSDZhdiv2KfYsSAyNCDYs9in2LnYqSDZhdiv2LnZiNmFINio2YAgR1BULTQuINmD2YrZgSDZitmF2YPZhtmG2Yog2YXYs9in2LnYr9iq2YPYnycsXG4gICAgICBtYXhfcmVhY2hlZDogJ9iq2YUg2KfZhNmI2LXZiNmEINmE2YTYrdivINin2YTYo9mC2LXZiSDZhdmGINin2YTYsdiv2YjYryDYp9mE2KrZhNmC2KfYptmK2KkuINiz2YrZgtmI2YUg2KPYrdivINin2YTZhdit2KfZhdmK2YYg2KjYp9mE2LHYryDYudmE2YrZgyDZgtix2YrYqNin2YsuJ1xuICAgIH1cbiAgfSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3NhdmluZywgc2V0U2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbdGVzdGluZywgc2V0VGVzdGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Rlc3RNZXNzYWdlLCBzZXRUZXN0TWVzc2FnZV0gPSB1c2VTdGF0ZSgn2YXYsdit2KjYp9iMINij2K3Yqtin2Kwg2KfYs9iq2LTYp9ix2Kkg2YLYp9mG2YjZhtmK2KknKVxuICBjb25zdCBbdGVzdFJlc3BvbnNlLCBzZXRUZXN0UmVzcG9uc2VdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFttZXNzYWdlLCBzZXRNZXNzYWdlXSA9IHVzZVN0YXRlPHsgdHlwZTogJ3N1Y2Nlc3MnIHwgJ2Vycm9yJywgdGV4dDogc3RyaW5nIH0gfCBudWxsPihudWxsKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hEYXRhKClcbiAgfSwgW10pXG5cbiAgY29uc3QgZmV0Y2hEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgLy8g2KzZhNioINin2YTZhtmF2KfYsNisINin2YTZhdiq2KfYrdipXG4gICAgICBjb25zdCBtb2RlbHNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FpL2xvY2FsLW1vZGVscycpXG4gICAgICBjb25zdCBtb2RlbHNEYXRhID0gYXdhaXQgbW9kZWxzUmVzcG9uc2UuanNvbigpXG5cbiAgICAgIC8vINis2YTYqCDYp9mE2KXYudiv2KfYr9in2Kog2YXZhiBBUEkg2KfZhNis2K/ZitivXG4gICAgICBjb25zdCBzZXR0aW5nc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYWkvc2V0dGluZ3MnKVxuICAgICAgY29uc3Qgc2V0dGluZ3NEYXRhID0gYXdhaXQgc2V0dGluZ3NSZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKG1vZGVsc0RhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRNb2RlbHMobW9kZWxzRGF0YS5kYXRhLm1vZGVscyB8fCBbXSlcbiAgICAgIH1cblxuICAgICAgaWYgKHNldHRpbmdzRGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldFNldHRpbmdzKHNldHRpbmdzRGF0YS5kYXRhKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBkYXRhOicsIGVycm9yKVxuICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIHRleHQ6ICfZgdi02YQg2YHZiiDYrNmE2Kgg2KfZhNio2YrYp9mG2KfYqidcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2F2ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTYXZpbmcodHJ1ZSlcbiAgICBzZXRNZXNzYWdlKG51bGwpXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9haS9zZXR0aW5ncycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShzZXR0aW5ncylcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgIHRleHQ6ICfYqtmFINit2YHYuCDYpdi52K/Yp9iv2KfYqiDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKINio2YbYrNin2K0nXG4gICAgICAgIH0pXG5cbiAgICAgICAgLy8g2KXYudin2K/YqSDYrNmE2Kgg2KfZhNil2LnYr9in2K/Yp9iqINmE2YTYqtij2YPYryDZhdmGINin2YTYrdmB2LhcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgZmV0Y2hEYXRhKClcbiAgICAgICAgfSwgMTAwMClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldE1lc3NhZ2Uoe1xuICAgICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgICAgdGV4dDogcmVzdWx0LmVycm9yIHx8ICfZgdi02YQg2YHZiiDYrdmB2Lgg2KfZhNil2LnYr9in2K/Yp9iqJ1xuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgc2V0dGluZ3M6JywgZXJyb3IpXG4gICAgICBzZXRNZXNzYWdlKHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgdGV4dDogJ9it2K/YqyDYrti32KMg2YHZiiDYrdmB2Lgg2KfZhNil2LnYr9in2K/Yp9iqJ1xuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U2F2aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRlc3RBSSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXRlc3RNZXNzYWdlLnRyaW0oKSkge1xuICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIHRleHQ6ICfZitix2KzZiSDYpdiv2K7Yp9mEINix2LPYp9mE2Kkg2YTZhNin2K7Yqtio2KfYsSdcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBzZXRUZXN0aW5nKHRydWUpXG4gICAgc2V0VGVzdFJlc3BvbnNlKCcnKVxuICAgIHNldE1lc3NhZ2UobnVsbClcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FpL2xvY2FsLW1vZGVscycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbWVzc2FnZTogdGVzdE1lc3NhZ2UsXG4gICAgICAgICAgbW9kZWw6IHNldHRpbmdzLm1vZGVsLFxuICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiAndGVzdC0nICsgRGF0ZS5ub3coKVxuICAgICAgICB9KVxuICAgICAgfSlcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MgJiYgZGF0YS5yZXNwb25zZSkge1xuICAgICAgICBzZXRUZXN0UmVzcG9uc2UoZGF0YS5yZXNwb25zZSlcbiAgICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgIHRleHQ6ICfYqtmFINin2K7Yqtio2KfYsSDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKINio2YbYrNin2K0hJ1xuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEuZXJyb3IgfHwgJ9mE2YUg2YrYqtmFINin2YTYrdi12YjZhCDYudmE2Ykg2LHYryDZhdmGINin2YTZhtmF2YjYsNisJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdGVzdGluZyBBSTonLCBlcnJvcilcbiAgICAgIHNldE1lc3NhZ2Uoe1xuICAgICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgICB0ZXh0OiAn2YHYtNmEINmB2Yog2KfYrtiq2KjYp9ixINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yo6ICcgKyBlcnJvci5tZXNzYWdlXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRUZXN0aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHRlc3RBSSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRUZXN0aW5nKHRydWUpXG4gICAgc2V0TWVzc2FnZShudWxsKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn6eqINio2K/YoSDYp9iu2KrYqNin2LEg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZii4uLicpXG4gICAgICBjb25zb2xlLmxvZygn2KfZhNmG2YXZiNiw2Kwg2KfZhNmF2K3Yr9ivOicsIHNldHRpbmdzLm1vZGVsKVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FpL2xvY2FsLW1vZGVscycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbWVzc2FnZTogJ9mF2LHYrdio2KfYjCDZh9iw2Kcg2KfYrtiq2KjYp9ixINmE2YTZhti42KfZhScsXG4gICAgICAgICAgbW9kZWw6IHNldHRpbmdzLm1vZGVsLFxuICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiAncXVpY2stdGVzdC0nICsgRGF0ZS5ub3coKVxuICAgICAgICB9KVxuICAgICAgfSlcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Eg2KfYs9iq2KzYp9io2Kkg2KfZhNiu2KfYr9mFOicsIHJlc3BvbnNlLnN0YXR1cylcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2VzcyAmJiByZXN1bHQucmVzcG9uc2UpIHtcbiAgICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgIHRleHQ6IGDYqtmFINin2K7Yqtio2KfYsSDYp9mE2YbZhdmI2LDYrCDYqNmG2KzYp9itISDYp9mE2LHYrzogJHtyZXN1bHQucmVzcG9uc2Uuc3Vic3RyaW5nKDAsIDEwMCl9Li4uYFxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0TWVzc2FnZSh7XG4gICAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgICB0ZXh0OiByZXN1bHQuZXJyb3IgfHwgJ9mE2YUg2YrYqtmFINin2YTYrdi12YjZhCDYudmE2Ykg2LHYryDZhdmGINin2YTZhtmF2YjYsNisJ1xuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg2K7Yt9ijINmB2Yog2KfYrtiq2KjYp9ixINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yo6JywgZXJyb3IpXG4gICAgICBzZXRNZXNzYWdlKHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgdGV4dDogYNmB2LTZhCDZgdmKINin2YTYp9iq2LXYp9mEINio2KfZhNmG2YXZiNiw2Kwg2KfZhNmF2K3ZhNmKOiAke2Vycm9yLm1lc3NhZ2V9YFxuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0VGVzdGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhdmFpbGFibGUnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgIGNhc2UgJ25vdF9mb3VuZCc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnXG4gICAgICBjYXNlICdzZXJ2aWNlX3VuYXZhaWxhYmxlJzogcmV0dXJuICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCdcbiAgICAgIGNhc2UgJ29mZmxpbmUnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgY2FzZSAnYXBpX2tleV9yZXF1aXJlZCc6IHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCdcbiAgICAgIGNhc2UgJ2FwaV9lcnJvcic6IHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzVGV4dCA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhdmFpbGFibGUnOiByZXR1cm4gJ9mF2KrYp9itJ1xuICAgICAgY2FzZSAnbm90X2ZvdW5kJzogcmV0dXJuICfYutmK2LEg2YXZiNis2YjYrydcbiAgICAgIGNhc2UgJ3NlcnZpY2VfdW5hdmFpbGFibGUnOiByZXR1cm4gJ9in2YTYrtiv2YXYqSDYutmK2LEg2YXYqtin2K3YqSdcbiAgICAgIGNhc2UgJ29mZmxpbmUnOiByZXR1cm4gJ9i62YrYsSDZhdiq2LXZhCdcbiAgICAgIGNhc2UgJ2FwaV9rZXlfcmVxdWlyZWQnOiByZXR1cm4gJ9mK2K3Yqtin2KwgQVBJIEtleSdcbiAgICAgIGNhc2UgJ2FwaV9lcnJvcic6IHJldHVybiAn2K7Yt9ijINmB2YogQVBJJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICfYutmK2LEg2YXYudix2YjZgSdcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8TWFpbkxheW91dD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XG4gICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPtis2KfYsdmKINin2YTYqtit2YXZitmELi4uPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTWFpbkxheW91dD5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxNYWluTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qINin2YTYudmG2YjYp9mGICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgPEJvdCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj7Ypdi52K/Yp9iv2KfYqiDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPtil2K/Yp9ix2Kkg2KfZhNmG2YXYp9iw2Kwg2KfZhNmF2K3ZhNmK2Kkg2YjYp9mE2LHYryDYp9mE2KrZhNmC2KfYptmKINmE2YTYudmF2YTYp9ihPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hEYXRhfVxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMlwiIC8+XG4gICAgICAgICAgICAgINiq2K3Yr9mK2KtcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0QUl9XG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Rlc3RpbmcgfHwgIXNldHRpbmdzLmVuYWJsZWR9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt0ZXN0aW5nID8gKFxuICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICDYp9iu2KrYqNin2LEg2KfZhNmG2YXZiNiw2KxcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17c2F2aW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7c2F2aW5nID8gKFxuICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAg2K3Zgdi4INin2YTYpdi52K/Yp9iv2KfYqlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYsdiz2KfYptmEINin2YTYqtmG2KjZitmHICovfVxuICAgICAgICB7bWVzc2FnZSAmJiAoXG4gICAgICAgICAgPEFsZXJ0IGNsYXNzTmFtZT17bWVzc2FnZS50eXBlID09PSAnc3VjY2VzcycgPyAnYm9yZGVyLWdyZWVuLTIwMCBiZy1ncmVlbi01MCcgOiAnYm9yZGVyLXJlZC0yMDAgYmctcmVkLTUwJ30+XG4gICAgICAgICAgICB7bWVzc2FnZS50eXBlID09PSAnc3VjY2VzcycgPyAoXG4gICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbiBjbGFzc05hbWU9e21lc3NhZ2UudHlwZSA9PT0gJ3N1Y2Nlc3MnID8gJ3RleHQtZ3JlZW4tODAwJyA6ICd0ZXh0LXJlZC04MDAnfT5cbiAgICAgICAgICAgICAge21lc3NhZ2UudGV4dH1cbiAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiDYrdin2YTYqSDYp9mE2YbZhdin2LDYrCAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cImgtNSB3LTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgICAg2K3Yp9mE2Kkg2KfZhNmG2YXYp9iw2Kwg2KfZhNmF2K3ZhNmK2Kkg2YjYp9mE2K7Yp9ix2KzZitipXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMiBweC0yIHB5LTEgYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIHRleHQteHMgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICDZhdit2LPZhiDZhNmE2LPYsdi52KlcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hEYXRhfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1sLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICAgINiq2K3Yr9mK2KtcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgIHttb2RlbHMubWFwKChtb2RlbCkgPT4gKFxuICAgICAgICAgICAgICAgIDxDYXJkIGtleT17bW9kZWwua2V5fSBjbGFzc05hbWU9XCJib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPnttb2RlbC5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT17Z2V0U3RhdHVzQ29sb3IobW9kZWwuc3RhdHVzKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzVGV4dChtb2RlbC5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMlwiPnttb2RlbC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHA+2KfZhNmG2YXZiNiw2Kw6IHttb2RlbC5tb2RlbH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHA+2KLYrtixINmB2K3YtToge25ldyBEYXRlKG1vZGVsLmxhc3RDaGVja2VkKS50b0xvY2FsZVN0cmluZygnYXItU0EnKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAge21vZGVsLmVycm9yICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBtdC0xXCI+2K7Yt9ijOiB7bW9kZWwuZXJyb3J9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDYp9mE2KXYudiv2KfYr9in2Kog2KfZhNi52KfZhdipICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cImgtNSB3LTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgINin2YTYpdi52K/Yp9iv2KfYqiDYp9mE2LnYp9mF2KlcbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj7YqtmB2LnZitmEINin2YTYsdivINin2YTYqtmE2YLYp9im2Yo8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtiq2LTYutmK2YQg2KPZiCDYpdmK2YLYp9mBINin2YTZhdiz2KfYudivINin2YTYsNmD2Yo8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17c2V0dGluZ3MuZW5hYmxlZH1cbiAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiBzZXRTZXR0aW5ncyh7Li4uc2V0dGluZ3MsIGVuYWJsZWQ6IGNoZWNrZWR9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8TGFiZWw+2KfZhNmG2YXZiNiw2Kwg2KfZhNmF2LPYqtiu2K/ZhTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NldHRpbmdzLm1vZGVsfVxuICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRTZXR0aW5ncyh7Li4uc2V0dGluZ3MsIG1vZGVsOiB2YWx1ZX0pfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICB7bW9kZWxzLmZpbHRlcihtID0+IG0uc3RhdHVzID09PSAnYXZhaWxhYmxlJykubWFwKChtb2RlbCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17bW9kZWwua2V5fSB2YWx1ZT17bW9kZWwua2V5fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHttb2RlbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsPtiq2KPYrtmK2LEg2KfZhNix2K8gKNio2KfZhNir2YjYp9mG2YopPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NldHRpbmdzLmRlbGF5X3NlY29uZHN9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNldHRpbmdzKHsuLi5zZXR0aW5ncywgZGVsYXlfc2Vjb25kczogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpfSl9XG4gICAgICAgICAgICAgICAgICBtaW49XCI1XCJcbiAgICAgICAgICAgICAgICAgIG1heD1cIjMwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsPtin2YTYrdivINin2YTYo9mC2LXZiSDZhNmE2LHYr9mI2K8g2YTZg9mEINmF2K3Yp9iv2KvYqTwvTGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZXR0aW5ncy5tYXhfcmVzcG9uc2VzX3Blcl9jb252ZXJzYXRpb259XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZXR0aW5ncyh7Li4uc2V0dGluZ3MsIG1heF9yZXNwb25zZXNfcGVyX2NvbnZlcnNhdGlvbjogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpfSl9XG4gICAgICAgICAgICAgICAgbWluPVwiMVwiXG4gICAgICAgICAgICAgICAgbWF4PVwiMjBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDYs9in2LnYp9iqINin2YTYudmF2YQgLSDZhNmE2LnYsdi2INmB2YLYtyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTUgdy01IG1sLTJcIiAvPlxuICAgICAgICAgICAgICDYs9in2LnYp9iqINin2YTYudmF2YRcbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj7Yrdin2YTYqSDYp9mE2KrYtNi62YrZhDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2KfZhNmG2LjYp9mFINmK2LnZhdmEIDI0INiz2KfYudipINi32KfZhNmF2Kcg2YXZgdiq2KfYrSDYp9mE2KrYtNi62YrZhCDZhdmB2LnZhDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmctZ3JlZW4tNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTYwMFwiPtmF2KrYp9itIDI0Lzc8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxBbGVydD5cbiAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICDYqtmFINiq2LnYt9mK2YQg2YLZitmI2K8g2LPYp9i52KfYqiDYp9mE2LnZhdmELiDYp9mE2YXYs9in2LnYryDYp9mE2LDZg9mKINmF2KrYp9itINi52YTZiSDZhdiv2KfYsSAyNCDYs9in2LnYqSDYt9in2YTZhdinINij2YYg2YXZgdiq2KfYrSDYp9mE2KrYtNi62YrZhCDZhdmB2LnZhC5cbiAgICAgICAgICAgICAgPC9BbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9BbGVydD5cblxuICAgICAgICAgICAgey8qINmF2LnZhNmI2YXYp9iqINil2LbYp9mB2YrYqSDZhNmE2LnYsdi2INmB2YLYtyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNCBvcGFjaXR5LTUwXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsPtiz2KfYudin2Kog2KfZhNi52YXZhCDYp9mE2KfZgdiq2LHYp9i22YrYqSAo2YTZhNi52LHYtiDZgdmC2LcpPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0aW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPVwiMDA6MDBcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWRcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+2KjYr9in2YrYqTog2YXZhtiq2LXZgSDYp9mE2YTZitmEPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbD7ZhtmH2KfZitipINiz2KfYudin2Kog2KfZhNi52YXZhCAo2YTZhNi52LHYtiDZgdmC2LcpPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0aW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPVwiMjM6NTlcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWRcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+2YbZh9in2YrYqTog2YLYqNmEINmF2YbYqti12YEg2KfZhNmE2YrZhCDYqNiv2YLZitmC2Kk8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDYp9mE2YPZhNmF2KfYqiDYp9mE2YXZgdiq2KfYrdmK2KkgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TWVzc2FnZVNxdWFyZSBjbGFzc05hbWU9XCJoLTUgdy01IG1sLTJcIiAvPlxuICAgICAgICAgICAgICDYp9mE2YPZhNmF2KfYqiDYp9mE2YXZgdiq2KfYrdmK2KlcbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWw+2KfZhNmD2YTZhdin2Kog2KfZhNmF2K3Zgdiy2Kkg2YTZhNix2K8gKNmF2YHYtdmI2YTYqSDYqNmB2KfYtdmE2KkpPC9MYWJlbD5cbiAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NldHRpbmdzLmtleXdvcmRzX3RyaWdnZXIuam9pbignLCAnKX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNldHRpbmdzKHtcbiAgICAgICAgICAgICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAgICAgICAgICAga2V5d29yZHNfdHJpZ2dlcjogZS50YXJnZXQudmFsdWUuc3BsaXQoJywnKS5tYXAoayA9PiBrLnRyaW0oKSkuZmlsdGVyKGsgPT4gaylcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmF2LPYp9i52K/YqSwg2KfYs9iq2YHYs9in2LEsINiz2KTYp9mELCDZhdi52YTZiNmF2KfYqlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsPtin2YTZg9mE2YXYp9iqINin2YTZhdiz2KrYqNi52K/YqSAo2YXZgdi12YjZhNipINio2YHYp9i12YTYqSk8L0xhYmVsPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3MuZXhjbHVkZWRfa2V5d29yZHMuam9pbignLCAnKX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNldHRpbmdzKHtcbiAgICAgICAgICAgICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAgICAgICAgICAgZXhjbHVkZWRfa2V5d29yZHM6IGUudGFyZ2V0LnZhbHVlLnNwbGl0KCcsJykubWFwKGsgPT4gay50cmltKCkpLmZpbHRlcihrID0+IGspXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYudin2KzZhCwg2LfYp9ix2KYsINmF2YfZhSDYrNiv2KfZiywg2YXYrdin2YXZilwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qINin2YTYsdiv2YjYryDYp9mE2KrZhNmC2KfYptmK2KkgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtNSB3LTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgINin2YTYsdiv2YjYryDYp9mE2KrZhNmC2KfYptmK2Kkg2KfZhNmF2K3Yr9iv2Kkg2YXYs9io2YLYp9mLXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsPtix2LPYp9mE2Kkg2KfZhNiq2LHYrdmK2Kg8L0xhYmVsPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3MuYXV0b19yZXNwb25zZXMuZ3JlZXRpbmd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZXR0aW5ncyh7XG4gICAgICAgICAgICAgICAgICAuLi5zZXR0aW5ncyxcbiAgICAgICAgICAgICAgICAgIGF1dG9fcmVzcG9uc2VzOiB7Li4uc2V0dGluZ3MuYXV0b19yZXNwb25zZXMsIGdyZWV0aW5nOiBlLnRhcmdldC52YWx1ZX1cbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWw+2LHYs9in2YTYqSDYrtin2LHYrCDYs9in2LnYp9iqINin2YTYudmF2YQ8L0xhYmVsPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3MuYXV0b19yZXNwb25zZXMud29ya2luZ19ob3Vyc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNldHRpbmdzKHtcbiAgICAgICAgICAgICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAgICAgICAgICAgYXV0b19yZXNwb25zZXM6IHsuLi5zZXR0aW5ncy5hdXRvX3Jlc3BvbnNlcywgd29ya2luZ19ob3VyczogZS50YXJnZXQudmFsdWV9XG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsPtix2LPYp9mE2Kkg2KfZhNmI2LXZiNmEINmE2YTYrdivINin2YTYo9mC2LXZiTwvTGFiZWw+XG4gICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZXR0aW5ncy5hdXRvX3Jlc3BvbnNlcy5tYXhfcmVhY2hlZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNldHRpbmdzKHtcbiAgICAgICAgICAgICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAgICAgICAgICAgYXV0b19yZXNwb25zZXM6IHsuLi5zZXR0aW5ncy5hdXRvX3Jlc3BvbnNlcywgbWF4X3JlYWNoZWQ6IGUudGFyZ2V0LnZhbHVlfVxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDYp9iu2KrYqNin2LEg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZiiAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJoLTUgdy01IG1sLTJcIiAvPlxuICAgICAgICAgICAgICDYp9iu2KrYqNin2LEg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbD7Ysdiz2KfZhNipINin2YTYp9iu2KrYqNin2LE8L0xhYmVsPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17dGVzdE1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUZXN0TWVzc2FnZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9mD2KrYqCDYsdiz2KfZhNipINmE2KfYrtiq2KjYp9ixINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YouLi5cIlxuICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVUZXN0QUl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXt0ZXN0aW5nIHx8ICF0ZXN0TWVzc2FnZS50cmltKCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt0ZXN0aW5nID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMiBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAg2KzYp9ix2Yog2KfZhNin2K7Yqtio2KfYsS4uLlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgINin2K7Yqtio2KfYsSDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAge3Rlc3RSZXNwb25zZSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbD7YsdivINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yo8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBwLTQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIHt0ZXN0UmVzcG9uc2V9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvTWFpbkxheW91dD5cbiAgKVxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIk1haW5MYXlvdXQiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIlN3aXRjaCIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiVGV4dGFyZWEiLCJCYWRnZSIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsIkJvdCIsIlNldHRpbmdzIiwiU2F2ZSIsIlJlZnJlc2hDdyIsIkNoZWNrQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJaYXAiLCJDbG9jayIsIk1lc3NhZ2VTcXVhcmUiLCJTaGllbGQiLCJBY3Rpdml0eSIsIkFJU2V0dGluZ3NQYWdlIiwibW9kZWxzIiwic2V0TW9kZWxzIiwic2V0dGluZ3MiLCJzZXRTZXR0aW5ncyIsImVuYWJsZWQiLCJtb2RlbCIsImRlbGF5X3NlY29uZHMiLCJ3b3JraW5nX2hvdXJzX29ubHkiLCJ3b3JraW5nX2hvdXJzX3N0YXJ0Iiwid29ya2luZ19ob3Vyc19lbmQiLCJ3b3JraW5nX2RheXMiLCJtYXhfcmVzcG9uc2VzX3Blcl9jb252ZXJzYXRpb24iLCJrZXl3b3Jkc190cmlnZ2VyIiwiZXhjbHVkZWRfa2V5d29yZHMiLCJhdXRvX3Jlc3BvbnNlcyIsImdyZWV0aW5nIiwid29ya2luZ19ob3VycyIsIm1heF9yZWFjaGVkIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzYXZpbmciLCJzZXRTYXZpbmciLCJ0ZXN0aW5nIiwic2V0VGVzdGluZyIsInRlc3RNZXNzYWdlIiwic2V0VGVzdE1lc3NhZ2UiLCJ0ZXN0UmVzcG9uc2UiLCJzZXRUZXN0UmVzcG9uc2UiLCJtZXNzYWdlIiwic2V0TWVzc2FnZSIsImZldGNoRGF0YSIsIm1vZGVsc1Jlc3BvbnNlIiwiZmV0Y2giLCJtb2RlbHNEYXRhIiwianNvbiIsInNldHRpbmdzUmVzcG9uc2UiLCJzZXR0aW5nc0RhdGEiLCJzdWNjZXNzIiwiZGF0YSIsImVycm9yIiwiY29uc29sZSIsInR5cGUiLCJ0ZXh0IiwiaGFuZGxlU2F2ZSIsInJlc3BvbnNlIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwicmVzdWx0Iiwic2V0VGltZW91dCIsImhhbmRsZVRlc3RBSSIsInRyaW0iLCJjb252ZXJzYXRpb25JZCIsIkRhdGUiLCJub3ciLCJFcnJvciIsInRlc3RBSSIsImxvZyIsInN0YXR1cyIsIm9rIiwic3Vic3RyaW5nIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNUZXh0IiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgxIiwicCIsIm9uQ2xpY2siLCJ2YXJpYW50IiwiZGlzYWJsZWQiLCJzaXplIiwibWFwIiwiaDMiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJsYXN0Q2hlY2tlZCIsInRvTG9jYWxlU3RyaW5nIiwia2V5IiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsImZpbHRlciIsIm0iLCJvbkNoYW5nZSIsImUiLCJwYXJzZUludCIsInRhcmdldCIsIm1pbiIsIm1heCIsImpvaW4iLCJzcGxpdCIsImsiLCJwbGFjZWhvbGRlciIsInJvd3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/ai-settings/page.tsx\n"));

/***/ })

});