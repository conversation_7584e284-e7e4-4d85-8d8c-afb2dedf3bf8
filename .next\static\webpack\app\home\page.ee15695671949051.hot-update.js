"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                console.log('🔄 تحميل رسالة الترحيب...');\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            console.log('📡 جاري الاتصال بـ /api/chat...');\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            console.log('📨 استجابة API:', result);\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                console.log('✅ تم إضافة رسالة الترحيب:', welcomeMsg);\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            console.log('🔄 استخدام رسالة ترحيب افتراضية');\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse flex items-center justify-center\",\n                size: \"lg\",\n                style: {\n                    zIndex: 9999\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\",\n                    strokeWidth: 2\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n        style: {\n            zIndex: 9999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 shadow-2xl border-0 transition-all duration-300 \".concat(isMinimized ? 'h-14' : 'h-96', \" bg-white\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"p-3 \".concat(isMinimized ? 'rounded-lg' : 'rounded-t-lg'),\n                    style: {\n                        background: 'linear-gradient(to right, #2563eb, #1d4ed8)',\n                        color: 'white'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-white/20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        title: isMinimized ? 'توسيع' : 'تصغير',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsOpen(false),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        title: \"إغلاق\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-2 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 \".concat(message.type === 'user' ? 'text-blue-100' : 'text-gray-500'),\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            size: \"sm\",\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});