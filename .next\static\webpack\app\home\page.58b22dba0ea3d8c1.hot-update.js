"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/simple-chat-widget */ \"(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCompanyData */ \"(app-pages-browser)/./src/hooks/useCompanyData.ts\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst companyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // استخدام hook بيانات الشركة مع التخزين المحلي\n    const { companyData, loading: companyLoading, error: companyError, getThemeColor, getCompanyName, isDataAvailable } = (0,_hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData)();\n    // استخدام البيانات المحملة أو الافتراضية\n    const companyDataState = companyData || defaultCompanyData;\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__.SimpleChatWidget, {\n                isOpen: isChatWidgetOpen,\n                onClose: ()=>setIsChatWidgetOpen(false),\n                onOpen: ()=>setIsChatWidgetOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"O/ceB+Iu6CvIuCsB3XkHEZGk0w4=\", false, function() {\n    return [\n        _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData\n    ];\n});\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useCompanyData.ts":
/*!*************************************!*\
  !*** ./src/hooks/useCompanyData.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCompanyData: () => (/* binding */ useCompanyData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Hook لإدارة بيانات الشركة والتخزين المحلي\n\nconst STORAGE_KEY = 'company_data';\nconst CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 ساعة\n;\nfunction useCompanyData() {\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // تحميل البيانات من التخزين المحلي\n    const loadFromStorage = ()=>{\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (!stored) return null;\n            const { data, timestamp } = JSON.parse(stored);\n            // التحقق من انتهاء صلاحية البيانات\n            if (Date.now() - timestamp > CACHE_DURATION) {\n                localStorage.removeItem(STORAGE_KEY);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('خطأ في تحميل البيانات من التخزين المحلي:', error);\n            localStorage.removeItem(STORAGE_KEY);\n            return null;\n        }\n    };\n    // حفظ البيانات في التخزين المحلي\n    const saveToStorage = (data)=>{\n        try {\n            const storageData = {\n                data,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(storageData));\n        } catch (error) {\n            console.error('خطأ في حفظ البيانات في التخزين المحلي:', error);\n        }\n    };\n    // جلب البيانات من الخادم\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/company');\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (result.success && result.data && result.data.length > 0) {\n                return result.data[0] // أخذ أول شركة\n                ;\n            } else {\n                throw new Error('لا توجد بيانات شركة متاحة');\n            }\n        } catch (error) {\n            console.error('خطأ في جلب بيانات الشركة:', error);\n            throw error;\n        }\n    };\n    // تحديث البيانات\n    const refreshData = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const data = await fetchCompanyData();\n            if (data) {\n                setCompanyData(data);\n                saveToStorage(data);\n            }\n        } catch (error) {\n            setError(error instanceof Error ? error.message : 'خطأ في تحميل البيانات');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تحميل البيانات عند بدء التشغيل\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCompanyData.useEffect\": ()=>{\n            const initializeData = {\n                \"useCompanyData.useEffect.initializeData\": async ()=>{\n                    // محاولة تحميل البيانات من التخزين المحلي أولاً\n                    const cachedData = loadFromStorage();\n                    if (cachedData) {\n                        setCompanyData(cachedData);\n                        setLoading(false);\n                        // جلب البيانات المحدثة في الخلفية\n                        try {\n                            const freshData = await fetchCompanyData();\n                            if (freshData && JSON.stringify(freshData) !== JSON.stringify(cachedData)) {\n                                setCompanyData(freshData);\n                                saveToStorage(freshData);\n                            }\n                        } catch (error) {\n                            // في حالة فشل التحديث، نبقي البيانات المخزنة\n                            console.warn('فشل في تحديث البيانات، سيتم استخدام البيانات المخزنة');\n                        }\n                    } else {\n                        // لا توجد بيانات مخزنة، جلب من الخادم\n                        await refreshData();\n                    }\n                }\n            }[\"useCompanyData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useCompanyData.useEffect\"], []);\n    // مسح البيانات المخزنة\n    const clearCache = ()=>{\n        localStorage.removeItem(STORAGE_KEY);\n        setCompanyData(null);\n    };\n    // الحصول على لون الموضوع\n    const getThemeColor = ()=>{\n        // من متغيرات البيئة (نظام التوجيه)\n        if (true) {\n            const port = window.location.port;\n            if (port === '7443') return '#cca967' // لون مؤسسة الجرافي\n            ;\n            if (port === '8914') return '#2563eb' // لون شركة الربيعي\n            ;\n        }\n        // من بيانات الشركة\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.theme_color) {\n            return companyData.theme_color;\n        }\n        // الافتراضي\n        return '#cca967';\n    };\n    // الحصول على اسم الشركة المناسب\n    const getCompanyName = ()=>{\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.company_name) return companyData.company_name;\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.name) return companyData.name;\n        if (companyData === null || companyData === void 0 ? void 0 : companyData.legal_name) return companyData.legal_name;\n        // من نظام التوجيه\n        if (true) {\n            const port = window.location.port;\n            if (port === '7443') return 'مؤسسة الجرافي للمحاماة';\n            if (port === '8914') return 'شركة الربيعي للمحاماة';\n        }\n        return 'النظام القانوني';\n    };\n    return {\n        companyData,\n        loading,\n        error,\n        refreshData,\n        clearCache,\n        getThemeColor,\n        getCompanyName,\n        isDataAvailable: !!companyData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VDb21wYW55RGF0YS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSw0Q0FBNEM7QUFDRDtBQTRCM0MsTUFBTUUsY0FBYztBQUNwQixNQUFNQyxpQkFBaUIsS0FBSyxLQUFLLEtBQUssS0FBSyxVQUFVOztBQUU5QyxTQUFTQztJQUNkLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHTiwrQ0FBUUEsQ0FBcUI7SUFDbkUsTUFBTSxDQUFDTyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ1MsT0FBT0MsU0FBUyxHQUFHViwrQ0FBUUEsQ0FBZ0I7SUFFbEQsbUNBQW1DO0lBQ25DLE1BQU1XLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTUMsU0FBU0MsYUFBYUMsT0FBTyxDQUFDWjtZQUNwQyxJQUFJLENBQUNVLFFBQVEsT0FBTztZQUVwQixNQUFNLEVBQUVHLElBQUksRUFBRUMsU0FBUyxFQUFFLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ047WUFFdkMsbUNBQW1DO1lBQ25DLElBQUlPLEtBQUtDLEdBQUcsS0FBS0osWUFBWWIsZ0JBQWdCO2dCQUMzQ1UsYUFBYVEsVUFBVSxDQUFDbkI7Z0JBQ3hCLE9BQU87WUFDVDtZQUVBLE9BQU9hO1FBQ1QsRUFBRSxPQUFPTixPQUFPO1lBQ2RhLFFBQVFiLEtBQUssQ0FBQyw0Q0FBNENBO1lBQzFESSxhQUFhUSxVQUFVLENBQUNuQjtZQUN4QixPQUFPO1FBQ1Q7SUFDRjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNcUIsZ0JBQWdCLENBQUNSO1FBQ3JCLElBQUk7WUFDRixNQUFNUyxjQUFjO2dCQUNsQlQ7Z0JBQ0FDLFdBQVdHLEtBQUtDLEdBQUc7WUFDckI7WUFDQVAsYUFBYVksT0FBTyxDQUFDdkIsYUFBYWUsS0FBS1MsU0FBUyxDQUFDRjtRQUNuRCxFQUFFLE9BQU9mLE9BQU87WUFDZGEsUUFBUWIsS0FBSyxDQUFDLDBDQUEwQ0E7UUFDMUQ7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNa0IsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU07WUFFN0IsSUFBSSxDQUFDRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTSx1QkFBdUMsT0FBaEJILFNBQVNJLE1BQU07WUFDeEQ7WUFFQSxNQUFNQyxTQUFTLE1BQU1MLFNBQVNNLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxJQUFJRixPQUFPbEIsSUFBSSxJQUFJa0IsT0FBT2xCLElBQUksQ0FBQ3FCLE1BQU0sR0FBRyxHQUFHO2dCQUMzRCxPQUFPSCxPQUFPbEIsSUFBSSxDQUFDLEVBQUUsQ0FBQyxlQUFlOztZQUN2QyxPQUFPO2dCQUNMLE1BQU0sSUFBSWdCLE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU90QixPQUFPO1lBQ2RhLFFBQVFiLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNNEIsY0FBYztRQUNsQjdCLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNSyxPQUFPLE1BQU1ZO1lBQ25CLElBQUlaLE1BQU07Z0JBQ1JULGVBQWVTO2dCQUNmUSxjQUFjUjtZQUNoQjtRQUNGLEVBQUUsT0FBT04sT0FBTztZQUNkQyxTQUFTRCxpQkFBaUJzQixRQUFRdEIsTUFBTTZCLE9BQU8sR0FBRztRQUNwRCxTQUFVO1lBQ1I5QixXQUFXO1FBQ2I7SUFDRjtJQUVBLGlDQUFpQztJQUNqQ1AsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXNDOzJEQUFpQjtvQkFDckIsZ0RBQWdEO29CQUNoRCxNQUFNQyxhQUFhN0I7b0JBRW5CLElBQUk2QixZQUFZO3dCQUNkbEMsZUFBZWtDO3dCQUNmaEMsV0FBVzt3QkFFWCxrQ0FBa0M7d0JBQ2xDLElBQUk7NEJBQ0YsTUFBTWlDLFlBQVksTUFBTWQ7NEJBQ3hCLElBQUljLGFBQWF4QixLQUFLUyxTQUFTLENBQUNlLGVBQWV4QixLQUFLUyxTQUFTLENBQUNjLGFBQWE7Z0NBQ3pFbEMsZUFBZW1DO2dDQUNmbEIsY0FBY2tCOzRCQUNoQjt3QkFDRixFQUFFLE9BQU9oQyxPQUFPOzRCQUNkLDZDQUE2Qzs0QkFDN0NhLFFBQVFvQixJQUFJLENBQUM7d0JBQ2Y7b0JBQ0YsT0FBTzt3QkFDTCxzQ0FBc0M7d0JBQ3RDLE1BQU1MO29CQUNSO2dCQUNGOztZQUVBRTtRQUNGO21DQUFHLEVBQUU7SUFFTCx1QkFBdUI7SUFDdkIsTUFBTUksYUFBYTtRQUNqQjlCLGFBQWFRLFVBQVUsQ0FBQ25CO1FBQ3hCSSxlQUFlO0lBQ2pCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1zQyxnQkFBZ0I7UUFDcEIsbUNBQW1DO1FBQ25DLElBQUksSUFBNkIsRUFBRTtZQUNqQyxNQUFNQyxPQUFPQyxPQUFPQyxRQUFRLENBQUNGLElBQUk7WUFDakMsSUFBSUEsU0FBUyxRQUFRLE9BQU8sVUFBVSxvQkFBb0I7O1lBQzFELElBQUlBLFNBQVMsUUFBUSxPQUFPLFVBQVUsbUJBQW1COztRQUMzRDtRQUVBLG1CQUFtQjtRQUNuQixJQUFJeEMsd0JBQUFBLGtDQUFBQSxZQUFhMkMsV0FBVyxFQUFFO1lBQzVCLE9BQU8zQyxZQUFZMkMsV0FBVztRQUNoQztRQUVBLFlBQVk7UUFDWixPQUFPO0lBQ1Q7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUk1Qyx3QkFBQUEsa0NBQUFBLFlBQWE2QyxZQUFZLEVBQUUsT0FBTzdDLFlBQVk2QyxZQUFZO1FBQzlELElBQUk3Qyx3QkFBQUEsa0NBQUFBLFlBQWE4QyxJQUFJLEVBQUUsT0FBTzlDLFlBQVk4QyxJQUFJO1FBQzlDLElBQUk5Qyx3QkFBQUEsa0NBQUFBLFlBQWErQyxVQUFVLEVBQUUsT0FBTy9DLFlBQVkrQyxVQUFVO1FBRTFELGtCQUFrQjtRQUNsQixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTVAsT0FBT0MsT0FBT0MsUUFBUSxDQUFDRixJQUFJO1lBQ2pDLElBQUlBLFNBQVMsUUFBUSxPQUFPO1lBQzVCLElBQUlBLFNBQVMsUUFBUSxPQUFPO1FBQzlCO1FBRUEsT0FBTztJQUNUO0lBRUEsT0FBTztRQUNMeEM7UUFDQUU7UUFDQUU7UUFDQTRCO1FBQ0FNO1FBQ0FDO1FBQ0FLO1FBQ0FJLGlCQUFpQixDQUFDLENBQUNoRDtJQUNyQjtBQUNGIiwic291cmNlcyI6WyJEOlxcbW9oYW1pbmV3XFxzcmNcXGhvb2tzXFx1c2VDb21wYW55RGF0YS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBIb29rINmE2KXYr9in2LHYqSDYqNmK2KfZhtin2Kog2KfZhNi02LHZg9ipINmI2KfZhNiq2K7YstmK2YYg2KfZhNmF2K3ZhNmKXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBDb21wYW55RGF0YSB7XG4gIGlkOiBudW1iZXJcbiAgbmFtZTogc3RyaW5nXG4gIGxlZ2FsX25hbWU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGFkZHJlc3M6IHN0cmluZ1xuICBjaXR5OiBzdHJpbmdcbiAgY291bnRyeTogc3RyaW5nXG4gIHBob25lOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICB3ZWJzaXRlOiBzdHJpbmdcbiAgbG9nb191cmw6IHN0cmluZ1xuICBsb2dvX2ltYWdlX3VybDogc3RyaW5nXG4gIGxvZ29fcmlnaHRfdGV4dDogc3RyaW5nXG4gIGxvZ29fbGVmdF90ZXh0OiBzdHJpbmdcbiAgZXN0YWJsaXNoZWRfZGF0ZTogc3RyaW5nXG4gIHJlZ2lzdHJhdGlvbl9udW1iZXI6IHN0cmluZ1xuICBsZWdhbF9mb3JtOiBzdHJpbmdcbiAgY2FwaXRhbDogbnVtYmVyXG4gIHRheF9udW1iZXI6IHN0cmluZ1xuICBpc19hY3RpdmU6IGJvb2xlYW5cbiAgd29ya2luZ19ob3Vyczogc3RyaW5nXG4gIHRoZW1lX2NvbG9yPzogc3RyaW5nXG4gIGNvbXBhbnlfbmFtZT86IHN0cmluZ1xufVxuXG5jb25zdCBTVE9SQUdFX0tFWSA9ICdjb21wYW55X2RhdGEnXG5jb25zdCBDQUNIRV9EVVJBVElPTiA9IDI0ICogNjAgKiA2MCAqIDEwMDAgLy8gMjQg2LPYp9i52KlcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUNvbXBhbnlEYXRhKCkge1xuICBjb25zdCBbY29tcGFueURhdGEsIHNldENvbXBhbnlEYXRhXSA9IHVzZVN0YXRlPENvbXBhbnlEYXRhIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuXG4gIC8vINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqiDZhdmGINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuICBjb25zdCBsb2FkRnJvbVN0b3JhZ2UgPSAoKTogQ29tcGFueURhdGEgfCBudWxsID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU1RPUkFHRV9LRVkpXG4gICAgICBpZiAoIXN0b3JlZCkgcmV0dXJuIG51bGxcblxuICAgICAgY29uc3QgeyBkYXRhLCB0aW1lc3RhbXAgfSA9IEpTT04ucGFyc2Uoc3RvcmVkKVxuICAgICAgXG4gICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mG2KrZh9in2KEg2LXZhNin2K3ZitipINin2YTYqNmK2KfZhtin2KpcbiAgICAgIGlmIChEYXRlLm5vdygpIC0gdGltZXN0YW1wID4gQ0FDSEVfRFVSQVRJT04pIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU1RPUkFHRV9LRVkpXG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqiDZhdmGINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZijonLCBlcnJvcilcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFNUT1JBR0VfS0VZKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICAvLyDYrdmB2Lgg2KfZhNio2YrYp9mG2KfYqiDZgdmKINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuICBjb25zdCBzYXZlVG9TdG9yYWdlID0gKGRhdGE6IENvbXBhbnlEYXRhKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN0b3JhZ2VEYXRhID0ge1xuICAgICAgICBkYXRhLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH1cbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFNUT1JBR0VfS0VZLCBKU09OLnN0cmluZ2lmeShzdG9yYWdlRGF0YSkpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINit2YHYuCDYp9mE2KjZitin2YbYp9iqINmB2Yog2KfZhNiq2K7YstmK2YYg2KfZhNmF2K3ZhNmKOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8vINis2YTYqCDYp9mE2KjZitin2YbYp9iqINmF2YYg2KfZhNiu2KfYr9mFXG4gIGNvbnN0IGZldGNoQ29tcGFueURhdGEgPSBhc3luYyAoKTogUHJvbWlzZTxDb21wYW55RGF0YSB8IG51bGw+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jb21wYW55JylcbiAgICAgIFxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YClcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBcbiAgICAgIGlmIChyZXN1bHQuc3VjY2VzcyAmJiByZXN1bHQuZGF0YSAmJiByZXN1bHQuZGF0YS5sZW5ndGggPiAwKSB7XG4gICAgICAgIHJldHVybiByZXN1bHQuZGF0YVswXSAvLyDYo9iu2LAg2KPZiNmEINi02LHZg9ipXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ9mE2Kcg2KrZiNis2K8g2KjZitin2YbYp9iqINi02LHZg9ipINmF2KrYp9it2KknKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYrNmE2Kgg2KjZitin2YbYp9iqINin2YTYtNix2YPYqTonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLy8g2KrYrdiv2YrYqyDYp9mE2KjZitin2YbYp9iqXG4gIGNvbnN0IHJlZnJlc2hEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICBzZXRFcnJvcihudWxsKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBmZXRjaENvbXBhbnlEYXRhKClcbiAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgIHNldENvbXBhbnlEYXRhKGRhdGEpXG4gICAgICAgIHNhdmVUb1N0b3JhZ2UoZGF0YSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0RXJyb3IoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K7Yt9ijINmB2Yog2KrYrdmF2YrZhCDYp9mE2KjZitin2YbYp9iqJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICAvLyDYqtit2YXZitmEINin2YTYqNmK2KfZhtin2Kog2LnZhtivINio2K/YoSDYp9mE2KrYtNi62YrZhFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxpemVEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgICAgLy8g2YXYrdin2YjZhNipINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqiDZhdmGINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZiiDYo9mI2YTYp9mLXG4gICAgICBjb25zdCBjYWNoZWREYXRhID0gbG9hZEZyb21TdG9yYWdlKClcbiAgICAgIFxuICAgICAgaWYgKGNhY2hlZERhdGEpIHtcbiAgICAgICAgc2V0Q29tcGFueURhdGEoY2FjaGVkRGF0YSlcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgICAgXG4gICAgICAgIC8vINis2YTYqCDYp9mE2KjZitin2YbYp9iqINin2YTZhdit2K/Yq9ipINmB2Yog2KfZhNiu2YTZgdmK2KlcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBmcmVzaERhdGEgPSBhd2FpdCBmZXRjaENvbXBhbnlEYXRhKClcbiAgICAgICAgICBpZiAoZnJlc2hEYXRhICYmIEpTT04uc3RyaW5naWZ5KGZyZXNoRGF0YSkgIT09IEpTT04uc3RyaW5naWZ5KGNhY2hlZERhdGEpKSB7XG4gICAgICAgICAgICBzZXRDb21wYW55RGF0YShmcmVzaERhdGEpXG4gICAgICAgICAgICBzYXZlVG9TdG9yYWdlKGZyZXNoRGF0YSlcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgLy8g2YHZiiDYrdin2YTYqSDZgdi02YQg2KfZhNiq2K3Yr9mK2KvYjCDZhtio2YLZiiDYp9mE2KjZitin2YbYp9iqINin2YTZhdiu2LLZhtipXG4gICAgICAgICAgY29uc29sZS53YXJuKCfZgdi02YQg2YHZiiDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2KrYjCDYs9mK2KrZhSDYp9iz2KrYrtiv2KfZhSDYp9mE2KjZitin2YbYp9iqINin2YTZhdiu2LLZhtipJylcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8g2YTYpyDYqtmI2KzYryDYqNmK2KfZhtin2Kog2YXYrtiy2YbYqdiMINis2YTYqCDZhdmGINin2YTYrtin2K/ZhVxuICAgICAgICBhd2FpdCByZWZyZXNoRGF0YSgpXG4gICAgICB9XG4gICAgfVxuXG4gICAgaW5pdGlhbGl6ZURhdGEoKVxuICB9LCBbXSlcblxuICAvLyDZhdiz2K0g2KfZhNio2YrYp9mG2KfYqiDYp9mE2YXYrtiy2YbYqVxuICBjb25zdCBjbGVhckNhY2hlID0gKCkgPT4ge1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFNUT1JBR0VfS0VZKVxuICAgIHNldENvbXBhbnlEYXRhKG51bGwpXG4gIH1cblxuICAvLyDYp9mE2K3YtdmI2YQg2LnZhNmJINmE2YjZhiDYp9mE2YXZiNi22YjYuVxuICBjb25zdCBnZXRUaGVtZUNvbG9yID0gKCk6IHN0cmluZyA9PiB7XG4gICAgLy8g2YXZhiDZhdiq2LrZitix2KfYqiDYp9mE2KjZitim2KkgKNmG2LjYp9mFINin2YTYqtmI2KzZitmHKVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgcG9ydCA9IHdpbmRvdy5sb2NhdGlvbi5wb3J0XG4gICAgICBpZiAocG9ydCA9PT0gJzc0NDMnKSByZXR1cm4gJyNjY2E5NjcnIC8vINmE2YjZhiDZhdik2LPYs9ipINin2YTYrNix2KfZgdmKXG4gICAgICBpZiAocG9ydCA9PT0gJzg5MTQnKSByZXR1cm4gJyMyNTYzZWInIC8vINmE2YjZhiDYtNix2YPYqSDYp9mE2LHYqNmK2LnZilxuICAgIH1cbiAgICBcbiAgICAvLyDZhdmGINio2YrYp9mG2KfYqiDYp9mE2LTYsdmD2KlcbiAgICBpZiAoY29tcGFueURhdGE/LnRoZW1lX2NvbG9yKSB7XG4gICAgICByZXR1cm4gY29tcGFueURhdGEudGhlbWVfY29sb3JcbiAgICB9XG4gICAgXG4gICAgLy8g2KfZhNin2YHYqtix2KfYttmKXG4gICAgcmV0dXJuICcjY2NhOTY3J1xuICB9XG5cbiAgLy8g2KfZhNit2LXZiNmEINi52YTZiSDYp9iz2YUg2KfZhNi02LHZg9ipINin2YTZhdmG2KfYs9ioXG4gIGNvbnN0IGdldENvbXBhbnlOYW1lID0gKCk6IHN0cmluZyA9PiB7XG4gICAgaWYgKGNvbXBhbnlEYXRhPy5jb21wYW55X25hbWUpIHJldHVybiBjb21wYW55RGF0YS5jb21wYW55X25hbWVcbiAgICBpZiAoY29tcGFueURhdGE/Lm5hbWUpIHJldHVybiBjb21wYW55RGF0YS5uYW1lXG4gICAgaWYgKGNvbXBhbnlEYXRhPy5sZWdhbF9uYW1lKSByZXR1cm4gY29tcGFueURhdGEubGVnYWxfbmFtZVxuICAgIFxuICAgIC8vINmF2YYg2YbYuNin2YUg2KfZhNiq2YjYrNmK2YdcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHBvcnQgPSB3aW5kb3cubG9jYXRpb24ucG9ydFxuICAgICAgaWYgKHBvcnQgPT09ICc3NDQzJykgcmV0dXJuICfZhdik2LPYs9ipINin2YTYrNix2KfZgdmKINmE2YTZhdit2KfZhdin2KknXG4gICAgICBpZiAocG9ydCA9PT0gJzg5MTQnKSByZXR1cm4gJ9i02LHZg9ipINin2YTYsdio2YrYudmKINmE2YTZhdit2KfZhdin2KknXG4gICAgfVxuICAgIFxuICAgIHJldHVybiAn2KfZhNmG2LjYp9mFINin2YTZgtin2YbZiNmG2YonXG4gIH1cblxuICByZXR1cm4ge1xuICAgIGNvbXBhbnlEYXRhLFxuICAgIGxvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgcmVmcmVzaERhdGEsXG4gICAgY2xlYXJDYWNoZSxcbiAgICBnZXRUaGVtZUNvbG9yLFxuICAgIGdldENvbXBhbnlOYW1lLFxuICAgIGlzRGF0YUF2YWlsYWJsZTogISFjb21wYW55RGF0YVxuICB9XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTVE9SQUdFX0tFWSIsIkNBQ0hFX0RVUkFUSU9OIiwidXNlQ29tcGFueURhdGEiLCJjb21wYW55RGF0YSIsInNldENvbXBhbnlEYXRhIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibG9hZEZyb21TdG9yYWdlIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImRhdGEiLCJ0aW1lc3RhbXAiLCJKU09OIiwicGFyc2UiLCJEYXRlIiwibm93IiwicmVtb3ZlSXRlbSIsImNvbnNvbGUiLCJzYXZlVG9TdG9yYWdlIiwic3RvcmFnZURhdGEiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiZmV0Y2hDb21wYW55RGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwic3RhdHVzIiwicmVzdWx0IiwianNvbiIsInN1Y2Nlc3MiLCJsZW5ndGgiLCJyZWZyZXNoRGF0YSIsIm1lc3NhZ2UiLCJpbml0aWFsaXplRGF0YSIsImNhY2hlZERhdGEiLCJmcmVzaERhdGEiLCJ3YXJuIiwiY2xlYXJDYWNoZSIsImdldFRoZW1lQ29sb3IiLCJwb3J0Iiwid2luZG93IiwibG9jYXRpb24iLCJ0aGVtZV9jb2xvciIsImdldENvbXBhbnlOYW1lIiwiY29tcGFueV9uYW1lIiwibmFtZSIsImxlZ2FsX25hbWUiLCJpc0RhdGFBdmFpbGFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useCompanyData.ts\n"));

/***/ })

});