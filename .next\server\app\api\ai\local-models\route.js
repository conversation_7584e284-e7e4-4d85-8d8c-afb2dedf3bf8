/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! يمكنني مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج - مجانية ومدفوعة\nconst LOCAL_MODELS = {\n    'groq-llama-8b': {\n        name: 'Llama 3.1 8B (Groq) - مجاني وسريع',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-8b-instant',\n        description: 'نموذج Llama 3.1 8B عبر Groq - مجاني وسريع جداً ومناسب للاستشارات القانونية',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'groq-llama-70b': {\n        name: 'Llama 3.1 70B (Groq) - مجاني وقوي',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-70b-versatile',\n        description: 'نموذج Llama 3.1 70B عبر Groq - مجاني وقوي جداً للقضايا القانونية المعقدة',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'openai-gpt4': {\n        name: 'GPT-4o (OpenAI) - مدفوع',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4o',\n        description: 'نموذج GPT-4o من OpenAI - الأحدث والأفضل للقضايا القانونية (مدفوع)',\n        type: 'openai-api',\n        requiresKey: true,\n        free: false\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة النماذج المختلفة\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                // فحص النماذج حسب النوع\n                if (model1.type === 'groq-api') {\n                    // فحص Groq API (مجاني)\n                    const apiKey = process.env.GROQ_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.groq.com/openai/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Groq API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'GROQ_API_KEY مطلوب (مجاني من groq.com)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'openai-api') {\n                    // فحص OpenAI API (مدفوع)\n                    const apiKey = process.env.OPENAI_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.openai.com/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ OpenAI API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'OPENAI_API_KEY مطلوب (مدفوع)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error instanceof Error ? error.message : 'خطأ غير معروف',\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key حسب نوع النموذج\n            let apiKey = '';\n            if (selectedModel1.type === 'openai-api') {\n                apiKey = process.env.OPENAI_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4o.'\n                    }, {\n                        status: 500\n                    });\n                }\n            } else if (selectedModel1.type === 'groq-api') {\n                apiKey = process.env.GROQ_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'GROQ_API_KEY غير متوفر. احصل على مفتاح مجاني من groq.com'\n                    }, {\n                        status: 500\n                    });\n                }\n            }\n            headers['Authorization'] = `Bearer ${apiKey}`;\n            // إعداد الـ prompt المحسن للقضايا القانونية (محدد وليس عام)\n            const legalSystemPrompt = `أنت محامي يمني خبير. اتبع هذه القواعد بدقة:\n\nللأسئلة العامة (مثل \"أحتاج استشارة\" أو \"مساعدة\"):\n- أجب فقط: \"يرجى توضيح موضوع استشارتك القانونية بالتفصيل حتى أتمكن من مساعدتك.\"\n- لا تعطي أي قوائم أو خيارات\n\nللأسئلة المحددة (تحتوي على كلمات مثل \"ما هي\" أو \"كيف\" أو \"إجراءات\"):\n- أجب مباشرة بالمعلومات القانونية\n- اذكر 3 نقاط رئيسية فقط\n- لا تطلب تفاصيل إضافية أبداً\n- كن مختصراً (50-80 كلمة)\n\nممنوع تماماً:\n- تكرار السؤال\n- المقدمات الطويلة\n- طلب تفاصيل للأسئلة المحددة\n- القوائم الطويلة`;\n            const userPrompt = `\"${message1}\"`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else if (selectedModel1.type === 'huggingface-api') {\n            // معالجة خاصة لـ Hugging Face API\n            const huggingFacePrompt = `أنت محامي يمني خبير. العميل يسأل: \"${message1}\"\\n\\nقدم استشارة قانونية مفصلة باللغة العربية:`;\n            const hfApiKey = process.env.HUGGINGFACE_API_KEY || '';\n            if (!hfApiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'HUGGINGFACE_API_KEY غير متوفر. احصل على مفتاح مجاني من huggingface.co'\n                }, {\n                    status: 500\n                });\n            }\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${hfApiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    inputs: huggingFacePrompt,\n                    parameters: {\n                        max_new_tokens: 300,\n                        temperature: 0.7,\n                        return_full_text: false,\n                        do_sample: true\n                    }\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة توقيع المساعد الذكي\n        if (!cleanedResponse.includes('المساعد الذكي')) {\n            cleanedResponse += '\\n\\n---\\n🤖 المساعد الذكي للمكتب';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً.\n\nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى\n\n---\n🤖 المساعد الذكي للمكتب (وضع الطوارئ)`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();