/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! أنا المساعد القانوني للمكتب.\\n\\nأستطيع مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج - GPT-4 فقط\nconst LOCAL_MODELS = {\n    'openai-gpt4': {\n        name: 'GPT-4 (OpenAI)',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4',\n        description: 'نموذج GPT-4 من OpenAI - الأفضل للقضايا القانونية والاستشارات',\n        type: 'openai-api',\n        requiresKey: true\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة نموذج GPT-4\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                // فحص OpenAI API فقط\n                const apiKey = process.env.OPENAI_API_KEY;\n                if (apiKey) {\n                    try {\n                        const testResponse = await fetch('https://api.openai.com/v1/models', {\n                            method: 'GET',\n                            headers: {\n                                'Authorization': `Bearer ${apiKey}`,\n                                'Content-Type': 'application/json'\n                            },\n                            signal: AbortSignal.timeout(10000)\n                        });\n                        return {\n                            key,\n                            ...model1,\n                            status: testResponse.ok ? 'available' : 'api_error',\n                            lastChecked: new Date().toISOString()\n                        };\n                    } catch (apiError) {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_error',\n                            error: 'فشل في الاتصال بـ OpenAI API',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else {\n                    return {\n                        key,\n                        ...model1,\n                        status: 'api_key_required',\n                        error: 'OPENAI_API_KEY مطلوب في متغيرات البيئة',\n                        lastChecked: new Date().toISOString()\n                    };\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error instanceof Error ? error.message : 'خطأ غير معروف',\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key لـ OpenAI\n            const apiKey = process.env.OPENAI_API_KEY;\n            if (!apiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4.'\n                }, {\n                    status: 500\n                });\n            }\n            headers['Authorization'] = `Bearer ${apiKey}`;\n            // إعداد الـ prompt المحسن للقضايا القانونية (محسن للسرعة والدقة)\n            const legalSystemPrompt = `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.\n\nخبرتك تشمل:\n- قانون العمل اليمني رقم 5 لسنة 1995\n- قانون الأحوال الشخصية اليمني\n- قانون الجرائم والعقوبات اليمني\n- قانون المرافعات المدنية والتجارية\n- أحكام الميراث في الشريعة الإسلامية\n- قانون الشركات والاستثمار اليمني\n\nمهامك:\n- تقديم استشارات قانونية دقيقة ومفصلة\n- الاستشهاد بالمواد القانونية ذات الصلة\n- شرح الإجراءات العملية خطوة بخطوة\n- تقديم نصائح عملية قابلة للتطبيق\n\nأسلوبك: مهني، واضح، مفصل، باللغة العربية.`;\n            const userPrompt = `استشارة قانونية: \"${message1}\"\\n\\nقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة توقيع المساعد الذكي\n        if (!cleanedResponse.includes('المساعد الذكي')) {\n            cleanedResponse += '\\n\\n---\\n🤖 المساعد الذكي للمكتب';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً.\n\nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى\n\n---\n🤖 المساعد الذكي للمكتب (وضع الطوارئ)`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();