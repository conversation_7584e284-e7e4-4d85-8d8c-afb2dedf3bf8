/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! يمكنني مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج - مجانية ومدفوعة\nconst LOCAL_MODELS = {\n    'groq-llama-8b': {\n        name: 'Llama 3.1 8B (Groq) - مجاني وسريع',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-8b-instant',\n        description: 'نموذج Llama 3.1 8B عبر Groq - مجاني وسريع جداً ومناسب للاستشارات القانونية',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'groq-llama-70b': {\n        name: 'Llama 3.1 70B (Groq) - مجاني وقوي',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-70b-versatile',\n        description: 'نموذج Llama 3.1 70B عبر Groq - مجاني وقوي جداً للقضايا القانونية المعقدة',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'openai-gpt4': {\n        name: 'GPT-4o (OpenAI) - مدفوع',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4o',\n        description: 'نموذج GPT-4o من OpenAI - الأحدث والأفضل للقضايا القانونية (مدفوع)',\n        type: 'openai-api',\n        requiresKey: true,\n        free: false\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة النماذج المختلفة\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                // فحص النماذج حسب النوع\n                if (model1.type === 'groq-api') {\n                    // فحص Groq API (مجاني)\n                    const apiKey = process.env.GROQ_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.groq.com/openai/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Groq API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'GROQ_API_KEY مطلوب (مجاني من groq.com)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'openai-api') {\n                    // فحص OpenAI API (مدفوع)\n                    const apiKey = process.env.OPENAI_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.openai.com/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ OpenAI API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'OPENAI_API_KEY مطلوب (مدفوع)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error instanceof Error ? error.message : 'خطأ غير معروف',\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key حسب نوع النموذج\n            let apiKey = '';\n            if (selectedModel1.type === 'openai-api') {\n                apiKey = process.env.OPENAI_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4o.'\n                    }, {\n                        status: 500\n                    });\n                }\n            } else if (selectedModel1.type === 'groq-api') {\n                apiKey = process.env.GROQ_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'GROQ_API_KEY غير متوفر. احصل على مفتاح مجاني من groq.com'\n                    }, {\n                        status: 500\n                    });\n                }\n            }\n            headers['Authorization'] = `Bearer ${apiKey}`;\n            // إعداد الـ prompt البسيط والمباشر\n            const legalSystemPrompt = `أنت مساعد ذكي لمؤسسة الجرافي للمحاماة والاستشارات القانونية في اليمن.\n\nتخصصك فقط في القانون اليمني والشريعة الإسلامية.\n\nقواعد صارمة:\n1. لا تجب على أسئلة خارج القانون اليمني (مثل قوانين دول أخرى، الفضاء، التكنولوجيا، إلخ)\n2. لا تقل \"مرحباً\" أو أي ترحيب\n3. للأسئلة خارج تخصصك أو المعقدة جداً، قل فقط: \"أنصح بالتواصل مع أحد محامينا للحصول على استشارة مفصلة.\"\n4. للأسئلة القانونية اليمنية البسيطة، أجب مباشرة في 30-80 كلمة\n\nأجب فقط عن القانون اليمني والشريعة الإسلامية.`;\n            const userPrompt = `${message1}`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else if (selectedModel1.type === 'huggingface-api') {\n            // معالجة خاصة لـ Hugging Face API\n            const huggingFacePrompt = `أنت محامي يمني خبير. العميل يسأل: \"${message1}\"\\n\\nقدم استشارة قانونية مفصلة باللغة العربية:`;\n            const hfApiKey = process.env.HUGGINGFACE_API_KEY || '';\n            if (!hfApiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'HUGGINGFACE_API_KEY غير متوفر. احصل على مفتاح مجاني من huggingface.co'\n                }, {\n                    status: 500\n                });\n            }\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${hfApiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    inputs: huggingFacePrompt,\n                    parameters: {\n                        max_new_tokens: 300,\n                        temperature: 0.7,\n                        return_full_text: false,\n                        do_sample: true\n                    }\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة رقم الهاتف عند التوجيه لمحامي المؤسسة\n        if (cleanedResponse.includes('أنصح بالتواصل مع أحد محامينا')) {\n            try {\n                // جلب معلومات الشركة لإضافة رقم الهاتف\n                const companyResult = await client.query('SELECT phone FROM companies LIMIT 1');\n                if (companyResult.rows.length > 0) {\n                    const phone = companyResult.rows[0].phone;\n                    cleanedResponse = cleanedResponse.replace('أنصح بالتواصل مع أحد محامينا للحصول على استشارة مفصلة.', `أنصح بالتواصل مع أحد محامينا على الرقم ${phone}`);\n                }\n            } catch (phoneError) {\n                console.error('Error fetching phone number:', phoneError);\n            // في حالة فشل جلب الرقم، اتركه كما هو\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، الخدمة غير متاحة حالياً.\n\nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();