(()=>{var e={};e.id=8999,e.ids=[8999],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19237:(e,t,o)=>{"use strict";o.r(t),o.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var r={};o.r(r),o.d(r,{GET:()=>p,POST:()=>c});var s=o(96559),n=o(48088),a=o(37719),i=o(32190);let l={"groq-llama-8b":{name:"Llama 3.1 8B (Groq) - مجاني وسريع",endpoint:"https://api.groq.com/openai/v1/chat/completions",model:"llama-3.1-8b-instant",description:"نموذج Llama 3.1 8B عبر Groq - مجاني وسريع جداً ومناسب للاستشارات القانونية",type:"groq-api",requiresKey:!0,free:!0},"groq-llama-70b":{name:"Llama 3.1 70B (Groq) - مجاني وقوي",endpoint:"https://api.groq.com/openai/v1/chat/completions",model:"llama-3.1-70b-versatile",description:"نموذج Llama 3.1 70B عبر Groq - مجاني وقوي جداً للقضايا القانونية المعقدة",type:"groq-api",requiresKey:!0,free:!0},"openai-gpt4":{name:"GPT-4o (OpenAI) - مدفوع",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-4o",description:"نموذج GPT-4o من OpenAI - الأحدث والأفضل للقضايا القانونية (مدفوع)",type:"openai-api",requiresKey:!0,free:!1}};async function p(){try{let e=await Promise.all(Object.entries(l).map(async([e,t])=>{try{if("groq-api"===t.type){let o=process.env.GROQ_API_KEY;if(!o)return{key:e,...t,status:"api_key_required",error:"GROQ_API_KEY مطلوب (مجاني من groq.com)",lastChecked:new Date().toISOString()};try{let r=await fetch("https://api.groq.com/openai/v1/models",{method:"GET",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},signal:AbortSignal.timeout(1e4)});return{key:e,...t,status:r.ok?"available":"api_error",lastChecked:new Date().toISOString()}}catch(o){return{key:e,...t,status:"api_error",error:"فشل في الاتصال بـ Groq API",lastChecked:new Date().toISOString()}}}if("openai-api"===t.type){let o=process.env.OPENAI_API_KEY;if(!o)return{key:e,...t,status:"api_key_required",error:"OPENAI_API_KEY مطلوب (مدفوع)",lastChecked:new Date().toISOString()};try{let r=await fetch("https://api.openai.com/v1/models",{method:"GET",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},signal:AbortSignal.timeout(1e4)});return{key:e,...t,status:r.ok?"available":"api_error",lastChecked:new Date().toISOString()}}catch(o){return{key:e,...t,status:"api_error",error:"فشل في الاتصال بـ OpenAI API",lastChecked:new Date().toISOString()}}}}catch(o){return{key:e,...t,status:"offline",error:o instanceof Error?o.message:"خطأ غير معروف",lastChecked:new Date().toISOString()}}}));return i.NextResponse.json({success:!0,data:{models:e,ollamaService:e.some(e=>"offline"!==e.status)?"online":"offline"}})}catch(e){return console.error("Error checking local models:",e),i.NextResponse.json({success:!1,error:"فشل في فحص النماذج المحلية"},{status:500})}}async function c(e){try{var t;let o,{message:r,model:s="codellama",conversationId:n,context:a=[]}=await e.json();if(!r||!r.trim())return i.NextResponse.json({success:!1,error:"الرسالة مطلوبة"},{status:400});console.log("\uD83E\uDD16 AI Request:",{message:r,model:s,conversationId:n}),console.log("\uD83D\uDE80 استخدام AI مباشرة للحصول على استجابة سريعة...");let p=l[s];if(!p)return i.NextResponse.json({success:!1,error:"النموذج المحدد غير متاح"},{status:400});let c=`أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.

مهامك القانونية فقط:
1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد
2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية
3. شرح الإجراءات القانونية والقوانين اليمنية
4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية
5. الرد باللغة العربية بشكل مهني وودود

قواعد مهمة:
- اجب فقط عن الأسئلة القانونية
- لا تذكر البرمجة أو الكود أو التطوير أبداً
- قدم معلومات قانونية عامة مفيدة
- اشرح الخطوات والإجراءات القانونية بوضوح
- كن مهذباً ومهنياً ومفيداً
- اجعل كل رد مخصص للسؤال القانوني المطروح
- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة

خدمات المكتب القانونية:
- الاستشارات القانونية في جميع المجالات
- صياغة العقود والوثائق القانونية
- التمثيل أمام المحاكم والجهات القضائية
- القضايا المدنية والتجارية والجنائية
- قضايا الأحوال الشخصية والميراث
- القضايا الإدارية والعمالية
- التحكيم وحل النزاعات

رسالة العميل: "${r}"

اجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`,u="";if("openai-compatible"===p.type||"openai-api"===p.type||"groq-api"===p.type){let e={"Content-Type":"application/json"},t="";if("openai-api"===p.type){if(!(t=process.env.OPENAI_API_KEY||""))return i.NextResponse.json({success:!1,error:"OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4o."},{status:500})}else if("groq-api"===p.type&&!(t=process.env.GROQ_API_KEY||""))return i.NextResponse.json({success:!1,error:"GROQ_API_KEY غير متوفر. احصل على مفتاح مجاني من groq.com"},{status:500});e.Authorization=`Bearer ${t}`;let s=`أنت مساعد ذكي لمؤسسة الجرافي للمحاماة والاستشارات القانونية في اليمن.

تخصصك فقط في القانون اليمني والشريعة الإسلامية.

قواعد صارمة:
1. لا تجب على أسئلة خارج القانون اليمني (مثل قوانين دول أخرى، الفضاء، التكنولوجيا، إلخ)
2. لا تقل "مرحباً" أو أي ترحيب
3. للأسئلة خارج تخصصك أو المعقدة جداً، قل فقط: "أنصح بالتواصل مع أحد محامينا للحصول على استشارة مفصلة."
4. للأسئلة القانونية اليمنية البسيطة، أجب مباشرة في 30-80 كلمة

أجب فقط عن القانون اليمني والشريعة الإسلامية.`,n=`${r}`;o=await fetch(p.endpoint,{method:"POST",headers:e,body:JSON.stringify({model:p.model,messages:[{role:"system",content:s},{role:"user",content:n}],temperature:.7,max_tokens:600,stream:!1}),signal:AbortSignal.timeout(45e3)})}else if("huggingface-api"===p.type){let e=`أنت محامي يمني خبير. العميل يسأل: "${r}"

قدم استشارة قانونية مفصلة باللغة العربية:`,t=process.env.HUGGINGFACE_API_KEY||"";if(!t)return i.NextResponse.json({success:!1,error:"HUGGINGFACE_API_KEY غير متوفر. احصل على مفتاح مجاني من huggingface.co"},{status:500});o=await fetch(p.endpoint,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({inputs:e,parameters:{max_new_tokens:300,temperature:.7,return_full_text:!1,do_sample:!0}}),signal:AbortSignal.timeout(45e3)})}else o=await fetch(p.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:p.model,prompt:c,stream:!1,options:{temperature:.7,top_p:.9,max_tokens:500,stop:["\n\nUser:","\n\nالعميل:","\n\nالمستخدم:"]}}),signal:AbortSignal.timeout(3e4)});if(!o.ok)throw Error(`AI service error: ${o.status}`);let d=await o.json();if(console.log("\uD83E\uDD16 AI Response Data:",JSON.stringify(d,null,2)),"openai-compatible"===p.type||"openai-api"===p.type||"groq-api"===p.type)if(d.choices&&d.choices[0]&&d.choices[0].message)u=d.choices[0].message.content;else throw Error("لم يتم الحصول على رد من النموذج");else{if(!d.response)throw Error("لم يتم الحصول على رد من النموذج");u=d.response}let m=["```","def ","function","import ","class ","مساعدك في البرمجة","شرح المفاهيم البرمجية","كتابة الكود","تطوير المشاريع"].some(e=>u.toLowerCase().includes(e.toLowerCase()))||u.includes("برمجة")&&u.includes("كود"),g=u.trim().replace(/^(المساعد|الذكي|AI|Assistant):\s*/i,"").replace(/\n{3,}/g,"\n\n").trim();if(console.log("\uD83D\uDD0D Original AI Response:",u.substring(0,200)),console.log("\uD83D\uDD0D Programming keywords detected:",m),console.log("\uD83D\uDD0D Original AI Response (First 200 chars):",u.substring(0,200)),console.log("\uD83D\uDD0D Programming keywords detected:",m),m&&(u.includes("شرح المفاهيم البرمجية")||u.includes("كتابة الكود"))&&(console.log("⚠️ Detected clear programming response, using smart fallback"),g=(t=r).includes("طلاق")?`بخصوص قضايا الطلاق في القانون اليمني:

• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية
• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات
• يجب مراعاة حقوق الزوجة والأطفال
• التوثيق أمام المحكمة ضروري

للحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`:t.includes("عقد")||t.includes("عمل")?`بخصوص عقود العمل والعقود القانونية:

• حقوق العامل محمية بالقانون اليمني
• العقد يجب أن يتضمن الراتب وساعات العمل
• الإجازات والتأمينات حق للعامل
• يمكن مراجعة وصياغة العقود

نساعدك في حماية حقوقك القانونية.`:t.includes("محكمة")||t.includes("قضية")?`بخصوص القضايا والمحاكم:

• نمثلك أمام جميع المحاكم اليمنية
• إعداد المذكرات والمرافعات
• متابعة القضية في جميع المراحل
• استشارة قانونية متخصصة

فريقنا جاهز لتمثيلك والدفاع عن حقوقك.`:`مرحباً! يمكنني مساعدتك في:
• الاستشارات القانونية
• شرح الإجراءات القانونية
• توجيهك للمحامي المناسب
• معلومات عن خدماتنا

ما الموضوع القانوني الذي تحتاج المساعدة فيه؟`),g.includes("أنصح بالتواصل مع أحد محامينا"))try{let e=await client.query("SELECT phone FROM companies LIMIT 1");if(e.rows.length>0){let t=e.rows[0].phone;g=g.replace("أنصح بالتواصل مع أحد محامينا للحصول على استشارة مفصلة.",`أنصح بالتواصل مع أحد محامينا على الرقم ${t}`)}}catch(e){console.error("Error fetching phone number:",e)}return i.NextResponse.json({success:!0,response:g,model:p.name,conversationId:n,timestamp:new Date().toISOString(),usage:{prompt_tokens:d.prompt_eval_count||0,completion_tokens:d.eval_count||0,total_tokens:(d.prompt_eval_count||0)+(d.eval_count||0)}})}catch(t){console.error("❌ Error with local AI model:",t),console.error("\uD83D\uDCCA Error details:",{message:t.message,stack:t.stack,selectedModel:selectedModel?.name,endpoint:selectedModel?.endpoint,requestData:{message,model,conversationId}});let e=`عذراً، الخدمة غير متاحة حالياً.

يرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.

📞 للاستفسارات العاجلة: اتصل بالمكتب
💬 أو انتظر قليلاً وحاول مرة أخرى`;return i.NextResponse.json({success:!1,error:`فشل في الاتصال بالنموذج المحلي: ${t.message}`,fallback_response:e,model:selectedModel?.name||"unknown",conversationId,timestamp:new Date().toISOString(),details:{errorType:t.constructor.name,errorMessage:t.message,selectedModel:selectedModel?.name}},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/local-models/route",pathname:"/api/ai/local-models",filename:"route",bundlePath:"app/api/ai/local-models/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\ai\\local-models\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:g}=u;function h(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[4447,580],()=>o(19237));module.exports=r})();