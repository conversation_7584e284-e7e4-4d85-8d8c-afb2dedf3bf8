/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! أنا المساعد القانوني للمكتب.\\n\\nأستطيع مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج - مجانية ومدفوعة\nconst LOCAL_MODELS = {\n    'groq-llama-8b': {\n        name: 'Llama 3.1 8B (Groq) - مجاني وسريع',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-8b-instant',\n        description: 'نموذج Llama 3.1 8B عبر Groq - مجاني وسريع جداً ومناسب للاستشارات القانونية',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'hf-qwen': {\n        name: 'Qwen 2.5 (Hugging Face) - مجاني',\n        endpoint: 'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-72B-Instruct',\n        model: 'Qwen/Qwen2.5-72B-Instruct',\n        description: 'نموذج Qwen 2.5 عبر Hugging Face - مجاني وممتاز للغة العربية والقضايا القانونية',\n        type: 'huggingface-api',\n        requiresKey: true,\n        free: true\n    },\n    'openai-gpt4': {\n        name: 'GPT-4o (OpenAI) - مدفوع',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4o',\n        description: 'نموذج GPT-4o من OpenAI - الأحدث والأفضل للقضايا القانونية (مدفوع)',\n        type: 'openai-api',\n        requiresKey: true,\n        free: false\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة النماذج المختلفة\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                // فحص النماذج حسب النوع\n                if (model1.type === 'groq-api') {\n                    // فحص Groq API (مجاني)\n                    const apiKey = process.env.GROQ_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.groq.com/openai/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Groq API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'GROQ_API_KEY مطلوب (مجاني من groq.com)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'huggingface-api') {\n                    // فحص Hugging Face API (مجاني)\n                    const apiKey = process.env.HUGGINGFACE_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch(model1.endpoint, {\n                                method: 'POST',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    inputs: \"test\",\n                                    parameters: {\n                                        max_new_tokens: 1\n                                    }\n                                }),\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok || testResponse.status === 422 ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Hugging Face API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'HUGGINGFACE_API_KEY مطلوب (مجاني من huggingface.co)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'openai-api') {\n                    // فحص OpenAI API (مدفوع)\n                    const apiKey = process.env.OPENAI_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.openai.com/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ OpenAI API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'OPENAI_API_KEY مطلوب (مدفوع)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error instanceof Error ? error.message : 'خطأ غير معروف',\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key حسب نوع النموذج\n            let apiKey = '';\n            if (selectedModel1.type === 'openai-api') {\n                apiKey = process.env.OPENAI_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4o.'\n                    }, {\n                        status: 500\n                    });\n                }\n            } else if (selectedModel1.type === 'groq-api') {\n                apiKey = process.env.GROQ_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'GROQ_API_KEY غير متوفر. احصل على مفتاح مجاني من groq.com'\n                    }, {\n                        status: 500\n                    });\n                }\n            } else if (selectedModel1.type === 'huggingface-api') {\n                apiKey = process.env.HUGGINGFACE_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'HUGGINGFACE_API_KEY غير متوفر. احصل على مفتاح مجاني من huggingface.co'\n                    }, {\n                        status: 500\n                    });\n                }\n            }\n            headers['Authorization'] = `Bearer ${apiKey}`;\n            // إعداد الـ prompt المحسن للقضايا القانونية (محسن للسرعة والدقة)\n            const legalSystemPrompt = `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.\n\nخبرتك تشمل:\n- قانون العمل اليمني رقم 5 لسنة 1995\n- قانون الأحوال الشخصية اليمني\n- قانون الجرائم والعقوبات اليمني\n- قانون المرافعات المدنية والتجارية\n- أحكام الميراث في الشريعة الإسلامية\n- قانون الشركات والاستثمار اليمني\n\nمهامك:\n- تقديم استشارات قانونية دقيقة ومفصلة\n- الاستشهاد بالمواد القانونية ذات الصلة\n- شرح الإجراءات العملية خطوة بخطوة\n- تقديم نصائح عملية قابلة للتطبيق\n\nأسلوبك: مهني، واضح، مفصل، باللغة العربية.`;\n            const userPrompt = `استشارة قانونية: \"${message1}\"\\n\\nقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة توقيع المساعد الذكي\n        if (!cleanedResponse.includes('المساعد الذكي')) {\n            cleanedResponse += '\\n\\n---\\n🤖 المساعد الذكي للمكتب';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً.\n\nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى\n\n---\n🤖 المساعد الذكي للمكتب (وضع الطوارئ)`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();