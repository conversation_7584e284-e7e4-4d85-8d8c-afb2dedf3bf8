/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! أنا المساعد القانوني للمكتب.\\n\\nأستطيع مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج المحلية والخارجية\nconst LOCAL_MODELS = {\n    codellama: {\n        name: 'CodeLlama:13b',\n        endpoint: 'http://localhost:11434/api/generate',\n        model: 'codellama:13b',\n        description: 'نموذج CodeLlama للمساعدة في الأمور القانونية والبرمجية',\n        type: 'ollama'\n    },\n    codegeex2: {\n        name: 'CodeGeeX2 6B',\n        endpoint: 'http://localhost:8001/v1/chat/completions',\n        model: 'THUDM/codegeex2-6b',\n        description: 'نموذج CodeGeeX2 6B للبرمجة والنصوص - محلي',\n        type: 'openai-compatible',\n        checkEndpoint: 'http://localhost:8001/health'\n    },\n    'openai-gpt4': {\n        name: 'GPT-4 (OpenAI)',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4',\n        description: 'نموذج GPT-4 من OpenAI - الأفضل للقضايا القانونية',\n        type: 'openai-api',\n        requiresKey: true\n    },\n    'openai-gpt35': {\n        name: 'GPT-3.5 Turbo (OpenAI)',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-3.5-turbo',\n        description: 'نموذج GPT-3.5 من OpenAI - سريع ومناسب للاستشارات',\n        type: 'openai-api',\n        requiresKey: true\n    },\n    'groq-llama': {\n        name: 'Llama 3 70B (Groq)',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama3-70b-8192',\n        description: 'نموذج Llama 3 70B عبر Groq - مجاني وسريع وممتاز للقضايا القانونية',\n        type: 'groq-api',\n        requiresKey: true\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة النماذج\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                if (model1.type === 'openai-compatible') {\n                    // فحص نماذج OpenAI-compatible (مثل CodeGeeX2)\n                    const checkUrl = model1.checkEndpoint || model1.endpoint;\n                    const response = await fetch(checkUrl, {\n                        method: 'GET',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        signal: AbortSignal.timeout(5000)\n                    });\n                    if (response.ok) {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'available',\n                            lastChecked: new Date().toISOString()\n                        };\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'service_unavailable',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'groq-api') {\n                    // فحص Groq API\n                    const apiKey = process.env.GROQ_API_KEY;\n                    if (apiKey && apiKey !== 'gsk_demo_key_for_testing') {\n                        try {\n                            const testResponse = await fetch('https://api.groq.com/openai/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(5000)\n                            });\n                            if (testResponse.ok) {\n                                return {\n                                    key,\n                                    ...model1,\n                                    status: 'available',\n                                    lastChecked: new Date().toISOString()\n                                };\n                            } else {\n                                return {\n                                    key,\n                                    ...model1,\n                                    status: 'api_error',\n                                    lastChecked: new Date().toISOString()\n                                };\n                            }\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Groq API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'API Key مطلوب',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'openai-api') {\n                    // فحص OpenAI API\n                    const apiKey = process.env.OPENAI_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.openai.com/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(5000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ OpenAI API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'API Key مطلوب',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else {\n                    // فحص نماذج Ollama\n                    const response = await fetch('http://localhost:11434/api/tags', {\n                        method: 'GET',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        signal: AbortSignal.timeout(5000)\n                    });\n                    if (response.ok) {\n                        const data = await response.json();\n                        const isAvailable = data.models?.some((m)=>m.name.toLowerCase().includes(model1.model.split(':')[0].toLowerCase()));\n                        return {\n                            key,\n                            ...model1,\n                            status: isAvailable ? 'available' : 'not_found',\n                            lastChecked: new Date().toISOString()\n                        };\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'service_unavailable',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error.message,\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key إذا كان مطلوباً\n            if (selectedModel1.requiresKey) {\n                let apiKey = '';\n                if (selectedModel1.type === 'openai-api') {\n                    apiKey = process.env.OPENAI_API_KEY || '';\n                } else if (selectedModel1.type === 'groq-api') {\n                    apiKey = process.env.GROQ_API_KEY || '';\n                }\n                if (!apiKey) {\n                    console.log(`⚠️ API Key غير متوفر للنموذج ${selectedModel1.name}, التبديل إلى CodeLlama`);\n                    // التبديل إلى النموذج المحلي\n                    const fallbackModel = LOCAL_MODELS['codellama'];\n                    return await handleLocalModel(fallbackModel, message1, conversationId1);\n                }\n                headers['Authorization'] = `Bearer ${apiKey}`;\n            }\n            // إعداد الـ prompt المحسن للقضايا القانونية (محسن للسرعة والدقة)\n            const legalSystemPrompt = `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.\n\nخبرتك تشمل:\n- قانون العمل اليمني رقم 5 لسنة 1995\n- قانون الأحوال الشخصية اليمني\n- قانون الجرائم والعقوبات اليمني\n- قانون المرافعات المدنية والتجارية\n- أحكام الميراث في الشريعة الإسلامية\n- قانون الشركات والاستثمار اليمني\n\nمهامك:\n- تقديم استشارات قانونية دقيقة ومفصلة\n- الاستشهاد بالمواد القانونية ذات الصلة\n- شرح الإجراءات العملية خطوة بخطوة\n- تقديم نصائح عملية قابلة للتطبيق\n\nأسلوبك: مهني، واضح، مفصل، باللغة العربية.`;\n            const userPrompt = `استشارة قانونية: \"${message1}\"\\n\\nقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة توقيع المساعد الذكي\n        if (!cleanedResponse.includes('المساعد الذكي')) {\n            cleanedResponse += '\\n\\n---\\n🤖 المساعد الذكي للمكتب';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً. \n    \nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى\n\n---\n🤖 المساعد الذكي للمكتب (وضع الطوارئ)`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();