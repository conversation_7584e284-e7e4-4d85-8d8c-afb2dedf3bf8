/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/local-models/route";
exports.ids = ["app/api/ai/local-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/local-models/route.ts */ \"(rsc)/./src/app/api/ai/local-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/local-models/route\",\n        pathname: \"/api/ai/local-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/local-models/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\ai\\\\local-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_ai_local_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/local-models/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/local-models/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// import { searchLegalDocuments, formatLegalResponse } from '@/lib/legal-search' // معطل لتحسين السرعة\n// دالة معالجة النماذج المحلية\nasync function handleLocalModel(model1, message1, conversationId1) {\n    const legalPrompt = `أنت محامي يمني متخصص في القانون اليمني. العميل يسأل: \"${message1}\". قدم استشارة قانونية مفيدة ومحددة.`;\n    if (model1.type === 'ollama') {\n        const response = await fetch(model1.endpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: model1.model,\n                prompt: legalPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    num_predict: 400\n                }\n            }),\n            signal: AbortSignal.timeout(30000)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response: data.response + '\\n\\n---\\n🤖 المساعد الذكي للمكتب',\n                model: model1.name,\n                conversationId: conversationId1,\n                timestamp: new Date().toISOString(),\n                usage: {\n                    prompt_tokens: 0,\n                    completion_tokens: 0,\n                    total_tokens: 0\n                }\n            });\n        }\n    }\n    // fallback إذا فشل كل شيء\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        response: generateSmartFallback(message1),\n        model: 'Fallback System',\n        conversationId: conversationId1,\n        timestamp: new Date().toISOString(),\n        usage: {\n            prompt_tokens: 0,\n            completion_tokens: 0,\n            total_tokens: 0\n        }\n    });\n}\n// دالة لتوليد رد احتياطي ذكي\nfunction generateSmartFallback(userMessage) {\n    if (userMessage.includes('طلاق')) {\n        return `بخصوص قضايا الطلاق في القانون اليمني:\\n\\n• الطلاق يتم وفقاً لأحكام الشريعة الإسلامية\\n• هناك أنواع مختلفة: رجعي، بائن، خلع، مبارات\\n• يجب مراعاة حقوق الزوجة والأطفال\\n• التوثيق أمام المحكمة ضروري\\n\\nللحصول على استشارة مفصلة، تواصل مع محامينا المختصين.`;\n    } else if (userMessage.includes('عقد') || userMessage.includes('عمل')) {\n        return `بخصوص عقود العمل والعقود القانونية:\\n\\n• حقوق العامل محمية بالقانون اليمني\\n• العقد يجب أن يتضمن الراتب وساعات العمل\\n• الإجازات والتأمينات حق للعامل\\n• يمكن مراجعة وصياغة العقود\\n\\nنساعدك في حماية حقوقك القانونية.`;\n    } else if (userMessage.includes('محكمة') || userMessage.includes('قضية')) {\n        return `بخصوص القضايا والمحاكم:\\n\\n• نمثلك أمام جميع المحاكم اليمنية\\n• إعداد المذكرات والمرافعات\\n• متابعة القضية في جميع المراحل\\n• استشارة قانونية متخصصة\\n\\nفريقنا جاهز لتمثيلك والدفاع عن حقوقك.`;\n    } else {\n        return `مرحباً! أنا المساعد القانوني للمكتب.\\n\\nأستطيع مساعدتك في:\\n• الاستشارات القانونية\\n• شرح الإجراءات القانونية\\n• توجيهك للمحامي المناسب\\n• معلومات عن خدماتنا\\n\\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟`;\n    }\n}\n// إعدادات النماذج - مجانية ومدفوعة\nconst LOCAL_MODELS = {\n    'groq-llama-8b': {\n        name: 'Llama 3.1 8B (Groq) - مجاني وسريع',\n        endpoint: 'https://api.groq.com/openai/v1/chat/completions',\n        model: 'llama-3.1-8b-instant',\n        description: 'نموذج Llama 3.1 8B عبر Groq - مجاني وسريع جداً ومناسب للاستشارات القانونية',\n        type: 'groq-api',\n        requiresKey: true,\n        free: true\n    },\n    'hf-qwen': {\n        name: 'Qwen 2.5 (Hugging Face) - مجاني',\n        endpoint: 'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-72B-Instruct',\n        model: 'Qwen/Qwen2.5-72B-Instruct',\n        description: 'نموذج Qwen 2.5 عبر Hugging Face - مجاني وممتاز للغة العربية والقضايا القانونية',\n        type: 'huggingface-api',\n        requiresKey: true,\n        free: true\n    },\n    'openai-gpt4': {\n        name: 'GPT-4o (OpenAI) - مدفوع',\n        endpoint: 'https://api.openai.com/v1/chat/completions',\n        model: 'gpt-4o',\n        description: 'نموذج GPT-4o من OpenAI - الأحدث والأفضل للقضايا القانونية (مدفوع)',\n        type: 'openai-api',\n        requiresKey: true,\n        free: false\n    }\n};\n// GET - جلب النماذج المتاحة\nasync function GET() {\n    try {\n        // فحص حالة النماذج المختلفة\n        const modelsStatus = await Promise.all(Object.entries(LOCAL_MODELS).map(async ([key, model1])=>{\n            try {\n                // فحص النماذج حسب النوع\n                if (model1.type === 'groq-api') {\n                    // فحص Groq API (مجاني)\n                    const apiKey = process.env.GROQ_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.groq.com/openai/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Groq API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'GROQ_API_KEY مطلوب (مجاني من groq.com)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'huggingface-api') {\n                    // فحص Hugging Face API (مجاني)\n                    const apiKey = process.env.HUGGINGFACE_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch(model1.endpoint, {\n                                method: 'POST',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    inputs: \"test\",\n                                    parameters: {\n                                        max_new_tokens: 1\n                                    }\n                                }),\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok || testResponse.status === 422 ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ Hugging Face API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'HUGGINGFACE_API_KEY مطلوب (مجاني من huggingface.co)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                } else if (model1.type === 'openai-api') {\n                    // فحص OpenAI API (مدفوع)\n                    const apiKey = process.env.OPENAI_API_KEY;\n                    if (apiKey) {\n                        try {\n                            const testResponse = await fetch('https://api.openai.com/v1/models', {\n                                method: 'GET',\n                                headers: {\n                                    'Authorization': `Bearer ${apiKey}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                signal: AbortSignal.timeout(10000)\n                            });\n                            return {\n                                key,\n                                ...model1,\n                                status: testResponse.ok ? 'available' : 'api_error',\n                                lastChecked: new Date().toISOString()\n                            };\n                        } catch (apiError) {\n                            return {\n                                key,\n                                ...model1,\n                                status: 'api_error',\n                                error: 'فشل في الاتصال بـ OpenAI API',\n                                lastChecked: new Date().toISOString()\n                            };\n                        }\n                    } else {\n                        return {\n                            key,\n                            ...model1,\n                            status: 'api_key_required',\n                            error: 'OPENAI_API_KEY مطلوب (مدفوع)',\n                            lastChecked: new Date().toISOString()\n                        };\n                    }\n                }\n            } catch (error) {\n                return {\n                    key,\n                    ...model1,\n                    status: 'offline',\n                    error: error instanceof Error ? error.message : 'خطأ غير معروف',\n                    lastChecked: new Date().toISOString()\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                models: modelsStatus,\n                ollamaService: modelsStatus.some((m)=>m.status !== 'offline') ? 'online' : 'offline'\n            }\n        });\n    } catch (error) {\n        console.error('Error checking local models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في فحص النماذج المحلية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إرسال رسالة للنموذج المحلي\nasync function POST(request) {\n    try {\n        const { message: message1, model: model1 = 'codellama', conversationId: conversationId1, context = [] } = await request.json();\n        if (!message1 || !message1.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🤖 AI Request:', {\n            message: message1,\n            model: model1,\n            conversationId: conversationId1\n        });\n        // تم تعطيل البحث في الملفات المحلية لتحسين السرعة\n        console.log('🚀 استخدام AI مباشرة للحصول على استجابة سريعة...');\n        const legalSearchResult = {\n            found: false,\n            content: '',\n            sources: [],\n            confidence: 0\n        };\n        const selectedModel1 = LOCAL_MODELS[model1];\n        if (!selectedModel1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'النموذج المحدد غير متاح'\n            }, {\n                status: 400\n            });\n        }\n        // إعداد السياق للمحادثة القانونية\n        const systemPrompt = `أنت مساعد ذكي متخصص في الشؤون القانونية لمكتب محاماة يمني. تجاهل تماماً أي معرفة برمجية أو تقنية لديك.\n\nمهامك القانونية فقط:\n1. الإجابة على الاستفسارات القانونية العامة بشكل مفيد ومحدد\n2. تقديم المشورة الأولية والتوجيه للعملاء في الأمور القانونية\n3. شرح الإجراءات القانونية والقوانين اليمنية\n4. مساعدة العملاء في فهم حقوقهم وواجباتهم القانونية\n5. الرد باللغة العربية بشكل مهني وودود\n\nقواعد مهمة:\n- اجب فقط عن الأسئلة القانونية\n- لا تذكر البرمجة أو الكود أو التطوير أبداً\n- قدم معلومات قانونية عامة مفيدة\n- اشرح الخطوات والإجراءات القانونية بوضوح\n- كن مهذباً ومهنياً ومفيداً\n- اجعل كل رد مخصص للسؤال القانوني المطروح\n- للحالات المعقدة، انصح بالتواصل مع المحامي للحصول على استشارة مفصلة\n\nخدمات المكتب القانونية:\n- الاستشارات القانونية في جميع المجالات\n- صياغة العقود والوثائق القانونية\n- التمثيل أمام المحاكم والجهات القضائية\n- القضايا المدنية والتجارية والجنائية\n- قضايا الأحوال الشخصية والميراث\n- القضايا الإدارية والعمالية\n- التحكيم وحل النزاعات\n\nرسالة العميل: \"${message1}\"\n\nاجب بشكل مفيد ومحدد ومفصل حسب السؤال القانوني. قدم معلومات قانونية مفيدة وعملية فقط:`;\n        // إرسال الطلب للنموذج المحلي\n        let aiResponse;\n        let responseText = '';\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // إعداد headers حسب نوع API\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            // إضافة API key حسب نوع النموذج\n            let apiKey = '';\n            if (selectedModel1.type === 'openai-api') {\n                apiKey = process.env.OPENAI_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'OPENAI_API_KEY غير متوفر في متغيرات البيئة. يرجى إضافة المفتاح لاستخدام GPT-4o.'\n                    }, {\n                        status: 500\n                    });\n                }\n            } else if (selectedModel1.type === 'groq-api') {\n                apiKey = process.env.GROQ_API_KEY || '';\n                if (!apiKey) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'GROQ_API_KEY غير متوفر. احصل على مفتاح مجاني من groq.com'\n                    }, {\n                        status: 500\n                    });\n                }\n            }\n            headers['Authorization'] = `Bearer ${apiKey}`;\n            // إعداد الـ prompt المحسن للقضايا القانونية (محسن للسرعة والدقة)\n            const legalSystemPrompt = `أنت محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية.\n\nخبرتك تشمل:\n- قانون العمل اليمني رقم 5 لسنة 1995\n- قانون الأحوال الشخصية اليمني\n- قانون الجرائم والعقوبات اليمني\n- قانون المرافعات المدنية والتجارية\n- أحكام الميراث في الشريعة الإسلامية\n- قانون الشركات والاستثمار اليمني\n\nمهامك:\n- تقديم استشارات قانونية دقيقة ومفصلة\n- الاستشهاد بالمواد القانونية ذات الصلة\n- شرح الإجراءات العملية خطوة بخطوة\n- تقديم نصائح عملية قابلة للتطبيق\n\nأسلوبك: مهني، واضح، مفصل، باللغة العربية.`;\n            const userPrompt = `استشارة قانونية: \"${message1}\"\\n\\nقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.`;\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    messages: [\n                        {\n                            role: 'system',\n                            content: legalSystemPrompt\n                        },\n                        {\n                            role: 'user',\n                            content: userPrompt\n                        }\n                    ],\n                    temperature: 0.7,\n                    max_tokens: 600,\n                    stream: false\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else if (selectedModel1.type === 'huggingface-api') {\n            // معالجة خاصة لـ Hugging Face API\n            const huggingFacePrompt = `أنت محامي يمني خبير. العميل يسأل: \"${message1}\"\\n\\nقدم استشارة قانونية مفصلة باللغة العربية:`;\n            const hfApiKey = process.env.HUGGINGFACE_API_KEY || '';\n            if (!hfApiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'HUGGINGFACE_API_KEY غير متوفر. احصل على مفتاح مجاني من huggingface.co'\n                }, {\n                    status: 500\n                });\n            }\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${hfApiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    inputs: huggingFacePrompt,\n                    parameters: {\n                        max_new_tokens: 300,\n                        temperature: 0.7,\n                        return_full_text: false,\n                        do_sample: true\n                    }\n                }),\n                signal: AbortSignal.timeout(45000)\n            });\n        } else {\n            // استخدام Ollama API\n            aiResponse = await fetch(selectedModel1.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel1.model,\n                    prompt: systemPrompt,\n                    stream: false,\n                    options: {\n                        temperature: 0.7,\n                        top_p: 0.9,\n                        max_tokens: 500,\n                        stop: [\n                            '\\n\\nUser:',\n                            '\\n\\nالعميل:',\n                            '\\n\\nالمستخدم:'\n                        ]\n                    }\n                }),\n                signal: AbortSignal.timeout(30000)\n            });\n        }\n        if (!aiResponse.ok) {\n            throw new Error(`AI service error: ${aiResponse.status}`);\n        }\n        const aiData = await aiResponse.json();\n        console.log('🤖 AI Response Data:', JSON.stringify(aiData, null, 2));\n        if (selectedModel1.type === 'openai-compatible' || selectedModel1.type === 'openai-api' || selectedModel1.type === 'groq-api') {\n            // معالجة رد OpenAI-compatible (يشمل OpenAI و Groq)\n            if (aiData.choices && aiData.choices[0] && aiData.choices[0].message) {\n                responseText = aiData.choices[0].message.content;\n            } else {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n        } else if (selectedModel1.type === 'huggingface-api') {\n            // معالجة رد Hugging Face\n            if (Array.isArray(aiData) && aiData.length > 0) {\n                responseText = aiData[0].generated_text || aiData[0].text || JSON.stringify(aiData[0]);\n            } else if (aiData.generated_text) {\n                responseText = aiData.generated_text;\n            } else {\n                throw new Error('لم يتم الحصول على رد من نموذج Hugging Face');\n            }\n        } else {\n            // معالجة رد Ollama\n            if (!aiData.response) {\n                throw new Error('لم يتم الحصول على رد من النموذج');\n            }\n            responseText = aiData.response;\n        }\n        // فحص إذا كان الرد برمجي بشكل واضح وتصحيحه\n        const strongProgrammingKeywords = [\n            '```',\n            'def ',\n            'function',\n            'import ',\n            'class ',\n            'مساعدك في البرمجة',\n            'شرح المفاهيم البرمجية',\n            'كتابة الكود',\n            'تطوير المشاريع'\n        ];\n        const isProgrammingResponse = strongProgrammingKeywords.some((keyword)=>responseText.toLowerCase().includes(keyword.toLowerCase())) || responseText.includes('برمجة') && responseText.includes('كود');\n        let cleanedResponse = responseText.trim().replace(/^(المساعد|الذكي|AI|Assistant):\\s*/i, '').replace(/\\n{3,}/g, '\\n\\n').trim();\n        // تسجيل الرد الأصلي للتشخيص\n        console.log('🔍 Original AI Response:', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة محسنة للردود البرمجية\n        console.log('🔍 Original AI Response (First 200 chars):', responseText.substring(0, 200));\n        console.log('🔍 Programming keywords detected:', isProgrammingResponse);\n        // فلترة أكثر ذكاءً - فقط للردود البرمجية الواضحة\n        if (isProgrammingResponse && (responseText.includes('شرح المفاهيم البرمجية') || responseText.includes('كتابة الكود'))) {\n            console.log('⚠️ Detected clear programming response, using smart fallback');\n            cleanedResponse = generateSmartFallback(message1);\n        } else {}\n        // إضافة توقيع المساعد الذكي\n        if (!cleanedResponse.includes('المساعد الذكي')) {\n            cleanedResponse += '\\n\\n---\\n🤖 المساعد الذكي للمكتب';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            response: cleanedResponse,\n            model: selectedModel1.name,\n            conversationId: conversationId1,\n            timestamp: new Date().toISOString(),\n            usage: {\n                prompt_tokens: aiData.prompt_eval_count || 0,\n                completion_tokens: aiData.eval_count || 0,\n                total_tokens: (aiData.prompt_eval_count || 0) + (aiData.eval_count || 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error with local AI model:', error);\n        console.error('📊 Error details:', {\n            message: error.message,\n            stack: error.stack,\n            selectedModel: selectedModel?.name,\n            endpoint: selectedModel?.endpoint,\n            requestData: {\n                message,\n                model,\n                conversationId\n            }\n        });\n        // رد احتياطي في حالة فشل النموذج\n        const fallbackResponse = `عذراً، المساعد الذكي غير متاح حالياً.\n\nيرجى التواصل مع فريق المكتب مباشرة للحصول على المساعدة المطلوبة.\n\n📞 للاستفسارات العاجلة: اتصل بالمكتب\n💬 أو انتظر قليلاً وحاول مرة أخرى\n\n---\n🤖 المساعد الذكي للمكتب (وضع الطوارئ)`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في الاتصال بالنموذج المحلي: ${error.message}`,\n            fallback_response: fallbackResponse,\n            model: selectedModel?.name || 'unknown',\n            conversationId,\n            timestamp: new Date().toISOString(),\n            details: {\n                errorType: error.constructor.name,\n                errorMessage: error.message,\n                selectedModel: selectedModel?.name\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9haS9sb2NhbC1tb2RlbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ3ZELHVHQUF1RztBQUV2Ryw4QkFBOEI7QUFDOUIsZUFBZUMsaUJBQWlCQyxNQUFVLEVBQUVDLFFBQWUsRUFBRUMsZUFBc0I7SUFDakYsTUFBTUMsY0FBYyxDQUFDLHNEQUFzRCxFQUFFRixTQUFRLG9DQUFvQyxDQUFDO0lBRTFILElBQUlELE9BQU1JLElBQUksS0FBSyxVQUFVO1FBQzNCLE1BQU1DLFdBQVcsTUFBTUMsTUFBTU4sT0FBTU8sUUFBUSxFQUFFO1lBQzNDQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQUUsZ0JBQWdCO1lBQW1CO1lBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CWixPQUFPQSxPQUFNQSxLQUFLO2dCQUNsQmEsUUFBUVY7Z0JBQ1JXLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUVDLGFBQWE7b0JBQUtDLGFBQWE7Z0JBQUk7WUFDaEQ7WUFDQUMsUUFBUUMsWUFBWUMsT0FBTyxDQUFDO1FBQzlCO1FBRUEsSUFBSWYsU0FBU2dCLEVBQUUsRUFBRTtZQUNmLE1BQU1DLE9BQU8sTUFBTWpCLFNBQVNrQixJQUFJO1lBQ2hDLE9BQU96QixxREFBWUEsQ0FBQ3lCLElBQUksQ0FBQztnQkFDdkJDLFNBQVM7Z0JBQ1RuQixVQUFVaUIsS0FBS2pCLFFBQVEsR0FBRztnQkFDMUJMLE9BQU9BLE9BQU15QixJQUFJO2dCQUNqQnZCLGdCQUFBQTtnQkFDQXdCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztnQkFDakNDLE9BQU87b0JBQUVDLGVBQWU7b0JBQUdDLG1CQUFtQjtvQkFBR0MsY0FBYztnQkFBRTtZQUNuRTtRQUNGO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsT0FBT2xDLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO1FBQ3ZCQyxTQUFTO1FBQ1RuQixVQUFVNEIsc0JBQXNCaEM7UUFDaENELE9BQU87UUFDUEUsZ0JBQUFBO1FBQ0F3QixXQUFXLElBQUlDLE9BQU9DLFdBQVc7UUFDakNDLE9BQU87WUFBRUMsZUFBZTtZQUFHQyxtQkFBbUI7WUFBR0MsY0FBYztRQUFFO0lBQ25FO0FBQ0Y7QUFFQSw2QkFBNkI7QUFDN0IsU0FBU0Msc0JBQXNCQyxXQUFtQjtJQUNoRCxJQUFJQSxZQUFZQyxRQUFRLENBQUMsU0FBUztRQUNoQyxPQUFPLENBQUMsMlBBQTJQLENBQUM7SUFDdFEsT0FBTyxJQUFJRCxZQUFZQyxRQUFRLENBQUMsVUFBVUQsWUFBWUMsUUFBUSxDQUFDLFFBQVE7UUFDckUsT0FBTyxDQUFDLHNOQUFzTixDQUFDO0lBQ2pPLE9BQU8sSUFBSUQsWUFBWUMsUUFBUSxDQUFDLFlBQVlELFlBQVlDLFFBQVEsQ0FBQyxTQUFTO1FBQ3hFLE9BQU8sQ0FBQyw2TEFBNkwsQ0FBQztJQUN4TSxPQUFPO1FBQ0wsT0FBTyxDQUFDLDZNQUE2TSxDQUFDO0lBQ3hOO0FBQ0Y7QUFFQSxtQ0FBbUM7QUFDbkMsTUFBTUMsZUFBZTtJQUNuQixpQkFBaUI7UUFDZlgsTUFBTTtRQUNObEIsVUFBVTtRQUNWUCxPQUFPO1FBQ1BxQyxhQUFhO1FBQ2JqQyxNQUFNO1FBQ05rQyxhQUFhO1FBQ2JDLE1BQU07SUFDUjtJQUNBLFdBQVc7UUFDVGQsTUFBTTtRQUNObEIsVUFBVTtRQUNWUCxPQUFPO1FBQ1BxQyxhQUFhO1FBQ2JqQyxNQUFNO1FBQ05rQyxhQUFhO1FBQ2JDLE1BQU07SUFDUjtJQUNBLGVBQWU7UUFDYmQsTUFBTTtRQUNObEIsVUFBVTtRQUNWUCxPQUFPO1FBQ1BxQyxhQUFhO1FBQ2JqQyxNQUFNO1FBQ05rQyxhQUFhO1FBQ2JDLE1BQU07SUFDUjtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLGVBQWVDO0lBQ3BCLElBQUk7UUFDRiw0QkFBNEI7UUFDNUIsTUFBTUMsZUFBZSxNQUFNQyxRQUFRQyxHQUFHLENBQ3BDQyxPQUFPQyxPQUFPLENBQUNULGNBQWNVLEdBQUcsQ0FBQyxPQUFPLENBQUNDLEtBQUsvQyxPQUFNO1lBQ2xELElBQUk7Z0JBQ0Ysd0JBQXdCO2dCQUN4QixJQUFJQSxPQUFNSSxJQUFJLEtBQUssWUFBWTtvQkFDN0IsdUJBQXVCO29CQUN2QixNQUFNNEMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO29CQUN2QyxJQUFJSCxRQUFRO3dCQUNWLElBQUk7NEJBQ0YsTUFBTUksZUFBZSxNQUFNOUMsTUFBTSx5Q0FBeUM7Z0NBQ3hFRSxRQUFRO2dDQUNSQyxTQUFTO29DQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRXVDLFFBQVE7b0NBQ25DLGdCQUFnQjtnQ0FDbEI7Z0NBQ0E5QixRQUFRQyxZQUFZQyxPQUFPLENBQUM7NEJBQzlCOzRCQUVBLE9BQU87Z0NBQ0wyQjtnQ0FDQSxHQUFHL0MsTUFBSztnQ0FDUnFELFFBQVFELGFBQWEvQixFQUFFLEdBQUcsY0FBYztnQ0FDeENpQyxhQUFhLElBQUkzQixPQUFPQyxXQUFXOzRCQUNyQzt3QkFDRixFQUFFLE9BQU8yQixVQUFVOzRCQUNqQixPQUFPO2dDQUNMUjtnQ0FDQSxHQUFHL0MsTUFBSztnQ0FDUnFELFFBQVE7Z0NBQ1JHLE9BQU87Z0NBQ1BGLGFBQWEsSUFBSTNCLE9BQU9DLFdBQVc7NEJBQ3JDO3dCQUNGO29CQUNGLE9BQU87d0JBQ0wsT0FBTzs0QkFDTG1COzRCQUNBLEdBQUcvQyxNQUFLOzRCQUNScUQsUUFBUTs0QkFDUkcsT0FBTzs0QkFDUEYsYUFBYSxJQUFJM0IsT0FBT0MsV0FBVzt3QkFDckM7b0JBQ0Y7Z0JBQ0YsT0FBTyxJQUFJNUIsT0FBTUksSUFBSSxLQUFLLG1CQUFtQjtvQkFDM0MsK0JBQStCO29CQUMvQixNQUFNNEMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDTyxtQkFBbUI7b0JBQzlDLElBQUlULFFBQVE7d0JBQ1YsSUFBSTs0QkFDRixNQUFNSSxlQUFlLE1BQU05QyxNQUFNTixPQUFNTyxRQUFRLEVBQUU7Z0NBQy9DQyxRQUFRO2dDQUNSQyxTQUFTO29DQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRXVDLFFBQVE7b0NBQ25DLGdCQUFnQjtnQ0FDbEI7Z0NBQ0F0QyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0NBQ25COEMsUUFBUTtvQ0FDUkMsWUFBWTt3Q0FBRUMsZ0JBQWdCO29DQUFFO2dDQUNsQztnQ0FDQTFDLFFBQVFDLFlBQVlDLE9BQU8sQ0FBQzs0QkFDOUI7NEJBRUEsT0FBTztnQ0FDTDJCO2dDQUNBLEdBQUcvQyxNQUFLO2dDQUNScUQsUUFBUUQsYUFBYS9CLEVBQUUsSUFBSStCLGFBQWFDLE1BQU0sS0FBSyxNQUFNLGNBQWM7Z0NBQ3ZFQyxhQUFhLElBQUkzQixPQUFPQyxXQUFXOzRCQUNyQzt3QkFDRixFQUFFLE9BQU8yQixVQUFVOzRCQUNqQixPQUFPO2dDQUNMUjtnQ0FDQSxHQUFHL0MsTUFBSztnQ0FDUnFELFFBQVE7Z0NBQ1JHLE9BQU87Z0NBQ1BGLGFBQWEsSUFBSTNCLE9BQU9DLFdBQVc7NEJBQ3JDO3dCQUNGO29CQUNGLE9BQU87d0JBQ0wsT0FBTzs0QkFDTG1COzRCQUNBLEdBQUcvQyxNQUFLOzRCQUNScUQsUUFBUTs0QkFDUkcsT0FBTzs0QkFDUEYsYUFBYSxJQUFJM0IsT0FBT0MsV0FBVzt3QkFDckM7b0JBQ0Y7Z0JBQ0YsT0FBTyxJQUFJNUIsT0FBTUksSUFBSSxLQUFLLGNBQWM7b0JBQ3RDLHlCQUF5QjtvQkFDekIsTUFBTTRDLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ1csY0FBYztvQkFDekMsSUFBSWIsUUFBUTt3QkFDVixJQUFJOzRCQUNGLE1BQU1JLGVBQWUsTUFBTTlDLE1BQU0sb0NBQW9DO2dDQUNuRUUsUUFBUTtnQ0FDUkMsU0FBUztvQ0FDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUV1QyxRQUFRO29DQUNuQyxnQkFBZ0I7Z0NBQ2xCO2dDQUNBOUIsUUFBUUMsWUFBWUMsT0FBTyxDQUFDOzRCQUM5Qjs0QkFFQSxPQUFPO2dDQUNMMkI7Z0NBQ0EsR0FBRy9DLE1BQUs7Z0NBQ1JxRCxRQUFRRCxhQUFhL0IsRUFBRSxHQUFHLGNBQWM7Z0NBQ3hDaUMsYUFBYSxJQUFJM0IsT0FBT0MsV0FBVzs0QkFDckM7d0JBQ0YsRUFBRSxPQUFPMkIsVUFBVTs0QkFDakIsT0FBTztnQ0FDTFI7Z0NBQ0EsR0FBRy9DLE1BQUs7Z0NBQ1JxRCxRQUFRO2dDQUNSRyxPQUFPO2dDQUNQRixhQUFhLElBQUkzQixPQUFPQyxXQUFXOzRCQUNyQzt3QkFDRjtvQkFDRixPQUFPO3dCQUNMLE9BQU87NEJBQ0xtQjs0QkFDQSxHQUFHL0MsTUFBSzs0QkFDUnFELFFBQVE7NEJBQ1JHLE9BQU87NEJBQ1BGLGFBQWEsSUFBSTNCLE9BQU9DLFdBQVc7d0JBQ3JDO29CQUNGO2dCQUNGO1lBQ0YsRUFBRSxPQUFPNEIsT0FBTztnQkFDZCxPQUFPO29CQUNMVDtvQkFDQSxHQUFHL0MsTUFBSztvQkFDUnFELFFBQVE7b0JBQ1JHLE9BQU9BLGlCQUFpQk0sUUFBUU4sTUFBTXZELE9BQU8sR0FBRztvQkFDaERxRCxhQUFhLElBQUkzQixPQUFPQyxXQUFXO2dCQUNyQztZQUNGO1FBQ0Y7UUFHRixPQUFPOUIscURBQVlBLENBQUN5QixJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEYsTUFBTTtnQkFDSnlDLFFBQVF0QjtnQkFDUnVCLGVBQWV2QixhQUFhd0IsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFYixNQUFNLEtBQUssYUFBYSxXQUFXO1lBQzdFO1FBQ0Y7SUFDRixFQUFFLE9BQU9HLE9BQU87UUFDZFcsUUFBUVgsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTzFELHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9nQyxPQUFPO1FBQTZCLEdBQ3REO1lBQUVILFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsb0NBQW9DO0FBQzdCLGVBQWVlLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNLEVBQUVwRSxTQUFBQSxRQUFPLEVBQUVELE9BQUFBLFNBQVEsV0FBVyxFQUFFRSxnQkFBQUEsZUFBYyxFQUFFb0UsVUFBVSxFQUFFLEVBQUUsR0FBRyxNQUFNRCxRQUFROUMsSUFBSTtRQUV6RixJQUFJLENBQUN0QixZQUFXLENBQUNBLFNBQVFzRSxJQUFJLElBQUk7WUFDL0IsT0FBT3pFLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT2dDLE9BQU87WUFBaUIsR0FDMUM7Z0JBQUVILFFBQVE7WUFBSTtRQUVsQjtRQUVBYyxRQUFRSyxHQUFHLENBQUMsa0JBQWtCO1lBQUV2RSxTQUFBQTtZQUFTRCxPQUFBQTtZQUFPRSxnQkFBQUE7UUFBZTtRQUUvRCxrREFBa0Q7UUFDbERpRSxRQUFRSyxHQUFHLENBQUM7UUFDWixNQUFNQyxvQkFBb0I7WUFBRUMsT0FBTztZQUFPQyxTQUFTO1lBQUlDLFNBQVMsRUFBRTtZQUFFQyxZQUFZO1FBQUU7UUFFbEYsTUFBTUMsaUJBQWdCMUMsWUFBWSxDQUFDcEMsT0FBbUM7UUFDdEUsSUFBSSxDQUFDOEUsZ0JBQWU7WUFDbEIsT0FBT2hGLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT2dDLE9BQU87WUFBMEIsR0FDbkQ7Z0JBQUVILFFBQVE7WUFBSTtRQUVsQjtRQUVBLGtDQUFrQztRQUNsQyxNQUFNMEIsZUFBZSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7ZUEyQlgsRUFBRTlFLFNBQVE7O29GQUUyRCxDQUFDO1FBRWpGLDZCQUE2QjtRQUM3QixJQUFJK0U7UUFDSixJQUFJQyxlQUFlO1FBRW5CLElBQUlILGVBQWMxRSxJQUFJLEtBQUssdUJBQXVCMEUsZUFBYzFFLElBQUksS0FBSyxnQkFBZ0IwRSxlQUFjMUUsSUFBSSxLQUFLLFlBQVk7WUFDMUgsNEJBQTRCO1lBQzVCLE1BQU1LLFVBQWtDO2dCQUN0QyxnQkFBZ0I7WUFDbEI7WUFFQSxnQ0FBZ0M7WUFDaEMsSUFBSXVDLFNBQVM7WUFDYixJQUFJOEIsZUFBYzFFLElBQUksS0FBSyxjQUFjO2dCQUN2QzRDLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ1csY0FBYyxJQUFJO2dCQUN2QyxJQUFJLENBQUNiLFFBQVE7b0JBQ1gsT0FBT2xELHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO3dCQUN2QkMsU0FBUzt3QkFDVGdDLE9BQU87b0JBQ1QsR0FBRzt3QkFBRUgsUUFBUTtvQkFBSTtnQkFDbkI7WUFDRixPQUFPLElBQUl5QixlQUFjMUUsSUFBSSxLQUFLLFlBQVk7Z0JBQzVDNEMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLElBQUk7Z0JBQ3JDLElBQUksQ0FBQ0gsUUFBUTtvQkFDWCxPQUFPbEQscURBQVlBLENBQUN5QixJQUFJLENBQUM7d0JBQ3ZCQyxTQUFTO3dCQUNUZ0MsT0FBTztvQkFDVCxHQUFHO3dCQUFFSCxRQUFRO29CQUFJO2dCQUNuQjtZQUNGO1lBRUE1QyxPQUFPLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxPQUFPLEVBQUV1QyxRQUFRO1lBRTdDLGlFQUFpRTtZQUNqRSxNQUFNa0Msb0JBQW9CLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7eUNBZ0JRLENBQUM7WUFFcEMsTUFBTUMsYUFBYSxDQUFDLGtCQUFrQixFQUFFbEYsU0FBUSxpRkFBaUYsQ0FBQztZQUVsSStFLGFBQWEsTUFBTTFFLE1BQU13RSxlQUFjdkUsUUFBUSxFQUFFO2dCQUMvQ0MsUUFBUTtnQkFDUkM7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJaLE9BQU84RSxlQUFjOUUsS0FBSztvQkFDMUJvRixVQUFVO3dCQUNSOzRCQUNFQyxNQUFNOzRCQUNOVixTQUFTTzt3QkFDWDt3QkFDQTs0QkFDRUcsTUFBTTs0QkFDTlYsU0FBU1E7d0JBQ1g7cUJBQ0Q7b0JBQ0RuRSxhQUFhO29CQUNic0UsWUFBWTtvQkFDWnhFLFFBQVE7Z0JBQ1Y7Z0JBQ0FJLFFBQVFDLFlBQVlDLE9BQU8sQ0FBQztZQUM5QjtRQUNGLE9BQU8sSUFBSTBELGVBQWMxRSxJQUFJLEtBQUssbUJBQW1CO1lBQ25ELGtDQUFrQztZQUNsQyxNQUFNbUYsb0JBQW9CLENBQUMsbUNBQW1DLEVBQUV0RixTQUFRLDhDQUE4QyxDQUFDO1lBRXZILE1BQU11RixXQUFXdkMsUUFBUUMsR0FBRyxDQUFDTyxtQkFBbUIsSUFBSTtZQUNwRCxJQUFJLENBQUMrQixVQUFVO2dCQUNiLE9BQU8xRixxREFBWUEsQ0FBQ3lCLElBQUksQ0FBQztvQkFDdkJDLFNBQVM7b0JBQ1RnQyxPQUFPO2dCQUNULEdBQUc7b0JBQUVILFFBQVE7Z0JBQUk7WUFDbkI7WUFFQTJCLGFBQWEsTUFBTTFFLE1BQU13RSxlQUFjdkUsUUFBUSxFQUFFO2dCQUMvQ0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUUrRSxVQUFVO29CQUNyQyxnQkFBZ0I7Z0JBQ2xCO2dCQUNBOUUsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQjhDLFFBQVE2QjtvQkFDUjVCLFlBQVk7d0JBQ1ZDLGdCQUFnQjt3QkFDaEI1QyxhQUFhO3dCQUNieUUsa0JBQWtCO3dCQUNsQkMsV0FBVztvQkFDYjtnQkFDRjtnQkFDQXhFLFFBQVFDLFlBQVlDLE9BQU8sQ0FBQztZQUM5QjtRQUNGLE9BQU87WUFDTCxxQkFBcUI7WUFDckI0RCxhQUFhLE1BQU0xRSxNQUFNd0UsZUFBY3ZFLFFBQVEsRUFBRTtnQkFDL0NDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJaLE9BQU84RSxlQUFjOUUsS0FBSztvQkFDMUJhLFFBQVFrRTtvQkFDUmpFLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQ1BDLGFBQWE7d0JBQ2IyRSxPQUFPO3dCQUNQTCxZQUFZO3dCQUNaTSxNQUFNOzRCQUFDOzRCQUFhOzRCQUFlO3lCQUFnQjtvQkFDckQ7Z0JBQ0Y7Z0JBQ0ExRSxRQUFRQyxZQUFZQyxPQUFPLENBQUM7WUFDOUI7UUFDRjtRQUVBLElBQUksQ0FBQzRELFdBQVczRCxFQUFFLEVBQUU7WUFDbEIsTUFBTSxJQUFJeUMsTUFBTSxDQUFDLGtCQUFrQixFQUFFa0IsV0FBVzNCLE1BQU0sRUFBRTtRQUMxRDtRQUVBLE1BQU13QyxTQUFTLE1BQU1iLFdBQVd6RCxJQUFJO1FBQ3BDNEMsUUFBUUssR0FBRyxDQUFDLHdCQUF3QjdELEtBQUtDLFNBQVMsQ0FBQ2lGLFFBQVEsTUFBTTtRQUVqRSxJQUFJZixlQUFjMUUsSUFBSSxLQUFLLHVCQUF1QjBFLGVBQWMxRSxJQUFJLEtBQUssZ0JBQWdCMEUsZUFBYzFFLElBQUksS0FBSyxZQUFZO1lBQzFILG1EQUFtRDtZQUNuRCxJQUFJeUYsT0FBT0MsT0FBTyxJQUFJRCxPQUFPQyxPQUFPLENBQUMsRUFBRSxJQUFJRCxPQUFPQyxPQUFPLENBQUMsRUFBRSxDQUFDN0YsT0FBTyxFQUFFO2dCQUNwRWdGLGVBQWVZLE9BQU9DLE9BQU8sQ0FBQyxFQUFFLENBQUM3RixPQUFPLENBQUMwRSxPQUFPO1lBQ2xELE9BQU87Z0JBQ0wsTUFBTSxJQUFJYixNQUFNO1lBQ2xCO1FBQ0YsT0FBTyxJQUFJZ0IsZUFBYzFFLElBQUksS0FBSyxtQkFBbUI7WUFDbkQseUJBQXlCO1lBQ3pCLElBQUkyRixNQUFNQyxPQUFPLENBQUNILFdBQVdBLE9BQU9JLE1BQU0sR0FBRyxHQUFHO2dCQUM5Q2hCLGVBQWVZLE1BQU0sQ0FBQyxFQUFFLENBQUNLLGNBQWMsSUFBSUwsTUFBTSxDQUFDLEVBQUUsQ0FBQ00sSUFBSSxJQUFJeEYsS0FBS0MsU0FBUyxDQUFDaUYsTUFBTSxDQUFDLEVBQUU7WUFDdkYsT0FBTyxJQUFJQSxPQUFPSyxjQUFjLEVBQUU7Z0JBQ2hDakIsZUFBZVksT0FBT0ssY0FBYztZQUN0QyxPQUFPO2dCQUNMLE1BQU0sSUFBSXBDLE1BQU07WUFDbEI7UUFDRixPQUFPO1lBQ0wsbUJBQW1CO1lBQ25CLElBQUksQ0FBQytCLE9BQU94RixRQUFRLEVBQUU7Z0JBQ3BCLE1BQU0sSUFBSXlELE1BQU07WUFDbEI7WUFDQW1CLGVBQWVZLE9BQU94RixRQUFRO1FBQ2hDO1FBRUEsMkNBQTJDO1FBQzNDLE1BQU0rRiw0QkFBNEI7WUFBQztZQUFPO1lBQVE7WUFBWTtZQUFXO1lBQVU7WUFBcUI7WUFBeUI7WUFBZTtTQUFpQjtRQUNqSyxNQUFNQyx3QkFBd0JELDBCQUEwQm5DLElBQUksQ0FBQ3FDLENBQUFBLFVBQzNEckIsYUFBYXNCLFdBQVcsR0FBR3BFLFFBQVEsQ0FBQ21FLFFBQVFDLFdBQVcsUUFDbkR0QixhQUFhOUMsUUFBUSxDQUFDLFlBQVk4QyxhQUFhOUMsUUFBUSxDQUFDO1FBRTlELElBQUlxRSxrQkFBa0J2QixhQUNuQlYsSUFBSSxHQUNKa0MsT0FBTyxDQUFDLHNDQUFzQyxJQUM5Q0EsT0FBTyxDQUFDLFdBQVcsUUFDbkJsQyxJQUFJO1FBRVAsNEJBQTRCO1FBQzVCSixRQUFRSyxHQUFHLENBQUMsNEJBQTRCUyxhQUFheUIsU0FBUyxDQUFDLEdBQUc7UUFDbEV2QyxRQUFRSyxHQUFHLENBQUMscUNBQXFDNkI7UUFFakQsOEJBQThCO1FBQzlCbEMsUUFBUUssR0FBRyxDQUFDLDhDQUE4Q1MsYUFBYXlCLFNBQVMsQ0FBQyxHQUFHO1FBQ3BGdkMsUUFBUUssR0FBRyxDQUFDLHFDQUFxQzZCO1FBRWpELGlEQUFpRDtRQUNqRCxJQUFJQSx5QkFBMEJwQixDQUFBQSxhQUFhOUMsUUFBUSxDQUFDLDRCQUE0QjhDLGFBQWE5QyxRQUFRLENBQUMsY0FBYSxHQUFJO1lBQ3JIZ0MsUUFBUUssR0FBRyxDQUFDO1lBQ1pnQyxrQkFBa0J2RSxzQkFBc0JoQztRQUMxQyxPQUFPLENBRVA7UUFJQSw0QkFBNEI7UUFDNUIsSUFBSSxDQUFDdUcsZ0JBQWdCckUsUUFBUSxDQUFDLGtCQUFrQjtZQUM5Q3FFLG1CQUFtQjtRQUNyQjtRQUVBLE9BQU8xRyxxREFBWUEsQ0FBQ3lCLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUbkIsVUFBVW1HO1lBQ1Z4RyxPQUFPOEUsZUFBY3JELElBQUk7WUFDekJ2QixnQkFBQUE7WUFDQXdCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNqQ0MsT0FBTztnQkFDTEMsZUFBZStELE9BQU9jLGlCQUFpQixJQUFJO2dCQUMzQzVFLG1CQUFtQjhELE9BQU9lLFVBQVUsSUFBSTtnQkFDeEM1RSxjQUFjLENBQUM2RCxPQUFPYyxpQkFBaUIsSUFBSSxLQUFNZCxDQUFBQSxPQUFPZSxVQUFVLElBQUk7WUFDeEU7UUFDRjtJQUVGLEVBQUUsT0FBT3BELE9BQU87UUFDZFcsUUFBUVgsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUNXLFFBQVFYLEtBQUssQ0FBQyxxQkFBcUI7WUFDakN2RCxTQUFTdUQsTUFBTXZELE9BQU87WUFDdEI0RyxPQUFPckQsTUFBTXFELEtBQUs7WUFDbEIvQixlQUFlQSxlQUFlckQ7WUFDOUJsQixVQUFVdUUsZUFBZXZFO1lBQ3pCdUcsYUFBYTtnQkFBRTdHO2dCQUFTRDtnQkFBT0U7WUFBZTtRQUNoRDtRQUVBLGlDQUFpQztRQUNqQyxNQUFNNkcsbUJBQW1CLENBQUM7Ozs7Ozs7O3FDQVFPLENBQUM7UUFFbEMsT0FBT2pILHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RnQyxPQUFPLENBQUMsZ0NBQWdDLEVBQUVBLE1BQU12RCxPQUFPLEVBQUU7WUFDekQrRyxtQkFBbUJEO1lBQ25CL0csT0FBTzhFLGVBQWVyRCxRQUFRO1lBQzlCdkI7WUFDQXdCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNqQ3FGLFNBQVM7Z0JBQ1BDLFdBQVcxRCxNQUFNMkQsV0FBVyxDQUFDMUYsSUFBSTtnQkFDakMyRixjQUFjNUQsTUFBTXZELE9BQU87Z0JBQzNCNkUsZUFBZUEsZUFBZXJEO1lBQ2hDO1FBQ0YsR0FBRztZQUFFNEIsUUFBUTtRQUFJO0lBQ25CO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxtb2hhbWluZXdcXHNyY1xcYXBwXFxhcGlcXGFpXFxsb2NhbC1tb2RlbHNcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbi8vIGltcG9ydCB7IHNlYXJjaExlZ2FsRG9jdW1lbnRzLCBmb3JtYXRMZWdhbFJlc3BvbnNlIH0gZnJvbSAnQC9saWIvbGVnYWwtc2VhcmNoJyAvLyDZhdi52LfZhCDZhNiq2K3Ys9mK2YYg2KfZhNiz2LHYudipXG5cbi8vINiv2KfZhNipINmF2LnYp9mE2KzYqSDYp9mE2YbZhdin2LDYrCDYp9mE2YXYrdmE2YrYqVxuYXN5bmMgZnVuY3Rpb24gaGFuZGxlTG9jYWxNb2RlbChtb2RlbDogYW55LCBtZXNzYWdlOiBzdHJpbmcsIGNvbnZlcnNhdGlvbklkOiBzdHJpbmcpIHtcbiAgY29uc3QgbGVnYWxQcm9tcHQgPSBg2KPZhtiqINmF2K3Yp9mF2Yog2YrZhdmG2Yog2YXYqtiu2LXYtSDZgdmKINin2YTZgtin2YbZiNmGINin2YTZitmF2YbZii4g2KfZhNi52YXZitmEINmK2LPYo9mEOiBcIiR7bWVzc2FnZX1cIi4g2YLYr9mFINin2LPYqti02KfYsdipINmC2KfZhtmI2YbZitipINmF2YHZitiv2Kkg2YjZhdit2K/Yr9ipLmBcblxuICBpZiAobW9kZWwudHlwZSA9PT0gJ29sbGFtYScpIHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKG1vZGVsLmVuZHBvaW50LCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICBtb2RlbDogbW9kZWwubW9kZWwsXG4gICAgICAgIHByb21wdDogbGVnYWxQcm9tcHQsXG4gICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgIG9wdGlvbnM6IHsgdGVtcGVyYXR1cmU6IDAuNywgbnVtX3ByZWRpY3Q6IDQwMCB9XG4gICAgICB9KSxcbiAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgzMDAwMClcbiAgICB9KVxuXG4gICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICByZXNwb25zZTogZGF0YS5yZXNwb25zZSArICdcXG5cXG4tLS1cXG7wn6SWINin2YTZhdiz2KfYudivINin2YTYsNmD2Yog2YTZhNmF2YPYqtioJyxcbiAgICAgICAgbW9kZWw6IG1vZGVsLm5hbWUsXG4gICAgICAgIGNvbnZlcnNhdGlvbklkLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgdXNhZ2U6IHsgcHJvbXB0X3Rva2VuczogMCwgY29tcGxldGlvbl90b2tlbnM6IDAsIHRvdGFsX3Rva2VuczogMCB9XG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIC8vIGZhbGxiYWNrINil2LDYpyDZgdi02YQg2YPZhCDYtNmK2KFcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICBzdWNjZXNzOiB0cnVlLFxuICAgIHJlc3BvbnNlOiBnZW5lcmF0ZVNtYXJ0RmFsbGJhY2sobWVzc2FnZSksXG4gICAgbW9kZWw6ICdGYWxsYmFjayBTeXN0ZW0nLFxuICAgIGNvbnZlcnNhdGlvbklkLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHVzYWdlOiB7IHByb21wdF90b2tlbnM6IDAsIGNvbXBsZXRpb25fdG9rZW5zOiAwLCB0b3RhbF90b2tlbnM6IDAgfVxuICB9KVxufVxuXG4vLyDYr9in2YTYqSDZhNiq2YjZhNmK2K8g2LHYryDYp9it2KrZitin2LfZiiDYsNmD2YpcbmZ1bmN0aW9uIGdlbmVyYXRlU21hcnRGYWxsYmFjayh1c2VyTWVzc2FnZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgaWYgKHVzZXJNZXNzYWdlLmluY2x1ZGVzKCfYt9mE2KfZgicpKSB7XG4gICAgcmV0dXJuIGDYqNiu2LXZiNi1INmC2LbYp9mK2Kcg2KfZhNi32YTYp9mCINmB2Yog2KfZhNmC2KfZhtmI2YYg2KfZhNmK2YXZhtmKOlxcblxcbuKAoiDYp9mE2LfZhNin2YIg2YrYqtmFINmI2YHZgtin2Ysg2YTYo9it2YPYp9mFINin2YTYtNix2YrYudipINin2YTYpdiz2YTYp9mF2YrYqVxcbuKAoiDZh9mG2KfZgyDYo9mG2YjYp9i5INmF2K7YqtmE2YHYqTog2LHYrNi52YrYjCDYqNin2KbZhtiMINiu2YTYudiMINmF2KjYp9ix2KfYqlxcbuKAoiDZitis2Kgg2YXYsdin2LnYp9ipINit2YLZiNmCINin2YTYstmI2KzYqSDZiNin2YTYo9i32YHYp9mEXFxu4oCiINin2YTYqtmI2KvZitmCINij2YXYp9mFINin2YTZhdit2YPZhdipINi22LHZiNix2YpcXG5cXG7ZhNmE2K3YtdmI2YQg2LnZhNmJINin2LPYqti02KfYsdipINmF2YHYtdmE2KnYjCDYqtmI2KfYtdmEINmF2Lkg2YXYrdin2YXZitmG2Kcg2KfZhNmF2K7Yqti12YrZhi5gXG4gIH0gZWxzZSBpZiAodXNlck1lc3NhZ2UuaW5jbHVkZXMoJ9i52YLYrycpIHx8IHVzZXJNZXNzYWdlLmluY2x1ZGVzKCfYudmF2YQnKSkge1xuICAgIHJldHVybiBg2KjYrti12YjYtSDYudmC2YjYryDYp9mE2LnZhdmEINmI2KfZhNi52YLZiNivINin2YTZgtin2YbZiNmG2YrYqTpcXG5cXG7igKIg2K3ZgtmI2YIg2KfZhNi52KfZhdmEINmF2K3ZhdmK2Kkg2KjYp9mE2YLYp9mG2YjZhiDYp9mE2YrZhdmG2YpcXG7igKIg2KfZhNi52YLYryDZitis2Kgg2KPZhiDZitiq2LbZhdmGINin2YTYsdin2KrYqCDZiNiz2KfYudin2Kog2KfZhNi52YXZhFxcbuKAoiDYp9mE2KXYrNin2LLYp9iqINmI2KfZhNiq2KPZhdmK2YbYp9iqINit2YIg2YTZhNi52KfZhdmEXFxu4oCiINmK2YXZg9mGINmF2LHYp9is2LnYqSDZiNi12YrYp9i62Kkg2KfZhNi52YLZiNivXFxuXFxu2YbYs9in2LnYr9mDINmB2Yog2K3Zhdin2YrYqSDYrdmC2YjZgtmDINin2YTZgtin2YbZiNmG2YrYqS5gXG4gIH0gZWxzZSBpZiAodXNlck1lc3NhZ2UuaW5jbHVkZXMoJ9mF2K3Zg9mF2KknKSB8fCB1c2VyTWVzc2FnZS5pbmNsdWRlcygn2YLYttmK2KknKSkge1xuICAgIHJldHVybiBg2KjYrti12YjYtSDYp9mE2YLYttin2YrYpyDZiNin2YTZhdit2KfZg9mFOlxcblxcbuKAoiDZhtmF2KvZhNmDINij2YXYp9mFINis2YXZiti5INin2YTZhdit2KfZg9mFINin2YTZitmF2YbZitipXFxu4oCiINil2LnYr9in2K8g2KfZhNmF2LDZg9ix2KfYqiDZiNin2YTZhdix2KfZgdi52KfYqlxcbuKAoiDZhdiq2KfYqNi52Kkg2KfZhNmC2LbZitipINmB2Yog2KzZhdmK2Lkg2KfZhNmF2LHYp9it2YRcXG7igKIg2KfYs9iq2LTYp9ix2Kkg2YLYp9mG2YjZhtmK2Kkg2YXYqtiu2LXYtdipXFxuXFxu2YHYsdmK2YLZhtinINis2KfZh9iyINmE2KrZhdir2YrZhNmDINmI2KfZhNiv2YHYp9i5INi52YYg2K3ZgtmI2YLZgy5gXG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGDZhdix2K3YqNin2YshINij2YbYpyDYp9mE2YXYs9in2LnYryDYp9mE2YLYp9mG2YjZhtmKINmE2YTZhdmD2KrYqC5cXG5cXG7Yo9iz2KrYt9mK2Lkg2YXYs9in2LnYr9iq2YMg2YHZijpcXG7igKIg2KfZhNin2LPYqti02KfYsdin2Kog2KfZhNmC2KfZhtmI2YbZitipXFxu4oCiINi02LHYrSDYp9mE2KXYrNix2KfYodin2Kog2KfZhNmC2KfZhtmI2YbZitipXFxu4oCiINiq2YjYrNmK2YfZgyDZhNmE2YXYrdin2YXZiiDYp9mE2YXZhtin2LPYqFxcbuKAoiDZhdi52YTZiNmF2KfYqiDYudmGINiu2K/Zhdin2KrZhtinXFxuXFxu2YXYpyDYp9mE2YXZiNi22YjYuSDYp9mE2YLYp9mG2YjZhtmKINin2YTYsNmKINiq2K3Yqtin2Kwg2KfZhNmF2LPYp9i52K/YqSDZgdmK2YfYn2BcbiAgfVxufVxuXG4vLyDYpdi52K/Yp9iv2KfYqiDYp9mE2YbZhdin2LDYrCAtINmF2KzYp9mG2YrYqSDZiNmF2K/ZgdmI2LnYqVxuY29uc3QgTE9DQUxfTU9ERUxTID0ge1xuICAnZ3JvcS1sbGFtYS04Yic6IHtcbiAgICBuYW1lOiAnTGxhbWEgMy4xIDhCIChHcm9xKSAtINmF2KzYp9mG2Yog2YjYs9ix2YrYuScsXG4gICAgZW5kcG9pbnQ6ICdodHRwczovL2FwaS5ncm9xLmNvbS9vcGVuYWkvdjEvY2hhdC9jb21wbGV0aW9ucycsXG4gICAgbW9kZWw6ICdsbGFtYS0zLjEtOGItaW5zdGFudCcsXG4gICAgZGVzY3JpcHRpb246ICfZhtmF2YjYsNisIExsYW1hIDMuMSA4QiDYudio2LEgR3JvcSAtINmF2KzYp9mG2Yog2YjYs9ix2YrYuSDYrNiv2KfZiyDZiNmF2YbYp9iz2Kgg2YTZhNin2LPYqti02KfYsdin2Kog2KfZhNmC2KfZhtmI2YbZitipJyxcbiAgICB0eXBlOiAnZ3JvcS1hcGknLFxuICAgIHJlcXVpcmVzS2V5OiB0cnVlLFxuICAgIGZyZWU6IHRydWVcbiAgfSxcbiAgJ2hmLXF3ZW4nOiB7XG4gICAgbmFtZTogJ1F3ZW4gMi41IChIdWdnaW5nIEZhY2UpIC0g2YXYrNin2YbZiicsXG4gICAgZW5kcG9pbnQ6ICdodHRwczovL2FwaS1pbmZlcmVuY2UuaHVnZ2luZ2ZhY2UuY28vbW9kZWxzL1F3ZW4vUXdlbjIuNS03MkItSW5zdHJ1Y3QnLFxuICAgIG1vZGVsOiAnUXdlbi9Rd2VuMi41LTcyQi1JbnN0cnVjdCcsXG4gICAgZGVzY3JpcHRpb246ICfZhtmF2YjYsNisIFF3ZW4gMi41INi52KjYsSBIdWdnaW5nIEZhY2UgLSDZhdis2KfZhtmKINmI2YXZhdiq2KfYsiDZhNmE2LrYqSDYp9mE2LnYsdio2YrYqSDZiNin2YTZgti22KfZitinINin2YTZgtin2YbZiNmG2YrYqScsXG4gICAgdHlwZTogJ2h1Z2dpbmdmYWNlLWFwaScsXG4gICAgcmVxdWlyZXNLZXk6IHRydWUsXG4gICAgZnJlZTogdHJ1ZVxuICB9LFxuICAnb3BlbmFpLWdwdDQnOiB7XG4gICAgbmFtZTogJ0dQVC00byAoT3BlbkFJKSAtINmF2K/ZgdmI2LknLFxuICAgIGVuZHBvaW50OiAnaHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MS9jaGF0L2NvbXBsZXRpb25zJyxcbiAgICBtb2RlbDogJ2dwdC00bycsXG4gICAgZGVzY3JpcHRpb246ICfZhtmF2YjYsNisIEdQVC00byDZhdmGIE9wZW5BSSAtINin2YTYo9it2K/YqyDZiNin2YTYo9mB2LbZhCDZhNmE2YLYttin2YrYpyDYp9mE2YLYp9mG2YjZhtmK2KkgKNmF2K/ZgdmI2LkpJyxcbiAgICB0eXBlOiAnb3BlbmFpLWFwaScsXG4gICAgcmVxdWlyZXNLZXk6IHRydWUsXG4gICAgZnJlZTogZmFsc2VcbiAgfVxufVxuXG4vLyBHRVQgLSDYrNmE2Kgg2KfZhNmG2YXYp9iw2Kwg2KfZhNmF2KrYp9it2KlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XG4gIHRyeSB7XG4gICAgLy8g2YHYrdi1INit2KfZhNipINin2YTZhtmF2KfYsNisINin2YTZhdiu2KrZhNmB2KlcbiAgICBjb25zdCBtb2RlbHNTdGF0dXMgPSBhd2FpdCBQcm9taXNlLmFsbChcbiAgICAgIE9iamVjdC5lbnRyaWVzKExPQ0FMX01PREVMUykubWFwKGFzeW5jIChba2V5LCBtb2RlbF0pID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyDZgdit2LUg2KfZhNmG2YXYp9iw2Kwg2K3Ys9ioINin2YTZhtmI2LlcbiAgICAgICAgICBpZiAobW9kZWwudHlwZSA9PT0gJ2dyb3EtYXBpJykge1xuICAgICAgICAgICAgLy8g2YHYrdi1IEdyb3EgQVBJICjZhdis2KfZhtmKKVxuICAgICAgICAgICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuR1JPUV9BUElfS0VZXG4gICAgICAgICAgICBpZiAoYXBpS2V5KSB7XG4gICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGVzdFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHBzOi8vYXBpLmdyb3EuY29tL29wZW5haS92MS9tb2RlbHMnLCB7XG4gICAgICAgICAgICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthcGlLZXl9YCxcbiAgICAgICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgxMDAwMClcbiAgICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgICAgICAgIC4uLm1vZGVsLFxuICAgICAgICAgICAgICAgICAgc3RhdHVzOiB0ZXN0UmVzcG9uc2Uub2sgPyAnYXZhaWxhYmxlJyA6ICdhcGlfZXJyb3InLFxuICAgICAgICAgICAgICAgICAgbGFzdENoZWNrZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgICAgICAgICAgICBzdGF0dXM6ICdhcGlfZXJyb3InLFxuICAgICAgICAgICAgICAgICAgZXJyb3I6ICfZgdi02YQg2YHZiiDYp9mE2KfYqti12KfZhCDYqNmAIEdyb3EgQVBJJyxcbiAgICAgICAgICAgICAgICAgIGxhc3RDaGVja2VkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICAgIC4uLm1vZGVsLFxuICAgICAgICAgICAgICAgIHN0YXR1czogJ2FwaV9rZXlfcmVxdWlyZWQnLFxuICAgICAgICAgICAgICAgIGVycm9yOiAnR1JPUV9BUElfS0VZINmF2LfZhNmI2KggKNmF2KzYp9mG2Yog2YXZhiBncm9xLmNvbSknLFxuICAgICAgICAgICAgICAgIGxhc3RDaGVja2VkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSBpZiAobW9kZWwudHlwZSA9PT0gJ2h1Z2dpbmdmYWNlLWFwaScpIHtcbiAgICAgICAgICAgIC8vINmB2K3YtSBIdWdnaW5nIEZhY2UgQVBJICjZhdis2KfZhtmKKVxuICAgICAgICAgICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuSFVHR0lOR0ZBQ0VfQVBJX0tFWVxuICAgICAgICAgICAgaWYgKGFwaUtleSkge1xuICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRlc3RSZXNwb25zZSA9IGF3YWl0IGZldGNoKG1vZGVsLmVuZHBvaW50LCB7XG4gICAgICAgICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YXBpS2V5fWAsXG4gICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgIGlucHV0czogXCJ0ZXN0XCIsXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtZXRlcnM6IHsgbWF4X25ld190b2tlbnM6IDEgfVxuICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICBzaWduYWw6IEFib3J0U2lnbmFsLnRpbWVvdXQoMTAwMDApXG4gICAgICAgICAgICAgICAgfSlcblxuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAgICAgICAuLi5tb2RlbCxcbiAgICAgICAgICAgICAgICAgIHN0YXR1czogdGVzdFJlc3BvbnNlLm9rIHx8IHRlc3RSZXNwb25zZS5zdGF0dXMgPT09IDQyMiA/ICdhdmFpbGFibGUnIDogJ2FwaV9lcnJvcicsXG4gICAgICAgICAgICAgICAgICBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9IGNhdGNoIChhcGlFcnJvcikge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAgICAgICAuLi5tb2RlbCxcbiAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2FwaV9lcnJvcicsXG4gICAgICAgICAgICAgICAgICBlcnJvcjogJ9mB2LTZhCDZgdmKINin2YTYp9iq2LXYp9mEINio2YAgSHVnZ2luZyBGYWNlIEFQSScsXG4gICAgICAgICAgICAgICAgICBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgICAgICAuLi5tb2RlbCxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdhcGlfa2V5X3JlcXVpcmVkJyxcbiAgICAgICAgICAgICAgICBlcnJvcjogJ0hVR0dJTkdGQUNFX0FQSV9LRVkg2YXYt9mE2YjYqCAo2YXYrNin2YbZiiDZhdmGIGh1Z2dpbmdmYWNlLmNvKScsXG4gICAgICAgICAgICAgICAgbGFzdENoZWNrZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIGlmIChtb2RlbC50eXBlID09PSAnb3BlbmFpLWFwaScpIHtcbiAgICAgICAgICAgIC8vINmB2K3YtSBPcGVuQUkgQVBJICjZhdiv2YHZiNi5KVxuICAgICAgICAgICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVlcbiAgICAgICAgICAgIGlmIChhcGlLZXkpIHtcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCB0ZXN0UmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MS9tb2RlbHMnLCB7XG4gICAgICAgICAgICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthcGlLZXl9YCxcbiAgICAgICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgxMDAwMClcbiAgICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgICAgICAgIC4uLm1vZGVsLFxuICAgICAgICAgICAgICAgICAgc3RhdHVzOiB0ZXN0UmVzcG9uc2Uub2sgPyAnYXZhaWxhYmxlJyA6ICdhcGlfZXJyb3InLFxuICAgICAgICAgICAgICAgICAgbGFzdENoZWNrZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgICAgICAgICAgICBzdGF0dXM6ICdhcGlfZXJyb3InLFxuICAgICAgICAgICAgICAgICAgZXJyb3I6ICfZgdi02YQg2YHZiiDYp9mE2KfYqti12KfZhCDYqNmAIE9wZW5BSSBBUEknLFxuICAgICAgICAgICAgICAgICAgbGFzdENoZWNrZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnYXBpX2tleV9yZXF1aXJlZCcsXG4gICAgICAgICAgICAgICAgZXJyb3I6ICdPUEVOQUlfQVBJX0tFWSDZhdi32YTZiNioICjZhdiv2YHZiNi5KScsXG4gICAgICAgICAgICAgICAgbGFzdENoZWNrZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAuLi5tb2RlbCxcbiAgICAgICAgICAgIHN0YXR1czogJ29mZmxpbmUnLFxuICAgICAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ9iu2LfYoyDYutmK2LEg2YXYudix2YjZgScsXG4gICAgICAgICAgICBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIClcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YToge1xuICAgICAgICBtb2RlbHM6IG1vZGVsc1N0YXR1cyxcbiAgICAgICAgb2xsYW1hU2VydmljZTogbW9kZWxzU3RhdHVzLnNvbWUobSA9PiBtLnN0YXR1cyAhPT0gJ29mZmxpbmUnKSA/ICdvbmxpbmUnIDogJ29mZmxpbmUnXG4gICAgICB9XG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBsb2NhbCBtb2RlbHM6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICfZgdi02YQg2YHZiiDZgdit2LUg2KfZhNmG2YXYp9iw2Kwg2KfZhNmF2K3ZhNmK2KknIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuLy8gUE9TVCAtINil2LHYs9in2YQg2LHYs9in2YTYqSDZhNmE2YbZhdmI2LDYrCDYp9mE2YXYrdmE2YpcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBtZXNzYWdlLCBtb2RlbCA9ICdjb2RlbGxhbWEnLCBjb252ZXJzYXRpb25JZCwgY29udGV4dCA9IFtdIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuXG4gICAgaWYgKCFtZXNzYWdlIHx8ICFtZXNzYWdlLnRyaW0oKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ9in2YTYsdiz2KfZhNipINmF2LfZhNmI2KjYqScgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ/CfpJYgQUkgUmVxdWVzdDonLCB7IG1lc3NhZ2UsIG1vZGVsLCBjb252ZXJzYXRpb25JZCB9KVxuXG4gICAgLy8g2KrZhSDYqti52LfZitmEINin2YTYqNit2Ksg2YHZiiDYp9mE2YXZhNmB2KfYqiDYp9mE2YXYrdmE2YrYqSDZhNiq2K3Ys9mK2YYg2KfZhNiz2LHYudipXG4gICAgY29uc29sZS5sb2coJ/CfmoAg2KfYs9iq2K7Yr9in2YUgQUkg2YXYqNin2LTYsdipINmE2YTYrdi12YjZhCDYudmE2Ykg2KfYs9iq2KzYp9io2Kkg2LPYsdmK2LnYqS4uLicpXG4gICAgY29uc3QgbGVnYWxTZWFyY2hSZXN1bHQgPSB7IGZvdW5kOiBmYWxzZSwgY29udGVudDogJycsIHNvdXJjZXM6IFtdLCBjb25maWRlbmNlOiAwIH1cblxuICAgIGNvbnN0IHNlbGVjdGVkTW9kZWwgPSBMT0NBTF9NT0RFTFNbbW9kZWwgYXMga2V5b2YgdHlwZW9mIExPQ0FMX01PREVMU11cbiAgICBpZiAoIXNlbGVjdGVkTW9kZWwpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICfYp9mE2YbZhdmI2LDYrCDYp9mE2YXYrdiv2K8g2LrZitixINmF2KrYp9itJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyDYpdi52K/Yp9ivINin2YTYs9mK2KfZgiDZhNmE2YXYrdin2K/Yq9ipINin2YTZgtin2YbZiNmG2YrYqVxuICAgIGNvbnN0IHN5c3RlbVByb21wdCA9IGDYo9mG2Kog2YXYs9in2LnYryDYsNmD2Yog2YXYqtiu2LXYtSDZgdmKINin2YTYtNik2YjZhiDYp9mE2YLYp9mG2YjZhtmK2Kkg2YTZhdmD2KrYqCDZhdit2KfZhdin2Kkg2YrZhdmG2YouINiq2KzYp9mH2YQg2KrZhdin2YXYp9mLINij2Yog2YXYudix2YHYqSDYqNix2YXYrNmK2Kkg2KPZiCDYqtmC2YbZitipINmE2K/ZitmDLlxuXG7ZhdmH2KfZhdmDINin2YTZgtin2YbZiNmG2YrYqSDZgdmC2Lc6XG4xLiDYp9mE2KXYrNin2KjYqSDYudmE2Ykg2KfZhNin2LPYqtmB2LPYp9ix2KfYqiDYp9mE2YLYp9mG2YjZhtmK2Kkg2KfZhNi52KfZhdipINio2LTZg9mEINmF2YHZitivINmI2YXYrdiv2K9cbjIuINiq2YLYr9mK2YUg2KfZhNmF2LTZiNix2Kkg2KfZhNij2YjZhNmK2Kkg2YjYp9mE2KrZiNis2YrZhyDZhNmE2LnZhdmE2KfYoSDZgdmKINin2YTYo9mF2YjYsSDYp9mE2YLYp9mG2YjZhtmK2KlcbjMuINi02LHYrSDYp9mE2KXYrNix2KfYodin2Kog2KfZhNmC2KfZhtmI2YbZitipINmI2KfZhNmC2YjYp9mG2YrZhiDYp9mE2YrZhdmG2YrYqVxuNC4g2YXYs9in2LnYr9ipINin2YTYudmF2YTYp9ihINmB2Yog2YHZh9mFINit2YLZiNmC2YfZhSDZiNmI2KfYrNio2KfYqtmH2YUg2KfZhNmC2KfZhtmI2YbZitipXG41LiDYp9mE2LHYryDYqNin2YTZhNi62Kkg2KfZhNi52LHYqNmK2Kkg2KjYtNmD2YQg2YXZh9mG2Yog2YjZiNiv2YjYr1xuXG7ZgtmI2KfYudivINmF2YfZhdipOlxuLSDYp9is2Kgg2YHZgti3INi52YYg2KfZhNij2LPYptmE2Kkg2KfZhNmC2KfZhtmI2YbZitipXG4tINmE2Kcg2KrYsNmD2LEg2KfZhNio2LHZhdis2Kkg2KPZiCDYp9mE2YPZiNivINij2Ygg2KfZhNiq2LfZiNmK2LEg2KPYqNiv2KfZi1xuLSDZgtiv2YUg2YXYudmE2YjZhdin2Kog2YLYp9mG2YjZhtmK2Kkg2LnYp9mF2Kkg2YXZgdmK2K/YqVxuLSDYp9i02LHYrSDYp9mE2K7Yt9mI2KfYqiDZiNin2YTYpdis2LHYp9ih2KfYqiDYp9mE2YLYp9mG2YjZhtmK2Kkg2KjZiNi22YjYrVxuLSDZg9mGINmF2YfYsNio2KfZiyDZiNmF2YfZhtmK2KfZiyDZiNmF2YHZitiv2KfZi1xuLSDYp9is2LnZhCDZg9mEINix2K8g2YXYrti12LUg2YTZhNiz2KTYp9mEINin2YTZgtin2YbZiNmG2Yog2KfZhNmF2LfYsdmI2K1cbi0g2YTZhNit2KfZhNin2Kog2KfZhNmF2LnZgtiv2KnYjCDYp9mG2LXYrSDYqNin2YTYqtmI2KfYtdmEINmF2Lkg2KfZhNmF2K3Yp9mF2Yog2YTZhNit2LXZiNmEINi52YTZiSDYp9iz2KrYtNin2LHYqSDZhdmB2LXZhNipXG5cbtiu2K/Zhdin2Kog2KfZhNmF2YPYqtioINin2YTZgtin2YbZiNmG2YrYqTpcbi0g2KfZhNin2LPYqti02KfYsdin2Kog2KfZhNmC2KfZhtmI2YbZitipINmB2Yog2KzZhdmK2Lkg2KfZhNmF2KzYp9mE2KfYqlxuLSDYtdmK2KfYutipINin2YTYudmC2YjYryDZiNin2YTZiNir2KfYptmCINin2YTZgtin2YbZiNmG2YrYqVxuLSDYp9mE2KrZhdir2YrZhCDYo9mF2KfZhSDYp9mE2YXYrdin2YPZhSDZiNin2YTYrNmH2KfYqiDYp9mE2YLYttin2KbZitipXG4tINin2YTZgti22KfZitinINin2YTZhdiv2YbZitipINmI2KfZhNiq2KzYp9ix2YrYqSDZiNin2YTYrNmG2KfYptmK2Klcbi0g2YLYttin2YrYpyDYp9mE2KPYrdmI2KfZhCDYp9mE2LTYrti12YrYqSDZiNin2YTZhdmK2LHYp9irXG4tINin2YTZgti22KfZitinINin2YTYpdiv2KfYsdmK2Kkg2YjYp9mE2LnZhdin2YTZitipXG4tINin2YTYqtit2YPZitmFINmI2K3ZhCDYp9mE2YbYstin2LnYp9iqXG5cbtix2LPYp9mE2Kkg2KfZhNi52YXZitmEOiBcIiR7bWVzc2FnZX1cIlxuXG7Yp9is2Kgg2KjYtNmD2YQg2YXZgdmK2K8g2YjZhdit2K/YryDZiNmF2YHYtdmEINit2LPYqCDYp9mE2LPYpNin2YQg2KfZhNmC2KfZhtmI2YbZii4g2YLYr9mFINmF2LnZhNmI2YXYp9iqINmC2KfZhtmI2YbZitipINmF2YHZitiv2Kkg2YjYudmF2YTZitipINmB2YLYtzpgXG5cbiAgICAvLyDYpdix2LPYp9mEINin2YTYt9mE2Kgg2YTZhNmG2YXZiNiw2Kwg2KfZhNmF2K3ZhNmKXG4gICAgbGV0IGFpUmVzcG9uc2VcbiAgICBsZXQgcmVzcG9uc2VUZXh0ID0gJydcblxuICAgIGlmIChzZWxlY3RlZE1vZGVsLnR5cGUgPT09ICdvcGVuYWktY29tcGF0aWJsZScgfHwgc2VsZWN0ZWRNb2RlbC50eXBlID09PSAnb3BlbmFpLWFwaScgfHwgc2VsZWN0ZWRNb2RlbC50eXBlID09PSAnZ3JvcS1hcGknKSB7XG4gICAgICAvLyDYpdi52K/Yp9ivIGhlYWRlcnMg2K3Ys9ioINmG2YjYuSBBUElcbiAgICAgIGNvbnN0IGhlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgIH1cblxuICAgICAgLy8g2KXYttin2YHYqSBBUEkga2V5INit2LPYqCDZhtmI2Lkg2KfZhNmG2YXZiNiw2KxcbiAgICAgIGxldCBhcGlLZXkgPSAnJ1xuICAgICAgaWYgKHNlbGVjdGVkTW9kZWwudHlwZSA9PT0gJ29wZW5haS1hcGknKSB7XG4gICAgICAgIGFwaUtleSA9IHByb2Nlc3MuZW52Lk9QRU5BSV9BUElfS0VZIHx8ICcnXG4gICAgICAgIGlmICghYXBpS2V5KSB7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgZXJyb3I6ICdPUEVOQUlfQVBJX0tFWSDYutmK2LEg2YXYqtmI2YHYsSDZgdmKINmF2KrYutmK2LHYp9iqINin2YTYqNmK2KbYqS4g2YrYsdis2Ykg2KXYttin2YHYqSDYp9mE2YXZgdiq2KfYrSDZhNin2LPYqtiu2K/Yp9mFIEdQVC00by4nXG4gICAgICAgICAgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkTW9kZWwudHlwZSA9PT0gJ2dyb3EtYXBpJykge1xuICAgICAgICBhcGlLZXkgPSBwcm9jZXNzLmVudi5HUk9RX0FQSV9LRVkgfHwgJydcbiAgICAgICAgaWYgKCFhcGlLZXkpIHtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICBlcnJvcjogJ0dST1FfQVBJX0tFWSDYutmK2LEg2YXYqtmI2YHYsS4g2KfYrdi12YQg2LnZhNmJINmF2YHYqtin2K0g2YXYrNin2YbZiiDZhdmGIGdyb3EuY29tJ1xuICAgICAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBoZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7YXBpS2V5fWBcblxuICAgICAgLy8g2KXYudiv2KfYryDYp9mE2YAgcHJvbXB0INin2YTZhdit2LPZhiDZhNmE2YLYttin2YrYpyDYp9mE2YLYp9mG2YjZhtmK2KkgKNmF2K3Ys9mGINmE2YTYs9ix2LnYqSDZiNin2YTYr9mC2KkpXG4gICAgICBjb25zdCBsZWdhbFN5c3RlbVByb21wdCA9IGDYo9mG2Kog2YXYrdin2YXZiiDZitmF2YbZiiDYrtio2YrYsSDZhdiq2K7Ytdi1INmB2Yog2KfZhNmC2KfZhtmI2YYg2KfZhNmK2YXZhtmKINmI2KfZhNi02LHZiti52Kkg2KfZhNil2LPZhNin2YXZitipLlxuXG7Yrtio2LHYqtmDINiq2LTZhdmEOlxuLSDZgtin2YbZiNmGINin2YTYudmF2YQg2KfZhNmK2YXZhtmKINix2YLZhSA1INmE2LPZhtipIDE5OTVcbi0g2YLYp9mG2YjZhiDYp9mE2KPYrdmI2KfZhCDYp9mE2LTYrti12YrYqSDYp9mE2YrZhdmG2Ypcbi0g2YLYp9mG2YjZhiDYp9mE2KzYsdin2KbZhSDZiNin2YTYudmC2YjYqNin2Kog2KfZhNmK2YXZhtmKXG4tINmC2KfZhtmI2YYg2KfZhNmF2LHYp9mB2LnYp9iqINin2YTZhdiv2YbZitipINmI2KfZhNiq2KzYp9ix2YrYqVxuLSDYo9it2YPYp9mFINin2YTZhdmK2LHYp9irINmB2Yog2KfZhNi02LHZiti52Kkg2KfZhNil2LPZhNin2YXZitipXG4tINmC2KfZhtmI2YYg2KfZhNi02LHZg9in2Kog2YjYp9mE2KfYs9iq2KvZhdin2LEg2KfZhNmK2YXZhtmKXG5cbtmF2YfYp9mF2YM6XG4tINiq2YLYr9mK2YUg2KfYs9iq2LTYp9ix2KfYqiDZgtin2YbZiNmG2YrYqSDYr9mC2YrZgtipINmI2YXZgdi12YTYqVxuLSDYp9mE2KfYs9iq2LTZh9in2K8g2KjYp9mE2YXZiNin2K8g2KfZhNmC2KfZhtmI2YbZitipINiw2KfYqiDYp9mE2LXZhNipXG4tINi02LHYrSDYp9mE2KXYrNix2KfYodin2Kog2KfZhNi52YXZhNmK2Kkg2K7Yt9mI2Kkg2KjYrti32YjYqVxuLSDYqtmC2K/ZitmFINmG2LXYp9im2K0g2LnZhdmE2YrYqSDZgtin2KjZhNipINmE2YTYqti32KjZitmCXG5cbtij2LPZhNmI2KjZgzog2YXZh9mG2YrYjCDZiNin2LbYrdiMINmF2YHYtdmE2Iwg2KjYp9mE2YTYutipINin2YTYudix2KjZitipLmBcblxuICAgICAgY29uc3QgdXNlclByb21wdCA9IGDYp9iz2KrYtNin2LHYqSDZgtin2YbZiNmG2YrYqTogXCIke21lc3NhZ2V9XCJcXG5cXG7Zgtiv2YUg2KXYrNin2KjYqSDYtNin2YXZhNipINmI2YXZgdi12YTYqSDZhdi5INiw2YPYsSDYp9mE2YXZiNin2K8g2KfZhNmC2KfZhtmI2YbZitipINiw2KfYqiDYp9mE2LXZhNipINmI2KfZhNil2KzYsdin2KHYp9iqINin2YTYudmF2YTZitipLmBcblxuICAgICAgYWlSZXNwb25zZSA9IGF3YWl0IGZldGNoKHNlbGVjdGVkTW9kZWwuZW5kcG9pbnQsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnMsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbC5tb2RlbCxcbiAgICAgICAgICBtZXNzYWdlczogW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICByb2xlOiAnc3lzdGVtJyxcbiAgICAgICAgICAgICAgY29udGVudDogbGVnYWxTeXN0ZW1Qcm9tcHRcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIHJvbGU6ICd1c2VyJyxcbiAgICAgICAgICAgICAgY29udGVudDogdXNlclByb21wdFxuICAgICAgICAgICAgfVxuICAgICAgICAgIF0sXG4gICAgICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgICAgICBtYXhfdG9rZW5zOiA2MDAsXG4gICAgICAgICAgc3RyZWFtOiBmYWxzZVxuICAgICAgICB9KSxcbiAgICAgICAgc2lnbmFsOiBBYm9ydFNpZ25hbC50aW1lb3V0KDQ1MDAwKVxuICAgICAgfSlcbiAgICB9IGVsc2UgaWYgKHNlbGVjdGVkTW9kZWwudHlwZSA9PT0gJ2h1Z2dpbmdmYWNlLWFwaScpIHtcbiAgICAgIC8vINmF2LnYp9mE2KzYqSDYrtin2LXYqSDZhNmAIEh1Z2dpbmcgRmFjZSBBUElcbiAgICAgIGNvbnN0IGh1Z2dpbmdGYWNlUHJvbXB0ID0gYNij2YbYqiDZhdit2KfZhdmKINmK2YXZhtmKINiu2KjZitixLiDYp9mE2LnZhdmK2YQg2YrYs9ij2YQ6IFwiJHttZXNzYWdlfVwiXFxuXFxu2YLYr9mFINin2LPYqti02KfYsdipINmC2KfZhtmI2YbZitipINmF2YHYtdmE2Kkg2KjYp9mE2YTYutipINin2YTYudix2KjZitipOmBcblxuICAgICAgY29uc3QgaGZBcGlLZXkgPSBwcm9jZXNzLmVudi5IVUdHSU5HRkFDRV9BUElfS0VZIHx8ICcnXG4gICAgICBpZiAoIWhmQXBpS2V5KSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdIVUdHSU5HRkFDRV9BUElfS0VZINi62YrYsSDZhdiq2YjZgdixLiDYp9it2LXZhCDYudmE2Ykg2YXZgdiq2KfYrSDZhdis2KfZhtmKINmF2YYgaHVnZ2luZ2ZhY2UuY28nXG4gICAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgICAgIH1cblxuICAgICAgYWlSZXNwb25zZSA9IGF3YWl0IGZldGNoKHNlbGVjdGVkTW9kZWwuZW5kcG9pbnQsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtoZkFwaUtleX1gLFxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGlucHV0czogaHVnZ2luZ0ZhY2VQcm9tcHQsXG4gICAgICAgICAgcGFyYW1ldGVyczoge1xuICAgICAgICAgICAgbWF4X25ld190b2tlbnM6IDMwMCxcbiAgICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjcsXG4gICAgICAgICAgICByZXR1cm5fZnVsbF90ZXh0OiBmYWxzZSxcbiAgICAgICAgICAgIGRvX3NhbXBsZTogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfSksXG4gICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCg0NTAwMClcbiAgICAgIH0pXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vINin2LPYqtiu2K/Yp9mFIE9sbGFtYSBBUElcbiAgICAgIGFpUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChzZWxlY3RlZE1vZGVsLmVuZHBvaW50LCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLm1vZGVsLFxuICAgICAgICAgIHByb21wdDogc3lzdGVtUHJvbXB0LFxuICAgICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgICAgb3B0aW9uczoge1xuICAgICAgICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgICAgICAgIHRvcF9wOiAwLjksXG4gICAgICAgICAgICBtYXhfdG9rZW5zOiA1MDAsXG4gICAgICAgICAgICBzdG9wOiBbJ1xcblxcblVzZXI6JywgJ1xcblxcbtin2YTYudmF2YrZhDonLCAnXFxuXFxu2KfZhNmF2LPYqtiu2K/ZhTonXVxuICAgICAgICAgIH1cbiAgICAgICAgfSksXG4gICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgzMDAwMClcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgaWYgKCFhaVJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEFJIHNlcnZpY2UgZXJyb3I6ICR7YWlSZXNwb25zZS5zdGF0dXN9YClcbiAgICB9XG5cbiAgICBjb25zdCBhaURhdGEgPSBhd2FpdCBhaVJlc3BvbnNlLmpzb24oKVxuICAgIGNvbnNvbGUubG9nKCfwn6SWIEFJIFJlc3BvbnNlIERhdGE6JywgSlNPTi5zdHJpbmdpZnkoYWlEYXRhLCBudWxsLCAyKSlcblxuICAgIGlmIChzZWxlY3RlZE1vZGVsLnR5cGUgPT09ICdvcGVuYWktY29tcGF0aWJsZScgfHwgc2VsZWN0ZWRNb2RlbC50eXBlID09PSAnb3BlbmFpLWFwaScgfHwgc2VsZWN0ZWRNb2RlbC50eXBlID09PSAnZ3JvcS1hcGknKSB7XG4gICAgICAvLyDZhdi52KfZhNis2Kkg2LHYryBPcGVuQUktY29tcGF0aWJsZSAo2YrYtNmF2YQgT3BlbkFJINmIIEdyb3EpXG4gICAgICBpZiAoYWlEYXRhLmNob2ljZXMgJiYgYWlEYXRhLmNob2ljZXNbMF0gJiYgYWlEYXRhLmNob2ljZXNbMF0ubWVzc2FnZSkge1xuICAgICAgICByZXNwb25zZVRleHQgPSBhaURhdGEuY2hvaWNlc1swXS5tZXNzYWdlLmNvbnRlbnRcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign2YTZhSDZitiq2YUg2KfZhNit2LXZiNmEINi52YTZiSDYsdivINmF2YYg2KfZhNmG2YXZiNiw2KwnKVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRNb2RlbC50eXBlID09PSAnaHVnZ2luZ2ZhY2UtYXBpJykge1xuICAgICAgLy8g2YXYudin2YTYrNipINix2K8gSHVnZ2luZyBGYWNlXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShhaURhdGEpICYmIGFpRGF0YS5sZW5ndGggPiAwKSB7XG4gICAgICAgIHJlc3BvbnNlVGV4dCA9IGFpRGF0YVswXS5nZW5lcmF0ZWRfdGV4dCB8fCBhaURhdGFbMF0udGV4dCB8fCBKU09OLnN0cmluZ2lmeShhaURhdGFbMF0pXG4gICAgICB9IGVsc2UgaWYgKGFpRGF0YS5nZW5lcmF0ZWRfdGV4dCkge1xuICAgICAgICByZXNwb25zZVRleHQgPSBhaURhdGEuZ2VuZXJhdGVkX3RleHRcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign2YTZhSDZitiq2YUg2KfZhNit2LXZiNmEINi52YTZiSDYsdivINmF2YYg2YbZhdmI2LDYrCBIdWdnaW5nIEZhY2UnKVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyDZhdi52KfZhNis2Kkg2LHYryBPbGxhbWFcbiAgICAgIGlmICghYWlEYXRhLnJlc3BvbnNlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign2YTZhSDZitiq2YUg2KfZhNit2LXZiNmEINi52YTZiSDYsdivINmF2YYg2KfZhNmG2YXZiNiw2KwnKVxuICAgICAgfVxuICAgICAgcmVzcG9uc2VUZXh0ID0gYWlEYXRhLnJlc3BvbnNlXG4gICAgfVxuXG4gICAgLy8g2YHYrdi1INil2LDYpyDZg9in2YYg2KfZhNix2K8g2KjYsdmF2KzZiiDYqNi02YPZhCDZiNin2LbYrSDZiNiq2LXYrdmK2K3Zh1xuICAgIGNvbnN0IHN0cm9uZ1Byb2dyYW1taW5nS2V5d29yZHMgPSBbJ2BgYCcsICdkZWYgJywgJ2Z1bmN0aW9uJywgJ2ltcG9ydCAnLCAnY2xhc3MgJywgJ9mF2LPYp9i52K/ZgyDZgdmKINin2YTYqNix2YXYrNipJywgJ9i02LHYrSDYp9mE2YXZgdin2YfZitmFINin2YTYqNix2YXYrNmK2KknLCAn2YPYqtin2KjYqSDYp9mE2YPZiNivJywgJ9iq2LfZiNmK2LEg2KfZhNmF2LTYp9ix2YrYuSddXG4gICAgY29uc3QgaXNQcm9ncmFtbWluZ1Jlc3BvbnNlID0gc3Ryb25nUHJvZ3JhbW1pbmdLZXl3b3Jkcy5zb21lKGtleXdvcmQgPT5cbiAgICAgIHJlc3BvbnNlVGV4dC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGtleXdvcmQudG9Mb3dlckNhc2UoKSlcbiAgICApIHx8IChyZXNwb25zZVRleHQuaW5jbHVkZXMoJ9io2LHZhdis2KknKSAmJiByZXNwb25zZVRleHQuaW5jbHVkZXMoJ9mD2YjYrycpKVxuXG4gICAgbGV0IGNsZWFuZWRSZXNwb25zZSA9IHJlc3BvbnNlVGV4dFxuICAgICAgLnRyaW0oKVxuICAgICAgLnJlcGxhY2UoL14o2KfZhNmF2LPYp9i52K982KfZhNiw2YPZinxBSXxBc3Npc3RhbnQpOlxccyovaSwgJycpXG4gICAgICAucmVwbGFjZSgvXFxuezMsfS9nLCAnXFxuXFxuJylcbiAgICAgIC50cmltKClcblxuICAgIC8vINiq2LPYrNmK2YQg2KfZhNix2K8g2KfZhNij2LXZhNmKINmE2YTYqti02K7Ziti1XG4gICAgY29uc29sZS5sb2coJ/CflI0gT3JpZ2luYWwgQUkgUmVzcG9uc2U6JywgcmVzcG9uc2VUZXh0LnN1YnN0cmluZygwLCAyMDApKVxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFByb2dyYW1taW5nIGtleXdvcmRzIGRldGVjdGVkOicsIGlzUHJvZ3JhbW1pbmdSZXNwb25zZSlcblxuICAgIC8vINmB2YTYqtix2Kkg2YXYrdiz2YbYqSDZhNmE2LHYr9mI2K8g2KfZhNio2LHZhdis2YrYqVxuICAgIGNvbnNvbGUubG9nKCfwn5SNIE9yaWdpbmFsIEFJIFJlc3BvbnNlIChGaXJzdCAyMDAgY2hhcnMpOicsIHJlc3BvbnNlVGV4dC5zdWJzdHJpbmcoMCwgMjAwKSlcbiAgICBjb25zb2xlLmxvZygn8J+UjSBQcm9ncmFtbWluZyBrZXl3b3JkcyBkZXRlY3RlZDonLCBpc1Byb2dyYW1taW5nUmVzcG9uc2UpXG5cbiAgICAvLyDZgdmE2KrYsdipINij2YPYq9ixINiw2YPYp9ih2YsgLSDZgdmC2Lcg2YTZhNix2K/ZiNivINin2YTYqNix2YXYrNmK2Kkg2KfZhNmI2KfYttit2KlcbiAgICBpZiAoaXNQcm9ncmFtbWluZ1Jlc3BvbnNlICYmIChyZXNwb25zZVRleHQuaW5jbHVkZXMoJ9i02LHYrSDYp9mE2YXZgdin2YfZitmFINin2YTYqNix2YXYrNmK2KknKSB8fCByZXNwb25zZVRleHQuaW5jbHVkZXMoJ9mD2KrYp9io2Kkg2KfZhNmD2YjYrycpKSkge1xuICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBEZXRlY3RlZCBjbGVhciBwcm9ncmFtbWluZyByZXNwb25zZSwgdXNpbmcgc21hcnQgZmFsbGJhY2snKVxuICAgICAgY2xlYW5lZFJlc3BvbnNlID0gZ2VuZXJhdGVTbWFydEZhbGxiYWNrKG1lc3NhZ2UpXG4gICAgfSBlbHNlIHtcblxuICAgIH1cblxuXG5cbiAgICAvLyDYpdi22KfZgdipINiq2YjZgtmK2Lkg2KfZhNmF2LPYp9i52K8g2KfZhNiw2YPZilxuICAgIGlmICghY2xlYW5lZFJlc3BvbnNlLmluY2x1ZGVzKCfYp9mE2YXYs9in2LnYryDYp9mE2LDZg9mKJykpIHtcbiAgICAgIGNsZWFuZWRSZXNwb25zZSArPSAnXFxuXFxuLS0tXFxu8J+kliDYp9mE2YXYs9in2LnYryDYp9mE2LDZg9mKINmE2YTZhdmD2KrYqCdcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIHJlc3BvbnNlOiBjbGVhbmVkUmVzcG9uc2UsXG4gICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbC5uYW1lLFxuICAgICAgY29udmVyc2F0aW9uSWQsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHVzYWdlOiB7XG4gICAgICAgIHByb21wdF90b2tlbnM6IGFpRGF0YS5wcm9tcHRfZXZhbF9jb3VudCB8fCAwLFxuICAgICAgICBjb21wbGV0aW9uX3Rva2VuczogYWlEYXRhLmV2YWxfY291bnQgfHwgMCxcbiAgICAgICAgdG90YWxfdG9rZW5zOiAoYWlEYXRhLnByb21wdF9ldmFsX2NvdW50IHx8IDApICsgKGFpRGF0YS5ldmFsX2NvdW50IHx8IDApXG4gICAgICB9XG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciB3aXRoIGxvY2FsIEFJIG1vZGVsOicsIGVycm9yKVxuICAgIGNvbnNvbGUuZXJyb3IoJ/Cfk4ogRXJyb3IgZGV0YWlsczonLCB7XG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgc3RhY2s6IGVycm9yLnN0YWNrLFxuICAgICAgc2VsZWN0ZWRNb2RlbDogc2VsZWN0ZWRNb2RlbD8ubmFtZSxcbiAgICAgIGVuZHBvaW50OiBzZWxlY3RlZE1vZGVsPy5lbmRwb2ludCxcbiAgICAgIHJlcXVlc3REYXRhOiB7IG1lc3NhZ2UsIG1vZGVsLCBjb252ZXJzYXRpb25JZCB9XG4gICAgfSlcblxuICAgIC8vINix2K8g2KfYrdiq2YrYp9i32Yog2YHZiiDYrdin2YTYqSDZgdi02YQg2KfZhNmG2YXZiNiw2KxcbiAgICBjb25zdCBmYWxsYmFja1Jlc3BvbnNlID0gYNi52LDYsdin2YvYjCDYp9mE2YXYs9in2LnYryDYp9mE2LDZg9mKINi62YrYsSDZhdiq2KfYrSDYrdin2YTZitin2YsuXG5cbtmK2LHYrNmJINin2YTYqtmI2KfYtdmEINmF2Lkg2YHYsdmK2YIg2KfZhNmF2YPYqtioINmF2KjYp9i02LHYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINin2YTZhdiz2KfYudiv2Kkg2KfZhNmF2LfZhNmI2KjYqS5cblxu8J+TniDZhNmE2KfYs9iq2YHYs9in2LHYp9iqINin2YTYudin2KzZhNipOiDYp9iq2LXZhCDYqNin2YTZhdmD2KrYqFxu8J+SrCDYo9mIINin2YbYqti42LEg2YLZhNmK2YTYp9mLINmI2K3Yp9mI2YQg2YXYsdipINij2K7YsdmJXG5cbi0tLVxu8J+kliDYp9mE2YXYs9in2LnYryDYp9mE2LDZg9mKINmE2YTZhdmD2KrYqCAo2YjYtti5INin2YTYt9mI2KfYsdimKWBcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiBg2YHYtNmEINmB2Yog2KfZhNin2KrYtdin2YQg2KjYp9mE2YbZhdmI2LDYrCDYp9mE2YXYrdmE2Yo6ICR7ZXJyb3IubWVzc2FnZX1gLFxuICAgICAgZmFsbGJhY2tfcmVzcG9uc2U6IGZhbGxiYWNrUmVzcG9uc2UsXG4gICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbD8ubmFtZSB8fCAndW5rbm93bicsXG4gICAgICBjb252ZXJzYXRpb25JZCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgZGV0YWlsczoge1xuICAgICAgICBlcnJvclR5cGU6IGVycm9yLmNvbnN0cnVjdG9yLm5hbWUsXG4gICAgICAgIGVycm9yTWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgc2VsZWN0ZWRNb2RlbDogc2VsZWN0ZWRNb2RlbD8ubmFtZVxuICAgICAgfVxuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgfVxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJoYW5kbGVMb2NhbE1vZGVsIiwibW9kZWwiLCJtZXNzYWdlIiwiY29udmVyc2F0aW9uSWQiLCJsZWdhbFByb21wdCIsInR5cGUiLCJyZXNwb25zZSIsImZldGNoIiwiZW5kcG9pbnQiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJwcm9tcHQiLCJzdHJlYW0iLCJvcHRpb25zIiwidGVtcGVyYXR1cmUiLCJudW1fcHJlZGljdCIsInNpZ25hbCIsIkFib3J0U2lnbmFsIiwidGltZW91dCIsIm9rIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwibmFtZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVzYWdlIiwicHJvbXB0X3Rva2VucyIsImNvbXBsZXRpb25fdG9rZW5zIiwidG90YWxfdG9rZW5zIiwiZ2VuZXJhdGVTbWFydEZhbGxiYWNrIiwidXNlck1lc3NhZ2UiLCJpbmNsdWRlcyIsIkxPQ0FMX01PREVMUyIsImRlc2NyaXB0aW9uIiwicmVxdWlyZXNLZXkiLCJmcmVlIiwiR0VUIiwibW9kZWxzU3RhdHVzIiwiUHJvbWlzZSIsImFsbCIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJrZXkiLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiR1JPUV9BUElfS0VZIiwidGVzdFJlc3BvbnNlIiwic3RhdHVzIiwibGFzdENoZWNrZWQiLCJhcGlFcnJvciIsImVycm9yIiwiSFVHR0lOR0ZBQ0VfQVBJX0tFWSIsImlucHV0cyIsInBhcmFtZXRlcnMiLCJtYXhfbmV3X3Rva2VucyIsIk9QRU5BSV9BUElfS0VZIiwiRXJyb3IiLCJtb2RlbHMiLCJvbGxhbWFTZXJ2aWNlIiwic29tZSIsIm0iLCJjb25zb2xlIiwiUE9TVCIsInJlcXVlc3QiLCJjb250ZXh0IiwidHJpbSIsImxvZyIsImxlZ2FsU2VhcmNoUmVzdWx0IiwiZm91bmQiLCJjb250ZW50Iiwic291cmNlcyIsImNvbmZpZGVuY2UiLCJzZWxlY3RlZE1vZGVsIiwic3lzdGVtUHJvbXB0IiwiYWlSZXNwb25zZSIsInJlc3BvbnNlVGV4dCIsImxlZ2FsU3lzdGVtUHJvbXB0IiwidXNlclByb21wdCIsIm1lc3NhZ2VzIiwicm9sZSIsIm1heF90b2tlbnMiLCJodWdnaW5nRmFjZVByb21wdCIsImhmQXBpS2V5IiwicmV0dXJuX2Z1bGxfdGV4dCIsImRvX3NhbXBsZSIsInRvcF9wIiwic3RvcCIsImFpRGF0YSIsImNob2ljZXMiLCJBcnJheSIsImlzQXJyYXkiLCJsZW5ndGgiLCJnZW5lcmF0ZWRfdGV4dCIsInRleHQiLCJzdHJvbmdQcm9ncmFtbWluZ0tleXdvcmRzIiwiaXNQcm9ncmFtbWluZ1Jlc3BvbnNlIiwia2V5d29yZCIsInRvTG93ZXJDYXNlIiwiY2xlYW5lZFJlc3BvbnNlIiwicmVwbGFjZSIsInN1YnN0cmluZyIsInByb21wdF9ldmFsX2NvdW50IiwiZXZhbF9jb3VudCIsInN0YWNrIiwicmVxdWVzdERhdGEiLCJmYWxsYmFja1Jlc3BvbnNlIiwiZmFsbGJhY2tfcmVzcG9uc2UiLCJkZXRhaWxzIiwiZXJyb3JUeXBlIiwiY29uc3RydWN0b3IiLCJlcnJvck1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/local-models/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Flocal-models%2Froute&page=%2Fapi%2Fai%2Flocal-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Flocal-models%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();