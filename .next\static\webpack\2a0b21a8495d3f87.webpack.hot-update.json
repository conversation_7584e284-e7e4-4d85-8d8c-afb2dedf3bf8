{"c": ["app/layout", "webpack"], "r": ["app/issues/page", "/_error"], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmohaminew%5C%5Csrc%5C%5Capp%5C%5Cissues%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/issues/page.tsx", "(app-pages-browser)/./src/components/ui/client-select.tsx", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cmohaminew%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}