"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    var _currencies_find;\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currencies, setCurrencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [statuses, setStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contractMethods, setContractMethods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [issueTypes, setIssueTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_id: '',\n        court_name: '',\n        issue_type: '',\n        status: 'new',\n        case_amount: '',\n        currency_id: '1',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0]\n    });\n    // جلب البيانات من APIs مع لوق تفصيلي\n    const fetchIssues = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب القضايا...');\n        try {\n            const response = await fetch('/api/issues');\n            console.log('📡 Issues Page: استجابة API القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setIssues(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة القضايا، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب القضايا:', error);\n        }\n    };\n    const fetchCourts = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب المحاكم...');\n        try {\n            const response = await fetch('/api/courts');\n            console.log('📡 Issues Page: استجابة API المحاكم:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API المحاكم:', result);\n            if (result.success) {\n                var _result_data;\n                setCourts(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة المحاكم، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n                console.log('🏛️ Issues Page: قائمة المحاكم:', result.data);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب المحاكم:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب المحاكم:', error);\n        }\n    };\n    const fetchCurrencies = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب العملات...');\n        try {\n            const response = await fetch('/api/currencies');\n            console.log('📡 Issues Page: استجابة API العملات:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API العملات:', result);\n            if (result.success) {\n                var _result_data;\n                setCurrencies(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة العملات، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب العملات:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب العملات:', error);\n        }\n    };\n    const fetchStatuses = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب حالات القضايا...');\n        try {\n            const response = await fetch('/api/issue-statuses');\n            console.log('📡 Issues Page: استجابة API حالات القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API حالات القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setStatuses(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة الحالات، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب حالات القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب حالات القضايا:', error);\n        }\n    };\n    const fetchContractMethods = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب طرق التعاقد...');\n        try {\n            const response = await fetch('/api/contract-methods');\n            console.log('📡 Issues Page: استجابة API طرق التعاقد:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API طرق التعاقد:', result);\n            if (result.success) {\n                var _result_data;\n                setContractMethods(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة طرق التعاقد، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب طرق التعاقد:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب طرق التعاقد:', error);\n        }\n    };\n    const fetchIssueTypes = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب أنواع القضايا...');\n        try {\n            const response = await fetch('/api/issue-types');\n            console.log('📡 Issues Page: استجابة API أنواع القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API أنواع القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setIssueTypes(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة أنواع القضايا، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب أنواع القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب أنواع القضايا:', error);\n        }\n    };\n    // حساب المبلغ بالريال اليمني\n    const calculateAmountYer = (amount, currencyId)=>{\n        const numAmount = parseFloat(amount) || 0;\n        const currency = currencies.find((c)=>c.id.toString() === currencyId);\n        if (!currency) return numAmount;\n        if (currency.is_base_currency) return numAmount;\n        return numAmount * currency.exchange_rate;\n    };\n    // معالجة تغيير العميل مع لوق تفصيلي\n    const handleClientChange = (clientId, clientData)=>{\n        console.log('👤 Issues Page: تغيير العميل...');\n        console.log('📋 Issues Page: معرف العميل:', clientId);\n        console.log('📊 Issues Page: بيانات العميل:', clientData);\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: (clientData === null || clientData === void 0 ? void 0 : clientData.name) || '',\n            client_phone: (clientData === null || clientData === void 0 ? void 0 : clientData.phone) || ''\n        });\n        console.log('✅ Issues Page: تم تحديث بيانات النموذج للعميل');\n    };\n    // إضافة قضية جديدة مع لوق تفصيلي\n    const handleAddNew = ()=>{\n        console.log('➕ Issues Page: فتح نافذة إضافة قضية جديدة...');\n        console.log('🏛️ Issues Page: عدد المحاكم المتاحة:', courts.length);\n        console.log('📋 Issues Page: قائمة المحاكم:', courts);\n        console.log('📊 Issues Page: عدد العملات المتاحة:', currencies.length);\n        console.log('📈 Issues Page: عدد حالات القضايا المتاحة:', statuses.length);\n        console.log('📝 Issues Page: عدد طرق التعاقد المتاحة:', contractMethods.length);\n        console.log('⚖️ Issues Page: عدد أنواع القضايا المتاحة:', issueTypes.length);\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_id: '',\n            court_name: '',\n            issue_type: '',\n            status: 'new',\n            case_amount: '',\n            currency_id: '1',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0]\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n        console.log('✅ Issues Page: تم فتح النافذة المنبثقة لإضافة قضية جديدة');\n    };\n    // تعديل قضية\n    const handleEdit = (issue)=>{\n        var _issue_court_id, _issue_case_amount, _issue_currency_id;\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number,\n            title: issue.title,\n            description: issue.description,\n            client_id: issue.client_id.toString(),\n            client_name: issue.client_name,\n            client_phone: issue.client_phone,\n            court_id: ((_issue_court_id = issue.court_id) === null || _issue_court_id === void 0 ? void 0 : _issue_court_id.toString()) || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status,\n            case_amount: ((_issue_case_amount = issue.case_amount) === null || _issue_case_amount === void 0 ? void 0 : _issue_case_amount.toString()) || '',\n            currency_id: ((_issue_currency_id = issue.currency_id) === null || _issue_currency_id === void 0 ? void 0 : _issue_currency_id.toString()) || '1',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date || new Date().toISOString().split('T')[0]\n        });\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    // عرض قضية\n    const handleView = (issue)=>{\n        var _issue_court_id, _issue_case_amount, _issue_currency_id;\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number,\n            title: issue.title,\n            description: issue.description,\n            client_id: issue.client_id.toString(),\n            client_name: issue.client_name,\n            client_phone: issue.client_phone,\n            court_id: ((_issue_court_id = issue.court_id) === null || _issue_court_id === void 0 ? void 0 : _issue_court_id.toString()) || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status,\n            case_amount: ((_issue_case_amount = issue.case_amount) === null || _issue_case_amount === void 0 ? void 0 : _issue_case_amount.toString()) || '',\n            currency_id: ((_issue_currency_id = issue.currency_id) === null || _issue_currency_id === void 0 ? void 0 : _issue_currency_id.toString()) || '1',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date || new Date().toISOString().split('T')[0]\n        });\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    // حفظ القضية\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        ...formData,\n                        created_by: 1\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(formData)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    // حذف قضية\n    const handleDelete = async (id)=>{\n        if (confirm('هل أنت متأكد من حذف هذه القضية؟')) {\n            try {\n                const response = await fetch(\"/api/issues/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم حذف القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في حذف القضية');\n                }\n            } catch (error) {\n                console.error('Error deleting issue:', error);\n                alert('حدث خطأ في الاتصال');\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            const loadData = {\n                \"IssuesPage.useEffect.loadData\": async ()=>{\n                    console.log('🚀 Issues Page: بدء تحميل جميع البيانات...');\n                    setIsLoading(true);\n                    try {\n                        await Promise.all([\n                            fetchIssues(),\n                            fetchCourts(),\n                            fetchCurrencies(),\n                            fetchStatuses(),\n                            fetchContractMethods(),\n                            fetchIssueTypes()\n                        ]);\n                        console.log('✅ Issues Page: تم تحميل جميع البيانات بنجاح');\n                        console.log('📊 Issues Page: الحالة النهائية للبيانات:');\n                        console.log('   - القضايا:', issues.length);\n                        console.log('   - المحاكم:', courts.length);\n                        console.log('   - العملات:', currencies.length);\n                        console.log('   - الحالات:', statuses.length);\n                        console.log('   - طرق التعاقد:', contractMethods.length);\n                        console.log('   - أنواع القضايا:', issueTypes.length);\n                    } catch (error) {\n                        console.error('💥 Issues Page: خطأ في تحميل البيانات:', error);\n                    } finally{\n                        setIsLoading(false);\n                        console.log('🏁 Issues Page: انتهى تحميل البيانات');\n                    }\n                }\n            }[\"IssuesPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    // تصفية القضايا\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    // إحصائيات\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل البيانات...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 451,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 450,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إدارة القضايا\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleAddNew,\n                            className: \"bg-emerald-600 hover:bg-emerald-700 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة قضية جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: stats.total\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"إجمالي القضايا\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: stats.new\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"جديدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: stats.pending\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"معلقة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: stats.in_progress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"قيد المعالجة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-emerald-600\",\n                                            children: stats.completed\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"مكتملة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"جميع الحالات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 19\n                                            }, this),\n                                            statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: status.value,\n                                                    children: status.label\n                                                }, status.value, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: [\n                                    \"قائمة القضايا (\",\n                                    filteredIssues.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: filteredIssues.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"لا توجد قضايا\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"رقم القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"العنوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"الموكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"المحكمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"المبلغ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: filteredIssues.map((issue)=>{\n                                                var _statuses_find;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 font-medium text-blue-600\",\n                                                            children: issue.case_number\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: issue.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: issue.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: issue.client_phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: issue.court_name || '-'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(issue.status === 'new' ? 'bg-green-100 text-green-800' : issue.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : issue.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : issue.status === 'completed' ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'),\n                                                                children: ((_statuses_find = statuses.find((s)=>s.value === issue.status)) === null || _statuses_find === void 0 ? void 0 : _statuses_find.label) || issue.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            (issue.case_amount || 0).toLocaleString(),\n                                                                            \" \",\n                                                                            issue.currency_symbol\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: [\n                                                                            (issue.amount_yer || 0).toLocaleString(),\n                                                                            \" ر.ي\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleView(issue),\n                                                                        className: \"text-blue-600 hover:text-blue-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleEdit(issue),\n                                                                        className: \"text-green-600 hover:text-green-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleDelete(issue.id),\n                                                                        className: \"text-red-600 hover:text-red-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, issue.id, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 9\n                }, this),\n                isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: [\n                                            modalType === 'add' && '📋 إضافة قضية جديدة',\n                                            modalType === 'edit' && '✏️ تعديل القضية',\n                                            modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsModalOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-10 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"case_number\",\n                                                        className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"case_number\",\n                                                        value: formData.case_number,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                case_number: e.target.value\n                                                            }),\n                                                        required: modalType !== 'view',\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل رقم القضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-7\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                title: e.target.value\n                                                            }),\n                                                        required: modalType !== 'view',\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل عنوان القضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                    children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    readOnly: modalType === 'view',\n                                                    className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors\",\n                                                    placeholder: \"أدخل وصف مختصر للقضية...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDC64 الموكل *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_6__.ClientSelect, {\n                                                            value: formData.client_id,\n                                                            onChange: handleClientChange,\n                                                            label: \"\",\n                                                            placeholder: \"اختر الموكل...\",\n                                                            required: true,\n                                                            disabled: modalType === 'view'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"court_id\",\n                                                        className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"court_id\",\n                                                        value: formData.court_id || \"\",\n                                                        onChange: (e)=>{\n                                                            const selectedCourt = courts.find((c)=>c.id.toString() === e.target.value);\n                                                            setFormData({\n                                                                ...formData,\n                                                                court_id: e.target.value,\n                                                                court_name: (selectedCourt === null || selectedCourt === void 0 ? void 0 : selectedCourt.name) || ''\n                                                            });\n                                                        },\n                                                        className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر المحكمة...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: court.id,\n                                                                    children: [\n                                                                        court.name,\n                                                                        \" - \",\n                                                                        court.governorate_name\n                                                                    ]\n                                                                }, court.id, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 753,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"status\",\n                                                        className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCCA حالة القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"status\",\n                                                        value: formData.status,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: status.value,\n                                                                children: status.label\n                                                            }, status.value, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"issue_type\",\n                                                        className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"⚖️ نوع القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"issue_type\",\n                                                        value: formData.issue_type || \"\",\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                issue_type: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر نوع القضية...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            issueTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: type.name,\n                                                                    children: type.name\n                                                                }, type.id, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"contract_method\",\n                                                        className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD طريقة التعاقد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"contract_method\",\n                                                        value: formData.contract_method,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                contract_method: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: contractMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: method.value,\n                                                                children: method.label\n                                                            }, method.value, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"contract_date\",\n                                                        className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"contract_date\",\n                                                        type: \"date\",\n                                                        value: formData.contract_date,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                contract_date: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency_id\",\n                                                        className: \"text-sm font-semibold text-yellow-700 bg-yellow-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB1 العملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"currency_id\",\n                                                        value: formData.currency_id || '1',\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                currency_id: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-yellow-50 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: currency.id,\n                                                                children: [\n                                                                    currency.currency_name,\n                                                                    \" (\",\n                                                                    currency.symbol,\n                                                                    \")\",\n                                                                    currency.is_base_currency && ' - الأساسية'\n                                                                ]\n                                                            }, currency.id, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"case_amount\",\n                                                        className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"case_amount\",\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                min: \"0\",\n                                                                value: formData.case_amount || '',\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        case_amount: e.target.value\n                                                                    }),\n                                                                readOnly: modalType === 'view',\n                                                                className: \"h-10 pr-12 text-black bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-sm\",\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: ((_currencies_find = currencies.find((c)=>c.id.toString() === (formData.currency_id || '1'))) === null || _currencies_find === void 0 ? void 0 : _currencies_find.symbol) || 'ر.ي'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"amount_yer\",\n                                                        className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB5 بالريال اليمني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"amount_yer\",\n                                                                type: \"text\",\n                                                                value: Math.round(calculateAmountYer(formData.case_amount || '0', formData.currency_id || '1')).toLocaleString('en-US'),\n                                                                readOnly: true,\n                                                                className: \"h-10 pr-12 bg-gray-50 text-black text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"ر.ي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"notes\",\n                                                        value: formData.notes,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                notes: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                        children: [\n                                            modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsModalOpen(false),\n                                                className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 463,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 462,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"0XCYreaDfcQ0Se8iFJOcd4l34Tc=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});