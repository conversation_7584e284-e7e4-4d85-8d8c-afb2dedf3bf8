(()=>{var c={};c.id=7356,c.ids=[7356],c.modules={3295:c=>{"use strict";c.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(c,t,e)=>{"use strict";e.a(c,async(c,a)=>{try{e.d(t,{P:()=>i});var o=e(64939),n=e(29021),r=e.n(n),s=e(33873),u=e.n(s),_=c([o]);o=(_.then?(await _)():_)[0];let d=null;try{let c=u().join(process.cwd(),"routing.config.json"),t=r().readFileSync(c,"utf8");d=JSON.parse(t)}catch(c){console.error("❌ خطأ في تحميل ملف التوجيه:",c)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let c=process.env.PORT||"7443";if(d&&d.routes[c]){let t=d.routes[c],e=d.default_config;return{database:t.database,user:e.db_user,host:e.db_host,password:process.env.DB_PASSWORD||e.db_password,port:e.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${c}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new o.Pool(l);async function i(c,t){let e=await p.connect();try{return await e.query(c,t)}finally{e.release()}}a()}catch(c){a(c)}})},10846:c=>{"use strict";c.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23714:(c,t,e)=>{"use strict";e.a(c,async(c,a)=>{try{e.r(t),e.d(t,{POST:()=>s});var o=e(32190),n=e(5069),r=c([n]);async function s(c){try{console.log("\uD83D\uDD27 إصلاح ربط السندات بالحسابات...");let c=[];console.log("\uD83D\uDD0D البحث عن الحسابات المناسبة...");let t=await (0,n.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%صندوق%' OR account_name ILIKE '%نقدية%' OR account_code LIKE '1101%'
      ORDER BY account_code 
      LIMIT 1
    `),e=await (0,n.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%إيراد%' OR account_name ILIKE '%أتعاب%' OR account_code LIKE '41%'
      ORDER BY account_code 
      LIMIT 1
    `),a=await (0,n.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%مصروف%' OR account_name ILIKE '%راتب%' OR account_code LIKE '51%'
      ORDER BY account_code 
      LIMIT 1
    `),r=null,s=null,u=null;if(t.rows.length>0)r=t.rows[0].id,c.push(`✅ تم العثور على حساب الصندوق: ${t.rows[0].account_name} (${t.rows[0].account_code})`);else{let t=await (0,n.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('1101001', 'الصندوق الرئيسي', 'أصول', 'مدين', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '1101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `);r=t.rows[0].id,c.push(`✅ تم إنشاء حساب الصندوق: ${t.rows[0].account_name} (${t.rows[0].account_code})`)}if(e.rows.length>0)s=e.rows[0].id,c.push(`✅ تم العثور على حساب الإيرادات: ${e.rows[0].account_name} (${e.rows[0].account_code})`);else{let t=await (0,n.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('4101001', 'إيرادات الأتعاب القانونية', 'إيرادات', 'دائن', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '4101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `);s=t.rows[0].id,c.push(`✅ تم إنشاء حساب الإيرادات: ${t.rows[0].account_name} (${t.rows[0].account_code})`)}if(a.rows.length>0)u=a.rows[0].id,c.push(`✅ تم العثور على حساب المصروفات: ${a.rows[0].account_name} (${a.rows[0].account_code})`);else{let t=await (0,n.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('5101001', 'مصروفات الرواتب', 'مصروفات', 'مدين', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '5101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `);u=t.rows[0].id,c.push(`✅ تم إنشاء حساب المصروفات: ${t.rows[0].account_name} (${t.rows[0].account_code})`)}console.log("\uD83D\uDCDD تحديث سندات القبض...");let _=await (0,n.P)(`
      UPDATE receipt_vouchers
      SET
        debit_account_id = $1,
        credit_account_id = $2
    `,[r,s]);c.push(`✅ تم تحديث ${_.rowCount} سند قبض`),console.log("\uD83D\uDCDD تحديث سندات الصرف...");let i=await (0,n.P)(`
      UPDATE payment_vouchers
      SET
        debit_account_id = $1,
        credit_account_id = $2
    `,[u,r]);c.push(`✅ تم تحديث ${i.rowCount} سند صرف`),console.log("\uD83E\uDDEA اختبار السندات بعد التحديث...");let d=await (0,n.P)(`
      SELECT 
        rv.*,
        da.account_name as debit_account_name,
        da.account_code as debit_account_code,
        ca.account_name as credit_account_name,
        ca.account_code as credit_account_code
      FROM receipt_vouchers rv
      LEFT JOIN chart_of_accounts da ON rv.debit_account_id = da.id
      LEFT JOIN chart_of_accounts ca ON rv.credit_account_id = ca.id
      LIMIT 3
    `);c.push(`✅ اختبار سندات القبض: ${d.rows.length} سند مع أسماء الحسابات`);let l=await (0,n.P)(`
      SELECT 
        pv.*,
        da.account_name as debit_account_name,
        da.account_code as debit_account_code,
        ca.account_name as credit_account_name,
        ca.account_code as credit_account_code
      FROM payment_vouchers pv
      LEFT JOIN chart_of_accounts da ON pv.debit_account_id = da.id
      LEFT JOIN chart_of_accounts ca ON pv.credit_account_id = ca.id
      LIMIT 3
    `);c.push(`✅ اختبار سندات الصرف: ${l.rows.length} سند مع أسماء الحسابات`),console.log("\uD83D\uDCB0 تحديث أرصدة الحسابات...");let p=await (0,n.P)(`
      SELECT COALESCE(SUM(amount), 0) as total 
      FROM receipt_vouchers 
      WHERE status = 'approved'
    `),E=await (0,n.P)(`
      SELECT COALESCE(SUM(amount), 0) as total 
      FROM payment_vouchers 
      WHERE status = 'approved'
    `),h=parseFloat(p.rows[0].total)-parseFloat(E.rows[0].total);await (0,n.P)(`
      UPDATE chart_of_accounts 
      SET current_balance = $1 
      WHERE id = $2
    `,[h,r]),c.push(`💰 تم تحديث رصيد الصندوق: ${h} ر.س`);let w={receiptVouchers:d.rows.length,paymentVouchers:l.rows.length,cashAccountId:r,revenueAccountId:s,expenseAccountId:u,cashBalance:h};return console.log("\uD83C\uDF89 تم إصلاح ربط السندات بالحسابات بنجاح!"),o.NextResponse.json({success:!0,message:"تم إصلاح ربط السندات بالحسابات بنجاح",data:{results:c,stats:w,accounts:{cash:t.rows[0]||{id:r,account_name:"الصندوق الرئيسي",account_code:"1101001"},revenue:e.rows[0]||{id:s,account_name:"إيرادات الأتعاب القانونية",account_code:"4101001"},expense:a.rows[0]||{id:u,account_name:"مصروفات الرواتب",account_code:"5101001"}}}})}catch(c){return console.error("❌ خطأ في إصلاح ربط السندات:",c),o.NextResponse.json({success:!1,message:"فشل في إصلاح ربط السندات",error:c instanceof Error?c.message:"خطأ غير معروف"},{status:500})}}n=(r.then?(await r)():r)[0],a()}catch(c){a(c)}})},29021:c=>{"use strict";c.exports=require("fs")},29294:c=>{"use strict";c.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:c=>{"use strict";c.exports=require("path")},44870:c=>{"use strict";c.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:c=>{"use strict";c.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:c=>{"use strict";c.exports=import("pg")},78335:()=>{},96487:()=>{},96862:(c,t,e)=>{"use strict";e.a(c,async(c,a)=>{try{e.r(t),e.d(t,{patchFetch:()=>_,routeModule:()=>i,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var o=e(96559),n=e(48088),r=e(37719),s=e(23714),u=c([s]);s=(u.then?(await u)():u)[0];let i=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/accounting/fix-vouchers-accounts/route",pathname:"/api/accounting/fix-vouchers-accounts",filename:"route",bundlePath:"app/api/accounting/fix-vouchers-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\fix-vouchers-accounts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:p}=i;function _(){return(0,r.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}a()}catch(c){a(c)}})}};var t=require("../../../../webpack-runtime.js");t.C(c);var e=c=>t(t.s=c),a=t.X(0,[4447,580],()=>e(96862));module.exports=a})();