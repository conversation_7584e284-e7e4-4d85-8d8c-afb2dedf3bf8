// مدير النسخ - Instance Manager
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class InstanceManager {
  constructor() {
    this.configPath = path.join(__dirname, 'instances.config.json');
    this.config = this.loadConfig();
    this.runningInstances = new Map();
  }

  // تحميل ملف التكوين
  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('❌ خطأ في تحميل ملف التكوين:', error.message);
      process.exit(1);
    }
  }

  // عرض جميع النسخ المتاحة
  listInstances() {
    console.log('\n📋 النسخ المتاحة:');
    console.log('='.repeat(60));
    
    Object.entries(this.config.instances).forEach(([key, instance]) => {
      const status = this.runningInstances.has(key) ? '🟢 يعمل' : '🔴 متوقف';
      console.log(`${key.padEnd(15)} | ${instance.name.padEnd(25)} | المنفذ: ${instance.port} | ${status}`);
    });
    
    console.log('='.repeat(60));
  }

  // تشغيل نسخة محددة
  async startInstance(instanceKey) {
    if (!this.config.instances[instanceKey]) {
      console.error(`❌ النسخة "${instanceKey}" غير موجودة`);
      return false;
    }

    if (this.runningInstances.has(instanceKey)) {
      console.log(`⚠️ النسخة "${instanceKey}" تعمل بالفعل`);
      return false;
    }

    const instance = this.config.instances[instanceKey];
    const defaultConfig = this.config.default_config;

    console.log(`🚀 بدء تشغيل النسخة: ${instance.name}`);
    console.log(`   📍 المنفذ: ${instance.port}`);
    console.log(`   🗄️ قاعدة البيانات: ${instance.database}`);

    // إعداد متغيرات البيئة
    const env = {
      ...process.env,
      DB_NAME: instance.database,
      DB_HOST: defaultConfig.db_host,
      DB_PORT: defaultConfig.db_port,
      DB_USER: defaultConfig.db_user,
      DB_PASSWORD: defaultConfig.db_password,
      PORT: instance.port,
      NEXT_PUBLIC_API_URL: instance.api_url,
      JWT_SECRET: defaultConfig.jwt_secret,
      OPENAI_API_KEY: defaultConfig.openai_api_key,
      GROQ_API_KEY: defaultConfig.groq_api_key,
      HUGGINGFACE_API_KEY: defaultConfig.huggingface_api_key,
      INSTANCE_NAME: instanceKey,
      INSTANCE_THEME_COLOR: instance.theme_color,
      INSTANCE_LOGO_TEXT: instance.logo_text
    };

    try {
      // تشغيل Next.js
      const child = spawn('npx', ['next', 'dev', '-p', instance.port.toString()], {
        env,
        stdio: 'pipe',
        shell: true
      });

      // حفظ معلومات العملية
      this.runningInstances.set(instanceKey, {
        process: child,
        instance,
        startTime: new Date()
      });

      // معالجة المخرجات
      child.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Ready in')) {
          console.log(`✅ النسخة "${instance.name}" جاهزة على: ${instance.api_url}`);
        }
        // عرض المخرجات المهمة فقط
        if (output.includes('▲ Next.js') || output.includes('Ready in') || output.includes('Error')) {
          console.log(`[${instanceKey}] ${output.trim()}`);
        }
      });

      child.stderr.on('data', (data) => {
        const error = data.toString();
        if (!error.includes('Warning') && !error.includes('Invalid next.config.js')) {
          console.error(`[${instanceKey}] ❌ ${error.trim()}`);
        }
      });

      child.on('close', (code) => {
        console.log(`🔴 النسخة "${instanceKey}" توقفت بالكود: ${code}`);
        this.runningInstances.delete(instanceKey);
      });

      child.on('error', (error) => {
        console.error(`❌ خطأ في تشغيل النسخة "${instanceKey}":`, error.message);
        this.runningInstances.delete(instanceKey);
      });

      return true;

    } catch (error) {
      console.error(`❌ فشل في تشغيل النسخة "${instanceKey}":`, error.message);
      return false;
    }
  }

  // إيقاف نسخة محددة
  stopInstance(instanceKey) {
    if (!this.runningInstances.has(instanceKey)) {
      console.log(`⚠️ النسخة "${instanceKey}" غير قيد التشغيل`);
      return false;
    }

    const runningInstance = this.runningInstances.get(instanceKey);
    
    console.log(`🛑 إيقاف النسخة: ${runningInstance.instance.name}`);
    
    runningInstance.process.kill('SIGTERM');
    this.runningInstances.delete(instanceKey);
    
    console.log(`✅ تم إيقاف النسخة "${instanceKey}"`);
    return true;
  }

  // إيقاف جميع النسخ
  stopAllInstances() {
    console.log('🛑 إيقاف جميع النسخ...');
    
    for (const [key, runningInstance] of this.runningInstances) {
      console.log(`   🔴 إيقاف ${runningInstance.instance.name}`);
      runningInstance.process.kill('SIGTERM');
    }
    
    this.runningInstances.clear();
    console.log('✅ تم إيقاف جميع النسخ');
  }

  // عرض حالة النسخ
  showStatus() {
    console.log('\n📊 حالة النسخ:');
    console.log('='.repeat(80));
    
    if (this.runningInstances.size === 0) {
      console.log('🔴 لا توجد نسخ قيد التشغيل');
    } else {
      for (const [key, runningInstance] of this.runningInstances) {
        const uptime = Math.floor((new Date() - runningInstance.startTime) / 1000);
        console.log(`🟢 ${runningInstance.instance.name}`);
        console.log(`   📍 الرابط: ${runningInstance.instance.api_url}`);
        console.log(`   ⏱️ مدة التشغيل: ${uptime} ثانية`);
        console.log(`   🗄️ قاعدة البيانات: ${runningInstance.instance.database}`);
        console.log('');
      }
    }
    
    console.log('='.repeat(80));
  }

  // إضافة نسخة جديدة
  addInstance(key, config) {
    if (this.config.instances[key]) {
      console.error(`❌ النسخة "${key}" موجودة بالفعل`);
      return false;
    }

    this.config.instances[key] = config;
    this.saveConfig();
    
    console.log(`✅ تم إضافة النسخة "${key}" بنجاح`);
    return true;
  }

  // حفظ ملف التكوين
  saveConfig() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('❌ خطأ في حفظ ملف التكوين:', error.message);
    }
  }

  // معالجة إشارات النظام
  setupSignalHandlers() {
    process.on('SIGINT', () => {
      console.log('\n🛑 تم استلام إشارة الإيقاف...');
      this.stopAllInstances();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 تم استلام إشارة الإنهاء...');
      this.stopAllInstances();
      process.exit(0);
    });
  }
}

module.exports = InstanceManager;
