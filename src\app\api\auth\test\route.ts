import { NextRequest, NextResponse } from 'next/server'

// اختبار بسيط للAPI
export async function POST(request: NextRequest) {
  try {
    
    const body = await request.json()
    console.log('📋 البيانات المستلمة:', body)
    
    return NextResponse.json({
      success: true,
      message: 'API يعمل بشكل صحيح',
      receivedData: body
    })
    
  } catch (error) {
    console.error('❌ خطأ في API:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'API اختبار يعمل'
  })
}
