// إصلاح العمود next_hearing وربطه بجدول الجلسات
const { Pool } = require('pg');

async function fixNextHearingColumn() {
  console.log('🔧 إصلاح العمود next_hearing وربطه بجدول الجلسات...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص وإنشاء جدول الجلسات
      console.log('\n   📅 فحص وإنشاء جدول الجلسات:');
      
      const hearingsTableExists = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'hearings'
        )
      `);

      if (!hearingsTableExists.rows[0].exists) {
        console.log('      ➕ إنشاء جدول الجلسات...');
        await pool.query(`
          CREATE TABLE hearings (
            id SERIAL PRIMARY KEY,
            issue_id INTEGER NOT NULL,
            hearing_date TIMESTAMP NOT NULL,
            hearing_type VARCHAR(100), -- نوع الجلسة (مرافعة، شهود، حكم، إلخ)
            status VARCHAR(50) DEFAULT 'scheduled', -- حالة الجلسة (scheduled, completed, postponed, cancelled)
            notes TEXT,
            location VARCHAR(255), -- مكان الجلسة
            judge_name VARCHAR(255), -- اسم القاضي
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE
          )
        `);
        console.log('      ✅ تم إنشاء جدول الجلسات');

        // إضافة فهرس للأداء
        await pool.query(`
          CREATE INDEX idx_hearings_issue_id ON hearings(issue_id);
          CREATE INDEX idx_hearings_date ON hearings(hearing_date);
          CREATE INDEX idx_hearings_status ON hearings(status);
        `);
        console.log('      ✅ تم إضافة الفهارس');
      } else {
        console.log('      ✅ جدول الجلسات موجود');
      }

      // 2. فحص العمود next_hearing في جدول القضايا
      console.log('\n   🔍 فحص العمود next_hearing:');
      
      const nextHearingExists = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'next_hearing'
      `);

      if (nextHearingExists.rows.length === 0) {
        console.log('      ➕ إضافة العمود next_hearing...');
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN next_hearing TIMESTAMP
        `);
        console.log('      ✅ تم إضافة العمود next_hearing');
      } else {
        console.log(`      ✅ العمود موجود: ${nextHearingExists.rows[0].data_type}`);
      }

      // 3. إنشاء دالة لتحديث next_hearing
      console.log('\n   🔄 إنشاء دالة تحديث next_hearing:');
      
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_next_hearing(issue_id_param INTEGER)
        RETURNS VOID AS $$
        DECLARE
          next_hearing_date TIMESTAMP;
        BEGIN
          -- البحث عن أقرب جلسة مستقبلية للقضية
          SELECT hearing_date INTO next_hearing_date
          FROM hearings 
          WHERE issue_id = issue_id_param 
          AND hearing_date > CURRENT_TIMESTAMP
          AND status IN ('scheduled', 'postponed')
          ORDER BY hearing_date ASC
          LIMIT 1;
          
          -- تحديث العمود next_hearing في جدول القضايا
          UPDATE issues 
          SET next_hearing = next_hearing_date,
              updated_date = CURRENT_TIMESTAMP
          WHERE id = issue_id_param;
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('      ✅ تم إنشاء دالة update_next_hearing');

      // 4. إنشاء trigger لتحديث next_hearing تلقائياً
      console.log('\n   🔄 إنشاء triggers للتحديث التلقائي:');
      
      // Trigger عند إدراج جلسة جديدة
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_update_next_hearing_on_insert()
        RETURNS TRIGGER AS $$
        BEGIN
          PERFORM update_next_hearing(NEW.issue_id);
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_hearings_insert ON hearings;
        CREATE TRIGGER trigger_hearings_insert
          AFTER INSERT ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_update_next_hearing_on_insert();
      `);
      console.log('      ✅ تم إنشاء trigger للإدراج');

      // Trigger عند تحديث جلسة
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_update_next_hearing_on_update()
        RETURNS TRIGGER AS $$
        BEGIN
          -- تحديث القضية الجديدة إذا تغير issue_id
          IF NEW.issue_id != OLD.issue_id THEN
            PERFORM update_next_hearing(OLD.issue_id);
          END IF;
          
          -- تحديث القضية الحالية
          PERFORM update_next_hearing(NEW.issue_id);
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_hearings_update ON hearings;
        CREATE TRIGGER trigger_hearings_update
          AFTER UPDATE ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_update_next_hearing_on_update();
      `);
      console.log('      ✅ تم إنشاء trigger للتحديث');

      // Trigger عند حذف جلسة
      await pool.query(`
        CREATE OR REPLACE FUNCTION trigger_update_next_hearing_on_delete()
        RETURNS TRIGGER AS $$
        BEGIN
          PERFORM update_next_hearing(OLD.issue_id);
          RETURN OLD;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_hearings_delete ON hearings;
        CREATE TRIGGER trigger_hearings_delete
          AFTER DELETE ON hearings
          FOR EACH ROW
          EXECUTE FUNCTION trigger_update_next_hearing_on_delete();
      `);
      console.log('      ✅ تم إنشاء trigger للحذف');

      // 5. إدراج بيانات جلسات تجريبية
      console.log('\n   📅 إدراج بيانات جلسات تجريبية:');
      
      const existingIssues = await pool.query(`
        SELECT id, case_number FROM issues ORDER BY id LIMIT 3
      `);

      if (existingIssues.rows.length > 0) {
        console.log(`      📋 إدراج جلسات لـ ${existingIssues.rows.length} قضايا:`);
        
        for (const issue of existingIssues.rows) {
          try {
            // إدراج جلسة ماضية (مكتملة)
            const pastDate = new Date();
            pastDate.setDate(pastDate.getDate() - 7); // قبل أسبوع
            
            await pool.query(`
              INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes, judge_name)
              VALUES ($1, $2, $3, $4, $5, $6)
              ON CONFLICT DO NOTHING
            `, [
              issue.id, 
              pastDate.toISOString(), 
              'مرافعة', 
              'completed', 
              'جلسة مرافعة أولى', 
              'القاضي أحمد محمد'
            ]);

            // إدراج جلسة قادمة
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 14); // بعد أسبوعين
            
            await pool.query(`
              INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes, judge_name)
              VALUES ($1, $2, $3, $4, $5, $6)
              ON CONFLICT DO NOTHING
            `, [
              issue.id, 
              futureDate.toISOString(), 
              'شهود', 
              'scheduled', 
              'جلسة استماع للشهود', 
              'القاضي أحمد محمد'
            ]);

            console.log(`         ✅ ${issue.case_number}: تم إدراج جلستين`);
          } catch (error) {
            console.log(`         ❌ خطأ في إدراج جلسات ${issue.case_number}: ${error.message}`);
          }
        }
      }

      // 6. تحديث جميع القضايا الحالية
      console.log('\n   🔄 تحديث جميع القضايا الحالية:');
      
      const allIssues = await pool.query(`SELECT id, case_number FROM issues`);
      
      for (const issue of allIssues.rows) {
        try {
          await pool.query(`SELECT update_next_hearing($1)`, [issue.id]);
          console.log(`      ✅ تم تحديث ${issue.case_number}`);
        } catch (error) {
          console.log(`      ❌ خطأ في تحديث ${issue.case_number}: ${error.message}`);
        }
      }

      // 7. اختبار الـ triggers
      console.log('\n   🧪 اختبار الـ triggers:');
      
      if (existingIssues.rows.length > 0) {
        const testIssue = existingIssues.rows[0];
        console.log(`      🧪 اختبار إدراج جلسة جديدة للقضية ${testIssue.case_number}...`);
        
        // إدراج جلسة جديدة أقرب
        const nearFutureDate = new Date();
        nearFutureDate.setDate(nearFutureDate.getDate() + 7); // بعد أسبوع
        
        const insertResult = await pool.query(`
          INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, hearing_date
        `, [
          testIssue.id, 
          nearFutureDate.toISOString(), 
          'حكم', 
          'scheduled', 
          'جلسة إصدار الحكم'
        ]);

        if (insertResult.rows.length > 0) {
          console.log(`      ✅ تم إدراج جلسة جديدة: ${insertResult.rows[0].hearing_date}`);
          
          // فحص تحديث next_hearing
          const updatedIssue = await pool.query(`
            SELECT next_hearing FROM issues WHERE id = $1
          `, [testIssue.id]);
          
          if (updatedIssue.rows.length > 0) {
            console.log(`      ✅ تم تحديث next_hearing: ${updatedIssue.rows[0].next_hearing}`);
          }
          
          // حذف الجلسة التجريبية
          await pool.query(`DELETE FROM hearings WHERE id = $1`, [insertResult.rows[0].id]);
          console.log(`      🗑️ تم حذف الجلسة التجريبية`);
        }
      }

      // 8. عرض النتائج النهائية
      console.log('\n   📊 النتائج النهائية:');
      
      const finalResults = await pool.query(`
        SELECT 
          i.case_number,
          i.next_hearing,
          COUNT(h.id) as total_hearings,
          COUNT(CASE WHEN h.hearing_date > CURRENT_TIMESTAMP AND h.status IN ('scheduled', 'postponed') THEN 1 END) as future_hearings,
          MIN(CASE WHEN h.hearing_date > CURRENT_TIMESTAMP AND h.status IN ('scheduled', 'postponed') THEN h.hearing_date END) as actual_next_hearing
        FROM issues i
        LEFT JOIN hearings h ON i.id = h.issue_id
        GROUP BY i.id, i.case_number, i.next_hearing
        ORDER BY i.case_number
      `);

      console.log('      📋 القضايا وجلساتها:');
      finalResults.rows.forEach(row => {
        const match = row.next_hearing === row.actual_next_hearing || 
                     (row.next_hearing === null && row.actual_next_hearing === null);
        console.log(`         ${match ? '✅' : '❌'} ${row.case_number}:`);
        console.log(`            - إجمالي الجلسات: ${row.total_hearings}`);
        console.log(`            - جلسات مستقبلية: ${row.future_hearings}`);
        console.log(`            - next_hearing: ${row.next_hearing || 'فارغ'}`);
        console.log(`            - الجلسة القادمة الفعلية: ${row.actual_next_hearing || 'فارغ'}`);
      });

      // 9. إحصائيات نهائية
      console.log('\n   📈 إحصائيات نهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM hearings) as total_hearings,
          (SELECT COUNT(*) FROM issues WHERE next_hearing IS NOT NULL) as issues_with_next_hearing,
          (SELECT COUNT(*) FROM hearings WHERE hearing_date > CURRENT_TIMESTAMP AND status IN ('scheduled', 'postponed')) as future_hearings,
          (SELECT COUNT(*) FROM hearings WHERE status = 'completed') as completed_hearings
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - إجمالي الجلسات: ${stats.total_hearings}`);
      console.log(`      - قضايا بها جلسة قادمة: ${stats.issues_with_next_hearing}`);
      console.log(`      - جلسات مستقبلية: ${stats.future_hearings}`);
      console.log(`      - جلسات مكتملة: ${stats.completed_hearings}`);

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح العمود next_hearing');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ إنشاء جدول hearings مع العلاقة الصحيحة');
  console.log('2. ✅ إضافة العمود next_hearing إلى جدول القضايا');
  console.log('3. ✅ إنشاء دالة update_next_hearing للتحديث التلقائي');
  console.log('4. ✅ إنشاء triggers للتحديث عند إدراج/تحديث/حذف الجلسات');
  console.log('5. ✅ تحديث جميع القضايا الحالية');
  console.log('6. ✅ اختبار الـ triggers والتأكد من عملها');
  console.log('7. ✅ العمود next_hearing يُحدث تلقائياً من جدول الجلسات');
}

// تشغيل الإصلاح
fixNextHearingColumn().catch(console.error);
