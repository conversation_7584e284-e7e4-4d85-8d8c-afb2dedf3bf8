(()=>{var e={};e.id=222,e.ids=[222],e.modules={568:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{DELETE:()=>u,GET:()=>c,POST:()=>i,PUT:()=>_});var n=s(32190),r=s(5069),o=e([r]);async function c(){try{let e=await (0,r.P)(`
      SELECT 
        als.*,
        coa.account_name as default_account_name,
        coa.account_code as default_account_code
      FROM account_linking_settings als
      LEFT JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      ORDER BY als.table_display_name
    `);return n.NextResponse.json({success:!0,data:e.rows})}catch(e){return console.error("Error fetching account linking settings:",e),n.NextResponse.json({success:!1,error:"فشل في جلب إعدادات الربط"},{status:500})}}async function i(e){try{let{table_name:t,table_display_name:s,table_description:a,is_enabled:o,auto_create_on_insert:c,account_prefix:i,name_field:u,id_field:_,default_main_account_id:d}=await e.json();if(!t||!s||!d)return n.NextResponse.json({success:!1,error:"البيانات المطلوبة غير مكتملة"},{status:400});let p=await (0,r.P)("SELECT id, account_name FROM chart_of_accounts WHERE id = $1",[d]);if(0===p.rows.length)return n.NextResponse.json({success:!1,error:"الحساب الرئيسي غير موجود"},{status:404});let E=await (0,r.P)(`
      INSERT INTO account_linking_settings 
      (table_name, table_display_name, table_description, is_enabled, 
       auto_create_on_insert, account_prefix, name_field, id_field, default_main_account_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (table_name) DO UPDATE SET
        table_display_name = $2,
        table_description = $3,
        is_enabled = $4,
        auto_create_on_insert = $5,
        account_prefix = $6,
        name_field = $7,
        id_field = $8,
        default_main_account_id = $9
      RETURNING *
    `,[t,s,a||"",!1!==o,!1!==c,i||"",u||"name",_||"id",d]);return await (0,r.P)(`
      UPDATE chart_of_accounts 
      SET linked_table = $1, auto_create_sub_accounts = $2
      WHERE id = $3
    `,[t,!1!==o,d]),!1!==o&&await l(d,t,u||"name"),n.NextResponse.json({success:!0,message:"تم حفظ إعدادات الربط بنجاح",data:E.rows[0]})}catch(e){return console.error("Error saving account linking settings:",e),n.NextResponse.json({success:!1,error:"فشل في حفظ إعدادات الربط"},{status:500})}}async function u(e){try{let{table_name:t}=await e.json();if(!t)return n.NextResponse.json({success:!1,error:"اسم الجدول مطلوب"},{status:400});let s=await (0,r.P)("SELECT default_main_account_id FROM account_linking_settings WHERE table_name = $1",[t]);if(s.rows.length>0){let e=s.rows[0].default_main_account_id;await (0,r.P)(`
        UPDATE chart_of_accounts 
        SET linked_table = NULL, auto_create_sub_accounts = false
        WHERE id = $1
      `,[e]),await (0,r.P)("DELETE FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2",[e,t])}return await (0,r.P)("DELETE FROM account_linking_settings WHERE table_name = $1",[t]),n.NextResponse.json({success:!0,message:"تم حذف إعدادات الربط بنجاح"})}catch(e){return console.error("Error deleting account linking settings:",e),n.NextResponse.json({success:!1,error:"فشل في حذف إعدادات الربط"},{status:500})}}async function _(e){try{let{updates:t}=await e.json();if(!t||!Array.isArray(t))return n.NextResponse.json({success:!1,error:"بيانات التحديث مطلوبة"},{status:400});for(let e of t){let{id:t,default_main_account_id:s}=e;t&&await (0,r.P)(`
        UPDATE account_linking_settings 
        SET default_main_account_id = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `,[s,t])}return n.NextResponse.json({success:!0,message:"تم تحديث الحسابات الافتراضية بنجاح"})}catch(e){return console.error("Error updating account linking settings:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث الحسابات الافتراضية"},{status:500})}}async function l(e,t,s){try{let n=await (0,r.P)("SELECT account_code, account_name FROM chart_of_accounts WHERE id = $1",[e]);if(0===n.rows.length)return;let{account_code:o}=n.rows[0];for(let n of(await (0,r.P)(`SELECT id, ${s} as name FROM ${t} WHERE is_active = true`)).rows){var a;let s=`${o}-${n.id.toString().padStart(4,"0")}`,c=`${{clients:"الموكل",employees:"الموظف",issues:"القضية",courts:"المحكمة",governorates:"المحافظة",branches:"الفرع"}[a=t]||a}: ${n.name}`,i=await (0,r.P)("SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3",[e,t,n.id]);0===i.rows.length&&await (0,r.P)(`
          INSERT INTO account_sub_links
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
        `,[e,t,n.id,s,c,"النظام"])}}catch(e){console.error("Error creating sub accounts for existing records:",e)}}r=(o.then?(await o)():o)[0],a()}catch(e){a(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{P:()=>_});var n=s(64939),r=s(29021),o=s.n(r),c=s(33873),i=s.n(c),u=e([n]);n=(u.then?(await u)():u)[0];let l=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],s=l.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new n.Pool(d);async function _(e,t){let s=await p.connect();try{return await s.query(e,t)}finally{s.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54734:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>_,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var n=s(96559),r=s(48088),o=s(37719),c=s(568),i=e([c]);c=(i.then?(await i)():i)[0];let _=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/account-linking-settings/route",pathname:"/api/account-linking-settings",filename:"route",bundlePath:"app/api/account-linking-settings/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\account-linking-settings\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:p}=_;function u(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,580],()=>s(54734));module.exports=a})();