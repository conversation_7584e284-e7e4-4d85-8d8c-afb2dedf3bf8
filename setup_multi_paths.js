// إعداد نظام المسارات المتعددة للمكتبة القانونية
const { Pool } = require('pg');

async function setupMultiPaths() {
  console.log('🔧 إعداد نظام المسارات المتعددة للمكتبة القانونية...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // إنشاء جدول مسارات المكتبة القانونية
      console.log('   🔨 إنشاء جدول legal_library_paths...');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS legal_library_paths (
          id SERIAL PRIMARY KEY,
          path_name VARCHAR(255) NOT NULL,
          path_value TEXT NOT NULL,
          description TEXT,
          is_active BOOLEAN DEFAULT true,
          is_default BOOLEAN DEFAULT false,
          scan_enabled BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء فهارس
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_legal_library_paths_active ON legal_library_paths(is_active)
      `);
      
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_legal_library_paths_default ON legal_library_paths(is_default)
      `);

      // إنشاء دالة التحديث
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_legal_library_paths_timestamp()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql
      `);

      // إنشاء المحفز
      await pool.query(`
        DROP TRIGGER IF EXISTS trigger_update_legal_library_paths_timestamp ON legal_library_paths
      `);
      
      await pool.query(`
        CREATE TRIGGER trigger_update_legal_library_paths_timestamp
          BEFORE UPDATE ON legal_library_paths
          FOR EACH ROW
          EXECUTE FUNCTION update_legal_library_paths_timestamp()
      `);

      console.log('   ✅ تم إنشاء الجدول والمحفزات');

      // إدراج المسارات الافتراضية
      console.log('   📝 إدراج المسارات الافتراضية...');
      
      const defaultPaths = [
        {
          name: 'المسار الرئيسي',
          value: 'D:\\mohaminew\\legal-documents',
          description: 'مجلد الوثائق القانونية الرئيسي',
          is_default: true,
          scan_enabled: true
        },
        {
          name: 'مسار النسخ الاحتياطي',
          value: 'D:\\mohaminew\\legal-backup',
          description: 'مجلد النسخ الاحتياطية للوثائق القانونية',
          is_default: false,
          scan_enabled: false
        },
        {
          name: 'مسار الأرشيف',
          value: 'D:\\mohaminew\\legal-archive',
          description: 'مجلد أرشيف الوثائق القانونية القديمة',
          is_default: false,
          scan_enabled: true
        }
      ];

      for (const pathData of defaultPaths) {
        await pool.query(`
          INSERT INTO legal_library_paths (path_name, path_value, description, is_active, is_default, scan_enabled) 
          VALUES ($1, $2, $3, true, $4, $5)
          ON CONFLICT DO NOTHING
        `, [pathData.name, pathData.value, pathData.description, pathData.is_default, pathData.scan_enabled]);
      }

      console.log(`   ✅ تم إدراج ${defaultPaths.length} مسار افتراضي`);

      // تحديث الإعدادات الموجودة
      console.log('   🔄 تحديث الإعدادات...');
      
      await pool.query(`
        UPDATE system_settings 
        SET setting_value = 'D:\\mohaminew\\legal-documents' 
        WHERE setting_key = 'legal_library_path'
      `);

      // إضافة إعدادات جديدة
      const newSettings = [
        {
          key: 'legal_library_multi_path_enabled',
          value: 'true',
          type: 'boolean',
          description: 'تفعيل دعم المسارات المتعددة'
        },
        {
          key: 'legal_library_auto_create_dirs',
          value: 'true',
          type: 'boolean',
          description: 'إنشاء المجلدات تلقائياً إذا لم تكن موجودة'
        },
        {
          key: 'legal_library_scan_interval',
          value: '60',
          type: 'number',
          description: 'فترة الفحص التلقائي بالدقائق'
        },
        {
          key: 'legal_library_max_paths',
          value: '10',
          type: 'number',
          description: 'الحد الأقصى لعدد المسارات'
        }
      ];

      for (const setting of newSettings) {
        await pool.query(`
          INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) 
          VALUES ($1, $2, $3, $4, true)
          ON CONFLICT (setting_key) DO NOTHING
        `, [setting.key, setting.value, setting.type, setting.description]);
      }

      console.log(`   ✅ تم إضافة ${newSettings.length} إعداد جديد`);

      // التحقق من النتائج
      const pathsResult = await pool.query('SELECT COUNT(*) as count FROM legal_library_paths');
      const settingsResult = await pool.query('SELECT COUNT(*) as count FROM system_settings WHERE setting_key LIKE \'legal_library_%\'');
      
      console.log(`   📊 إجمالي المسارات: ${pathsResult.rows[0].count}`);
      console.log(`   📊 إجمالي الإعدادات: ${settingsResult.rows[0].count}`);

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من إعداد نظام المسارات المتعددة');
}

// تشغيل الإعداد
setupMultiPaths().catch(console.error);
