import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع روابط التذييل
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM footer_links 
      ORDER BY category, sort_order ASC, created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('خطأ في جلب روابط التذييل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب روابط التذييل' },
      { status: 500 }
    )
  }
}

// POST - إضافة رابط جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      category,
      name,
      href,
      sort_order = 0,
      is_active = true
    } = body

    if (!category || !name || !href) {
      return NextResponse.json(
        { success: false, error: 'الفئة والاسم والرابط مطلوبة' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO footer_links (
        category, name, href, sort_order, is_active
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [category, name, href, sort_order, is_active])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الرابط بنجاح'
    })
  } catch (error) {
    console.error('خطأ في إضافة الرابط:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الرابط' },
      { status: 500 }
    )
  }
}
