{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/accounting/chart-of-accounts/[id]", "regex": "^/api/accounting/chart\\-of\\-accounts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/accounting/chart\\-of\\-accounts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/accounting/journal-entries/[id]", "regex": "^/api/accounting/journal\\-entries/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/accounting/journal\\-entries/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/accounting/reports/[reportType]", "regex": "^/api/accounting/reports/([^/]+?)(?:/)?$", "routeKeys": {"nxtPreportType": "nxtPreportType"}, "namedRegex": "^/api/accounting/reports/(?<nxtPreportType>[^/]+?)(?:/)?$"}, {"page": "/api/chart-of-accounts/[id]", "regex": "^/api/chart\\-of\\-accounts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/chart\\-of\\-accounts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/chart-of-accounts-new/[id]", "regex": "^/api/chart\\-of\\-accounts\\-new/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/chart\\-of\\-accounts\\-new/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/client-portal/notifications/[id]", "regex": "^/api/client\\-portal/notifications/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/client\\-portal/notifications/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/companies/[id]", "regex": "^/api/companies/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/companies/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/documents/download/[id]", "regex": "^/api/documents/download/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/documents/download/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/footer-links/[id]", "regex": "^/api/footer\\-links/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/footer\\-links/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/issues/[id]", "regex": "^/api/issues/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/issues/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/main-accounts/[id]", "regex": "^/api/main\\-accounts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/main\\-accounts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/services/[slug]", "regex": "^/api/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/services/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/serviceslow/slug/[slug]", "regex": "^/api/serviceslow/slug/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/serviceslow/slug/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/serviceslow/[id]", "regex": "^/api/serviceslow/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/serviceslow/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/settings/announcements/[id]", "regex": "^/api/settings/announcements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/settings/announcements/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/settings/public-announcements/[id]", "regex": "^/api/settings/public\\-announcements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/settings/public\\-announcements/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/services/[slug]", "regex": "^/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/services/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/serviceslow/[slug]", "regex": "^/serviceslow/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/serviceslow/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/accounting", "regex": "^/accounting(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting(?:/)?$"}, {"page": "/accounting/account-linking", "regex": "^/accounting/account\\-linking(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/account\\-linking(?:/)?$"}, {"page": "/accounting/chart-of-accounts", "regex": "^/accounting/chart\\-of\\-accounts(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/chart\\-of\\-accounts(?:/)?$"}, {"page": "/accounting/create-sample-data", "regex": "^/accounting/create\\-sample\\-data(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/create\\-sample\\-data(?:/)?$"}, {"page": "/accounting/debtors-creditors", "regex": "^/accounting/debtors\\-creditors(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/debtors\\-creditors(?:/)?$"}, {"page": "/accounting/default-accounts", "regex": "^/accounting/default\\-accounts(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/default\\-accounts(?:/)?$"}, {"page": "/accounting/implement-structure", "regex": "^/accounting/implement\\-structure(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/implement\\-structure(?:/)?$"}, {"page": "/accounting/journal-entries", "regex": "^/accounting/journal\\-entries(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/journal\\-entries(?:/)?$"}, {"page": "/accounting/link-accounts", "regex": "^/accounting/link\\-accounts(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/link\\-accounts(?:/)?$"}, {"page": "/accounting/main-accounts", "regex": "^/accounting/main\\-accounts(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/main\\-accounts(?:/)?$"}, {"page": "/accounting/opening-balances", "regex": "^/accounting/opening\\-balances(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/opening\\-balances(?:/)?$"}, {"page": "/accounting/payment-vouchers", "regex": "^/accounting/payment\\-vouchers(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/payment\\-vouchers(?:/)?$"}, {"page": "/accounting/receipt-vouchers", "regex": "^/accounting/receipt\\-vouchers(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/receipt\\-vouchers(?:/)?$"}, {"page": "/accounting/reports", "regex": "^/accounting/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/reports(?:/)?$"}, {"page": "/accounting/reports/account-statement", "regex": "^/accounting/reports/account\\-statement(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting/reports/account\\-statement(?:/)?$"}, {"page": "/admin/ai-settings", "regex": "^/admin/ai\\-settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ai\\-settings(?:/)?$"}, {"page": "/admin/database-viewer", "regex": "^/admin/database\\-viewer(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/database\\-viewer(?:/)?$"}, {"page": "/admin/serviceslow", "regex": "^/admin/serviceslow(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/serviceslow(?:/)?$"}, {"page": "/branches", "regex": "^/branches(?:/)?$", "routeKeys": {}, "namedRegex": "^/branches(?:/)?$"}, {"page": "/case-distribution", "regex": "^/case\\-distribution(?:/)?$", "routeKeys": {}, "namedRegex": "^/case\\-distribution(?:/)?$"}, {"page": "/case-reports", "regex": "^/case\\-reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/case\\-reports(?:/)?$"}, {"page": "/chat-logs", "regex": "^/chat\\-logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/chat\\-logs(?:/)?$"}, {"page": "/client-accounts", "regex": "^/client\\-accounts(?:/)?$", "routeKeys": {}, "namedRegex": "^/client\\-accounts(?:/)?$"}, {"page": "/client-login", "regex": "^/client\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/client\\-login(?:/)?$"}, {"page": "/client-portal", "regex": "^/client\\-portal(?:/)?$", "routeKeys": {}, "namedRegex": "^/client\\-portal(?:/)?$"}, {"page": "/clients", "regex": "^/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/clients(?:/)?$"}, {"page": "/company", "regex": "^/company(?:/)?$", "routeKeys": {}, "namedRegex": "^/company(?:/)?$"}, {"page": "/courts", "regex": "^/courts(?:/)?$", "routeKeys": {}, "namedRegex": "^/courts(?:/)?$"}, {"page": "/create-sample-clients-employees", "regex": "^/create\\-sample\\-clients\\-employees(?:/)?$", "routeKeys": {}, "namedRegex": "^/create\\-sample\\-clients\\-employees(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/documents", "regex": "^/documents(?:/)?$", "routeKeys": {}, "namedRegex": "^/documents(?:/)?$"}, {"page": "/documents/archive", "regex": "^/documents/archive(?:/)?$", "routeKeys": {}, "namedRegex": "^/documents/archive(?:/)?$"}, {"page": "/documents/upload", "regex": "^/documents/upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/documents/upload(?:/)?$"}, {"page": "/employee-reports", "regex": "^/employee\\-reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/employee\\-reports(?:/)?$"}, {"page": "/employees", "regex": "^/employees(?:/)?$", "routeKeys": {}, "namedRegex": "^/employees(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/financial-reports", "regex": "^/financial\\-reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/financial\\-reports(?:/)?$"}, {"page": "/follows", "regex": "^/follows(?:/)?$", "routeKeys": {}, "namedRegex": "^/follows(?:/)?$"}, {"page": "/governorates", "regex": "^/governorates(?:/)?$", "routeKeys": {}, "namedRegex": "^/governorates(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/invoices", "regex": "^/invoices(?:/)?$", "routeKeys": {}, "namedRegex": "^/invoices(?:/)?$"}, {"page": "/issue-types", "regex": "^/issue\\-types(?:/)?$", "routeKeys": {}, "namedRegex": "^/issue\\-types(?:/)?$"}, {"page": "/issues", "regex": "^/issues(?:/)?$", "routeKeys": {}, "namedRegex": "^/issues(?:/)?$"}, {"page": "/issues/new", "regex": "^/issues/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/issues/new(?:/)?$"}, {"page": "/journal-entries-new", "regex": "^/journal\\-entries\\-new(?:/)?$", "routeKeys": {}, "namedRegex": "^/journal\\-entries\\-new(?:/)?$"}, {"page": "/lawyer-earnings", "regex": "^/lawyer\\-earnings(?:/)?$", "routeKeys": {}, "namedRegex": "^/lawyer\\-earnings(?:/)?$"}, {"page": "/legal-library", "regex": "^/legal\\-library(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal\\-library(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/movements", "regex": "^/movements(?:/)?$", "routeKeys": {}, "namedRegex": "^/movements(?:/)?$"}, {"page": "/opening-balances", "regex": "^/opening\\-balances(?:/)?$", "routeKeys": {}, "namedRegex": "^/opening\\-balances(?:/)?$"}, {"page": "/payment-vouchers", "regex": "^/payment\\-vouchers(?:/)?$", "routeKeys": {}, "namedRegex": "^/payment\\-vouchers(?:/)?$"}, {"page": "/percentages", "regex": "^/percentages(?:/)?$", "routeKeys": {}, "namedRegex": "^/percentages(?:/)?$"}, {"page": "/public-home", "regex": "^/public\\-home(?:/)?$", "routeKeys": {}, "namedRegex": "^/public\\-home(?:/)?$"}, {"page": "/receipt-vouchers", "regex": "^/receipt\\-vouchers(?:/)?$", "routeKeys": {}, "namedRegex": "^/receipt\\-vouchers(?:/)?$"}, {"page": "/reports", "regex": "^/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/reports(?:/)?$"}, {"page": "/roles", "regex": "^/roles(?:/)?$", "routeKeys": {}, "namedRegex": "^/roles(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/serviceslow", "regex": "^/serviceslow(?:/)?$", "routeKeys": {}, "namedRegex": "^/serviceslow(?:/)?$"}, {"page": "/settings/announcements", "regex": "^/settings/announcements(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/announcements(?:/)?$"}, {"page": "/settings/cost-centers", "regex": "^/settings/cost\\-centers(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/cost\\-centers(?:/)?$"}, {"page": "/settings/footer-links", "regex": "^/settings/footer\\-links(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/footer\\-links(?:/)?$"}, {"page": "/settings/navigation-pages", "regex": "^/settings/navigation\\-pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/navigation\\-pages(?:/)?$"}, {"page": "/setup", "regex": "^/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup(?:/)?$"}, {"page": "/setup-integrated", "regex": "^/setup\\-integrated(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup\\-integrated(?:/)?$"}, {"page": "/setup-simple", "regex": "^/setup\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup\\-simple(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test-db", "regex": "^/test\\-db(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-db(?:/)?$"}, {"page": "/test-issue-select", "regex": "^/test\\-issue\\-select(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-issue\\-select(?:/)?$"}, {"page": "/test-select", "regex": "^/test\\-select(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-select(?:/)?$"}, {"page": "/test-selects", "regex": "^/test\\-selects(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-selects(?:/)?$"}, {"page": "/time-tracking", "regex": "^/time\\-tracking(?:/)?$", "routeKeys": {}, "namedRegex": "^/time\\-tracking(?:/)?$"}, {"page": "/trial-balance", "regex": "^/trial\\-balance(?:/)?$", "routeKeys": {}, "namedRegex": "^/trial\\-balance(?:/)?$"}, {"page": "/under-construction", "regex": "^/under\\-construction(?:/)?$", "routeKeys": {}, "namedRegex": "^/under\\-construction(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/website-admin", "regex": "^/website\\-admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/website\\-admin(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}