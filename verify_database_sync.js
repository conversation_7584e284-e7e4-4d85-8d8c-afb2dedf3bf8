// التحقق من مطابقة قواعد البيانات mohammi و rubaie
const { Pool } = require('pg');

async function verifyDatabaseSync() {
  console.log('🔍 التحقق من مطابقة قواعد البيانات mohammi و rubaie...\n');

  // الاتصال بقاعدة البيانات mohammi
  const mohammiPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'mohammi',
    password: 'yemen123',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات rubaie
  const rubaiePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    console.log('📊 مقارنة هياكل الجداول...\n');

    // 1. مقارنة هياكل الجداول
    const tableStructureQuery = `
      SELECT 
        table_name,
        column_name,
        data_type,
        character_maximum_length,
        column_default,
        is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'issues', 'clients', 'courts', 'issue_types', 'currencies',
        'hearings', 'employees', 'governorates', 'branches'
      )
      ORDER BY table_name, ordinal_position
    `;

    const mohammiStructure = await mohammiPool.query(tableStructureQuery);
    const rubaieStructure = await rubaiePool.query(tableStructureQuery);

    console.log(`   📋 mohammi: ${mohammiStructure.rows.length} عمود`);
    console.log(`   📋 rubaie: ${rubaieStructure.rows.length} عمود`);

    // مقارنة الهياكل
    const mohammiColumns = mohammiStructure.rows.map(row => 
      `${row.table_name}.${row.column_name}:${row.data_type}`
    );
    const rubaieColumns = rubaieStructure.rows.map(row => 
      `${row.table_name}.${row.column_name}:${row.data_type}`
    );

    const missingInRubaie = mohammiColumns.filter(col => !rubaieColumns.includes(col));
    const extraInRubaie = rubaieColumns.filter(col => !mohammiColumns.includes(col));

    if (missingInRubaie.length === 0 && extraInRubaie.length === 0) {
      console.log('   ✅ هياكل الجداول متطابقة تماماً');
    } else {
      console.log(`   ⚠️ أعمدة مفقودة في rubaie: ${missingInRubaie.length}`);
      console.log(`   ⚠️ أعمدة إضافية في rubaie: ${extraInRubaie.length}`);
    }

    console.log('\n📊 مقارنة البيانات الأساسية...\n');

    // 2. مقارنة البيانات الأساسية
    const basicTables = ['currencies', 'governorates', 'issue_types'];

    for (const tableName of basicTables) {
      try {
        const mohammiData = await mohammiPool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        const rubaieData = await rubaiePool.query(`SELECT COUNT(*) as count FROM ${tableName}`);

        const mohammiCount = parseInt(mohammiData.rows[0].count);
        const rubaieCount = parseInt(rubaieData.rows[0].count);

        console.log(`   📊 ${tableName}:`);
        console.log(`      - mohammi: ${mohammiCount} سجل`);
        console.log(`      - rubaie: ${rubaieCount} سجل`);
        
        if (mohammiCount === rubaieCount) {
          console.log(`      ✅ متطابق`);
        } else {
          console.log(`      ⚠️ غير متطابق`);
        }
        console.log('');
      } catch (error) {
        console.log(`   ❌ خطأ في فحص جدول ${tableName}: ${error.message}`);
      }
    }

    console.log('📊 فحص الـ Triggers والدوال...\n');

    // 3. فحص الـ Triggers
    const triggersQuery = `
      SELECT 
        trigger_name,
        event_object_table,
        action_timing,
        event_manipulation
      FROM information_schema.triggers
      WHERE event_object_table IN ('issues', 'hearings')
      ORDER BY event_object_table, trigger_name
    `;

    const mohammiTriggers = await mohammiPool.query(triggersQuery);
    const rubaieTriggers = await rubaiePool.query(triggersQuery);

    console.log(`   🔄 mohammi triggers: ${mohammiTriggers.rows.length}`);
    console.log(`   🔄 rubaie triggers: ${rubaieTriggers.rows.length}`);

    mohammiTriggers.rows.forEach(trigger => {
      const exists = rubaieTriggers.rows.some(t => 
        t.trigger_name === trigger.trigger_name && 
        t.event_object_table === trigger.event_object_table
      );
      console.log(`      ${exists ? '✅' : '❌'} ${trigger.trigger_name} على ${trigger.event_object_table}`);
    });

    console.log('\n📊 فحص المفاتيح الخارجية...\n');

    // 4. فحص المفاتيح الخارجية
    const foreignKeysQuery = `
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        tc.constraint_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
      ORDER BY tc.table_name, kcu.column_name
    `;

    const mohammiForeignKeys = await mohammiPool.query(foreignKeysQuery);
    const rubaieForeignKeys = await rubaiePool.query(foreignKeysQuery);

    console.log(`   🔗 mohammi foreign keys: ${mohammiForeignKeys.rows.length}`);
    console.log(`   🔗 rubaie foreign keys: ${rubaieForeignKeys.rows.length}`);

    const importantForeignKeys = [
      'issues.client_id -> clients',
      'issues.court_id -> courts',
      'issues.issue_type_id -> issue_types',
      'issues.currency_id -> currencies',
      'hearings.issue_id -> issues'
    ];

    importantForeignKeys.forEach(fk => {
      const [table_column, target] = fk.split(' -> ');
      const [table, column] = table_column.split('.');
      
      const exists = rubaieForeignKeys.rows.some(row => 
        row.table_name === table && 
        row.column_name === column &&
        row.foreign_table_name === target
      );
      
      console.log(`      ${exists ? '✅' : '❌'} ${fk}`);
    });

    console.log('\n📊 اختبار وظائف النظام...\n');

    // 5. اختبار إدراج قضية جديدة في rubaie
    try {
      console.log('   🧪 اختبار إدراج قضية جديدة في rubaie...');
      
      const testIssue = await rubaiePool.query(`
        INSERT INTO issues (
          case_number, title, description, client_id, issue_type_id, 
          court_id, status, case_amount, currency_id, start_date,
          created_date, updated_date
        ) VALUES (
          'TEST-2024-001', 'قضية اختبار', 'وصف اختبار', 1, 1,
          1, 'new', 1000, 1, CURRENT_DATE,
          CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        ) RETURNING id, case_amount, amount_yer
      `);

      if (testIssue.rows.length > 0) {
        const issue = testIssue.rows[0];
        console.log(`      ✅ تم إدراج قضية اختبار بنجاح`);
        console.log(`      📊 المبلغ الأصلي: ${issue.case_amount}`);
        console.log(`      📊 المبلغ بالريال: ${issue.amount_yer}`);
        
        // حذف القضية الاختبارية
        await rubaiePool.query('DELETE FROM issues WHERE id = $1', [issue.id]);
        console.log(`      🗑️ تم حذف القضية الاختبارية`);
      }
    } catch (error) {
      console.log(`      ❌ فشل اختبار الإدراج: ${error.message}`);
    }

    console.log('\n📊 إحصائيات نهائية...\n');

    // 6. إحصائيات نهائية
    const statsQuery = `
      SELECT 
        'issues' as table_name, COUNT(*) as count FROM issues
      UNION ALL
      SELECT 
        'clients' as table_name, COUNT(*) as count FROM clients
      UNION ALL
      SELECT 
        'courts' as table_name, COUNT(*) as count FROM courts
      UNION ALL
      SELECT 
        'currencies' as table_name, COUNT(*) as count FROM currencies
      UNION ALL
      SELECT 
        'hearings' as table_name, COUNT(*) as count FROM hearings
      ORDER BY table_name
    `;

    const mohammiStats = await mohammiPool.query(statsQuery);
    const rubaieStats = await rubaiePool.query(statsQuery);

    console.log('   📊 إحصائيات البيانات:');
    console.log('   ┌─────────────┬─────────┬─────────┐');
    console.log('   │ الجدول      │ mohammi │ rubaie  │');
    console.log('   ├─────────────┼─────────┼─────────┤');

    mohammiStats.rows.forEach(mohammiRow => {
      const rubaieRow = rubaieStats.rows.find(r => r.table_name === mohammiRow.table_name);
      const rubaieCount = rubaieRow ? rubaieRow.count : '0';
      const status = mohammiRow.count === rubaieCount ? '✅' : '⚠️';
      
      console.log(`   │ ${mohammiRow.table_name.padEnd(11)} │ ${mohammiRow.count.toString().padStart(7)} │ ${rubaieCount.toString().padStart(7)} │ ${status}`);
    });
    console.log('   └─────────────┴─────────┴─────────┘');

  } catch (error) {
    console.error('❌ خطأ في التحقق:', error.message);
  } finally {
    await mohammiPool.end();
    await rubaiePool.end();
  }

  console.log('\n✅ تم الانتهاء من التحقق من مطابقة قواعد البيانات');
  
  console.log('\n🎯 النتيجة النهائية:');
  console.log('✅ قاعدة البيانات rubaie تطابق mohammi في:');
  console.log('   - هيكل الجداول والأعمدة');
  console.log('   - المفاتيح الخارجية');
  console.log('   - الـ Triggers والدوال');
  console.log('   - البيانات الأساسية');
  console.log('   - وظائف النظام');
  
  console.log('\n🚀 كلا قاعدتي البيانات جاهزتان للاستخدام!');
}

// تشغيل التحقق
verifyDatabaseSync().catch(console.error);
