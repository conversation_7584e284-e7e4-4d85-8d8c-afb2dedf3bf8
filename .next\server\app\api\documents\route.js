/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/documents/route";
exports.ids = ["app/api/documents/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Froute&page=%2Fapi%2Fdocuments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Froute&page=%2Fapi%2Fdocuments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_documents_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/documents/route.ts */ \"(rsc)/./src/app/api/documents/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_documents_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_documents_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/documents/route\",\n        pathname: \"/api/documents\",\n        filename: \"route\",\n        bundlePath: \"app/api/documents/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\documents\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_documents_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Froute&page=%2Fapi%2Fdocuments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/documents/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/documents/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع الوثائق مع إمكانية البحث والتصفية\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get('search') || '';\n        const category = searchParams.get('category') || '';\n        const caseId = searchParams.get('case_id') || '';\n        const clientId = searchParams.get('client_id') || '';\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = (page - 1) * limit;\n        let whereConditions = [\n            'd.is_active = true'\n        ];\n        let queryParams = [];\n        let paramIndex = 1;\n        // البحث في النص والعنوان\n        if (search) {\n            whereConditions.push(`(\n        d.title ILIKE $${paramIndex} OR \n        d.description ILIKE $${paramIndex} OR \n        d.content_text ILIKE $${paramIndex} OR\n        $${paramIndex} = ANY(d.tags)\n      )`);\n            queryParams.push(`%${search}%`);\n            paramIndex++;\n        }\n        // تصفية حسب الفئة\n        if (category) {\n            whereConditions.push(`d.category = $${paramIndex}`);\n            queryParams.push(category);\n            paramIndex++;\n        }\n        // تصفية حسب القضية\n        if (caseId) {\n            whereConditions.push(`d.case_id = $${paramIndex}`);\n            queryParams.push(parseInt(caseId));\n            paramIndex++;\n        }\n        // تصفية حسب العميل\n        if (clientId) {\n            whereConditions.push(`d.client_id = $${paramIndex}`);\n            queryParams.push(parseInt(clientId));\n            paramIndex++;\n        }\n        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';\n        // الاستعلام الرئيسي (مبسط)\n        const documentsQuery = `\n      SELECT \n        d.*,\n        c.name as client_name,\n        i.title as case_title,\n        i.case_number,\n        COUNT(*) OVER() as total_count\n      FROM documents d\n      LEFT JOIN clients c ON d.client_id = c.id\n      LEFT JOIN issues i ON d.case_id = i.id\n      ${whereClause}\n      ORDER BY d.created_date DESC\n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `;\n        queryParams.push(limit, offset);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(documentsQuery, queryParams);\n        // جلب إحصائيات الفئات\n        const categoriesQuery = `\n      SELECT category, COUNT(*) as count\n      FROM documents \n      WHERE is_active = true\n      GROUP BY category\n      ORDER BY count DESC\n    `;\n        const categoriesResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(categoriesQuery);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                documents: result.rows,\n                totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,\n                currentPage: page,\n                totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,\n                categories: categoriesResult.rows\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب الوثائق:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الوثائق'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة وثيقة جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { title, description, fileName, filePath, fileSize, fileType, mimeType, caseId, clientId, employeeId, category, subcategory, tags, contentText, accessLevel, isConfidential, uploadedBy } = body;\n        // التحقق من البيانات المطلوبة\n        if (!title || !fileName || !filePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات المطلوبة مفقودة'\n            }, {\n                status: 400\n            });\n        }\n        const insertQuery = `\n      INSERT INTO documents (\n        title, description, file_name, file_path, file_size, file_type, mime_type,\n        case_id, client_id, employee_id, category, subcategory, tags, content_text,\n        access_level, is_confidential, uploaded_by\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17\n      ) RETURNING *\n    `;\n        const values = [\n            title,\n            description,\n            fileName,\n            filePath,\n            fileSize,\n            fileType,\n            mimeType,\n            caseId || null,\n            clientId || null,\n            employeeId || null,\n            category || 'general',\n            subcategory,\n            tags || [],\n            contentText,\n            accessLevel || 'private',\n            isConfidential || false,\n            uploadedBy\n        ];\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(insertQuery, values);\n        // إنشاء إشعار للعميل إذا كانت الوثيقة مرتبطة بقضية\n        if (caseId && clientId) {\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        INSERT INTO client_notifications (client_id, case_id, title, message, type)\n        VALUES ($1, $2, $3, $4, $5)\n      `, [\n                clientId,\n                caseId,\n                'وثيقة جديدة',\n                `تم إضافة وثيقة جديدة: ${title}`,\n                'info'\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم إضافة الوثيقة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة الوثيقة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الوثيقة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث وثيقة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, title, description, category, subcategory, tags, accessLevel, isConfidential } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الوثيقة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const updateQuery = `\n      UPDATE documents \n      SET \n        title = COALESCE($2, title),\n        description = COALESCE($3, description),\n        category = COALESCE($4, category),\n        subcategory = COALESCE($5, subcategory),\n        tags = COALESCE($6, tags),\n        access_level = COALESCE($7, access_level),\n        is_confidential = COALESCE($8, is_confidential),\n        updated_at = CURRENT_TIMESTAMP\n      WHERE id = $1 AND is_active = true\n      RETURNING *\n    `;\n        const values = [\n            id,\n            title,\n            description,\n            category,\n            subcategory,\n            tags,\n            accessLevel,\n            isConfidential\n        ];\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(updateQuery, values);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الوثيقة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث الوثيقة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث الوثيقة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث الوثيقة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف وثيقة (حذف منطقي)\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الوثيقة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const deleteQuery = `\n      UPDATE documents \n      SET is_active = false, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $1 AND is_active = true\n      RETURNING id, title\n    `;\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(deleteQuery, [\n            parseInt(id)\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الوثيقة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الوثيقة بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف الوثيقة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف الوثيقة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/documents/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Froute&page=%2Fapi%2Fdocuments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();