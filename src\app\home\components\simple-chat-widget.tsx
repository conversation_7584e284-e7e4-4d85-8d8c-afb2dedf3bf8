'use client';

import { useState, useRef, useEffect } from 'react';
import { X, Send, Bot, Sparkles, Loader2 } from 'lucide-react';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  status: 'sending' | 'delivered' | 'error';
};

interface SimpleChatWidgetProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
}

export function SimpleChatWidget({ isOpen, onClose, onOpen }: SimpleChatWidgetProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [guestId, setGuestId] = useState('');
  const [companyData, setCompanyData] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const [messages, setMessages] = useState<Message[]>([]);

  // إنشاء معرف زائر فريد وجلب بيانات الشركة
  useEffect(() => {
    let guestIdentifier = localStorage.getItem('guestId');
    if (!guestIdentifier) {
      guestIdentifier = `guest_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem('guestId', guestIdentifier);
    }
    setGuestId(guestIdentifier);

    // جلب بيانات الشركة
    fetchCompanyData();
  }, []);

  // جلب بيانات الشركة
  const fetchCompanyData = async () => {
    try {
      const response = await fetch('/api/companies');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.length > 0) {
          const company = result.data[0];
          setCompanyData(company);

          // إنشاء الرسائل الترحيبية
          const welcomeMessages: Message[] = [
            {
              id: '1',
              content: `مرحباً بك في ${company.name}! 🏛️`,
              sender: 'assistant',
              timestamp: new Date(),
              status: 'delivered'
            },
            {
              id: '2',
              content: 'يمكنني مساعدتك في الاستشارات القانونية. كيف يمكنني مساعدتك؟',
              sender: 'assistant',
              timestamp: new Date(),
              status: 'delivered'
            }
          ];

          setMessages(welcomeMessages);
        }
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      // رسائل افتراضية
      const defaultMessages: Message[] = [
        {
          id: '1',
          content: 'مرحباً! كيف يمكنني مساعدتك؟ 🏛️',
          sender: 'assistant',
          timestamp: new Date(),
          status: 'delivered'
        }
      ];
      setMessages(defaultMessages);
    }
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) return;

    console.log('📤 Sending message:', message);

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date(),
      status: 'delivered'
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsTyping(true);

    try {
      // إرسال للذكاء الاصطناعي
      const response = await sendToAI(message);

      // Add AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.message,
        sender: 'assistant',
        timestamp: new Date(),
        status: 'delivered'
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'أعتذر، حدث خطأ. يرجى المحاولة مرة أخرى.',
        sender: 'assistant',
        timestamp: new Date(),
        status: 'error'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      // إعادة التركيز على مربع النص بعد الإرسال
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  const sendToAI = async (userMessage: string): Promise<{message: string}> => {
    try {
      console.log('🤖 Sending message to AI:', userMessage);

      const response = await fetch('/api/ai/local-models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          model: 'groq-llama-8b',
          conversationId: `guest_${guestId}_${Date.now()}`,
          context: []
        })
      });

      if (!response.ok) {
        throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');
      }

      const result = await response.json();
      console.log('🤖 AI Response:', result);

      if (result.success && result.response) {
        return {
          message: result.response
        };
      } else {
        throw new Error(result.error || 'خطأ في الاستجابة');
      }
    } catch (error) {
      console.error('AI Error:', error);

      // رد احتياطي
      const fallbackResponses = [
        `أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: ${companyData?.phone || '+967-1-123456'}`,
        'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',
        'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.'
      ];

      const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

      return {
        message: randomResponse
      };
    }
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 left-6 z-50">
        <button
          onClick={onOpen}
          className="h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700 flex items-center justify-center"
          title="المحادثات مع الذكاء الاصطناعي"
        >
          <Bot className="h-7 w-7 text-white" />
          <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse">
            <Sparkles className="h-3 w-3" />
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-24 left-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col">
      {/* رأس النافذة */}
      <div className="flex items-center justify-between p-3 border-b bg-blue-600 text-white rounded-t-lg">
        <div className="flex items-center">
          <Bot className="h-4 w-4 mr-2" />
          <div>
            <h3 className="font-semibold text-sm">
              المحادثات المباشرة
            </h3>
            <div className="flex items-center">
              <div className="bg-green-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                <Sparkles className="h-2 w-2 mr-1" />
                AI
              </div>
              <span className="text-xs text-blue-100 mr-2">متصل الآن</span>
            </div>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-1 rounded-full hover:bg-white/20"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* منطقة الرسائل */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-sm px-4 py-3 rounded-lg ${
                msg.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <p className="text-sm whitespace-pre-wrap leading-relaxed">{msg.content}</p>
              <p className="text-xs mt-2 opacity-70">
                {msg.timestamp.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-800 px-4 py-3 rounded-lg">
              <div className="flex items-center space-x-1 space-x-reverse">
                <Loader2 className="w-4 h-4 animate-spin ml-2" />
                <span className="text-sm">المساعد الذكي يكتب...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* منطقة الإدخال */}
      <div className="p-4 border-t">
        <form onSubmit={handleSendMessage} className="flex space-x-2 space-x-reverse">
          <input
            ref={inputRef}
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="اكتب رسالتك..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
            disabled={isTyping}
            dir="rtl"
          />
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
            disabled={!message.trim() || isTyping}
          >
            {isTyping ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
