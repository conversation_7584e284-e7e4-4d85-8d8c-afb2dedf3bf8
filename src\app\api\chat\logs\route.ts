import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب سجلات المحادثة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const sessionId = searchParams.get('session_id')
    const responseType = searchParams.get('response_type')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    const offset = (page - 1) * limit

    // بناء الاستعلام
    let whereConditions = []
    let queryParams = []
    let paramIndex = 1

    if (sessionId) {
      whereConditions.push(`session_id = $${paramIndex}`)
      queryParams.push(sessionId)
      paramIndex++
    }

    if (responseType) {
      whereConditions.push(`response_type = $${paramIndex}`)
      queryParams.push(responseType)
      paramIndex++
    }

    if (startDate) {
      whereConditions.push(`created_at >= $${paramIndex}`)
      queryParams.push(startDate)
      paramIndex++
    }

    if (endDate) {
      whereConditions.push(`created_at <= $${paramIndex}`)
      queryParams.push(endDate)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // جلب السجلات
    const logsQuery = `
      SELECT id, session_id, user_message, bot_response, response_type, user_ip, created_at
      FROM chat_logs 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    queryParams.push(limit, offset)

    const logsResult = await query(logsQuery, queryParams)

    // جلب العدد الإجمالي
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM chat_logs 
      ${whereClause}
    `
    const countResult = await query(countQuery, queryParams.slice(0, -2)) // إزالة limit و offset

    // إحصائيات إضافية
    const statsQuery = `
      SELECT 
        response_type,
        COUNT(*) as count
      FROM chat_logs 
      ${whereClause}
      GROUP BY response_type
      ORDER BY count DESC
    `
    const statsResult = await query(statsQuery, queryParams.slice(0, -2))

    return NextResponse.json({
      success: true,
      data: {
        logs: logsResult.rows,
        pagination: {
          page,
          limit,
          total: parseInt(countResult.rows[0].total),
          totalPages: Math.ceil(parseInt(countResult.rows[0].total) / limit)
        },
        stats: statsResult.rows
      }
    })

  } catch (error) {
    console.error('خطأ في جلب سجلات المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب السجلات' },
      { status: 500 }
    )
  }
}

// DELETE - حذف سجلات المحادثة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const sessionId = searchParams.get('session_id')
    const beforeDate = searchParams.get('before_date')

    if (action === 'clear_all') {
      // حذف جميع السجلات
      await query('DELETE FROM chat_logs')
      
      return NextResponse.json({
        success: true,
        message: 'تم حذف جميع سجلات المحادثة'
      })
    }

    if (action === 'clear_session' && sessionId) {
      // حذف سجلات جلسة محددة
      await query('DELETE FROM chat_logs WHERE session_id = $1', [sessionId])
      
      return NextResponse.json({
        success: true,
        message: 'تم حذف سجلات الجلسة'
      })
    }

    if (action === 'clear_old' && beforeDate) {
      // حذف السجلات القديمة
      const result = await query('DELETE FROM chat_logs WHERE created_at < $1', [beforeDate])
      
      return NextResponse.json({
        success: true,
        message: `تم حذف ${result.rowCount} سجل قديم`
      })
    }

    return NextResponse.json(
      { success: false, error: 'إجراء غير صحيح' },
      { status: 400 }
    )

  } catch (error) {
    console.error('خطأ في حذف سجلات المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في حذف السجلات' },
      { status: 500 }
    )
  }
}
