(()=>{var e={};e.id=8654,e.ids=[8654],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,n)=>{"use strict";n.a(e,async(e,c)=>{try{n.d(t,{P:()=>_});var r=n(64939),a=n(29021),s=n.n(a),o=n(33873),i=n.n(o),u=e([r]);r=(u.then?(await u)():u)[0];let l=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=s().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],n=l.default_config;return{database:t.database,user:n.db_user,host:n.db_host,password:process.env.DB_PASSWORD||n.db_password,port:n.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),p=new r.Pool(d);async function _(e,t){let n=await p.connect();try{return await n.query(e,t)}finally{n.release()}}c()}catch(e){c(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42310:(e,t,n)=>{"use strict";n.a(e,async(e,c)=>{try{n.r(t),n.d(t,{patchFetch:()=>u,routeModule:()=>_,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var r=n(96559),a=n(48088),s=n(37719),o=n(96656),i=e([o]);o=(i.then?(await i)():i)[0];let _=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/account-linking/route",pathname:"/api/accounting/account-linking",filename:"route",bundlePath:"app/api/accounting/account-linking/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\account-linking\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:p}=_;function u(){return(0,s.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}c()}catch(e){c(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{},96656:(e,t,n)=>{"use strict";n.a(e,async(e,c)=>{try{n.r(t),n.d(t,{GET:()=>o,POST:()=>i});var r=n(32190),a=n(5069),s=e([a]);async function o(){try{let e=[{id:1,setting_name:"حساب الإيرادات الرئيسي",setting_description:"لتسجيل جميع إيرادات المكتب من القضايا والخدمات القانونية",account_code:"4000",account_name:"الإيرادات الرئيسية",account_type:"I",is_enabled:!0,is_created:!1,current_balance:0,icon_name:"TrendingUp",color_class:"border-l-green-500"},{id:2,setting_name:"حساب المصروفات الرئيسي",setting_description:"لتسجيل جميع مصروفات المكتب التشغيلية والإدارية",account_code:"5000",account_name:"المصروفات الرئيسية",account_type:"E",is_enabled:!0,is_created:!1,current_balance:0,icon_name:"TrendingDown",color_class:"border-l-red-500"},{id:3,setting_name:"حساب رأس المال",setting_description:"لرأس المال المستثمر في المكتب",account_code:"3000",account_name:"رأس المال",account_type:"E",is_enabled:!0,is_created:!1,current_balance:0,icon_name:"Building",color_class:"border-l-purple-500"},{id:4,setting_name:"الصندوق الرئيسي",setting_description:"صندوق النقدية الرئيسي للمكتب",account_code:"1111",account_name:"الصندوق الرئيسي",account_type:"A",is_enabled:!0,is_created:!1,current_balance:0,icon_name:"Wallet",color_class:"border-l-blue-500"},{id:5,setting_name:"الحساب الرئيسي للعملاء",setting_description:"الحساب المراقب لجميع حسابات العملاء الفردية",account_code:"1121",account_name:"حسابات العملاء",account_type:"A",is_enabled:!0,is_created:!1,current_balance:0,linked_table:"clients",auto_create_sub_accounts:!0,icon_name:"Users",color_class:"border-l-indigo-500"},{id:6,setting_name:"الحساب الرئيسي للموظفين",setting_description:"الحساب المراقب لجميع حسابات الموظفين الفردية",account_code:"1122",account_name:"حسابات الموظفين",account_type:"A",is_enabled:!0,is_created:!1,current_balance:0,linked_table:"employees",auto_create_sub_accounts:!0,icon_name:"UserCheck",color_class:"border-l-orange-500"},{id:7,setting_name:"الحسابات الوسيطة",setting_description:"حسابات وسيطة للعمليات المحاسبية المؤقتة",account_code:"2500",account_name:"الحسابات الوسيطة",account_type:"L",is_enabled:!0,is_created:!1,current_balance:0,icon_name:"DollarSign",color_class:"border-l-teal-500"}];for(let t of e)try{let e=await (0,a.P)("SELECT id, account_balance FROM chart_of_accounts WHERE account_code = $1",[t.account_code]);e.rows.length>0&&(t.is_created=!0,t.current_balance=parseFloat(e.rows[0].account_balance)||0)}catch(e){console.error(`Error checking account ${t.account_code}:`,e)}return r.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching account linking settings:",e),r.NextResponse.json({success:!1,error:"فشل في جلب إعدادات الربط"},{status:500})}}async function i(e){try{let{settings:t}=await e.json();if(!t||!Array.isArray(t))return r.NextResponse.json({success:!1,error:"بيانات الإعدادات مطلوبة"},{status:400});for(let e of t){let{account_code:t,account_name:n,account_type:c,is_enabled:r,linked_table:s,auto_create_sub_accounts:o}=e,i=await (0,a.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[t]);0===i.rows.length&&r?await (0,a.P)(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, account_level, parent_id, 
           is_active, accepts_transactions, linked_table, auto_create_sub_accounts)
          VALUES ($1, $2, $3, 1, NULL, true, true, $4, $5)
        `,[t,n,c,s,o]):i.rows.length>0&&await (0,a.P)(`
          UPDATE chart_of_accounts 
          SET account_name = $1, is_active = $2, linked_table = $3, 
              auto_create_sub_accounts = $4
          WHERE account_code = $5
        `,[n,r,s,o,t])}return r.NextResponse.json({success:!0,message:"تم حفظ إعدادات الربط بنجاح"})}catch(e){return console.error("Error saving account linking settings:",e),r.NextResponse.json({success:!1,error:"فشل في حفظ إعدادات الربط"},{status:500})}}a=(s.then?(await s)():s)[0],c()}catch(e){c(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),c=t.X(0,[4447,580],()=>n(42310));module.exports=c})();