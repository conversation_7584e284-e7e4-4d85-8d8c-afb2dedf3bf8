// إصلاح كامل لجدول إعدادات الذكاء الاصطناعي
const { Pool } = require('pg');

async function completeAISettingsFix() {
  console.log('🔧 إصلاح كامل لجدول إعدادات الذكاء الاصطناعي...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // إضافة الأعمدة المفقودة
      const missingColumns = [
        { name: 'welcome_message', type: 'TEXT', default: "'مرحباً بك! كيف يمكنني مساعدتك؟'" },
        { name: 'default_response', type: 'TEXT', default: "'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.'" }
      ];

      for (const column of missingColumns) {
        try {
          console.log(`   ➕ إضافة العمود: ${column.name}`);
          await pool.query(`
            ALTER TABLE ai_settings 
            ADD COLUMN IF NOT EXISTS ${column.name} ${column.type} DEFAULT ${column.default}
          `);
        } catch (error) {
          console.log(`   ⚠️ العمود ${column.name} موجود بالفعل أو خطأ في الإضافة`);
        }
      }

      // تحديث البيانات
      console.log('   🔄 تحديث البيانات...');
      
      const welcomeMessage = dbName === 'rubaie' 
        ? 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟'
        : 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

      // تحديث السجل الأول أو إنشاؤه
      await pool.query(`
        INSERT INTO ai_settings (
          enabled, 
          is_enabled,
          welcome_message, 
          default_response, 
          auto_respond,
          keywords_trigger,
          model,
          delay_seconds
        ) VALUES (
          true,
          true,
          $1,
          'شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك من قبل فريقنا المختص في أقرب وقت ممكن.',
          true,
          ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم'],
          'gpt-3.5-turbo',
          2
        )
        ON CONFLICT (id) DO UPDATE SET
          enabled = true,
          is_enabled = true,
          welcome_message = $1,
          auto_respond = true,
          keywords_trigger = ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم']
      `, [welcomeMessage]);

      console.log('   ✅ تم تحديث البيانات بنجاح');

      // التحقق من النتيجة النهائية
      const finalResult = await pool.query('SELECT * FROM ai_settings WHERE id = 1');
      if (finalResult.rows.length > 0) {
        const settings = finalResult.rows[0];
        console.log(`   📋 الحالة النهائية:`);
        console.log(`      - enabled: ${settings.enabled}`);
        console.log(`      - is_enabled: ${settings.is_enabled}`);
        console.log(`      - auto_respond: ${settings.auto_respond}`);
        console.log(`      - رسالة الترحيب: ${settings.welcome_message ? settings.welcome_message.substring(0, 50) + '...' : 'غير محدد'}`);
      }

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من الإصلاح الكامل');
}

// تشغيل الإصلاح
completeAISettingsFix().catch(console.error);
