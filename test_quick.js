// اختبار سريع للردود المحسنة
require('dotenv').config({ path: '.env.local' });

async function testQuick() {
  console.log('🔄 اختبار سريع للردود المحسنة...\n');
  
  const apiKey = process.env.GROQ_API_KEY;
  
  if (!apiKey) {
    console.log('❌ GROQ_API_KEY غير متوفر');
    return;
  }
  
  // اختبار سؤال محدد
  const question = "ما هي حقوق العامل في قانون العمل اليمني؟";
  
  console.log(`❓ السؤال: "${question}"`);
  console.log(`🎯 المتوقع: إجابة مباشرة بدون طلب تفاصيل\n`);
  
  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'llama-3.1-8b-instant',
        messages: [
          {
            role: 'system',
            content: `أنت محامي يمني خبير. اتبع هذه القواعد بدقة:

للأسئلة العامة (مثل "أحتاج استشارة" أو "مساعدة"):
- أجب فقط: "يرجى توضيح موضوع استشارتك القانونية بالتفصيل حتى أتمكن من مساعدتك."
- لا تعطي أي قوائم أو خيارات

للأسئلة المحددة (تحتوي على كلمات مثل "ما هي" أو "كيف" أو "إجراءات"):
- أجب مباشرة بالمعلومات القانونية
- اذكر 3 نقاط رئيسية فقط
- لا تطلب تفاصيل إضافية أبداً
- كن مختصراً (50-80 كلمة)

ممنوع تماماً:
- تكرار السؤال
- المقدمات الطويلة
- طلب تفاصيل للأسئلة المحددة
- القوائم الطويلة`
          },
          {
            role: 'user',
            content: `"${question}"`
          }
        ],
        max_tokens: 150,
        temperature: 0.7
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      const aiResponse = data.choices[0].message.content;
      
      console.log('✅ نجح الاختبار!');
      console.log('🤖 رد النموذج:');
      console.log(`"${aiResponse}"`);
      
      // تحليل الرد
      const wordCount = aiResponse.split(' ').length;
      const asksForDetails = aiResponse.includes('توضيح') || aiResponse.includes('تفاصيل') || aiResponse.includes('محدد');
      const givesDirectAnswer = !asksForDetails && (aiResponse.includes('حقوق') || aiResponse.includes('العامل') || aiResponse.includes('قانون'));
      
      console.log('\n📊 تحليل الرد:');
      console.log(`   • عدد الكلمات: ${wordCount}`);
      console.log(`   • يطلب التفاصيل: ${asksForDetails ? '❌ نعم (خطأ)' : '✅ لا (صحيح)'}`);
      console.log(`   • يعطي إجابة مباشرة: ${givesDirectAnswer ? '✅ نعم (صحيح)' : '❌ لا (خطأ)'}`);
      
      if (givesDirectAnswer && !asksForDetails && wordCount <= 100) {
        console.log('   • التقييم: ✅ ممتاز - إجابة مباشرة ومختصرة');
      } else if (givesDirectAnswer && !asksForDetails) {
        console.log('   • التقييم: ⚠️ جيد لكن طويل قليلاً');
      } else {
        console.log('   • التقييم: ❌ يحتاج تحسين');
      }
      
    } else {
      console.log('❌ فشل الاختبار');
      console.log(`📄 رمز الخطأ: ${response.status}`);
    }
    
  } catch (error) {
    console.log('❌ خطأ في الشبكة:', error.message);
  }
}

// تشغيل الاختبار
testQuick().catch(console.error);
