# Database Configuration
DB_PASSWORD=yemen123

# JWT Secret for Authentication
JWT_SECRET=your_jwt_secret_here

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:7443

# AI Models API Configuration

# OpenAI API (GPT-4o) - مدفوع (اختياري)
OPENAI_API_KEY=your_openai_api_key_here

# Groq API (Llama 3.1) - مجاني وموصى به ✅
GROQ_API_KEY=your_groq_api_key_here

# Hugging Face API (Qwen 2.5) - مجاني (اختياري)
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Instructions:
# 1. Copy this file to .env.local
# 2. Get at least one free API key (Groq recommended)
# 3. Replace the placeholder values with your actual API keys
# 4. Update other values as needed for your environment

# Free API Keys (Recommended):
# 1. Groq (Llama 3.1) - FREE: https://console.groq.com/keys
# 2. Hugging Face (Qwen 2.5) - FREE: https://huggingface.co/settings/tokens

# Paid API Keys (Optional):
# 3. OpenAI (GPT-4o) - PAID: https://platform.openai.com/api-keys
