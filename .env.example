# Database Configuration
DB_PASSWORD=yemen123

# OpenAI API Configuration (Required for GPT-4)
OPENAI_API_KEY=your_openai_api_key_here

# JWT Secret for Authentication
JWT_SECRET=your_jwt_secret_here

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:7443

# Instructions:
# 1. Copy this file to .env.local
# 2. Replace 'your_openai_api_key_here' with your actual OpenAI API key
# 3. Replace 'your_jwt_secret_here' with a secure random string
# 4. Update other values as needed for your environment

# To get an OpenAI API key:
# 1. Go to https://platform.openai.com/api-keys
# 2. Sign in or create an account
# 3. Click "Create new secret key"
# 4. Copy the key and paste it here
