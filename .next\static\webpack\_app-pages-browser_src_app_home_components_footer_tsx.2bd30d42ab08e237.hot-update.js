"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_home_components_footer_tsx",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/footer.tsx":
/*!********************************************!*\
  !*** ./src/app/home/<USER>/footer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Footer(param) {\n    let { companyData } = param;\n    _s();\n    const currentYear = new Date().getFullYear();\n    const [footerLinks, setFooterLinks] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            fetchFooterLinks();\n        }\n    }[\"Footer.useEffect\"], []);\n    const fetchFooterLinks = async ()=>{\n        try {\n            const response = await fetch('/api/footer-links');\n            const data = await response.json();\n            if (data.success) {\n                setFooterLinks(data.data.filter((link)=>link.is_active));\n            }\n        } catch (error) {\n            console.error('Error fetching footer links:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تجميع الروابط حسب الفئة\n    const groupedLinks = footerLinks.reduce((acc, link)=>{\n        if (!acc[link.category]) {\n            acc[link.category] = [];\n        }\n        acc[link.category].push(link);\n        return acc;\n    }, {});\n    // ترتيب الروابط داخل كل فئة\n    Object.keys(groupedLinks).forEach((category)=>{\n        groupedLinks[category].sort((a, b)=>a.sort_order - b.sort_order);\n    });\n    const socialLinks = [\n        {\n            name: 'فيسبوك',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'تويتر',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'لينكد إن',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'إنستغرام',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'يوتيوب',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: '#'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"pt-20 pb-8\",\n        style: {\n            background: 'linear-gradient(135deg, #222222 0%, #171717 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-900 mr-3 shadow-lg\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                className: \"w-6 h-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                        points: \"14 2 14 8 20 8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold\",\n                                            style: {\n                                                color: '#cca967'\n                                            },\n                                            children: companyData.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: companyData.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 space-x-reverse\",\n                                    children: socialLinks.map((social, index)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"w-10 h-10 rounded-full flex items-center justify-center text-gray-300 hover:text-gray-900 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #444444 0%, #333333 100%)'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #444444 0%, #333333 100%)';\n                                            },\n                                            \"aria-label\": social.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        Object.entries(groupedLinks).map((param)=>{\n                            let [category, links] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        style: {\n                                            color: '#cca967'\n                                        },\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"tafahum-footer-widget text-gray-300 hover:text-yellow-400 transition-colors flex items-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-1 h-1 bg-yellow-600 rounded-full ml-2 group-hover:bg-yellow-400 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        link.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.id, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    style: {\n                                        color: '#cca967'\n                                    },\n                                    children: \"معلومات التواصل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-1 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: companyData.address && companyData.city ? \"\".concat(companyData.address, \", \").concat(companyData.city) : 'الرياض، المملكة العربية السعودية، شارع العليا، ص.ب 12345'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:\".concat((companyData.phone || '+967-1-123456').replace(/\\D/g, '')),\n                                                    className: \"text-gray-300 hover:text-yellow-400 transition-colors\",\n                                                    children: companyData.phone || '+967 1 234 11 966'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:\".concat(companyData.email || '<EMAIL>'),\n                                                    className: \"text-gray-300 hover:text-yellow-400 transition-colors\",\n                                                    children: companyData.email || '<EMAIL>'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-1 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"الأحد - الخميس: 8 صباحاً - 4 مساءً\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 53\n                                                        }, this),\n                                                        \"الجمعة والسبت: مغلق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    style: {\n                        borderColor: 'rgba(204, 169, 103, 0.2)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-300 text-sm mb-4 md:mb-0\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: '#cca967'\n                                    },\n                                    children: companyData.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 29\n                                }, this),\n                                \". جميع الحقوق محفوظة.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"سياسة الخصوصية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"الشروط والأحكام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/sitemap\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"خريطة الموقع\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"OMKwvCdPwTk+bl/Ia3n/ULsx9Ik=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/footer.tsx\n"));

/***/ })

});