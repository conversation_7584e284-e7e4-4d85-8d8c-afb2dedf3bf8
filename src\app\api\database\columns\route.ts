import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: process.env.DB_PASSWORD || 'your_password_here',
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tableName = searchParams.get('table')
    
    if (!tableName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الجدول مطلوب'
      }, { status: 400 })
    }
    
    const client = await pool.connect()
    
    const query = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = $1 
        AND table_schema = 'public'
      ORDER BY ordinal_position;
    `
    
    const result = await client.query(query, [tableName])
    client.release()
    
    return NextResponse.json({
      success: true,
      columns: result.rows
    })
    
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب أعمدة الجدول'
    }, { status: 500 })
  }
}
