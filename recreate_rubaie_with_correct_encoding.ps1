# سكريپت إعادة إنشاء قاعدة البيانات rubaie بترميز صحيح
# Recreate Rubaie Database with Correct Encoding

Write-Host "🔄 إعادة إنشاء قاعدة البيانات rubaie بترميز صحيح" -ForegroundColor Green
Write-Host "🔑 كلمة المرور: yemen123" -ForegroundColor Cyan

# تعيين كلمة المرور وترميز PowerShell
$env:PGPASSWORD = "yemen123"
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "`n🔍 التحقق من قاعدة البيانات المصدر..." -ForegroundColor Yellow

# التحقق من وجود قاعدة البيانات المصدر
$sourceExists = psql -U postgres -h localhost -p 5432 -lqt | Select-String "mohammi"
if (-not $sourceExists) {
    Write-Host "❌ قاعدة البيانات المصدر 'mohammi' غير موجودة" -ForegroundColor Red
    exit 1
}
Write-Host "✅ قاعدة البيانات المصدر موجودة" -ForegroundColor Green

# إيقاف جميع الاتصالات بقاعدة البيانات rubaie
Write-Host "`n🔌 إيقاف جميع الاتصالات بقاعدة البيانات rubaie..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'rubaie' AND pid <> pg_backend_pid();" 2>$null

Start-Sleep -Seconds 2

# حذف قاعدة البيانات rubaie تماماً
Write-Host "🗑️ حذف قاعدة البيانات rubaie تماماً..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -c "DROP DATABASE IF EXISTS rubaie;" 2>$null

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ تحذير: قد تكون قاعدة البيانات غير موجودة مسبقاً" -ForegroundColor Yellow
}

# إنشاء قاعدة البيانات rubaie جديدة بترميز UTF8 صريح
Write-Host "`n🏗️ إنشاء قاعدة البيانات rubaie جديدة بترميز UTF8..." -ForegroundColor Yellow
psql -U postgres -h localhost -p 5432 -c "
CREATE DATABASE rubaie 
WITH 
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TEMPLATE = template0;"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في إنشاء قاعدة البيانات" -ForegroundColor Red
    exit 1
}
Write-Host "✅ تم إنشاء قاعدة البيانات بترميز UTF8" -ForegroundColor Green

# التحقق من ترميز قاعدة البيانات الجديدة
Write-Host "`n🔍 التحقق من ترميز قاعدة البيانات الجديدة..." -ForegroundColor Yellow
$encoding = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "SHOW server_encoding;"
Write-Host "📋 ترميز قاعدة البيانات: $($encoding.Trim())" -ForegroundColor Cyan

# نسخ البيانات مع ضمان الترميز الصحيح
Write-Host "`n📋 نسخ البيانات مع ضمان الترميز UTF8..." -ForegroundColor Yellow
Write-Host "   📊 هذا قد يستغرق بضع دقائق..." -ForegroundColor Cyan

# استخدام pg_dump مع خيارات ترميز صريحة
pg_dump -U postgres -h localhost -p 5432 -d mohammi --encoding=UTF8 --no-owner --no-privileges --verbose | psql -U postgres -h localhost -p 5432 -d rubaie

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ تم نسخ البيانات بنجاح!" -ForegroundColor Green
    
    # اختبار النص العربي في البيانات المنسوخة
    Write-Host "`n🧪 اختبار النص العربي في البيانات..." -ForegroundColor Yellow
    
    # اختبار أسماء العملاء
    Write-Host "👥 اختبار أسماء العملاء:" -ForegroundColor Cyan
    $clientNames = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "SELECT name FROM clients LIMIT 3;"
    $clientNames -split "`n" | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
        Write-Host "   📝 $($_.Trim())" -ForegroundColor White
    }
    
    # اختبار عناوين القضايا
    Write-Host "`n⚖️ اختبار عناوين القضايا:" -ForegroundColor Cyan
    $issueTitles = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "SELECT title FROM issues LIMIT 3;"
    $issueTitles -split "`n" | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
        Write-Host "   📝 $($_.Trim())" -ForegroundColor White
    }
    
    # اختبار أسماء المحاكم
    Write-Host "`n🏛️ اختبار أسماء المحاكم:" -ForegroundColor Cyan
    $courtNames = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "SELECT name FROM courts LIMIT 3;"
    $courtNames -split "`n" | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
        Write-Host "   📝 $($_.Trim())" -ForegroundColor White
    }
    
    # عرض الإحصائيات النهائية
    Write-Host "`n📊 إحصائيات قاعدة البيانات المنسوخة:" -ForegroundColor Cyan
    $stats = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "
    SELECT 
        'العملاء: ' || COALESCE((SELECT COUNT(*) FROM clients), 0) ||
        ' | القضايا: ' || COALESCE((SELECT COUNT(*) FROM issues), 0) ||
        ' | المحاكم: ' || COALESCE((SELECT COUNT(*) FROM courts), 0) ||
        ' | العملات: ' || COALESCE((SELECT COUNT(*) FROM currencies), 0);"
    Write-Host "   $($stats.Trim())" -ForegroundColor White
    
    # التحقق من وجود علامات استفهام
    Write-Host "`n🔍 التحقق من وجود علامات استفهام..." -ForegroundColor Yellow
    $questionMarks = psql -U postgres -h localhost -p 5432 -d rubaie -t -c "
    SELECT COUNT(*) FROM (
        SELECT name FROM clients WHERE name LIKE '%?%'
        UNION ALL
        SELECT title FROM issues WHERE title LIKE '%?%'
        UNION ALL
        SELECT name FROM courts WHERE name LIKE '%?%'
    ) as problematic_data;"
    
    $questionMarkCount = $questionMarks.Trim()
    if ($questionMarkCount -eq "0") {
        Write-Host "✅ لا توجد علامات استفهام في البيانات!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ تم العثور على $questionMarkCount سجل يحتوي على علامات استفهام" -ForegroundColor Yellow
    }
    
    Write-Host "`n🎉 تم إعادة إنشاء قاعدة البيانات rubaie بنجاح!" -ForegroundColor Green
    Write-Host "🚀 يمكنك الآن تشغيل النظام على المنفذ 8914" -ForegroundColor Yellow
    Write-Host "   .\start_single_server.ps1 rubaie" -ForegroundColor Cyan
    
} else {
    Write-Host "`n❌ فشل في نسخ البيانات" -ForegroundColor Red
    Write-Host "💡 تحقق من:" -ForegroundColor Yellow
    Write-Host "   - اتصال قاعدة البيانات" -ForegroundColor White
    Write-Host "   - كلمة المرور صحيحة" -ForegroundColor White
    Write-Host "   - PostgreSQL يعمل بشكل صحيح" -ForegroundColor White
}
