(()=>{var e={};e.id=7425,e.ids=[7425],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>s,bI:()=>n});var c=r(64939),o=e([c]);if(c=(o.then?(await o)():o)[0],!process.env.DB_PASSWORD)throw Error("DB_PASSWORD environment variable is required");let i={host:"localhost",port:5432,database:"mohammi",user:"postgres",password:process.env.DB_PASSWORD||"your_password_here",ssl:!1,connectionTimeoutMillis:5e3,idleTimeoutMillis:3e4,max:20},u=new c.Pool(i);async function n(){let e=await u.connect();return{async exec(t){try{for(let r of t.split(";").filter(e=>e.trim()))r.trim()&&await e.query(r.trim())}catch(e){throw console.error("Database exec error:",e),e}},async get(t,r=[]){try{return(await e.query(t,r)).rows[0]||null}catch(e){throw console.error("Database get error:",e),e}},async all(t,r=[]){try{return(await e.query(t,r)).rows}catch(e){throw console.error("Database all error:",e),e}},async run(t,r=[]){try{if(t.trim().toUpperCase().startsWith("INSERT")){let a=t.includes("RETURNING")?t:t+" RETURNING id",c=await e.query(a,r);return{lastID:c.rows[0]?.id||null,changes:c.rowCount||0}}{let a=await e.query(t,r);return{lastID:null,changes:a.rowCount||0}}}catch(e){throw console.error("Database run error:",e),e}},close(){e.release()}}}async function s(e,t){let r;try{return r=await u.connect(),await r.query(e,t)}catch(r){throw console.error("Database query error:",r),console.error("Query:",e),console.error("Params:",t),r}finally{r&&r.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15883:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{POST:()=>s});var c=r(32190),o=r(6710),n=e([o]);async function s(e){try{let t,{link_type:r}=await e.json();if(!r||!["clients","employees","suppliers"].includes(r))return c.NextResponse.json({success:!1,error:"نوع الربط مطلوب ويجب أن يكون أحد: clients, employees, suppliers"},{status:400});switch(r){case"clients":t=await i();break;case"employees":t=await u();break;case"suppliers":t=await l();break;default:throw Error("نوع ربط غير مدعوم")}return c.NextResponse.json({success:!0,message:`تم إعادة ربط ${r} بنجاح`,...t})}catch(e){return console.error("Error in chart accounts relinking:",e),c.NextResponse.json({success:!1,error:"حدث خطأ في إعادة تطبيق الربط"},{status:500})}}async function i(){try{let e=await (0,o.P)(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%عملاء%' OR account_name LIKE '%العملاء%'
      ORDER BY account_level ASC
      LIMIT 1
    `);if(0===e.rows.length)return{created_accounts:0,linked_records:0,error:"لم يتم العثور على حساب العملاء الرئيسي"};let t=e.rows[0],r=await (0,o.P)(`
      SELECT id, name 
      FROM clients 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `,[t.id]),a=0,c=0;for(let e of r.rows)try{let r,n=`${t.account_code}${String(e.id).padStart(3,"0")}`,s=`عميل: ${e.name}`,i=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);i.rows.length>0?r=i.rows[0].id:(r=(await (0,o.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[n,s,t.account_type,t.id,(t.account_level||1)+1])).rows[0].id,a++),await (0,o.P)("UPDATE clients SET account_id = $1 WHERE id = $2",[r,e.id]),c++}catch(t){console.error(`خطأ في إعادة ربط العميل ${e.name}:`,t)}return{created_accounts:a,linked_records:c}}catch(e){return console.error("Error in relinkClients:",e),{created_accounts:0,linked_records:0}}}async function u(){try{let e=await (0,o.P)(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%موظف%' OR account_name LIKE '%الموظفين%'
      ORDER BY account_level ASC
      LIMIT 1
    `);if(0===e.rows.length)return{created_accounts:0,linked_records:0,error:"لم يتم العثور على حساب الموظفين الرئيسي"};let t=e.rows[0],r=await (0,o.P)(`
      SELECT id, name 
      FROM employees 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `,[t.id]),a=0,c=0;for(let e of r.rows)try{let r,n=`${t.account_code}${String(e.id).padStart(3,"0")}`,s=`موظف: ${e.name}`,i=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);i.rows.length>0?r=i.rows[0].id:(r=(await (0,o.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[n,s,t.account_type,t.id,(t.account_level||1)+1])).rows[0].id,a++),await (0,o.P)("UPDATE employees SET account_id = $1 WHERE id = $2",[r,e.id]),c++}catch(t){console.error(`خطأ في إعادة ربط الموظف ${e.name}:`,t)}return{created_accounts:a,linked_records:c}}catch(e){return console.error("Error in relinkEmployees:",e),{created_accounts:0,linked_records:0}}}async function l(){try{let e=await (0,o.P)(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `);if(0===e.rows.length)return{created_accounts:0,linked_records:0};let t=await (0,o.P)(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%مورد%' OR account_name LIKE '%الموردين%'
      ORDER BY account_level ASC
      LIMIT 1
    `);if(0===t.rows.length)return{created_accounts:0,linked_records:0,error:"لم يتم العثور على حساب الموردين الرئيسي"};let r=t.rows[0],a=await (0,o.P)(`
      SELECT id, name 
      FROM suppliers 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `,[r.id]),c=0,n=0;for(let e of a.rows)try{let t,a=`${r.account_code}${String(e.id).padStart(3,"0")}`,s=`مورد: ${e.name}`,i=await (0,o.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[a]);i.rows.length>0?t=i.rows[0].id:(t=(await (0,o.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[a,s,r.account_type,r.id,(r.account_level||1)+1])).rows[0].id,c++),await (0,o.P)("UPDATE suppliers SET account_id = $1 WHERE id = $2",[t,e.id]),n++}catch(t){console.error(`خطأ في إعادة ربط المورد ${e.name}:`,t)}return{created_accounts:c,linked_records:n}}catch(e){return console.error("Error in relinkSuppliers:",e),{created_accounts:0,linked_records:0}}}o=(n.then?(await n)():n)[0],a()}catch(e){a(e)}})},25700:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>_});var c=r(96559),o=r(48088),n=r(37719),s=r(15883),i=e([s]);s=(i.then?(await i)():i)[0];let l=new c.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/chart-of-accounts/relink/route",pathname:"/api/accounting/chart-of-accounts/relink",filename:"route",bundlePath:"app/api/accounting/chart-of-accounts/relink/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\chart-of-accounts\\relink\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:_,serverHooks:p}=l;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:_})}a()}catch(e){a(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(25700));module.exports=a})();