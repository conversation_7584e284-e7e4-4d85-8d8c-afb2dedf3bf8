import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

export async function GET() {
  let pool: Pool | null = null
  
  try {
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: process.env.DB_PASSWORD || 'your_password_here',
      connectionTimeoutMillis: 5000
    }

    pool = new Pool(dbConfig)
    const client = await pool.connect()
    
    // اختبار عمليات القراءة والكتابة
    const testResults = []
    
    try {
      // اختبار إنشاء جدول مؤقت
      await client.query(`
        CREATE TEMP TABLE test_table (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100),
          created_at TIMESTAMP DEFAULT NOW()
        )
      `)
      testResults.push('✅ إنشاء الجداول')
      
      // اختبار الإدراج
      await client.query(`
        INSERT INTO test_table (name) VALUES ('test_record')
      `)
      testResults.push('✅ عمليات الإدراج')
      
      // اختبار القراءة
      const selectResult = await client.query('SELECT * FROM test_table')
      if (selectResult.rows.length > 0) {
        testResults.push('✅ عمليات القراءة')
      }
      
      // اختبار التحديث
      await client.query(`
        UPDATE test_table SET name = 'updated_record' WHERE id = 1
      `)
      testResults.push('✅ عمليات التحديث')
      
      // اختبار الحذف
      await client.query('DELETE FROM test_table WHERE id = 1')
      testResults.push('✅ عمليات الحذف')
      
    } catch (opError: any) {
      testResults.push(`❌ خطأ في العمليات: ${opError.message}`)
    }
    
    client.release()
    
    const allPassed = testResults.every(result => result.startsWith('✅'))
    
    return NextResponse.json({
      success: allPassed,
      message: allPassed ? 'جميع العمليات تعمل بشكل صحيح' : 'يوجد مشاكل في بعض العمليات',
      details: testResults.join('\n')
    })
    
  } catch (error: any) {
    console.error('خطأ في اختبار العمليات:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في اختبار العمليات',
      details: `رمز الخطأ: ${error.code || 'غير محدد'}\nالرسالة: ${error.message}`
    }, { status: 500 })
    
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
