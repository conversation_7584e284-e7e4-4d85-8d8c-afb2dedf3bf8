'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import {
  Link2,
  Save,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  BookOpen,
  Settings,
  Search,
  X,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface MainAccount {
  id: number
  account_name: string
  account_code?: string
  chart_account_id?: number
  chart_account_name?: string
  chart_account_code?: string
  description: string
}

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
}

export default function MainAccountsPage() {
  const [mainAccounts, setMainAccounts] = useState<MainAccount[]>([])
  const [chartAccounts, setChartAccounts] = useState<ChartAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة الربط لكل حساب رئيسي
  const [accountLinks, setAccountLinks] = useState<{ [key: number]: number | null }>({})
  
  // حالة البحث
  const [searchTerms, setSearchTerms] = useState<{ [key: number]: string }>({})

  // حالة النموذج
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit'>('add')
  const [editingAccount, setEditingAccount] = useState<MainAccount | null>(null)
  const [formData, setFormData] = useState({
    account_name: '',
    description: '',
    chart_account_id: null as number | null
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/accounting/main-accounts')
      if (response.ok) {
        const data = await response.json()
        setMainAccounts(data.mainAccounts || [])
        setChartAccounts(data.chartAccounts || [])
        
        // تعيين الروابط الحالية
        const links: { [key: number]: number | null } = {}
        data.mainAccounts?.forEach((account: MainAccount) => {
          links[account.id] = account.chart_account_id || null
        })
        setAccountLinks(links)
      } else {
        setMessage({ type: 'error', text: 'فشل في جلب البيانات' })
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage({ type: 'error', text: 'حدث خطأ في جلب البيانات' })
    } finally {
      setLoading(false)
    }
  }

  const handleAccountChange = (mainAccountId: number, chartAccountId: string) => {
    setAccountLinks(prev => ({
      ...prev,
      [mainAccountId]: chartAccountId === 'none' ? null : parseInt(chartAccountId)
    }))
  }

  const handleSearchChange = (mainAccountId: number, searchTerm: string) => {
    setSearchTerms(prev => ({
      ...prev,
      [mainAccountId]: searchTerm
    }))
  }

  const clearSearch = (mainAccountId: number) => {
    setSearchTerms(prev => ({
      ...prev,
      [mainAccountId]: ''
    }))
  }

  const getFilteredAccounts = (mainAccountId: number) => {
    const searchTerm = searchTerms[mainAccountId] || ''
    if (!searchTerm.trim()) {
      return chartAccounts
    }
    
    return chartAccounts.filter(account => 
      account.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_type.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setMessage(null)

      // تحضير البيانات للحفظ
      const updatedAccounts = mainAccounts.map(account => ({
        id: account.id,
        chart_account_id: accountLinks[account.id],
        account_code: accountLinks[account.id] ?
          chartAccounts.find(ca => ca.id === accountLinks[account.id])?.account_code : null
      }))

      const response = await fetch('/api/accounting/main-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mainAccounts: updatedAccounts }),
      })

      if (response.ok) {
        const result = await response.json()

        // عرض نتائج الربط التلقائي
        let linkingMessage = 'تم حفظ ربط الحسابات بنجاح'

        if (result.linking_results && result.linking_results.length > 0) {
          const totalUpdated = result.linking_results.reduce((sum: number, lr: any) => {
            if (lr.linking_result && lr.linking_result.success) {
              const details = lr.linking_result.details
              return sum + details.clients_updated + details.employees_updated + details.suppliers_updated
            }
            return sum
          }, 0)

          if (totalUpdated > 0) {
            linkingMessage += `\n🔗 تم ربط ${totalUpdated} سجل تلقائياً بالحسابات الفرعية`
          }
        }

        setMessage({ type: 'success', text: linkingMessage })
        await fetchData() // إعادة جلب البيانات لتحديث العرض
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حفظ البيانات' })
      }
    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحفظ' })
    } finally {
      setSaving(false)
    }
  }

  // دوال إدارة النموذج
  const handleAdd = () => {
    setModalType('add')
    setEditingAccount(null)
    setFormData({
      account_name: '',
      description: '',
      chart_account_id: null
    })
    setIsModalOpen(true)
  }

  const handleEdit = (account: MainAccount) => {
    setModalType('edit')
    setEditingAccount(account)
    setFormData({
      account_name: account.account_name,
      description: account.description,
      chart_account_id: account.chart_account_id || null
    })
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.account_name.trim()) {
      setMessage({ type: 'error', text: 'يرجى إدخال اسم الحساب' })
      return
    }

    try {
      setSaving(true)
      setMessage(null)

      const url = modalType === 'add'
        ? '/api/main-accounts'
        : `/api/main-accounts/${editingAccount?.id}`

      const method = modalType === 'add' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const result = await response.json()

        let successMessage = modalType === 'add' ? 'تم إضافة الحساب بنجاح' : 'تم تحديث الحساب بنجاح'

        // عرض نتائج الربط التلقائي
        if (result.linking_result && result.linking_result.success) {
          const details = result.linking_result.details
          const totalUpdated = details.clients_updated + details.employees_updated + details.suppliers_updated

          if (totalUpdated > 0) {
            successMessage += `\n🔗 تم ربط ${totalUpdated} سجل تلقائياً:`
            if (details.clients_updated > 0) successMessage += `\n👥 ${details.clients_updated} عميل`
            if (details.employees_updated > 0) successMessage += `\n👨‍💼 ${details.employees_updated} موظف`
            if (details.suppliers_updated > 0) successMessage += `\n🏪 ${details.suppliers_updated} مورد`
          }
        }

        setMessage({ type: 'success', text: successMessage })
        setIsModalOpen(false)
        await fetchData()
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حفظ البيانات' })
      }
    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحفظ' })
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (account: MainAccount) => {
    if (!confirm(`هل أنت متأكد من حذف الحساب "${account.account_name}"؟`)) {
      return
    }

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch(`/api/main-accounts/${account.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'تم حذف الحساب بنجاح' })
        await fetchData()
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حذف الحساب' })
      }
    } catch (error) {
      console.error('خطأ في الحذف:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحذف' })
    } finally {
      setSaving(false)
    }
  }

  const getAccountStatus = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? 'مربوط' : 'غير مربوط'
  }

  const getStatusColor = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? 'text-green-600' : 'text-orange-600'
  }

  const getStatusIcon = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? CheckCircle : AlertCircle
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link2 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إدارة الحسابات الرئيسية</h1>
              <p className="text-gray-600">إضافة وربط الحسابات الهامة من دليل الحسابات</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            <Button
              onClick={handleAdd}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة حساب رئيسي
            </Button>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button
              onClick={fetchData}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-green-600 hover:bg-green-700"
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              حفظ الربط
            </Button>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* جدول الحسابات الرئيسية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <BookOpen className="h-5 w-5" />
              <span>الحسابات الرئيسية</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mainAccounts.map((mainAccount) => {
                const StatusIcon = getStatusIcon(mainAccount)
                const currentSelection = accountLinks[mainAccount.id]
                
                return (
                  <div key={mainAccount.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <StatusIcon className={`h-5 w-5 ${getStatusColor(mainAccount)}`} />
                        <div>
                          <h3 className="font-medium text-gray-900">{mainAccount.account_name}</h3>
                          <p className="text-sm text-gray-600">{mainAccount.description}</p>
                          {mainAccount.chart_account_code && (
                            <div className="flex items-center space-x-2 space-x-reverse mt-1">
                              <Badge variant="outline" className="text-xs">
                                {mainAccount.chart_account_code}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {mainAccount.chart_account_name}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      {/* أزرار الإجراءات */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(mainAccount)}
                          className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(mainAccount)}
                          className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <Badge
                        variant={mainAccount.chart_account_id ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {getAccountStatus(mainAccount)}
                      </Badge>
                      
                      <div className="flex flex-col space-y-2">
                        {/* حقل البحث */}
                        <div className="relative">
                          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="ابحث في الحسابات..."
                            value={searchTerms[mainAccount.id] || ''}
                            onChange={(e) => handleSearchChange(mainAccount.id, e.target.value)}
                            className="w-80 pr-10 pl-8"
                          />
                          {searchTerms[mainAccount.id] && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => clearSearch(mainAccount.id)}
                              className="absolute left-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                        
                        {/* قائمة الحسابات */}
                        <Select
                          value={currentSelection?.toString() || 'none'}
                          onValueChange={(value) => handleAccountChange(mainAccount.id, value)}
                        >
                          <SelectTrigger className="w-80">
                            <SelectValue placeholder="اختر الحساب من الدليل" />
                          </SelectTrigger>
                          <SelectContent className="max-h-60">
                            <SelectItem value="none">-- لم يتم الربط --</SelectItem>
                            {getFilteredAccounts(mainAccount.id).map((chartAccount) => (
                              <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                                <div className="flex items-center space-x-2 space-x-reverse">
                                  <span className="font-mono text-sm text-blue-600">
                                    {chartAccount.account_code}
                                  </span>
                                  <span className="truncate max-w-48">{chartAccount.account_name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {chartAccount.account_type}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                            {getFilteredAccounts(mainAccount.id).length === 0 && searchTerms[mainAccount.id] && (
                              <div className="p-2 text-center text-gray-500 text-sm">
                                لا توجد نتائج للبحث "{searchTerms[mainAccount.id]}"
                              </div>
                            )}
                          </SelectContent>
                        </Select>
                        
                        {/* عرض عدد النتائج */}
                        {searchTerms[mainAccount.id] && (
                          <div className="text-xs text-gray-500">
                            {getFilteredAccounts(mainAccount.id).length} من {chartAccounts.length} حساب
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3 space-x-reverse">
              <Settings className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">معلومات مهمة</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• يتم استخدام هذه الحسابات في جميع أنحاء النظام المحاسبي</p>
                  <p>• تأكد من ربط جميع الحسابات المطلوبة قبل البدء في العمليات المحاسبية</p>
                  <p>• يمكن تغيير الربط في أي وقت، لكن تأكد من عدم وجود معاملات مرتبطة</p>
                  <p>• الحسابات المربوطة ستظهر تلقائياً في السندات والقيود</p>
                  <p>• استخدم البحث للعثور على الحسابات بسرعة (رقم الحساب، الاسم، أو النوع)</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">
                {mainAccounts.length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الحسابات الرئيسية</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {mainAccounts.filter(acc => acc.chart_account_id).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات المربوطة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">
                {mainAccounts.filter(acc => !acc.chart_account_id).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات غير المربوطة</div>
            </CardContent>
          </Card>
        </div>

        {/* نافذة إضافة/تعديل الحساب */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {modalType === 'add' ? 'إضافة حساب رئيسي جديد' : 'تعديل الحساب الرئيسي'}
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="account_name">اسم الحساب الرئيسي *</Label>
                <Input
                  id="account_name"
                  value={formData.account_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, account_name: e.target.value }))}
                  placeholder="مثال: حسابات العملاء"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف مختصر للحساب الرئيسي"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="chart_account">ربط بحساب من دليل الحسابات (اختياري)</Label>
                <Select
                  value={formData.chart_account_id?.toString() || ''}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    chart_account_id: value === 'none' ? null : parseInt(value)
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر حساب من دليل الحسابات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">بدون ربط</SelectItem>
                    {chartAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id.toString()}>
                        {account.account_code} - {account.account_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalOpen(false)}
                  disabled={saving}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {saving ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin ml-2" />
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 ml-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}