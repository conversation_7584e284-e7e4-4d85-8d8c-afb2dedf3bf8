(()=>{var e={};e.id=737,e.ids=[737],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{P:()=>p});var n=s(64939),a=s(29021),c=s.n(a),o=s(33873),i=s.n(o),u=e([n]);n=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),r=c().readFileSync(e,"utf8");d=JSON.parse(r)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let r=d.routes[e],s=d.default_config;return{database:r.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),y=new n.Pool(l);async function p(e,r){let s=await y.connect();try{return await s.query(e,r)}finally{s.release()}}t()}catch(e){t(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33055:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{GET:()=>o,POST:()=>i});var n=s(32190),a=s(5069),c=e([a]);async function o(){try{let e=await (0,a.P)(`
      SELECT 
        id,
        currency_code,
        currency_name,
        symbol,
        exchange_rate,
        is_base_currency,
        is_active,
        created_date
      FROM currencies
      WHERE is_active = true
      ORDER BY is_base_currency DESC, currency_name
    `);return n.NextResponse.json({success:!0,currencies:e.rows,total:e.rows.length,message:"تم جلب العملات بنجاح"})}catch(e){return console.error("خطأ في جلب العملات:",e),n.NextResponse.json({success:!1,error:"فشل في جلب العملات",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e){try{let{currency_code:r,currency_name:s,symbol:t,exchange_rate:c=1,is_base_currency:o=!1}=await e.json();if(!r||!s)return n.NextResponse.json({success:!1,error:"رمز العملة واسم العملة مطلوبان"},{status:400});if((await (0,a.P)("SELECT id FROM currencies WHERE currency_code = $1",[r])).rows.length>0)return n.NextResponse.json({success:!1,error:"رمز العملة موجود مسبقاً"},{status:400});o&&await (0,a.P)("UPDATE currencies SET is_base_currency = false");let i=await (0,a.P)(`
      INSERT INTO currencies (currency_code, currency_name, symbol, exchange_rate, is_base_currency)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `,[r,s,t,c,o]);return n.NextResponse.json({success:!0,currency:i.rows[0],message:"تم إضافة العملة بنجاح"})}catch(e){return console.error("خطأ في إضافة العملة:",e),n.NextResponse.json({success:!1,error:"فشل في إضافة العملة",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}a=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49728:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n=s(96559),a=s(48088),c=s(37719),o=s(33055),i=e([o]);o=(i.then?(await i)():i)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/currencies/route",pathname:"/api/accounting/currencies",filename:"route",bundlePath:"app/api/accounting/currencies/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\currencies\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:y}=p;function u(){return(0,c.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}t()}catch(e){t(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580],()=>s(49728));module.exports=t})();