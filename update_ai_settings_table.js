// تحديث جدول إعدادات الذكاء الاصطناعي
const { Pool } = require('pg');

async function updateAISettingsTable() {
  console.log('🔧 تحديث جدول إعدادات الذكاء الاصطناعي...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // التحقق من بنية الجدول الحالية
      const columnsResult = await pool.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'ai_settings'
        ORDER BY ordinal_position
      `);

      console.log('   📋 الأعمدة الموجودة:');
      columnsResult.rows.forEach(col => {
        console.log(`      - ${col.column_name}: ${col.data_type}`);
      });

      // إضافة الأعمدة المفقودة
      const requiredColumns = [
        { name: 'is_enabled', type: 'BOOLEAN', default: 'true' },
        { name: 'auto_respond', type: 'BOOLEAN', default: 'true' },
        { name: 'keywords_trigger', type: 'TEXT[]', default: "ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات']" },
        { name: 'max_response_length', type: 'INTEGER', default: '500' },
        { name: 'response_delay', type: 'INTEGER', default: '1000' }
      ];

      for (const column of requiredColumns) {
        // التحقق من وجود العمود
        const columnExists = columnsResult.rows.some(col => col.column_name === column.name);
        
        if (!columnExists) {
          console.log(`   ➕ إضافة العمود: ${column.name}`);
          await pool.query(`
            ALTER TABLE ai_settings 
            ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}
          `);
        } else {
          console.log(`   ✅ العمود موجود: ${column.name}`);
        }
      }

      // التحقق من وجود بيانات
      const dataResult = await pool.query('SELECT COUNT(*) as count FROM ai_settings');
      const dataCount = parseInt(dataResult.rows[0].count);

      if (dataCount === 0) {
        // إدراج بيانات جديدة
        console.log('   ➕ إدراج بيانات جديدة...');
        
        const welcomeMessage = dbName === 'rubaie' 
          ? 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟'
          : 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

        await pool.query(`
          INSERT INTO ai_settings (
            is_enabled, 
            welcome_message, 
            default_response, 
            auto_respond,
            keywords_trigger,
            max_response_length,
            response_delay
          ) VALUES (
            true,
            $1,
            'شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك من قبل فريقنا المختص في أقرب وقت ممكن.',
            true,
            ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم'],
            500,
            1000
          )
        `, [welcomeMessage]);

        console.log('   ✅ تم إدراج البيانات الجديدة');
      } else {
        // تحديث البيانات الموجودة
        console.log('   🔄 تحديث البيانات الموجودة...');
        
        const welcomeMessage = dbName === 'rubaie' 
          ? 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟'
          : 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

        await pool.query(`
          UPDATE ai_settings 
          SET 
            is_enabled = true,
            welcome_message = $1,
            auto_respond = true,
            keywords_trigger = ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم']
          WHERE id = 1
        `, [welcomeMessage]);

        console.log('   ✅ تم تحديث البيانات');
      }

      // التحقق من النتيجة النهائية
      const finalResult = await pool.query('SELECT * FROM ai_settings LIMIT 1');
      if (finalResult.rows.length > 0) {
        const settings = finalResult.rows[0];
        console.log(`   📋 الحالة النهائية:`);
        console.log(`      - مفعل: ${settings.is_enabled}`);
        console.log(`      - رد تلقائي: ${settings.auto_respond}`);
        console.log(`      - رسالة الترحيب: ${settings.welcome_message ? settings.welcome_message.substring(0, 50) + '...' : 'غير محدد'}`);
      }

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من تحديث جدول إعدادات الذكاء الاصطناعي');
}

// تشغيل التحديث
updateAISettingsTable().catch(console.error);
