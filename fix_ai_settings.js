// إصلاح إعدادات الذكاء الاصطناعي
const { Pool } = require('pg');

async function fixAISettings() {
  console.log('🔧 إصلاح إعدادات الذكاء الاصطناعي...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // التحقق من وجود إعدادات
      const checkResult = await pool.query('SELECT COUNT(*) as count FROM ai_settings');
      const settingsCount = parseInt(checkResult.rows[0].count);
      
      console.log(`   📊 عدد الإعدادات الموجودة: ${settingsCount}`);

      if (settingsCount === 0) {
        // إدراج إعدادات جديدة
        console.log('   ➕ إدراج إعدادات جديدة...');
        
        const welcomeMessage = dbName === 'rubaie' 
          ? 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟'
          : 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

        await pool.query(`
          INSERT INTO ai_settings (
            is_enabled, 
            welcome_message, 
            default_response, 
            auto_respond,
            keywords_trigger
          ) VALUES (
            true,
            $1,
            'شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك من قبل فريقنا المختص في أقرب وقت ممكن.',
            true,
            ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم']
          )
        `, [welcomeMessage]);

        console.log('   ✅ تم إدراج الإعدادات الجديدة');
      } else {
        // تحديث الإعدادات الموجودة
        console.log('   🔄 تحديث الإعدادات الموجودة...');
        
        const welcomeMessage = dbName === 'rubaie' 
          ? 'مرحباً بك في شركة الربيعي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟'
          : 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟';

        await pool.query(`
          UPDATE ai_settings 
          SET 
            is_enabled = true,
            welcome_message = $1,
            auto_respond = true,
            keywords_trigger = ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم']
          WHERE id = 1
        `, [welcomeMessage]);

        console.log('   ✅ تم تحديث الإعدادات');
      }

      // التحقق من النتيجة
      const finalResult = await pool.query('SELECT * FROM ai_settings WHERE id = 1');
      if (finalResult.rows.length > 0) {
        const settings = finalResult.rows[0];
        console.log(`   📋 الحالة النهائية:`);
        console.log(`      - مفعل: ${settings.is_enabled}`);
        console.log(`      - رد تلقائي: ${settings.auto_respond}`);
        console.log(`      - رسالة الترحيب: ${settings.welcome_message.substring(0, 50)}...`);
      }

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من إصلاح إعدادات الذكاء الاصطناعي');
}

// تشغيل الإصلاح
fixAISettings().catch(console.error);
