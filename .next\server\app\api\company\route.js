/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/company/route";
exports.ids = ["app/api/company/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/company/route.ts */ \"(rsc)/./src/app/api/company/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/company/route\",\n        pathname: \"/api/company\",\n        filename: \"route\",\n        bundlePath: \"app/api/company/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\company\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/company/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/company/route.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب بيانات الشركة\nasync function GET() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        id,\n        name,\n        legal_name,\n        registration_number,\n        tax_number,\n        address,\n        city,\n        country,\n        phone,\n        email,\n        website,\n        logo_url,\n        logo_right_text,\n        logo_left_text,\n        logo_image_url,\n        established_date,\n        legal_form,\n        capital,\n        description,\n        is_active,\n        created_date,\n        latitude,\n        longitude,\n        working_hours\n      FROM companies\n      ORDER BY created_date DESC\n    `);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب بيانات الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة شركة جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log('📥 بيانات الشركة الجديدة:', body);\n        const { name, legal_name, registration_number, tax_number, address, city, country, phone, email, website, logo_url, logo_right_text, logo_left_text, logo_image_url, established_date, legal_form, capital, description, is_active } = body;\n        if (!name || !legal_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم الشركة والاسم القانوني مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // إضافة الشركة لقاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO companies (\n        name, legal_name, registration_number, tax_number, address, city, country,\n        phone, email, website, logo_url, logo_right_text, logo_left_text,\n        logo_image_url, established_date, legal_form, capital, description,\n        is_active, created_date\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, CURRENT_DATE\n      )\n      RETURNING *\n    `, [\n            name,\n            legal_name,\n            registration_number,\n            tax_number,\n            address,\n            city,\n            country,\n            phone,\n            email,\n            website,\n            logo_url,\n            logo_right_text,\n            logo_left_text,\n            logo_image_url,\n            established_date,\n            legal_form,\n            capital || 0,\n            description,\n            is_active !== undefined ? is_active : true\n        ]);\n        const newCompany = result.rows[0];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة الشركة بنجاح',\n            data: newCompany\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إضافة الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث بيانات الشركة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        console.log('📝 تحديث بيانات الشركة:', body);\n        const { id, name, legal_name, registration_number, tax_number, address, city, country, phone, email, website, logo_url, logo_right_text, logo_left_text, logo_image_url, established_date, legal_form, capital, description, is_active, latitude, longitude, working_hours } = body;\n        if (!id || !name || !legal_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف واسم الشركة والاسم القانوني مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث الشركة في قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE companies SET\n        name = $1,\n        legal_name = $2,\n        registration_number = $3,\n        tax_number = $4,\n        address = $5,\n        city = $6,\n        country = $7,\n        phone = $8,\n        email = $9,\n        website = $10,\n        logo_url = $11,\n        logo_right_text = $12,\n        logo_left_text = $13,\n        logo_image_url = $14,\n        established_date = $15,\n        legal_form = $16,\n        capital = $17,\n        description = $18,\n        is_active = $19,\n        latitude = $20,\n        longitude = $21,\n        working_hours = $22\n      WHERE id = $23\n      RETURNING *\n    `, [\n            name,\n            legal_name,\n            registration_number,\n            tax_number,\n            address,\n            city,\n            country,\n            phone,\n            email,\n            website,\n            logo_url,\n            logo_right_text,\n            logo_left_text,\n            logo_image_url,\n            established_date,\n            legal_form,\n            capital || 0,\n            description,\n            is_active !== undefined ? is_active : true,\n            latitude,\n            longitude,\n            working_hours,\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الشركة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث بيانات الشركة بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تحديث بيانات الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث بيانات الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف شركة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        console.log('🗑️ حذف الشركة:', id);\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الشركة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // حذف الشركة من قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM companies\n      WHERE id = $1\n      RETURNING id, name\n    `, [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الشركة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الشركة بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في حذف الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/company/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QjtBQUd6QiwwQ0FBMEM7QUFDMUMsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLENBQUNDLFdBQVcsRUFBRTtJQUM1QixNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxNQUFNQyxPQUFPLElBQUlMLG9DQUFJQSxDQUFDO0lBQ3BCTSxNQUFNTCxRQUFRQyxHQUFHLENBQUNLLE9BQU8sSUFBSTtJQUM3QkMsTUFBTVAsUUFBUUMsR0FBRyxDQUFDTyxPQUFPLElBQUk7SUFDN0JDLFVBQVVULFFBQVFDLEdBQUcsQ0FBQ1MsT0FBTyxJQUFJO0lBQ2pDQyxVQUFVWCxRQUFRQyxHQUFHLENBQUNDLFdBQVcsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7SUFDaEVVLE1BQU1DLFNBQVNiLFFBQVFDLEdBQUcsQ0FBQ2EsT0FBTyxJQUFJO0FBQ3hDO0FBRU8sZUFBZUMsTUFBTUMsSUFBWSxFQUFFQyxNQUFjO0lBQ3RELE1BQU1DLFNBQVMsTUFBTWQsS0FBS2UsT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNRixPQUFPSCxLQUFLLENBQUNDLE1BQU1DO1FBQ3hDLE9BQU9HO0lBQ1QsU0FBVTtRQUNSRixPQUFPRyxPQUFPO0lBQ2hCO0FBQ0Y7QUFFZSIsInNvdXJjZXMiOlsiRDpcXG1vaGFtaW5ld1xcc3JjXFxsaWJcXGRiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcblxuXG4vLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZg9mE2YXYqSDZhdix2YjYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KpcbmlmICghcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdEQl9QQVNTV09SRCBlbnZpcm9ubWVudCB2YXJpYWJsZSBpcyByZXF1aXJlZCcpXG59XG5cbmNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gIHVzZXI6IHByb2Nlc3MuZW52LkRCX1VTRVIgfHwgJ3Bvc3RncmVzJyxcbiAgaG9zdDogcHJvY2Vzcy5lbnYuREJfSE9TVCB8fCAnbG9jYWxob3N0JyxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ21vaGFtbWknLFxuICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgcHJvY2Vzcy5lbnYuREJfUEFTU1dPUkQgfHwgJ3lvdXJfcGFzc3dvcmRfaGVyZScsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbn0pXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBxdWVyeSh0ZXh0OiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKSB7XG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbmV4cG9ydCB7IHBvb2wgfVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJwcm9jZXNzIiwiZW52IiwiREJfUEFTU1dPUkQiLCJFcnJvciIsInBvb2wiLCJ1c2VyIiwiREJfVVNFUiIsImhvc3QiLCJEQl9IT1NUIiwiZGF0YWJhc2UiLCJEQl9OQU1FIiwicGFzc3dvcmQiLCJwb3J0IiwicGFyc2VJbnQiLCJEQl9QT1JUIiwicXVlcnkiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsInJlbGVhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();