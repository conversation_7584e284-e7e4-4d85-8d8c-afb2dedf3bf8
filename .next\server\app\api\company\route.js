/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/company/route";
exports.ids = ["app/api/company/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/company/route.ts */ \"(rsc)/./src/app/api/company/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/company/route\",\n        pathname: \"/api/company\",\n        filename: \"route\",\n        bundlePath: \"app/api/company/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\company\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_company_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjb21wYW55JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjb21wYW55JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY29tcGFueSUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDbW9oYW1pbmV3JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbW9oYW1pbmV3JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNEO0FBQzVFO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY29tcGFueVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY29tcGFueS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NvbXBhbnlcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NvbXBhbnkvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJEOlxcXFxtb2hhbWluZXdcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY29tcGFueVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/company/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/company/route.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب بيانات الشركة\nasync function GET() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        id,\n        name,\n        legal_name,\n        registration_number,\n        tax_number,\n        address,\n        city,\n        country,\n        phone,\n        email,\n        website,\n        logo_url,\n        logo_right_text,\n        logo_left_text,\n        logo_image_url,\n        established_date,\n        legal_form,\n        capital,\n        description,\n        is_active,\n        created_date,\n        latitude,\n        longitude\n      FROM companies\n      ORDER BY created_date DESC\n    `);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب بيانات الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة شركة جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log('📥 بيانات الشركة الجديدة:', body);\n        const { name, legal_name, registration_number, tax_number, address, city, country, phone, email, website, logo_url, logo_right_text, logo_left_text, logo_image_url, established_date, legal_form, capital, description, is_active } = body;\n        if (!name || !legal_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم الشركة والاسم القانوني مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // إضافة الشركة لقاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO companies (\n        name, legal_name, registration_number, tax_number, address, city, country,\n        phone, email, website, logo_url, logo_right_text, logo_left_text,\n        logo_image_url, established_date, legal_form, capital, description,\n        is_active, created_date\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, CURRENT_DATE\n      )\n      RETURNING *\n    `, [\n            name,\n            legal_name,\n            registration_number,\n            tax_number,\n            address,\n            city,\n            country,\n            phone,\n            email,\n            website,\n            logo_url,\n            logo_right_text,\n            logo_left_text,\n            logo_image_url,\n            established_date,\n            legal_form,\n            capital || 0,\n            description,\n            is_active !== undefined ? is_active : true\n        ]);\n        const newCompany = result.rows[0];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة الشركة بنجاح',\n            data: newCompany\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إضافة الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث بيانات الشركة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        console.log('📝 تحديث بيانات الشركة:', body);\n        const { id, name, legal_name, registration_number, tax_number, address, city, country, phone, email, website, logo_url, logo_right_text, logo_left_text, logo_image_url, established_date, legal_form, capital, description, is_active, latitude, longitude, working_hours } = body;\n        if (!id || !name || !legal_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف واسم الشركة والاسم القانوني مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث الشركة في قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE companies SET\n        name = $1,\n        legal_name = $2,\n        registration_number = $3,\n        tax_number = $4,\n        address = $5,\n        city = $6,\n        country = $7,\n        phone = $8,\n        email = $9,\n        website = $10,\n        logo_url = $11,\n        logo_right_text = $12,\n        logo_left_text = $13,\n        logo_image_url = $14,\n        established_date = $15,\n        legal_form = $16,\n        capital = $17,\n        description = $18,\n        is_active = $19,\n        latitude = $20,\n        longitude = $21,\n        working_hours = $22\n      WHERE id = $23\n      RETURNING *\n    `, [\n            name,\n            legal_name,\n            registration_number,\n            tax_number,\n            address,\n            city,\n            country,\n            phone,\n            email,\n            website,\n            logo_url,\n            logo_right_text,\n            logo_left_text,\n            logo_image_url,\n            established_date || null,\n            legal_form,\n            capital || 0,\n            description,\n            is_active !== undefined ? is_active : true,\n            latitude,\n            longitude,\n            working_hours,\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الشركة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث بيانات الشركة بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تحديث بيانات الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث بيانات الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف شركة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        console.log('🗑️ حذف الشركة:', id);\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الشركة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // حذف الشركة من قاعدة البيانات\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      DELETE FROM companies\n      WHERE id = $1\n      RETURNING id, name\n    `, [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الشركة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الشركة بنجاح'\n        });\n    } catch (error) {\n        console.error('❌ خطأ في حذف الشركة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف الشركة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/company/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ أو متغير البيئة\nconst getDatabaseName = ()=>{\n    // إذا كان هناك متغير DOTENV_CONFIG_PATH يشير لـ rubaie\n    if (process.env.DOTENV_CONFIG_PATH?.includes('rubaie')) {\n        return 'rubaie';\n    }\n    // أو إذا كان المنفذ 8914\n    if (process.env.PORT === '8914') {\n        return 'rubaie';\n    }\n    // أو إذا كان DB_NAME محدد صراحة\n    if (process.env.DB_NAME) {\n        return process.env.DB_NAME;\n    }\n    // الافتراضي\n    return 'mohammi';\n};\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: getDatabaseName(),\n    password: process.env.DB_PASSWORD || 'yemen123',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompany%2Froute&page=%2Fapi%2Fcompany%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompany%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();