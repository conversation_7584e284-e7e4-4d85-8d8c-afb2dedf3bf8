"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_home_components_footer_tsx",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/footer.tsx":
/*!********************************************!*\
  !*** ./src/app/home/<USER>/footer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Footer(param) {\n    let { companyData } = param;\n    _s();\n    const currentYear = new Date().getFullYear();\n    const [footerLinks, setFooterLinks] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            fetchFooterLinks();\n        }\n    }[\"Footer.useEffect\"], []);\n    const fetchFooterLinks = async ()=>{\n        try {\n            const response = await fetch('/api/footer-links');\n            const data = await response.json();\n            if (data.success) {\n                setFooterLinks(data.data.filter((link)=>link.is_active));\n            }\n        } catch (error) {\n            console.error('Error fetching footer links:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تجميع الروابط حسب الفئة\n    const groupedLinks = footerLinks.reduce((acc, link)=>{\n        if (!acc[link.category]) {\n            acc[link.category] = [];\n        }\n        acc[link.category].push(link);\n        return acc;\n    }, {});\n    // ترتيب الروابط داخل كل فئة\n    Object.keys(groupedLinks).forEach((category)=>{\n        groupedLinks[category].sort((a, b)=>a.sort_order - b.sort_order);\n    });\n    const socialLinks = [\n        {\n            name: 'فيسبوك',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'تويتر',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'لينكد إن',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'إنستغرام',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'يوتيوب',\n            icon: _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: '#'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"pt-20 pb-8\",\n        style: {\n            background: 'linear-gradient(135deg, #222222 0%, #171717 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-900 mr-3 shadow-lg\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                className: \"w-6 h-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                        points: \"14 2 14 8 20 8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold\",\n                                            style: {\n                                                color: '#cca967'\n                                            },\n                                            children: companyData.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: companyData.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 space-x-reverse\",\n                                    children: socialLinks.map((social, index)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"w-10 h-10 rounded-full flex items-center justify-center text-gray-300 hover:text-gray-900 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                            style: {\n                                                background: 'linear-gradient(135deg, #444444 0%, #333333 100%)'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = 'linear-gradient(135deg, #444444 0%, #333333 100%)';\n                                            },\n                                            \"aria-label\": social.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        Object.entries(groupedLinks).map((param)=>{\n                            let [category, links] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        style: {\n                                            color: '#cca967'\n                                        },\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.url || '#',\n                                                    className: \"tafahum-footer-widget text-gray-300 hover:text-yellow-400 transition-colors flex items-center group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-1 h-1 bg-yellow-600 rounded-full ml-2 group-hover:bg-yellow-400 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        link.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.id, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    style: {\n                                        color: '#cca967'\n                                    },\n                                    children: \"معلومات التواصل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-1 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: companyData.address && companyData.city ? \"\".concat(companyData.address, \", \").concat(companyData.city) : 'الرياض، المملكة العربية السعودية، شارع العليا، ص.ب 12345'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:\".concat((companyData.phone || '+967-1-123456').replace(/\\D/g, '')),\n                                                    className: \"text-gray-300 hover:text-yellow-400 transition-colors\",\n                                                    children: companyData.phone || '+967 1 234 11 966'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:\".concat(companyData.email || '<EMAIL>'),\n                                                    className: \"text-gray-300 hover:text-yellow-400 transition-colors\",\n                                                    children: companyData.email || '<EMAIL>'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 mt-1 ml-2 flex-shrink-0\",\n                                                    style: {\n                                                        color: '#cca967'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"الأحد - الخميس: 8 صباحاً - 4 مساءً\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 53\n                                                        }, this),\n                                                        \"الجمعة والسبت: مغلق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    style: {\n                        borderColor: 'rgba(204, 169, 103, 0.2)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-300 text-sm mb-4 md:mb-0\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: '#cca967'\n                                    },\n                                    children: companyData.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 29\n                                }, this),\n                                \". جميع الحقوق محفوظة.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"سياسة الخصوصية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"الشروط والأحكام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"|\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/sitemap\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                    children: \"خريطة الموقع\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\footer.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"OMKwvCdPwTk+bl/Ia3n/ULsx9Ik=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/footer.tsx\n"));

/***/ })

});