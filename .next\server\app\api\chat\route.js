/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// دالة لتحديد قاعدة البيانات حسب المنفذ\nfunction getDatabaseByPort(port) {\n    switch(port){\n        case '7443':\n            return 'mohammi';\n        case '8914':\n            return 'rubaie';\n        default:\n            return 'mohammi' // افتراضي\n            ;\n    }\n}\n// دالة للحصول على المنفذ من الطلب\nfunction getPortFromRequest(request) {\n    const host = request.headers.get('host') || '';\n    const port = host.split(':')[1] || '7443';\n    return port;\n}\n// دالة للحصول على إعدادات الذكاء الاصطناعي\nasync function getAISettings(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM ai_settings\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error);\n        return null;\n    }\n}\n// دالة للحصول على بيانات الشركة\nasync function getCompanyData(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM company_data\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الشركة:', error);\n        return null;\n    }\n}\n// دالة للحصول على الخدمات\nasync function getServices(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, slug FROM serviceslow\n      WHERE is_active = true\n      ORDER BY sort_order ASC\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب الخدمات:', error);\n        return [];\n    }\n}\n// دالة للحصول على المكتبة القانونية\nasync function getLegalLibrary(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, category FROM legal_library\n      WHERE is_active = true\n      ORDER BY created_date DESC\n      LIMIT 10\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب المكتبة القانونية:', error);\n        return [];\n    }\n}\n// دالة لمعالجة الرسالة وإنشاء الرد\nasync function processMessage(message, companyData, aiSettings, services, legalLibrary) {\n    const lowerMessage = message.toLowerCase().trim();\n    // التحقق من الكلمات المحفزة\n    const triggerKeywords = aiSettings?.keywords_trigger || [\n        'مساعدة',\n        'استفسار',\n        'سؤال',\n        'معلومات'\n    ];\n    const shouldRespond = triggerKeywords.some((keyword)=>lowerMessage.includes(keyword.toLowerCase()));\n    if (!shouldRespond && !aiSettings?.auto_respond) {\n        return {\n            type: 'no_response',\n            message: 'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.'\n        };\n    }\n    // رسائل الترحيب\n    const greetings = [\n        'مرحبا',\n        'السلام عليكم',\n        'أهلا',\n        'صباح الخير',\n        'مساء الخير',\n        'هلا',\n        'اهلين'\n    ];\n    if (greetings.some((greeting)=>lowerMessage.includes(greeting))) {\n        return {\n            type: 'greeting',\n            message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`\n        };\n    }\n    // أسئلة حول الخدمات\n    const serviceKeywords = [\n        'خدمات',\n        'خدمة',\n        'تخصص',\n        'مجال',\n        'عمل',\n        'قانوني',\n        'محاماة',\n        'استشارة'\n    ];\n    if (serviceKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `نحن نقدم الخدمات القانونية التالية:\\n\\n`;\n        services.forEach((service, index)=>{\n            response += `${index + 1}. **${service.title}**\\n${service.description}\\n\\n`;\n        });\n        response += `للمزيد من التفاصيل، يمكنك التواصل معنا على:\\n📞 ${companyData?.phone || 'غير متوفر'}`;\n        return {\n            type: 'services',\n            message: response\n        };\n    }\n    // أسئلة حول التواصل\n    const contactKeywords = [\n        'تواصل',\n        'رقم',\n        'هاتف',\n        'عنوان',\n        'موقع',\n        'مكان',\n        'اتصال',\n        'واتساب'\n    ];\n    if (contactKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `يمكنك التواصل معنا من خلال:\\n\\n`;\n        if (companyData?.phone) {\n            response += `📞 **الهاتف:** ${companyData.phone}\\n`;\n        }\n        if (companyData?.email) {\n            response += `📧 **البريد الإلكتروني:** ${companyData.email}\\n`;\n        }\n        if (companyData?.address) {\n            response += `📍 **العنوان:** ${companyData.address}\\n`;\n        }\n        if (companyData?.working_hours) {\n            response += `🕐 **ساعات العمل:** ${companyData.working_hours}\\n`;\n        }\n        response += `\\nنحن في خدمتك دائماً!`;\n        return {\n            type: 'contact',\n            message: response\n        };\n    }\n    // أسئلة حول المكتبة القانونية\n    const libraryKeywords = [\n        'قانون',\n        'قوانين',\n        'مكتبة',\n        'وثائق',\n        'مراجع',\n        'نصوص',\n        'تشريع'\n    ];\n    if (libraryKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `لدينا مكتبة قانونية شاملة تحتوي على:\\n\\n`;\n        if (legalLibrary.length > 0) {\n            legalLibrary.forEach((doc, index)=>{\n                response += `${index + 1}. **${doc.title}** (${doc.category})\\n`;\n            });\n            response += `\\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`;\n        } else {\n            response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`;\n        }\n        return {\n            type: 'library',\n            message: response\n        };\n    }\n    // أسئلة حول الشركة\n    const companyKeywords = [\n        'من أنتم',\n        'عنكم',\n        'تعريف',\n        'شركة',\n        'مكتب',\n        'تأسيس',\n        'خبرة'\n    ];\n    if (companyKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `**${companyData?.name || 'مكتبنا القانوني'}**\\n\\n`;\n        if (companyData?.description) {\n            response += `${companyData.description}\\n\\n`;\n        }\n        if (companyData?.established_date) {\n            const establishedYear = new Date(companyData.established_date).getFullYear();\n            const currentYear = new Date().getFullYear();\n            const experience = currentYear - establishedYear;\n            response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\\n`;\n        }\n        if (companyData?.legal_form) {\n            response += `📋 **الشكل القانوني:** ${companyData.legal_form}\\n`;\n        }\n        response += `\\nنحن هنا لخدمتك بأفضل الحلول القانونية.`;\n        return {\n            type: 'company',\n            message: response\n        };\n    }\n    // رد افتراضي\n    return {\n        type: 'default',\n        message: aiSettings?.default_response || `شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك في أقرب وقت ممكن.\\n\\nللتواصل المباشر:\\n📞 ${companyData?.phone || 'غير متوفر'}\\n📧 ${companyData?.email || 'غير متوفر'}`\n    };\n}\n// POST - معالجة رسالة المحادثة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || !message.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد قاعدة البيانات حسب المنفذ\n        const port = getPortFromRequest(request);\n        const dbName = getDatabaseByPort(port);\n        console.log(`🔍 معالجة طلب من المنفذ: ${port} - قاعدة البيانات: ${dbName}`);\n        // جلب البيانات المطلوبة\n        const [aiSettings, companyData, services, legalLibrary] = await Promise.all([\n            getAISettings(dbName),\n            getCompanyData(dbName),\n            getServices(dbName),\n            getLegalLibrary(dbName)\n        ]);\n        if (!aiSettings) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'إعدادات الذكاء الاصطناعي غير متوفرة'\n            }, {\n                status: 500\n            });\n        }\n        // التحقق من تفعيل النظام\n        if (!aiSettings.is_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    type: 'disabled',\n                    message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',\n                    contact: {\n                        phone: companyData?.phone,\n                        email: companyData?.email\n                    }\n                }\n            });\n        }\n        // معالجة الرسالة\n        const response = await processMessage(message, companyData, aiSettings, services, legalLibrary);\n        // حفظ المحادثة في قاعدة البيانات المناسبة\n        try {\n            const { Pool } = __webpack_require__(/*! pg */ \"pg\");\n            const pool = new Pool({\n                user: 'postgres',\n                host: 'localhost',\n                database: dbName,\n                password: 'yemen123',\n                port: 5432\n            });\n            await pool.query(`\n        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)\n        VALUES ($1, $2, $3, $4, NOW())\n      `, [\n                sessionId || 'anonymous',\n                message,\n                response.message,\n                response.type\n            ]);\n            await pool.end();\n        } catch (logError) {\n            console.error('خطأ في حفظ سجل المحادثة:', logError);\n        // لا نوقف العملية إذا فشل الحفظ\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...response,\n                timestamp: new Date().toISOString(),\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في معالجة رسالة المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في معالجة الرسالة'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - جلب رسالة الترحيب\nasync function GET(request) {\n    try {\n        const [aiSettings, companyData] = await Promise.all([\n            getAISettings(),\n            getCompanyData()\n        ]);\n        if (!aiSettings || !aiSettings.is_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'خدمة المحادثة غير متوفرة'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,\n                isEnabled: aiSettings.is_enabled,\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الإعدادات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("pg");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();