/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// دالة لتحديد قاعدة البيانات حسب المنفذ\nfunction getDatabaseByPort(port) {\n    switch(port){\n        case '7443':\n            return 'mohammi';\n        case '8914':\n            return 'rubaie';\n        default:\n            return 'mohammi' // افتراضي\n            ;\n    }\n}\n// دالة للحصول على المنفذ من الطلب\nfunction getPortFromRequest(request) {\n    const host = request.headers.get('host') || '';\n    const port = host.split(':')[1] || '7443';\n    return port;\n}\n// دالة للحصول على إعدادات الذكاء الاصطناعي\nasync function getAISettings(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM ai_settings\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error);\n        return null;\n    }\n}\n// دالة للحصول على بيانات الشركة\nasync function getCompanyData(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM company_data\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الشركة:', error);\n        return null;\n    }\n}\n// دالة للحصول على الخدمات\nasync function getServices(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, slug FROM serviceslow\n      WHERE is_active = true\n      ORDER BY sort_order ASC\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب الخدمات:', error);\n        return [];\n    }\n}\n// دالة للحصول على المكتبة القانونية\nasync function getLegalLibrary(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, category FROM legal_library\n      WHERE is_active = true\n      ORDER BY created_date DESC\n      LIMIT 10\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب المكتبة القانونية:', error);\n        return [];\n    }\n}\n// دالة لمعالجة الرسالة وإنشاء الرد\nasync function processMessage(message, companyData, aiSettings, services, legalLibrary) {\n    const lowerMessage = message.toLowerCase().trim();\n    // التحقق من الكلمات المحفزة\n    const triggerKeywords = aiSettings?.keywords_trigger || [\n        'مساعدة',\n        'استفسار',\n        'سؤال',\n        'معلومات'\n    ];\n    const shouldRespond = triggerKeywords.some((keyword)=>lowerMessage.includes(keyword.toLowerCase()));\n    if (!shouldRespond && !aiSettings?.auto_respond) {\n        return {\n            type: 'no_response',\n            message: 'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.'\n        };\n    }\n    // رسائل الترحيب\n    const greetings = [\n        'مرحبا',\n        'السلام عليكم',\n        'أهلا',\n        'صباح الخير',\n        'مساء الخير',\n        'هلا',\n        'اهلين'\n    ];\n    if (greetings.some((greeting)=>lowerMessage.includes(greeting))) {\n        return {\n            type: 'greeting',\n            message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`\n        };\n    }\n    // أسئلة حول الخدمات\n    const serviceKeywords = [\n        'خدمات',\n        'خدمة',\n        'تخصص',\n        'مجال',\n        'عمل',\n        'قانوني',\n        'محاماة',\n        'استشارة'\n    ];\n    if (serviceKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `نحن نقدم الخدمات القانونية التالية:\\n\\n`;\n        services.forEach((service, index)=>{\n            response += `${index + 1}. **${service.title}**\\n${service.description}\\n\\n`;\n        });\n        response += `للمزيد من التفاصيل، يمكنك التواصل معنا على:\\n📞 ${companyData?.phone || 'غير متوفر'}`;\n        return {\n            type: 'services',\n            message: response\n        };\n    }\n    // أسئلة حول التواصل\n    const contactKeywords = [\n        'تواصل',\n        'رقم',\n        'هاتف',\n        'عنوان',\n        'موقع',\n        'مكان',\n        'اتصال',\n        'واتساب'\n    ];\n    if (contactKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `يمكنك التواصل معنا من خلال:\\n\\n`;\n        if (companyData?.phone) {\n            response += `📞 **الهاتف:** ${companyData.phone}\\n`;\n        }\n        if (companyData?.email) {\n            response += `📧 **البريد الإلكتروني:** ${companyData.email}\\n`;\n        }\n        if (companyData?.address) {\n            response += `📍 **العنوان:** ${companyData.address}\\n`;\n        }\n        if (companyData?.working_hours) {\n            response += `🕐 **ساعات العمل:** ${companyData.working_hours}\\n`;\n        }\n        response += `\\nنحن في خدمتك دائماً!`;\n        return {\n            type: 'contact',\n            message: response\n        };\n    }\n    // أسئلة حول المكتبة القانونية\n    const libraryKeywords = [\n        'قانون',\n        'قوانين',\n        'مكتبة',\n        'وثائق',\n        'مراجع',\n        'نصوص',\n        'تشريع'\n    ];\n    if (libraryKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `لدينا مكتبة قانونية شاملة تحتوي على:\\n\\n`;\n        if (legalLibrary.length > 0) {\n            legalLibrary.forEach((doc, index)=>{\n                response += `${index + 1}. **${doc.title}** (${doc.category})\\n`;\n            });\n            response += `\\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`;\n        } else {\n            response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`;\n        }\n        return {\n            type: 'library',\n            message: response\n        };\n    }\n    // أسئلة حول الشركة\n    const companyKeywords = [\n        'من أنتم',\n        'عنكم',\n        'تعريف',\n        'شركة',\n        'مكتب',\n        'تأسيس',\n        'خبرة'\n    ];\n    if (companyKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `**${companyData?.name || 'مكتبنا القانوني'}**\\n\\n`;\n        if (companyData?.description) {\n            response += `${companyData.description}\\n\\n`;\n        }\n        if (companyData?.established_date) {\n            const establishedYear = new Date(companyData.established_date).getFullYear();\n            const currentYear = new Date().getFullYear();\n            const experience = currentYear - establishedYear;\n            response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\\n`;\n        }\n        if (companyData?.legal_form) {\n            response += `📋 **الشكل القانوني:** ${companyData.legal_form}\\n`;\n        }\n        response += `\\nنحن هنا لخدمتك بأفضل الحلول القانونية.`;\n        return {\n            type: 'company',\n            message: response\n        };\n    }\n    // رد افتراضي\n    return {\n        type: 'default',\n        message: aiSettings?.default_response || `شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك في أقرب وقت ممكن.\\n\\nللتواصل المباشر:\\n📞 ${companyData?.phone || 'غير متوفر'}\\n📧 ${companyData?.email || 'غير متوفر'}`\n    };\n}\n// POST - معالجة رسالة المحادثة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || !message.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد قاعدة البيانات حسب المنفذ\n        const port = getPortFromRequest(request);\n        const dbName = getDatabaseByPort(port);\n        console.log(`🔍 معالجة طلب من المنفذ: ${port} - قاعدة البيانات: ${dbName}`);\n        // جلب البيانات المطلوبة\n        const [aiSettings, companyData, services, legalLibrary] = await Promise.all([\n            getAISettings(dbName),\n            getCompanyData(dbName),\n            getServices(dbName),\n            getLegalLibrary(dbName)\n        ]);\n        if (!aiSettings) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'إعدادات الذكاء الاصطناعي غير متوفرة'\n            }, {\n                status: 500\n            });\n        }\n        // التحقق من تفعيل النظام\n        const isEnabled = aiSettings.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings.enabled;\n        if (!isEnabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    type: 'disabled',\n                    message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',\n                    contact: {\n                        phone: companyData?.phone,\n                        email: companyData?.email\n                    }\n                }\n            });\n        }\n        // معالجة الرسالة\n        const response = await processMessage(message, companyData, aiSettings, services, legalLibrary);\n        // حفظ المحادثة في قاعدة البيانات المناسبة\n        try {\n            const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n            const pool = new Pool({\n                user: 'postgres',\n                host: 'localhost',\n                database: dbName,\n                password: 'yemen123',\n                port: 5432\n            });\n            await pool.query(`\n        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)\n        VALUES ($1, $2, $3, $4, NOW())\n      `, [\n                sessionId || 'anonymous',\n                message,\n                response.message,\n                response.type\n            ]);\n            await pool.end();\n        } catch (logError) {\n            console.error('خطأ في حفظ سجل المحادثة:', logError);\n        // لا نوقف العملية إذا فشل الحفظ\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...response,\n                timestamp: new Date().toISOString(),\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في معالجة رسالة المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في معالجة الرسالة'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - جلب رسالة الترحيب\nasync function GET(request) {\n    try {\n        // تحديد قاعدة البيانات حسب المنفذ\n        const port = getPortFromRequest(request);\n        const dbName = getDatabaseByPort(port);\n        console.log(`🔍 طلب GET من المنفذ: ${port} - قاعدة البيانات: ${dbName}`);\n        const [aiSettings, companyData] = await Promise.all([\n            getAISettings(dbName),\n            getCompanyData(dbName)\n        ]);\n        const isEnabled = aiSettings?.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings?.enabled;\n        if (!aiSettings || !isEnabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'خدمة المحادثة غير متوفرة'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,\n                isEnabled: isEnabled,\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الإعدادات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg?7779":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("pg");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();