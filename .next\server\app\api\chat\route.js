/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// دالة للحصول على إعدادات الذكاء الاصطناعي\nasync function getAISettings() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT * FROM ai_settings \n      WHERE id = 1\n    `);\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error);\n        return null;\n    }\n}\n// دالة للحصول على بيانات الشركة\nasync function getCompanyData() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT * FROM company_data \n      WHERE id = 1\n    `);\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الشركة:', error);\n        return null;\n    }\n}\n// دالة للحصول على الخدمات\nasync function getServices() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT title, description, slug FROM serviceslow \n      WHERE is_active = true \n      ORDER BY sort_order ASC\n    `);\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب الخدمات:', error);\n        return [];\n    }\n}\n// دالة للحصول على المكتبة القانونية\nasync function getLegalLibrary() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT title, description, category FROM legal_library \n      WHERE is_active = true \n      ORDER BY created_date DESC \n      LIMIT 10\n    `);\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب المكتبة القانونية:', error);\n        return [];\n    }\n}\n// دالة لمعالجة الرسالة وإنشاء الرد\nasync function processMessage(message, companyData, aiSettings, services, legalLibrary) {\n    const lowerMessage = message.toLowerCase().trim();\n    // التحقق من الكلمات المحفزة\n    const triggerKeywords = aiSettings?.keywords_trigger || [\n        'مساعدة',\n        'استفسار',\n        'سؤال',\n        'معلومات'\n    ];\n    const shouldRespond = triggerKeywords.some((keyword)=>lowerMessage.includes(keyword.toLowerCase()));\n    if (!shouldRespond && !aiSettings?.auto_respond) {\n        return {\n            type: 'no_response',\n            message: 'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.'\n        };\n    }\n    // رسائل الترحيب\n    const greetings = [\n        'مرحبا',\n        'السلام عليكم',\n        'أهلا',\n        'صباح الخير',\n        'مساء الخير',\n        'هلا',\n        'اهلين'\n    ];\n    if (greetings.some((greeting)=>lowerMessage.includes(greeting))) {\n        return {\n            type: 'greeting',\n            message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`\n        };\n    }\n    // أسئلة حول الخدمات\n    const serviceKeywords = [\n        'خدمات',\n        'خدمة',\n        'تخصص',\n        'مجال',\n        'عمل',\n        'قانوني',\n        'محاماة',\n        'استشارة'\n    ];\n    if (serviceKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `نحن نقدم الخدمات القانونية التالية:\\n\\n`;\n        services.forEach((service, index)=>{\n            response += `${index + 1}. **${service.title}**\\n${service.description}\\n\\n`;\n        });\n        response += `للمزيد من التفاصيل، يمكنك التواصل معنا على:\\n📞 ${companyData?.phone || 'غير متوفر'}`;\n        return {\n            type: 'services',\n            message: response\n        };\n    }\n    // أسئلة حول التواصل\n    const contactKeywords = [\n        'تواصل',\n        'رقم',\n        'هاتف',\n        'عنوان',\n        'موقع',\n        'مكان',\n        'اتصال',\n        'واتساب'\n    ];\n    if (contactKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `يمكنك التواصل معنا من خلال:\\n\\n`;\n        if (companyData?.phone) {\n            response += `📞 **الهاتف:** ${companyData.phone}\\n`;\n        }\n        if (companyData?.email) {\n            response += `📧 **البريد الإلكتروني:** ${companyData.email}\\n`;\n        }\n        if (companyData?.address) {\n            response += `📍 **العنوان:** ${companyData.address}\\n`;\n        }\n        if (companyData?.working_hours) {\n            response += `🕐 **ساعات العمل:** ${companyData.working_hours}\\n`;\n        }\n        response += `\\nنحن في خدمتك دائماً!`;\n        return {\n            type: 'contact',\n            message: response\n        };\n    }\n    // أسئلة حول المكتبة القانونية\n    const libraryKeywords = [\n        'قانون',\n        'قوانين',\n        'مكتبة',\n        'وثائق',\n        'مراجع',\n        'نصوص',\n        'تشريع'\n    ];\n    if (libraryKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `لدينا مكتبة قانونية شاملة تحتوي على:\\n\\n`;\n        if (legalLibrary.length > 0) {\n            legalLibrary.forEach((doc, index)=>{\n                response += `${index + 1}. **${doc.title}** (${doc.category})\\n`;\n            });\n            response += `\\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`;\n        } else {\n            response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`;\n        }\n        return {\n            type: 'library',\n            message: response\n        };\n    }\n    // أسئلة حول الشركة\n    const companyKeywords = [\n        'من أنتم',\n        'عنكم',\n        'تعريف',\n        'شركة',\n        'مكتب',\n        'تأسيس',\n        'خبرة'\n    ];\n    if (companyKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `**${companyData?.name || 'مكتبنا القانوني'}**\\n\\n`;\n        if (companyData?.description) {\n            response += `${companyData.description}\\n\\n`;\n        }\n        if (companyData?.established_date) {\n            const establishedYear = new Date(companyData.established_date).getFullYear();\n            const currentYear = new Date().getFullYear();\n            const experience = currentYear - establishedYear;\n            response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\\n`;\n        }\n        if (companyData?.legal_form) {\n            response += `📋 **الشكل القانوني:** ${companyData.legal_form}\\n`;\n        }\n        response += `\\nنحن هنا لخدمتك بأفضل الحلول القانونية.`;\n        return {\n            type: 'company',\n            message: response\n        };\n    }\n    // رد افتراضي\n    return {\n        type: 'default',\n        message: aiSettings?.default_response || `شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك في أقرب وقت ممكن.\\n\\nللتواصل المباشر:\\n📞 ${companyData?.phone || 'غير متوفر'}\\n📧 ${companyData?.email || 'غير متوفر'}`\n    };\n}\n// POST - معالجة رسالة المحادثة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || !message.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // جلب البيانات المطلوبة\n        const [aiSettings, companyData, services, legalLibrary] = await Promise.all([\n            getAISettings(),\n            getCompanyData(),\n            getServices(),\n            getLegalLibrary()\n        ]);\n        if (!aiSettings) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'إعدادات الذكاء الاصطناعي غير متوفرة'\n            }, {\n                status: 500\n            });\n        }\n        // التحقق من تفعيل النظام\n        if (!aiSettings.is_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    type: 'disabled',\n                    message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',\n                    contact: {\n                        phone: companyData?.phone,\n                        email: companyData?.email\n                    }\n                }\n            });\n        }\n        // معالجة الرسالة\n        const response = await processMessage(message, companyData, aiSettings, services, legalLibrary);\n        // حفظ المحادثة في قاعدة البيانات (اختياري)\n        try {\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)\n        VALUES ($1, $2, $3, $4, NOW())\n      `, [\n                sessionId || 'anonymous',\n                message,\n                response.message,\n                response.type\n            ]);\n        } catch (logError) {\n            console.error('خطأ في حفظ سجل المحادثة:', logError);\n        // لا نوقف العملية إذا فشل الحفظ\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...response,\n                timestamp: new Date().toISOString(),\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في معالجة رسالة المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في معالجة الرسالة'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - جلب رسالة الترحيب\nasync function GET(request) {\n    try {\n        const [aiSettings, companyData] = await Promise.all([\n            getAISettings(),\n            getCompanyData()\n        ]);\n        if (!aiSettings || !aiSettings.is_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'خدمة المحادثة غير متوفرة'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,\n                isEnabled: aiSettings.is_enabled,\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الإعدادات'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();