/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// دالة لتحديد قاعدة البيانات حسب المنفذ\nfunction getDatabaseByPort(port) {\n    switch(port){\n        case '7443':\n            return 'mohammi';\n        case '8914':\n            return 'rubaie';\n        default:\n            return 'mohammi' // افتراضي\n            ;\n    }\n}\n// دالة للحصول على المنفذ من الطلب\nfunction getPortFromRequest(request) {\n    const host = request.headers.get('host') || '';\n    const port = host.split(':')[1] || '7443';\n    return port;\n}\n// دالة للحصول على إعدادات الذكاء الاصطناعي\nasync function getAISettings(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM ai_settings\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error);\n        return null;\n    }\n}\n// دالة للحصول على بيانات الشركة\nasync function getCompanyData(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT * FROM company_data\n      WHERE id = 1\n    `);\n        await pool.end();\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الشركة:', error);\n        return null;\n    }\n}\n// دالة للحصول على الخدمات\nasync function getServices(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, slug FROM serviceslow\n      WHERE is_active = true\n      ORDER BY sort_order ASC\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب الخدمات:', error);\n        return [];\n    }\n}\n// دالة للحصول على المكتبة القانونية\nasync function getLegalLibrary(dbName) {\n    try {\n        const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n        const pool = new Pool({\n            user: 'postgres',\n            host: 'localhost',\n            database: dbName,\n            password: 'yemen123',\n            port: 5432\n        });\n        const result = await pool.query(`\n      SELECT title, description, category FROM legal_library\n      WHERE is_active = true\n      ORDER BY created_date DESC\n      LIMIT 10\n    `);\n        await pool.end();\n        return result.rows || [];\n    } catch (error) {\n        console.error('خطأ في جلب المكتبة القانونية:', error);\n        return [];\n    }\n}\n// دالة لمعالجة الرسالة وإنشاء الرد\nasync function processMessage(message, companyData, aiSettings, services, legalLibrary) {\n    const lowerMessage = message.toLowerCase().trim();\n    // رسائل الترحيب\n    const greetings = [\n        'مرحبا',\n        'السلام عليكم',\n        'أهلا',\n        'صباح الخير',\n        'مساء الخير',\n        'هلا',\n        'اهلين'\n    ];\n    if (greetings.some((greeting)=>lowerMessage.includes(greeting))) {\n        return {\n            type: 'greeting',\n            message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`\n        };\n    }\n    // أسئلة عن الأسعار والرسوم - يجب أن تأتي قبل الاستشارة العامة\n    const priceKeywords = [\n        'سعر',\n        'تكلفة',\n        'رسوم',\n        'أتعاب',\n        'كم يكلف',\n        'كم السعر',\n        'كم تكلفة'\n    ];\n    if (priceKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        return {\n            type: 'pricing',\n            message: `أسعار الخدمات القانونية تختلف حسب نوع القضية وتعقيدها.\\n\\nللحصول على عرض سعر دقيق، يرجى:\\n• التواصل مع مكتبنا مباشرة\\n• تحديد نوع الخدمة المطلوبة\\n• شرح تفاصيل القضية\\n\\n📞 ${companyData?.phone || 'اتصل بنا'} للاستفسار عن الأسعار`\n        };\n    }\n    // طلبات الاستشارة القانونية - رد مخصص وذكي\n    const consultationKeywords = [\n        'استشارة',\n        'استشارات',\n        'مشورة',\n        'رأي قانوني',\n        'نصيحة قانونية',\n        'احتاج استشارة',\n        'اريد استشارة'\n    ];\n    if (consultationKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        return {\n            type: 'consultation',\n            message: `بالطبع! يسعدني مساعدتك في الحصول على الاستشارة القانونية المناسبة.\\n\\nما هو نوع الاستشارة التي تحتاجها؟ يمكنك إخباري بتفاصيل أكثر عن موضوعك القانوني وسأوجهك للمختص المناسب.\\n\\nأو يمكنك التواصل مباشرة مع مكتبنا:\\n📞 ${companyData?.phone || 'اتصل بنا'}`\n        };\n    }\n    // أسئلة عامة حول الخدمات - رد مختصر\n    const serviceKeywords = [\n        'خدمات',\n        'خدمة',\n        'تخصص',\n        'مجال',\n        'عمل',\n        'قانوني',\n        'محاماة',\n        'ماذا تقدمون'\n    ];\n    if (serviceKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        const mainServices = services.slice(0, 3) // أول 3 خدمات فقط\n        ;\n        let response = `نحن متخصصون في:\\n\\n`;\n        mainServices.forEach((service, index)=>{\n            response += `• ${service.title}\\n`;\n        });\n        response += `\\nهل تحتاج معلومات أكثر عن أي من هذه المجالات؟`;\n        return {\n            type: 'services',\n            message: response\n        };\n    }\n    // أسئلة حول التواصل\n    const contactKeywords = [\n        'تواصل',\n        'رقم',\n        'هاتف',\n        'عنوان',\n        'موقع',\n        'مكان',\n        'اتصال',\n        'واتساب'\n    ];\n    if (contactKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `يمكنك التواصل معنا من خلال:\\n\\n`;\n        if (companyData?.phone) {\n            response += `📞 **الهاتف:** ${companyData.phone}\\n`;\n        }\n        if (companyData?.email) {\n            response += `📧 **البريد الإلكتروني:** ${companyData.email}\\n`;\n        }\n        if (companyData?.address) {\n            response += `📍 **العنوان:** ${companyData.address}\\n`;\n        }\n        if (companyData?.working_hours) {\n            response += `🕐 **ساعات العمل:** ${companyData.working_hours}\\n`;\n        }\n        response += `\\nنحن في خدمتك دائماً!`;\n        return {\n            type: 'contact',\n            message: response\n        };\n    }\n    // أسئلة حول المكتبة القانونية\n    const libraryKeywords = [\n        'قانون',\n        'قوانين',\n        'مكتبة',\n        'وثائق',\n        'مراجع',\n        'نصوص',\n        'تشريع'\n    ];\n    if (libraryKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `لدينا مكتبة قانونية شاملة تحتوي على:\\n\\n`;\n        if (legalLibrary.length > 0) {\n            legalLibrary.forEach((doc, index)=>{\n                response += `${index + 1}. **${doc.title}** (${doc.category})\\n`;\n            });\n            response += `\\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`;\n        } else {\n            response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`;\n        }\n        return {\n            type: 'library',\n            message: response\n        };\n    }\n    // أسئلة حول الشركة\n    const companyKeywords = [\n        'من أنتم',\n        'عنكم',\n        'تعريف',\n        'شركة',\n        'مكتب',\n        'تأسيس',\n        'خبرة'\n    ];\n    if (companyKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        let response = `**${companyData?.name || 'مكتبنا القانوني'}**\\n\\n`;\n        if (companyData?.description) {\n            response += `${companyData.description}\\n\\n`;\n        }\n        if (companyData?.established_date) {\n            const establishedYear = new Date(companyData.established_date).getFullYear();\n            const currentYear = new Date().getFullYear();\n            const experience = currentYear - establishedYear;\n            response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\\n`;\n        }\n        if (companyData?.legal_form) {\n            response += `📋 **الشكل القانوني:** ${companyData.legal_form}\\n`;\n        }\n        response += `\\nنحن هنا لخدمتك بأفضل الحلول القانونية.`;\n        return {\n            type: 'company',\n            message: response\n        };\n    }\n    // أسئلة محددة حول مجالات قانونية\n    const legalAreas = {\n        'جنائي': [\n            'جنائي',\n            'جريمة',\n            'اتهام',\n            'محكمة جنائية',\n            'قضية جنائية'\n        ],\n        'مدني': [\n            'مدني',\n            'عقد',\n            'دين',\n            'تعويض',\n            'نزاع مدني'\n        ],\n        'تجاري': [\n            'تجاري',\n            'شركة',\n            'استثمار',\n            'عقد تجاري',\n            'نزاع تجاري'\n        ],\n        'عمل': [\n            'عمل',\n            'موظف',\n            'راتب',\n            'فصل',\n            'حقوق العمال'\n        ],\n        'أسرة': [\n            'طلاق',\n            'زواج',\n            'حضانة',\n            'نفقة',\n            'ميراث'\n        ]\n    };\n    for (const [area, keywords] of Object.entries(legalAreas)){\n        if (keywords.some((keyword)=>lowerMessage.includes(keyword))) {\n            return {\n                type: 'specific_area',\n                message: `أفهم أنك تحتاج مساعدة في قضايا ${area === 'أسرة' ? 'الأحوال الشخصية' : 'القانون ال' + area}.\\n\\nيمكنني توجيهك للمختص المناسب في هذا المجال. هل يمكنك إخباري بالمزيد من التفاصيل عن حالتك؟\\n\\nأو تواصل مباشرة:\\n📞 ${companyData?.phone || 'اتصل بنا'}`\n            };\n        }\n    }\n    // أسئلة عن المواعيد\n    const appointmentKeywords = [\n        'موعد',\n        'مقابلة',\n        'لقاء',\n        'زيارة',\n        'حجز موعد'\n    ];\n    if (appointmentKeywords.some((keyword)=>lowerMessage.includes(keyword))) {\n        return {\n            type: 'appointment',\n            message: `لحجز موعد للاستشارة:\\n\\n📞 اتصل بنا على: ${companyData?.phone || 'الرقم غير متوفر'}\\n📧 أو راسلنا على: ${companyData?.email || 'البريد غير متوفر'}\\n\\nسنكون سعداء لترتيب موعد يناسبك لمناقشة قضيتك بالتفصيل.`\n        };\n    }\n    // رد افتراضي ذكي\n    return {\n        type: 'default',\n        message: `شكراً لتواصلك معنا! \\n\\nيمكنني مساعدتك في:\\n• الاستفسار عن خدماتنا القانونية\\n• توجيهك للمختص المناسب\\n• معلومات التواصل والمواعيد\\n\\nما الذي تحتاج مساعدة فيه تحديداً؟\\n\\n📞 ${companyData?.phone || 'اتصل بنا'}`\n    };\n}\n// POST - معالجة رسالة المحادثة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { message, sessionId } = body;\n        if (!message || !message.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الرسالة مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // تحديد قاعدة البيانات حسب المنفذ\n        const port = getPortFromRequest(request);\n        const dbName = getDatabaseByPort(port);\n        console.log(`🔍 معالجة طلب من المنفذ: ${port} - قاعدة البيانات: ${dbName}`);\n        // جلب البيانات المطلوبة\n        const [aiSettings, companyData, services, legalLibrary] = await Promise.all([\n            getAISettings(dbName),\n            getCompanyData(dbName),\n            getServices(dbName),\n            getLegalLibrary(dbName)\n        ]);\n        if (!aiSettings) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'إعدادات الذكاء الاصطناعي غير متوفرة'\n            }, {\n                status: 500\n            });\n        }\n        // التحقق من تفعيل النظام\n        const isEnabled = aiSettings.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings.enabled;\n        if (!isEnabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    type: 'disabled',\n                    message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',\n                    contact: {\n                        phone: companyData?.phone,\n                        email: companyData?.email\n                    }\n                }\n            });\n        }\n        // معالجة الرسالة\n        const response = await processMessage(message, companyData, aiSettings, services, legalLibrary);\n        // حفظ المحادثة في قاعدة البيانات المناسبة\n        try {\n            const { Pool } = __webpack_require__(/*! pg */ \"pg?7779\");\n            const pool = new Pool({\n                user: 'postgres',\n                host: 'localhost',\n                database: dbName,\n                password: 'yemen123',\n                port: 5432\n            });\n            await pool.query(`\n        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)\n        VALUES ($1, $2, $3, $4, NOW())\n      `, [\n                sessionId || 'anonymous',\n                message,\n                response.message,\n                response.type\n            ]);\n            await pool.end();\n        } catch (logError) {\n            console.error('خطأ في حفظ سجل المحادثة:', logError);\n        // لا نوقف العملية إذا فشل الحفظ\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...response,\n                timestamp: new Date().toISOString(),\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في معالجة رسالة المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في معالجة الرسالة'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - جلب رسالة الترحيب\nasync function GET(request) {\n    try {\n        // تحديد قاعدة البيانات حسب المنفذ\n        const port = getPortFromRequest(request);\n        const dbName = getDatabaseByPort(port);\n        console.log(`🔍 طلب GET من المنفذ: ${port} - قاعدة البيانات: ${dbName}`);\n        const [aiSettings, companyData] = await Promise.all([\n            getAISettings(dbName),\n            getCompanyData(dbName)\n        ]);\n        const isEnabled = aiSettings?.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings?.enabled;\n        if (!aiSettings || !isEnabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'خدمة المحادثة غير متوفرة'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,\n                isEnabled: isEnabled,\n                companyInfo: {\n                    name: companyData?.name,\n                    phone: companyData?.phone,\n                    email: companyData?.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات المحادثة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الإعدادات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg?7779":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("pg");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();