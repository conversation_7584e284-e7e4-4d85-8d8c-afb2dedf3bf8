(()=>{var e={};e.id=1175,e.ids=[1175],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7234:(e,t,a)=>{Promise.resolve().then(a.bind(a,29253))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>p,eb:()=>b,gC:()=>h,l6:()=>d,yv:()=>m});var s=a(60687),r=a(43210),i=a(58106),n=a(78272),l=a(3589),c=a(13964),o=a(4780);let d=i.bL;i.YJ;let m=i.WT,p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-700"})})]}));p.displayName=i.l9.displayName;let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=i.PP.displayName;let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let h=r.forwardRef(({className:e,children:t,position:a="popper",...r},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(u,{}),(0,s.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(x,{})]})}));h.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let b=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:t})]}));b.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25386:(e,t,a)=>{Promise.resolve().then(a.bind(a,33459))},29253:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\receipt-vouchers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\receipt-vouchers\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33459:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var s=a(60687),r=a(43210),i=a(98254),n=a(44493),l=a(29523),c=a(89667),o=a(80013),d=a(34729),m=a(15079),p=a(63503),u=a(96834),x=a(10022),h=a(96474),b=a(99270),f=a(13861),y=a(63143),g=a(88233),j=a(71444);function v(){let[e,t]=(0,r.useState)([]),[a,v]=(0,r.useState)([]),[_,N]=(0,r.useState)([]),[w,k]=(0,r.useState)([]),[C,D]=(0,r.useState)([]),[S,A]=(0,r.useState)([]),[$,P]=(0,r.useState)([]),[q,L]=(0,r.useState)(!0),[R,J]=(0,r.useState)(!1),[z,E]=(0,r.useState)(null),[F,M]=(0,r.useState)(""),[T,V]=(0,r.useState)({entry_date:new Date().toISOString().split("T")[0],beneficiary_id:"",beneficiary_name:"",beneficiary_type:"",debit_account_id:"",credit_account_id:"",amount:"",currency_id:"1",payment_method_id:"1",cost_center_id:"",description:"",reference_number:"",case_id:"",service_id:""}),G=e=>{if(!e)return;let t=a.find(t=>t.id.toString()===e);if(!t)return;let s="",r="external";t.is_linked_record&&t.original_table?"clients"===t.original_table?(r="client",s=t.account_name.replace("حساب العميل: ","").replace("حساب العميل:","").trim()):"employees"===t.original_table?(r="employee",s=t.account_name.replace("حساب الموظف: ","").replace("حساب الموظف:","").trim()):"suppliers"===t.original_table&&(r="supplier",s=t.account_name.replace("حساب المورد: ","").replace("حساب المورد:","").trim()):(s=t.account_name,t.account_name.includes("عميل")||t.account_name.includes("العملاء")?r="client":t.account_name.includes("موظف")||t.account_name.includes("الموظفين")?r="employee":(t.account_name.includes("مورد")||t.account_name.includes("الموردين"))&&(r="supplier")),V(e=>({...e,beneficiary_name:s,beneficiary_type:r}))},U=async()=>{try{L(!0);let e=await fetch("/api/accounting/receipt-vouchers");if(e.ok){let a=await e.json();t(a.vouchers||[])}let a=await fetch("/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true");if(a.ok){let e=await a.json();v(e.accounts||[])}let s=await fetch("/api/accounting/currencies");if(s.ok){let e=await s.json();N(e.currencies||[])}let r=await fetch("/api/accounting/payment-methods");if(r.ok){let e=await r.json();k(e.methods||[])}let i=await fetch("/api/cost-centers");if(i.ok){let e=await i.json();D(e.centers||[])}let n=await fetch("/api/issues");if(n.ok){let e=await n.json();console.log("Cases data:",e),A(e.data||e.issues||[])}let l=await fetch("/api/services");if(l.ok){let e=await l.json();console.log("Services data:",e),P(e.data||e.services||[])}}catch(e){console.error("خطأ في جلب البيانات:",e)}finally{L(!1)}},B=async e=>{e.preventDefault();try{console.log("\uD83D\uDCE4 بيانات النموذج المرسلة:",T);let e=z?`/api/accounting/receipt-vouchers/${z.id}`:"/api/accounting/receipt-vouchers",t=z?"PUT":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(T)});if(a.ok)await U(),J(!1),E(null),O();else{let e=await a.json();console.error("❌ خطأ من الخادم:",e),alert(`خطأ: ${e.error}
التفاصيل: ${e.details||"غير متوفرة"}`)}}catch(e){console.error("خطأ في حفظ السند:",e),alert("حدث خطأ أثناء حفظ السند")}},O=()=>{V({entry_date:new Date().toISOString().split("T")[0],beneficiary_id:"",beneficiary_name:"",beneficiary_type:"",debit_account_id:"",credit_account_id:"",amount:"",currency_id:"1",payment_method_id:"1",cost_center_id:"",description:"",reference_number:"",case_id:"",service_id:""})},Z=e=>{E(e),V({entry_date:e.entry_date,beneficiary_id:"",beneficiary_name:e.payer_name||e.party_name||"",beneficiary_type:e.payer_type||e.party_type||"client",debit_account_id:e.debit_account_id?.toString()||"",credit_account_id:e.credit_account_id?.toString()||"",amount:(e.amount||e.total_debit||0).toString(),currency_id:e.currency_id?.toString()||"1",payment_method_id:e.payment_method_id?.toString()||"1",cost_center_id:e.cost_center_id?.toString()||"",description:e.description||"",reference_number:e.reference_number||"",case_id:e.case_id?.toString()||"",service_id:e.service_id?.toString()||""}),J(!0)},I=e=>{alert(`عرض تفاصيل السند رقم: ${e.entry_number}`)},H=e=>{let t=window.open("","_blank","width=800,height=600");t&&(t.document.write(`
        <html dir="rtl">
          <head>
            <title>سند قبض رقم ${e.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
              .label { font-weight: bold; text-align: right; }
              .amount { font-size: 18px; font-weight: bold; color: #2e7d32; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند قبض</h1>
              <h2>رقم: ${e.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span>${new Date(e.entry_date).toLocaleDateString("en-GB")}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="amount">${(e.amount||e.total_debit||0).toLocaleString()} ${e.currency_code||"ر.ي"}</span>
              </div>
              <div class="row">
                <span class="label">الدافع:</span>
                <span>${e.payer_name||"غير محدد"}</span>
                ${e.payer_type?`<br><small style="color: #666;">(${"client"===e.payer_type?"عميل":"employee"===e.payer_type?"موظف":"supplier"===e.payer_type?"مورد":"خارجي"})</small>`:""}
              </div>
              <div class="row">
                <span class="label">الحساب المدين:</span>
                <span>${e.debit_account_name||"غير محدد"}</span>
                ${e.debit_account_code?`<br><small style="color: #666;">(${e.debit_account_code})</small>`:""}
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span>${e.description}</span>
              </div>
            </div>
          </body>
        </html>
      `),t.document.close(),t.print())},W=async e=>{if(confirm("هل أنت متأكد من حذف هذا السند؟"))try{let t=await fetch(`/api/accounting/receipt-vouchers/${e}`,{method:"DELETE"});if(t.ok)await U(),alert("تم حذف السند بنجاح");else{let e=await t.json();alert(`خطأ في حذف السند: ${e.error}`)}}catch(e){console.error("خطأ في حذف السند:",e),alert("حدث خطأ أثناء حذف السند")}},Y=e.filter(e=>e.entry_number.toLowerCase().includes(F.toLowerCase())||e.payer_name.toLowerCase().includes(F.toLowerCase())||e.description.toLowerCase().includes(F.toLowerCase())),K=e=>{let t={draft:{label:"مسودة",variant:"secondary"},approved:{label:"معتمد",variant:"default"},cancelled:{label:"ملغي",variant:"destructive"}},a=t[e]||t.draft;return(0,s.jsx)(u.E,{variant:a.variant,children:a.label})};return(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"space-y-6 p-6 bg-white min-h-screen",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"سندات القبض"}),(0,s.jsx)("p",{className:"text-gray-600",children:"إدارة سندات القبض والمقبوضات"})]})]}),(0,s.jsxs)(l.$,{onClick:()=>J(!0),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 ml-2"}),"سند قبض جديد"]})]}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(c.p,{placeholder:"البحث في سندات القبض...",value:F,onChange:e=>M(e.target.value),className:"pr-10"})]})})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{children:["سندات القبض (",Y.length,")"]})}),(0,s.jsx)(n.Wu,{children:q?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"جاري تحميل السندات..."})]}):0===Y.length?(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"لا توجد سندات قبض"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"رقم السند"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"التاريخ"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"المبلغ"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"الدافع"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"المستلم"}),(0,s.jsx)("th",{className:"text-right p-3 font-semibold text-black",children:"البيان"}),(0,s.jsx)("th",{className:"text-center p-3 font-semibold text-black",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{children:Y.map((e,t)=>(0,s.jsxs)("tr",{className:`border-b hover:bg-gray-50 ${t%2==0?"bg-white":"bg-gray-25"}`,children:[(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"font-mono text-black font-medium",children:e.entry_number}),K(e.status)]})}),(0,s.jsx)("td",{className:"p-3 text-black",children:new Date(e.entry_date).toLocaleDateString("en-GB")}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("span",{className:"font-bold text-black",children:[(e.amount||e.total_debit||0).toLocaleString()," ",e.currency_code||"ر.ي"]})}),(0,s.jsxs)("td",{className:"p-3 text-black",children:[(0,s.jsx)("span",{className:"font-medium",children:e.payer_name||"غير محدد"}),e.payer_type&&(0,s.jsxs)("span",{className:"text-xs text-black block",children:["(","client"===e.payer_type?"عميل":"employee"===e.payer_type?"موظف":"supplier"===e.payer_type?"مورد":"خارجي",")"]})]}),(0,s.jsx)("td",{className:"p-3 text-black",children:(0,s.jsx)("span",{className:"font-medium",children:e.debit_account_name||"غير محدد"})}),(0,s.jsx)("td",{className:"p-3 text-black max-w-xs truncate",title:e.description,children:e.description}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>I(e),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",title:"مشاهدة",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>Z(e),className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",title:"تعديل",children:(0,s.jsx)(y.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>W(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"حذف",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>H(e),className:"text-gray-600 hover:text-gray-700 hover:bg-gray-50",title:"طباعة",children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})]})})]},`voucher-${e.id}-${t}`))})]})})})]}),(0,s.jsx)(p.lG,{open:R,onOpenChange:J,children:(0,s.jsxs)(p.Cf,{className:"max-w-[95vw] max-h-[95vh] w-[95vw] overflow-y-auto",children:[(0,s.jsxs)(p.c7,{className:"bg-gradient-to-r from-green-50 to-green-100 p-4 border-b",children:[(0,s.jsxs)(p.L3,{className:"text-2xl font-bold text-green-800 flex items-center",children:[(0,s.jsx)(x.A,{className:"h-7 w-7 mr-3"}),z?"✏️ تعديل سند القبض":"\uD83D\uDCB0 سند قبض جديد"]}),(0,s.jsx)(p.rr,{className:"text-green-600 mt-2",children:z?"تعديل بيانات سند القبض المحدد":"إنشاء سند قبض جديد مع تفاصيل المبلغ والحساب"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,s.jsxs)("form",{onSubmit:B,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-8 gap-3",children:[(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"entry_date",className:"text-xs font-medium text-black mb-1 block",children:"\uD83D\uDCC5 تاريخ السند"}),(0,s.jsx)(c.p,{id:"entry_date",type:"date",value:T.entry_date,onChange:e=>V({...T,entry_date:e.target.value}),required:!0,className:"h-8 text-sm"})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"amount",className:"text-xs font-medium text-black mb-1 block",children:"\uD83D\uDCB0 المبلغ"}),(0,s.jsx)(c.p,{id:"amount",type:"number",step:"0.01",value:T.amount,onChange:e=>V({...T,amount:e.target.value}),placeholder:"0.00",required:!0,className:"h-8 text-sm"})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"currency_id",className:"text-xs font-medium text-yellow-700 mb-1 block",children:"\uD83D\uDCB1 العملة"}),(0,s.jsxs)(m.l6,{value:T.currency_id,onValueChange:e=>V({...T,currency_id:e}),children:[(0,s.jsx)(m.bq,{className:"h-8 text-sm",children:(0,s.jsx)(m.yv,{placeholder:"اختر العملة"})}),(0,s.jsx)(m.gC,{children:_.map(e=>(0,s.jsxs)(m.eb,{value:e.id.toString(),children:[e.currency_code," - ",e.currency_name]},e.id))})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"payment_method_id",className:"text-xs font-medium text-indigo-700 mb-1 block",children:"\uD83D\uDCB3 طريقة الدفع"}),(0,s.jsxs)(m.l6,{value:T.payment_method_id,onValueChange:e=>V({...T,payment_method_id:e}),children:[(0,s.jsx)(m.bq,{className:"h-8 text-sm",children:(0,s.jsx)(m.yv,{placeholder:"اختر طريقة الدفع"})}),(0,s.jsx)(m.gC,{children:w.map(e=>(0,s.jsx)(m.eb,{value:e.id.toString(),children:e.method_name},e.id))})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-8 gap-3",children:[(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"case_id",className:"text-xs font-medium text-black mb-1 block",children:"⚖️ القضية"}),(0,s.jsxs)(m.l6,{value:T.case_id,onValueChange:e=>V({...T,case_id:e}),children:[(0,s.jsx)(m.bq,{className:"h-8 text-sm",children:(0,s.jsx)(m.yv,{placeholder:"اختر القضية"})}),(0,s.jsxs)(m.gC,{className:"max-h-60",children:[(0,s.jsx)(m.eb,{value:"0",children:"بدون قضية"}),S.map(e=>(0,s.jsx)(m.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.title})},e.id))]})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"cost_center_id",className:"text-xs font-medium text-black mb-1 block",children:"\uD83C\uDFE2 مركز التكلفة"}),(0,s.jsxs)(m.l6,{value:T.cost_center_id,onValueChange:e=>V({...T,cost_center_id:e}),children:[(0,s.jsx)(m.bq,{className:"h-8 text-sm",children:(0,s.jsx)(m.yv,{placeholder:"اختر مركز التكلفة"})}),(0,s.jsxs)(m.gC,{className:"max-h-60",children:[(0,s.jsx)(m.eb,{value:"0",children:"بدون مركز تكلفة"}),C.map(e=>(0,s.jsx)(m.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.center_name})},e.id))]})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"service_id",className:"text-xs font-medium text-teal-700 mb-1 block",children:"\uD83D\uDD27 الخدمة"}),(0,s.jsxs)(m.l6,{value:T.service_id,onValueChange:e=>V({...T,service_id:e}),children:[(0,s.jsx)(m.bq,{className:"h-8 text-sm",children:(0,s.jsx)(m.yv,{placeholder:"اختر الخدمة"})}),(0,s.jsxs)(m.gC,{className:"max-h-60",children:[(0,s.jsx)(m.eb,{value:"0",children:"بدون خدمة"}),$.map(e=>(0,s.jsx)(m.eb,{value:e.id.toString(),children:(0,s.jsx)("span",{className:"font-medium text-black",children:e.name})},e.id))]})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"reference_number",className:"text-xs font-medium text-blue-700 mb-1 block",children:"\uD83D\uDCC4 رقم المرجع"}),(0,s.jsx)(c.p,{id:"reference_number",value:T.reference_number,onChange:e=>V({...T,reference_number:e.target.value}),placeholder:"رقم المرجع (اختياري)",className:"h-8 text-sm"})]})]}),T.beneficiary_name&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsxs)("div",{className:"text-blue-600",children:["client"===T.beneficiary_type&&"\uD83D\uDC64","employee"===T.beneficiary_type&&"\uD83D\uDC68‍\uD83D\uDCBC","supplier"===T.beneficiary_type&&"\uD83C\uDFEA","external"===T.beneficiary_type&&"\uD83C\uDF10"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-blue-600 font-medium",children:"الدافع: "}),(0,s.jsx)("span",{className:"font-semibold text-blue-800",children:T.beneficiary_name}),(0,s.jsxs)("span",{className:"text-xs text-blue-500 mr-2",children:["(","client"===T.beneficiary_type?"عميل":"employee"===T.beneficiary_type?"موظف":"supplier"===T.beneficiary_type?"مورد":"خارجي",")"]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-black",children:"\uD83C\uDFE6 الحساب المدين (المقبوض إليه)"}),(0,s.jsxs)("select",{value:T.debit_account_id,onChange:e=>V({...T,debit_account_id:e.target.value}),className:"w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"اختر الحساب المدين..."}),a.filter(e=>"أصول"===e.account_type&&e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")"]},e.id))]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-black",children:"\uD83D\uDCCA الحساب الدائن (الدافع)"}),(0,s.jsxs)("select",{value:T.credit_account_id,onChange:e=>{V({...T,credit_account_id:e.target.value}),G(e.target.value)},className:"w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"اختر الحساب الدائن (الدافع)..."}),a.filter(e=>e.allow_transactions).map(e=>(0,s.jsxs)("option",{value:e.id.toString(),style:{color:"black"},children:[e.account_name," (",e.account_code,")",e.is_linked_record&&e.original_table&&` - ${"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":""}`]},e.id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.J,{htmlFor:"description",className:"text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2",children:"\uD83D\uDCDD البيان"}),(0,s.jsx)(d.T,{id:"description",value:T.description,onChange:e=>V({...T,description:e.target.value}),placeholder:"وصف تفصيلي للسند...",required:!0,rows:3,className:"bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors"})]}),(0,s.jsxs)(p.Es,{className:"flex justify-between pt-6 border-t",children:[(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>J(!1),children:"إلغاء"}),(0,s.jsx)(l.$,{type:"submit",className:"bg-green-600 hover:bg-green-700",children:z?"تحديث السند":"حفظ السند"})]})]})})]})})]})})})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Textarea"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>u,L3:()=>x,c7:()=>p,lG:()=>c,rr:()=>h});var s=a(60687),r=a(43210),i=a(37908),n=a(11860),l=a(4780);let c=i.bL;i.l9;let o=i.ZL;i.bm;let d=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.hJ,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));d.displayName=i.hJ.displayName;let m=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(o,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(i.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.UC.displayName;let p=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let u=({className:e,...t})=>(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});u.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.hE,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=i.hE.displayName;let h=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.VY,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=i.VY.displayName},71444:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},73622:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let o={children:["",{children:["accounting",{children:["receipt-vouchers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,29253)),"D:\\mohaminew\\src\\app\\accounting\\receipt-vouchers\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\mohaminew\\src\\app\\accounting\\receipt-vouchers\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/accounting/receipt-vouchers/page",pathname:"/accounting/receipt-vouchers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("label",{ref:a,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName="Label"},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,8409,4036,8106,2614,7932],()=>a(73622));module.exports=s})();