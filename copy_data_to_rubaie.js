// نسخ البيانات من legal_system إلى rubaie
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function copyDataToRubaie() {
  console.log('🔄 بدء نسخ البيانات من legal_system إلى rubaie...\n');

  // الاتصال بقاعدة البيانات الأصلية
  const sourcePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'legal_system',
    password: process.env.DB_PASSWORD || '123456',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات الجديدة
  const targetPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: process.env.DB_PASSWORD || '123456',
    port: 5432,
  });

  try {
    // قائمة الجداول المراد نسخها
    const tables = [
      'companies',
      'services', 
      'conversations',
      'messages',
      'legal_library',
      'announcements',
      'users',
      'ai_settings',
      'footer_links'
    ];

    console.log(`📋 سيتم نسخ ${tables.length} جداول...\n`);

    for (const table of tables) {
      console.log(`📄 نسخ جدول: ${table}`);
      
      try {
        // جلب البيانات من الجدول الأصلي
        const sourceResult = await sourcePool.query(`SELECT * FROM ${table}`);
        const rows = sourceResult.rows;
        
        console.log(`   📊 عدد السجلات: ${rows.length}`);
        
        if (rows.length === 0) {
          console.log(`   ⚠️  الجدول فارغ، تم تخطيه\n`);
          continue;
        }

        // حذف البيانات الموجودة في الجدول الهدف (إن وجدت)
        await targetPool.query(`DELETE FROM ${table}`);
        
        // إعادة تعيين sequence للـ id
        try {
          await targetPool.query(`ALTER SEQUENCE ${table}_id_seq RESTART WITH 1`);
        } catch (seqError) {
          // بعض الجداول قد لا تحتوي على sequence
        }

        // نسخ البيانات سجل بسجل
        for (const row of rows) {
          const columns = Object.keys(row);
          const values = Object.values(row);
          
          // إنشاء placeholders للقيم
          const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
          const columnsStr = columns.join(', ');
          
          const insertQuery = `
            INSERT INTO ${table} (${columnsStr}) 
            VALUES (${placeholders})
          `;
          
          await targetPool.query(insertQuery, values);
        }
        
        console.log(`   ✅ تم نسخ ${rows.length} سجل بنجاح\n`);
        
      } catch (tableError) {
        console.log(`   ❌ خطأ في نسخ الجدول ${table}:`, tableError.message);
        console.log(`   🔄 محاولة إنشاء الجدول أولاً...\n`);
        
        // محاولة إنشاء الجدول إذا لم يكن موجوداً
        try {
          await createTableIfNotExists(targetPool, table);
          console.log(`   ✅ تم إنشاء الجدول ${table}\n`);
          
          // إعادة المحاولة
          const sourceResult = await sourcePool.query(`SELECT * FROM ${table}`);
          const rows = sourceResult.rows;
          
          for (const row of rows) {
            const columns = Object.keys(row);
            const values = Object.values(row);
            const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
            const columnsStr = columns.join(', ');
            
            const insertQuery = `
              INSERT INTO ${table} (${columnsStr}) 
              VALUES (${placeholders})
            `;
            
            await targetPool.query(insertQuery, values);
          }
          
          console.log(`   ✅ تم نسخ ${rows.length} سجل بنجاح بعد إنشاء الجدول\n`);
          
        } catch (createError) {
          console.log(`   ❌ فشل في إنشاء الجدول ${table}:`, createError.message, '\n');
        }
      }
    }

    console.log('🎉 تم الانتهاء من نسخ جميع البيانات بنجاح!');
    console.log('📊 يمكنك الآن استخدام قاعدة البيانات rubaie');

  } catch (error) {
    console.error('❌ خطأ عام في نسخ البيانات:', error);
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }
}

// دالة لإنشاء الجداول إذا لم تكن موجودة
async function createTableIfNotExists(pool, tableName) {
  const createQueries = {
    companies: `
      CREATE TABLE IF NOT EXISTS companies (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        legal_name VARCHAR(255),
        description TEXT,
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        phone VARCHAR(50),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url VARCHAR(500),
        logo_image_url VARCHAR(500),
        established_date DATE,
        registration_number VARCHAR(100),
        legal_form VARCHAR(100),
        capital DECIMAL(15,2),
        tax_number VARCHAR(100),
        is_active BOOLEAN DEFAULT true,
        working_hours TEXT,
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `,
    services: `
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        icon_name VARCHAR(100),
        icon_color VARCHAR(50),
        slug VARCHAR(255) UNIQUE,
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `,
    // يمكن إضافة المزيد من الجداول هنا حسب الحاجة
  };

  if (createQueries[tableName]) {
    await pool.query(createQueries[tableName]);
  }
}

// تشغيل النسخ
copyDataToRubaie().catch(console.error);
