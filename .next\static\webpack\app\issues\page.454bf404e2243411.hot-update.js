"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/issues/page",{

/***/ "(app-pages-browser)/./src/app/issues/page.tsx":
/*!*********************************!*\
  !*** ./src/app/issues/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IssuesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_client_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/client-select */ \"(app-pages-browser)/./src/components/ui/client-select.tsx\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Save,Scale,Search,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction IssuesPage() {\n    var _currencies_find;\n    _s();\n    const [issues, setIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courts, setCourts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currencies, setCurrencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [statuses, setStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contractMethods, setContractMethods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [issueTypes, setIssueTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingIssue, setEditingIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        case_number: '',\n        title: '',\n        description: '',\n        client_id: '',\n        client_name: '',\n        client_phone: '',\n        court_id: '',\n        court_name: '',\n        issue_type: '',\n        status: 'new',\n        case_amount: '',\n        currency_id: '1',\n        notes: '',\n        contract_method: 'بالجلسة',\n        contract_date: new Date().toISOString().split('T')[0]\n    });\n    // جلب البيانات من APIs مع لوق تفصيلي\n    const fetchIssues = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب القضايا...');\n        try {\n            const response = await fetch('/api/issues');\n            console.log('📡 Issues Page: استجابة API القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setIssues(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة القضايا، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب القضايا:', error);\n        }\n    };\n    const fetchCourts = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب المحاكم...');\n        try {\n            const response = await fetch('/api/courts');\n            console.log('📡 Issues Page: استجابة API المحاكم:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API المحاكم:', result);\n            if (result.success) {\n                var _result_data;\n                setCourts(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة المحاكم، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n                console.log('🏛️ Issues Page: قائمة المحاكم:', result.data);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب المحاكم:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب المحاكم:', error);\n        }\n    };\n    const fetchCurrencies = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب العملات...');\n        try {\n            const response = await fetch('/api/currencies');\n            console.log('📡 Issues Page: استجابة API العملات:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API العملات:', result);\n            if (result.success) {\n                var _result_data;\n                setCurrencies(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة العملات، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب العملات:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب العملات:', error);\n        }\n    };\n    const fetchStatuses = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب حالات القضايا...');\n        try {\n            const response = await fetch('/api/issue-statuses');\n            console.log('📡 Issues Page: استجابة API حالات القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API حالات القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setStatuses(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة الحالات، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب حالات القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب حالات القضايا:', error);\n        }\n    };\n    const fetchContractMethods = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب طرق التعاقد...');\n        try {\n            const response = await fetch('/api/contract-methods');\n            console.log('📡 Issues Page: استجابة API طرق التعاقد:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API طرق التعاقد:', result);\n            if (result.success) {\n                var _result_data;\n                setContractMethods(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة طرق التعاقد، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب طرق التعاقد:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب طرق التعاقد:', error);\n        }\n    };\n    const fetchIssueTypes = async ()=>{\n        console.log('🔄 Issues Page: بدء جلب أنواع القضايا...');\n        try {\n            const response = await fetch('/api/issue-types');\n            console.log('📡 Issues Page: استجابة API أنواع القضايا:', response.status, response.statusText);\n            const result = await response.json();\n            console.log('📊 Issues Page: نتيجة API أنواع القضايا:', result);\n            if (result.success) {\n                var _result_data;\n                setIssueTypes(result.data || []);\n                console.log('✅ Issues Page: تم تحديث حالة أنواع القضايا، العدد:', ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.length) || 0);\n            } else {\n                console.error('❌ Issues Page: فشل في جلب أنواع القضايا:', result.error);\n            }\n        } catch (error) {\n            console.error('💥 Issues Page: خطأ في جلب أنواع القضايا:', error);\n        }\n    };\n    // حساب المبلغ بالريال اليمني\n    const calculateAmountYer = (amount, currencyId)=>{\n        const numAmount = parseFloat(amount) || 0;\n        const currency = currencies.find((c)=>c.id.toString() === currencyId);\n        if (!currency) return numAmount;\n        if (currency.is_base_currency) return numAmount;\n        return numAmount * currency.exchange_rate;\n    };\n    // معالجة تغيير العميل مع لوق تفصيلي\n    const handleClientChange = (clientId, clientData)=>{\n        console.log('👤 Issues Page: تغيير العميل...');\n        console.log('📋 Issues Page: معرف العميل:', clientId);\n        console.log('📊 Issues Page: بيانات العميل:', clientData);\n        setFormData({\n            ...formData,\n            client_id: clientId,\n            client_name: (clientData === null || clientData === void 0 ? void 0 : clientData.name) || '',\n            client_phone: (clientData === null || clientData === void 0 ? void 0 : clientData.phone) || ''\n        });\n        console.log('✅ Issues Page: تم تحديث بيانات النموذج للعميل');\n    };\n    // إضافة قضية جديدة مع لوق تفصيلي\n    const handleAddNew = ()=>{\n        console.log('➕ Issues Page: فتح نافذة إضافة قضية جديدة...');\n        console.log('🏛️ Issues Page: عدد المحاكم المتاحة:', courts.length);\n        console.log('📋 Issues Page: قائمة المحاكم:', courts);\n        console.log('📊 Issues Page: عدد العملات المتاحة:', currencies.length);\n        console.log('📈 Issues Page: عدد حالات القضايا المتاحة:', statuses.length);\n        console.log('📝 Issues Page: عدد طرق التعاقد المتاحة:', contractMethods.length);\n        console.log('⚖️ Issues Page: عدد أنواع القضايا المتاحة:', issueTypes.length);\n        setEditingIssue(null);\n        setFormData({\n            case_number: '',\n            title: '',\n            description: '',\n            client_id: '',\n            client_name: '',\n            client_phone: '',\n            court_id: '',\n            court_name: '',\n            issue_type: '',\n            status: 'new',\n            case_amount: '',\n            currency_id: '1',\n            notes: '',\n            contract_method: 'بالجلسة',\n            contract_date: new Date().toISOString().split('T')[0]\n        });\n        setModalType('add');\n        setIsModalOpen(true);\n        console.log('✅ Issues Page: تم فتح النافذة المنبثقة لإضافة قضية جديدة');\n    };\n    // تعديل قضية\n    const handleEdit = (issue)=>{\n        var _issue_court_id, _issue_case_amount, _issue_currency_id;\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number,\n            title: issue.title,\n            description: issue.description,\n            client_id: issue.client_id.toString(),\n            client_name: issue.client_name,\n            client_phone: issue.client_phone,\n            court_id: ((_issue_court_id = issue.court_id) === null || _issue_court_id === void 0 ? void 0 : _issue_court_id.toString()) || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status,\n            case_amount: ((_issue_case_amount = issue.case_amount) === null || _issue_case_amount === void 0 ? void 0 : _issue_case_amount.toString()) || '',\n            currency_id: ((_issue_currency_id = issue.currency_id) === null || _issue_currency_id === void 0 ? void 0 : _issue_currency_id.toString()) || '1',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date || new Date().toISOString().split('T')[0]\n        });\n        setModalType('edit');\n        setIsModalOpen(true);\n    };\n    // عرض قضية\n    const handleView = (issue)=>{\n        var _issue_court_id, _issue_case_amount, _issue_currency_id;\n        setEditingIssue(issue);\n        setFormData({\n            case_number: issue.case_number,\n            title: issue.title,\n            description: issue.description,\n            client_id: issue.client_id.toString(),\n            client_name: issue.client_name,\n            client_phone: issue.client_phone,\n            court_id: ((_issue_court_id = issue.court_id) === null || _issue_court_id === void 0 ? void 0 : _issue_court_id.toString()) || '',\n            court_name: issue.court_name || '',\n            issue_type: issue.issue_type || '',\n            status: issue.status,\n            case_amount: ((_issue_case_amount = issue.case_amount) === null || _issue_case_amount === void 0 ? void 0 : _issue_case_amount.toString()) || '',\n            currency_id: ((_issue_currency_id = issue.currency_id) === null || _issue_currency_id === void 0 ? void 0 : _issue_currency_id.toString()) || '1',\n            notes: issue.notes || '',\n            contract_method: issue.contract_method || 'بالجلسة',\n            contract_date: issue.contract_date || new Date().toISOString().split('T')[0]\n        });\n        setModalType('view');\n        setIsModalOpen(true);\n    };\n    // حفظ القضية\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (modalType === 'add') {\n                const response = await fetch('/api/issues', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        ...formData,\n                        created_by: 1\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم إضافة القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في إضافة القضية');\n                    return;\n                }\n            } else if (modalType === 'edit' && editingIssue) {\n                const response = await fetch(\"/api/issues/\".concat(editingIssue.id), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(formData)\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم تحديث القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في تحديث القضية');\n                    return;\n                }\n            }\n            setIsModalOpen(false);\n            setEditingIssue(null);\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            alert('حدث خطأ في الاتصال');\n        }\n    };\n    // حذف قضية\n    const handleDelete = async (id)=>{\n        if (confirm('هل أنت متأكد من حذف هذه القضية؟')) {\n            try {\n                const response = await fetch(\"/api/issues/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (result.success) {\n                    alert('تم حذف القضية بنجاح');\n                    fetchIssues();\n                } else {\n                    alert(result.error || 'فشل في حذف القضية');\n                }\n            } catch (error) {\n                console.error('Error deleting issue:', error);\n                alert('حدث خطأ في الاتصال');\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IssuesPage.useEffect\": ()=>{\n            const loadData = {\n                \"IssuesPage.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        fetchIssues(),\n                        fetchCourts(),\n                        fetchCurrencies(),\n                        fetchStatuses(),\n                        fetchContractMethods(),\n                        fetchIssueTypes()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"IssuesPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"IssuesPage.useEffect\"], []);\n    // تصفية القضايا\n    const filteredIssues = issues.filter((issue)=>{\n        const matchesSearch = issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) || issue.title.toLowerCase().includes(searchTerm.toLowerCase()) || issue.client_name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    // إحصائيات\n    const stats = {\n        total: issues.length,\n        new: issues.filter((i)=>i.status === 'new').length,\n        pending: issues.filter((i)=>i.status === 'pending').length,\n        in_progress: issues.filter((i)=>i.status === 'in_progress').length,\n        completed: issues.filter((i)=>i.status === 'completed').length\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل البيانات...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 mr-3 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إدارة القضايا\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mt-1\",\n                                    children: \"إدارة ومتابعة جميع القضايا القانونية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleAddNew,\n                            className: \"bg-emerald-600 hover:bg-emerald-700 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة قضية جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: stats.total\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"إجمالي القضايا\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: stats.new\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"جديدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: stats.pending\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"معلقة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: stats.in_progress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"قيد المعالجة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-emerald-600\",\n                                            children: stats.completed\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"مكتملة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"البحث برقم القضية، العنوان، أو اسم الموكل...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"جميع الحالات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this),\n                                            statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: status.value,\n                                                    children: status.label\n                                                }, status.value, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: [\n                                    \"قائمة القضايا (\",\n                                    filteredIssues.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: filteredIssues.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"لا توجد قضايا\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"رقم القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"العنوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right p-3 font-semibold\",\n                                                        children: \"الموكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"المحكمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"المبلغ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center p-3 font-semibold\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: filteredIssues.map((issue)=>{\n                                                var _statuses_find;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 font-medium text-blue-600\",\n                                                            children: issue.case_number\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: issue.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: issue.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: issue.client_phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: issue.court_name || '-'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(issue.status === 'new' ? 'bg-green-100 text-green-800' : issue.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : issue.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : issue.status === 'completed' ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'),\n                                                                children: ((_statuses_find = statuses.find((s)=>s.value === issue.status)) === null || _statuses_find === void 0 ? void 0 : _statuses_find.label) || issue.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            (issue.case_amount || 0).toLocaleString(),\n                                                                            \" \",\n                                                                            issue.currency_symbol\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: [\n                                                                            (issue.amount_yer || 0).toLocaleString(),\n                                                                            \" ر.ي\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center space-x-2 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleView(issue),\n                                                                        className: \"text-blue-600 hover:text-blue-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleEdit(issue),\n                                                                        className: \"text-green-600 hover:text-green-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleDelete(issue.id),\n                                                                        className: \"text-red-600 hover:text-red-800\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, issue.id, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 9\n                }, this),\n                isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: [\n                                            modalType === 'add' && '📋 إضافة قضية جديدة',\n                                            modalType === 'edit' && '✏️ تعديل القضية',\n                                            modalType === 'view' && '👁️ عرض تفاصيل القضية'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsModalOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"p-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-10 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"case_number\",\n                                                        className: \"text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCCB رقم القضية *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"case_number\",\n                                                        value: formData.case_number,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                case_number: e.target.value\n                                                            }),\n                                                        required: modalType !== 'view',\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل رقم القضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-7\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD عنوان القضية *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                title: e.target.value\n                                                            }),\n                                                        required: modalType !== 'view',\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل عنوان القضية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                    children: \"\\uD83D\\uDCC4 وصف القضية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    readOnly: modalType === 'view',\n                                                    className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors\",\n                                                    placeholder: \"أدخل وصف مختصر للقضية...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDC64 الموكل *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_select__WEBPACK_IMPORTED_MODULE_6__.ClientSelect, {\n                                                            value: formData.client_id,\n                                                            onChange: handleClientChange,\n                                                            label: \"\",\n                                                            placeholder: \"اختر الموكل...\",\n                                                            required: true,\n                                                            disabled: modalType === 'view'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"court_id\",\n                                                        className: \"text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83C\\uDFDB️ المحكمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"court_id\",\n                                                        value: formData.court_id || \"\",\n                                                        onChange: (e)=>{\n                                                            const selectedCourt = courts.find((c)=>c.id.toString() === e.target.value);\n                                                            setFormData({\n                                                                ...formData,\n                                                                court_id: e.target.value,\n                                                                court_name: (selectedCourt === null || selectedCourt === void 0 ? void 0 : selectedCourt.name) || ''\n                                                            });\n                                                        },\n                                                        className: \"w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر المحكمة...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            courts.map((court)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: court.id,\n                                                                    children: [\n                                                                        court.name,\n                                                                        \" - \",\n                                                                        court.governorate_name\n                                                                    ]\n                                                                }, court.id, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"status\",\n                                                        className: \"text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCCA حالة القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"status\",\n                                                        value: formData.status,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                status: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: status.value,\n                                                                children: status.label\n                                                            }, status.value, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"issue_type\",\n                                                        className: \"text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"⚖️ نوع القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"issue_type\",\n                                                        value: formData.issue_type || \"\",\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                issue_type: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر نوع القضية...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            issueTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: type.name,\n                                                                    children: type.name\n                                                                }, type.id, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"contract_method\",\n                                                        className: \"text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD طريقة التعاقد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"contract_method\",\n                                                        value: formData.contract_method,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                contract_method: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: contractMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: method.value,\n                                                                children: method.label\n                                                            }, method.value, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"contract_date\",\n                                                        className: \"text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCC5 تاريخ التعاقد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"contract_date\",\n                                                        type: \"date\",\n                                                        value: formData.contract_date,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                contract_date: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"h-10 text-black bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency_id\",\n                                                        className: \"text-sm font-semibold text-yellow-700 bg-yellow-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB1 العملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"currency_id\",\n                                                        value: formData.currency_id || '1',\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                currency_id: e.target.value\n                                                            }),\n                                                        className: \"w-full h-10 px-3 py-2 bg-yellow-50 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:bg-white transition-colors text-sm text-black\",\n                                                        disabled: modalType === 'view',\n                                                        children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: currency.id,\n                                                                children: [\n                                                                    currency.currency_name,\n                                                                    \" (\",\n                                                                    currency.symbol,\n                                                                    \")\",\n                                                                    currency.is_base_currency && ' - الأساسية'\n                                                                ]\n                                                            }, currency.id, true, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"case_amount\",\n                                                        className: \"text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB0 قيمة القضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"case_amount\",\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                min: \"0\",\n                                                                value: formData.case_amount || '',\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        case_amount: e.target.value\n                                                                    }),\n                                                                readOnly: modalType === 'view',\n                                                                className: \"h-10 pr-12 text-black bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-sm\",\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: ((_currencies_find = currencies.find((c)=>c.id.toString() === (formData.currency_id || '1'))) === null || _currencies_find === void 0 ? void 0 : _currencies_find.symbol) || 'ر.ي'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"amount_yer\",\n                                                        className: \"text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCB5 بالريال اليمني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"amount_yer\",\n                                                                type: \"text\",\n                                                                value: Math.round(calculateAmountYer(formData.case_amount || '0', formData.currency_id || '1')).toLocaleString('en-US'),\n                                                                readOnly: true,\n                                                                className: \"h-10 pr-12 bg-gray-50 text-black text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"ر.ي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        className: \"text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2\",\n                                                        children: \"\\uD83D\\uDCDD ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"notes\",\n                                                        value: formData.notes,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                notes: e.target.value\n                                                            }),\n                                                        readOnly: modalType === 'view',\n                                                        className: \"w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors\",\n                                                        placeholder: \"أدخل أي ملاحظات إضافية...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n                                        children: [\n                                            modalType !== 'view' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Save_Scale_Search_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsModalOpen(false),\n                                                className: \"\".concat(modalType === 'view' ? 'w-full' : 'flex-1', \" h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50\"),\n                                                children: modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\issues\\\\page.tsx\",\n        lineNumber: 444,\n        columnNumber: 5\n    }, this);\n}\n_s(IssuesPage, \"0XCYreaDfcQ0Se8iFJOcd4l34Tc=\");\n_c = IssuesPage;\nvar _c;\n$RefreshReg$(_c, \"IssuesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/issues/page.tsx\n"));

/***/ })

});