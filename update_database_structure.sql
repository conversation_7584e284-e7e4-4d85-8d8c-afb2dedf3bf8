-- سكريبت تحديث هيكل قاعدة البيانات rubaie من mohammi
-- تاريخ التحديث: 2025-01-29

-- ===================================
-- 1. إن<PERSON>اء الجداول الأساسية
-- ===================================

-- جدول المحافظات
CREATE TABLE IF NOT EXISTS governorates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المحاكم
CREATE TABLE IF NOT EXISTS courts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(100),
    governorate_id INTEGER REFERENCES governorates(id),
    governorate_name VARCHAR(100),
    location VARCHAR(200),
    address TEXT,
    phone VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    id_number VARCHAR(50),
    address TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملات
CREATE TABLE IF NOT EXISTS currencies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    is_default BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أنواع القضايا
CREATE TABLE IF NOT EXISTS issue_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول حالات القضايا
CREATE TABLE IF NOT EXISTS issue_statuses (
    id SERIAL PRIMARY KEY,
    value VARCHAR(50) NOT NULL UNIQUE,
    label VARCHAR(100) NOT NULL,
    color VARCHAR(20) DEFAULT 'gray',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طرق التعاقد
CREATE TABLE IF NOT EXISTS contract_methods (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول القضايا الرئيسي
CREATE TABLE IF NOT EXISTS issues (
    id SERIAL PRIMARY KEY,
    case_number VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    client_id INTEGER REFERENCES clients(id),
    client_name VARCHAR(200),
    client_phone VARCHAR(20),
    issue_type_id INTEGER REFERENCES issue_types(id),
    issue_type VARCHAR(100),
    court_id INTEGER REFERENCES courts(id),
    court_name VARCHAR(200),
    status VARCHAR(50) DEFAULT 'new',
    case_amount DECIMAL(15,2) DEFAULT 0,
    currency_id INTEGER REFERENCES currencies(id) DEFAULT 1,
    currency_symbol VARCHAR(10) DEFAULT 'ر.ي',
    amount_yer DECIMAL(15,2) DEFAULT 0,
    notes TEXT,
    contract_method VARCHAR(100) DEFAULT 'بالجلسة',
    contract_date DATE,
    start_date DATE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER DEFAULT 1
);

-- ===================================
-- 2. إدراج البيانات الأساسية
-- ===================================

-- المحافظات
INSERT INTO governorates (name) VALUES 
('صنعاء'), ('عدن'), ('تعز'), ('الحديدة'), ('إب')
ON CONFLICT (name) DO NOTHING;

-- المحاكم
INSERT INTO courts (name, type, governorate_id, governorate_name, address) VALUES 
('المحكمة العليا', 'محكمة عليا', 1, 'صنعاء', 'شارع الزبيري، صنعاء'),
('محكمة استئناف صنعاء', 'محكمة استئناف', 1, 'صنعاء', 'شارع الستين، صنعاء'),
('المحكمة التجارية بصنعاء', 'محكمة تجارية', 1, 'صنعاء', 'شارع الحصبة، صنعاء'),
('محكمة الأحوال الشخصية', 'محكمة أحوال شخصية', 1, 'صنعاء', 'شارع الثورة، صنعاء'),
('المحكمة الجنائية', 'محكمة جنائية', 1, 'صنعاء', 'شارع الجمهورية، صنعاء')
ON CONFLICT (name) DO NOTHING;

-- العملات
INSERT INTO currencies (name, symbol, exchange_rate, is_default) VALUES 
('ريال يمني', 'ر.ي', 1.0000, true),
('دولار أمريكي', '$', 1500.0000, false),
('ريال سعودي', 'ر.س', 400.0000, false),
('يورو', '€', 1600.0000, false),
('درهم إماراتي', 'د.إ', 408.0000, false)
ON CONFLICT (name) DO NOTHING;

-- أنواع القضايا
INSERT INTO issue_types (name, description) VALUES 
('قضية مدنية', 'القضايا المدنية والتجارية'),
('قضية جنائية', 'القضايا الجنائية والجزائية'),
('قضية أحوال شخصية', 'قضايا الزواج والطلاق والميراث'),
('قضية إدارية', 'القضايا الإدارية والحكومية'),
('قضية عمالية', 'قضايا العمل والعمال')
ON CONFLICT (name) DO NOTHING;

-- حالات القضايا
INSERT INTO issue_statuses (value, label, color) VALUES 
('new', 'جديدة', 'blue'),
('pending', 'قيد المراجعة', 'yellow'),
('in_progress', 'قيد التنفيذ', 'orange'),
('completed', 'مكتملة', 'green'),
('cancelled', 'ملغية', 'red')
ON CONFLICT (value) DO NOTHING;

-- طرق التعاقد
INSERT INTO contract_methods (name, description) VALUES 
('بالجلسة', 'أتعاب محاماة بالجلسة'),
('مقطوعية', 'أتعاب محاماة مقطوعية')
ON CONFLICT (name) DO NOTHING;

-- ===================================
-- 3. إنشاء الفهارس للأداء
-- ===================================

CREATE INDEX IF NOT EXISTS idx_issues_case_number ON issues(case_number);
CREATE INDEX IF NOT EXISTS idx_issues_client_id ON issues(client_id);
CREATE INDEX IF NOT EXISTS idx_issues_court_id ON issues(court_id);
CREATE INDEX IF NOT EXISTS idx_issues_status ON issues(status);
CREATE INDEX IF NOT EXISTS idx_issues_created_date ON issues(created_date);
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients(name);
CREATE INDEX IF NOT EXISTS idx_courts_governorate_id ON courts(governorate_id);

-- ===================================
-- 4. إنشاء Triggers لتحديث البيانات تلقائياً
-- ===================================

-- Trigger لتحديث client_name و client_phone تلقائياً
CREATE OR REPLACE FUNCTION update_issue_client_info()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.client_id IS NOT NULL THEN
        SELECT name, phone INTO NEW.client_name, NEW.client_phone
        FROM clients WHERE id = NEW.client_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_issue_client_info ON issues;
CREATE TRIGGER trigger_update_issue_client_info
    BEFORE INSERT OR UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION update_issue_client_info();

-- Trigger لتحديث court_name تلقائياً
CREATE OR REPLACE FUNCTION update_issue_court_info()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.court_id IS NOT NULL THEN
        SELECT name INTO NEW.court_name
        FROM courts WHERE id = NEW.court_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_issue_court_info ON issues;
CREATE TRIGGER trigger_update_issue_court_info
    BEFORE INSERT OR UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION update_issue_court_info();

-- Trigger لتحديث issue_type تلقائياً
CREATE OR REPLACE FUNCTION update_issue_type_info()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.issue_type_id IS NOT NULL THEN
        SELECT name INTO NEW.issue_type
        FROM issue_types WHERE id = NEW.issue_type_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_issue_type_info ON issues;
CREATE TRIGGER trigger_update_issue_type_info
    BEFORE INSERT OR UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION update_issue_type_info();

-- Trigger لحساب المبلغ بالريال اليمني تلقائياً
CREATE OR REPLACE FUNCTION calculate_amount_yer()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.case_amount IS NOT NULL AND NEW.currency_id IS NOT NULL THEN
        SELECT 
            NEW.case_amount * c.exchange_rate,
            c.symbol
        INTO NEW.amount_yer, NEW.currency_symbol
        FROM currencies c WHERE c.id = NEW.currency_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_calculate_amount_yer ON issues;
CREATE TRIGGER trigger_calculate_amount_yer
    BEFORE INSERT OR UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION calculate_amount_yer();

-- Trigger لتحديث updated_date تلقائياً
CREATE OR REPLACE FUNCTION update_updated_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_updated_date ON issues;
CREATE TRIGGER trigger_update_updated_date
    BEFORE UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_date();

-- ===================================
-- تم إنشاء هيكل قاعدة البيانات بنجاح
-- ===================================
