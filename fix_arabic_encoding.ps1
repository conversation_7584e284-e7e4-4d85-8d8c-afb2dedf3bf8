# سكريپت إصلاح ترميز اللغة العربية
# Fix Arabic Encoding Script

Write-Host "🔧 إصلاح ترميز اللغة العربية" -ForegroundColor Green

# تعيين كلمة المرور
$env:PGPASSWORD = "yemen123"

Write-Host "`n🔍 التحقق من ترميز قواعد البيانات..." -ForegroundColor Yellow

# التحقق من ترميز قاعدة البيانات mohammi
$mohammiEncoding = psql -U postgres -d mohammi -t -c "SHOW server_encoding;"
Write-Host "📋 ترميز قاعدة البيانات mohammi: $($mohammiEncoding.Trim())" -ForegroundColor Cyan

# التحقق من ترميز قاعدة البيانات rubaie
$rubaieEncoding = psql -U postgres -d rubaie -t -c "SHOW server_encoding;"
Write-Host "📋 ترميز قاعدة البيانات rubaie: $($rubaieEncoding.Trim())" -ForegroundColor Cyan

# إصلاح ترميز قاعدة البيانات rubaie
Write-Host "`n🔧 إصلاح ترميز قاعدة البيانات rubaie..." -ForegroundColor Yellow

# قطع الاتصالات
psql -U postgres -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'rubaie' AND pid <> pg_backend_pid();" 2>$null

# حذف وإعادة إنشاء قاعدة البيانات بترميز UTF8
Write-Host "🗑️ حذف قاعدة البيانات rubaie..." -ForegroundColor Yellow
psql -U postgres -c "DROP DATABASE IF EXISTS rubaie;" 2>$null

Write-Host "🏗️ إنشاء قاعدة البيانات rubaie بترميز UTF8..." -ForegroundColor Yellow
psql -U postgres -c "CREATE DATABASE rubaie WITH ENCODING 'UTF8' LC_COLLATE='C' LC_CTYPE='C' TEMPLATE=template0;"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في إنشاء قاعدة البيانات" -ForegroundColor Red
    exit 1
}

# نسخ البيانات مع الحفاظ على الترميز
Write-Host "`n📋 نسخ البيانات مع الحفاظ على الترميز..." -ForegroundColor Yellow
pg_dump -U postgres -d mohammi --encoding=UTF8 | psql -U postgres -d rubaie

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إصلاح الترميز بنجاح!" -ForegroundColor Green
    
    # التحقق من الترميز الجديد
    $newEncoding = psql -U postgres -d rubaie -t -c "SHOW server_encoding;"
    Write-Host "📋 الترميز الجديد: $($newEncoding.Trim())" -ForegroundColor Cyan
    
    # اختبار النص العربي
    Write-Host "`n🧪 اختبار النص العربي..." -ForegroundColor Yellow
    $arabicTest = psql -U postgres -d rubaie -t -c "SELECT 'مرحبا بك في نظام إدارة المحاماة' as test_arabic;"
    Write-Host "📝 نتيجة الاختبار: $($arabicTest.Trim())" -ForegroundColor White
    
} else {
    Write-Host "❌ فشل في إصلاح الترميز" -ForegroundColor Red
}
