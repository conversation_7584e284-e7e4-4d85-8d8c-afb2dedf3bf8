import { NextResponse } from 'next/server'
import { testConnection, initializeTables } from '@/lib/db'

export async function GET() {
  try {
    
    
    // اختبار الاتصال
    const connectionTest = await testConnection()
    if (!connectionTest) {
      return NextResponse.json({
        success: false,
        error: 'فشل في الاتصال بقاعدة البيانات'
      }, { status: 500 })
    }

    // إنشاء الجداول
    await initializeTables()

    return NextResponse.json({
      success: true,
      message: 'تم تهيئة قاعدة البيانات بنجاح',
      database: {
        host: 'localhost',
        port: 5432,
        database: 'mohammi',
        user: 'postgres'
      }
    })
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تهيئة قاعدة البيانات: ' + (error as Error).message
    }, { status: 500 })
  }
}
