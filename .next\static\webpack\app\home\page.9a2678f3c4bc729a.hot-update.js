"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/simple-chat-widget */ \"(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* harmony import */ var _styles_vibrant_theme_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../styles/vibrant-theme.css */ \"(app-pages-browser)/./src/styles/vibrant-theme.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst companyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyDataState, setCompanyDataState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // جلب بيانات الشركة من قاعدة البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchCompanyData = {\n                \"HomePage.useEffect.fetchCompanyData\": async ()=>{\n                    try {\n                        const response = await fetch('/api/company');\n                        const result = await response.json();\n                        if (result.success && result.data && result.data.length > 0) {\n                            // أخذ أول شركة من القائمة (الشركة الرئيسية)\n                            const company = result.data[0];\n                            setCompanyDataState({\n                                ...company,\n                                working_hours: company.working_hours || 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n                            });\n                        } else {\n                            // استخدام البيانات الافتراضية في حالة عدم وجود شركة\n                            setCompanyDataState(companyData);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب بيانات الشركة:', error);\n                        // استخدام البيانات الافتراضية في حالة الخطأ\n                        setCompanyDataState(companyData);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchCompanyData\"];\n            fetchCompanyData();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen vibrant-text-primary\",\n        style: {\n            background: 'linear-gradient(135deg, #f9fafb 0%, #ffffff 100%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16 vibrant-section-light\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 vibrant-section-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_simple_chat_widget__WEBPACK_IMPORTED_MODULE_4__.SimpleChatWidget, {\n                isOpen: isChatWidgetOpen,\n                onClose: ()=>setIsChatWidgetOpen(false),\n                onOpen: ()=>setIsChatWidgetOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"18l5owaLq2anWF6r71/MFBYG+h4=\");\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ })

});