// إصلاح نوع البيانات للعمود next_hearing
const { Pool } = require('pg');

async function fixNextHearingDatatype() {
  console.log('🔧 إصلاح نوع البيانات للعمود next_hearing...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص نوع البيانات الحالي
      console.log('\n   🔍 فحص نوع البيانات الحالي:');
      
      const currentType = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name = 'next_hearing'
      `);

      if (currentType.rows.length > 0) {
        const col = currentType.rows[0];
        console.log(`      العمود الحالي: ${col.column_name} - ${col.data_type} (nullable: ${col.is_nullable})`);
        
        if (col.data_type === 'date') {
          console.log('      ⚠️ نوع البيانات date - يحتاج تغيير إلى timestamp');
          
          // 2. تغيير نوع البيانات
          console.log('\n   🔄 تغيير نوع البيانات إلى timestamp:');
          
          await pool.query(`
            ALTER TABLE issues 
            ALTER COLUMN next_hearing TYPE TIMESTAMP USING next_hearing::timestamp
          `);
          console.log('      ✅ تم تغيير نوع البيانات إلى timestamp');
        } else {
          console.log('      ✅ نوع البيانات صحيح');
        }
      } else {
        console.log('      ❌ العمود next_hearing غير موجود');
      }

      // 3. تحديث دالة update_next_hearing
      console.log('\n   🔄 تحديث دالة update_next_hearing:');
      
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_next_hearing_for_issue(p_issue_id INTEGER)
        RETURNS VOID AS $$
        DECLARE
          next_date TIMESTAMP;
        BEGIN
          -- البحث عن أقرب جلسة مستقبلية
          SELECT hearing_date INTO next_date
          FROM hearings 
          WHERE issue_id = p_issue_id 
          AND hearing_date > CURRENT_TIMESTAMP
          AND status IN ('scheduled', 'postponed')
          ORDER BY hearing_date ASC
          LIMIT 1;
          
          -- تحديث العمود next_hearing
          UPDATE issues 
          SET next_hearing = next_date,
              updated_date = CURRENT_TIMESTAMP
          WHERE id = p_issue_id;
          
          -- رسالة تأكيد للتتبع
          RAISE NOTICE 'Updated next_hearing for issue % to %', p_issue_id, next_date;
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('      ✅ تم تحديث دالة update_next_hearing_for_issue');

      // 4. تحديث جميع القضايا
      console.log('\n   🔄 تحديث جميع القضايا:');
      
      const allIssues = await pool.query(`SELECT id, case_number FROM issues`);
      
      for (const issue of allIssues.rows) {
        try {
          await pool.query(`SELECT update_next_hearing_for_issue($1)`, [issue.id]);
          console.log(`      ✅ تم تحديث ${issue.case_number}`);
        } catch (error) {
          console.log(`      ❌ خطأ في تحديث ${issue.case_number}: ${error.message}`);
        }
      }

      // 5. اختبار التحديث
      console.log('\n   🧪 اختبار التحديث:');
      
      const testResults = await pool.query(`
        SELECT 
          i.case_number,
          i.next_hearing,
          h.hearing_date as actual_next_hearing
        FROM issues i
        LEFT JOIN (
          SELECT DISTINCT ON (issue_id) 
            issue_id, 
            hearing_date
          FROM hearings 
          WHERE hearing_date > CURRENT_TIMESTAMP
          AND status IN ('scheduled', 'postponed')
          ORDER BY issue_id, hearing_date ASC
        ) h ON i.id = h.issue_id
        ORDER BY i.case_number
      `);

      console.log('      📊 نتائج التحديث:');
      testResults.rows.forEach(row => {
        const match = (row.next_hearing && row.actual_next_hearing && 
                      new Date(row.next_hearing).getTime() === new Date(row.actual_next_hearing).getTime()) ||
                     (row.next_hearing === null && row.actual_next_hearing === null);
        
        console.log(`         ${match ? '✅' : '❌'} ${row.case_number}:`);
        console.log(`            - next_hearing: ${row.next_hearing || 'فارغ'}`);
        console.log(`            - actual_next: ${row.actual_next_hearing || 'فارغ'}`);
      });

      // 6. اختبار إدراج جلسة جديدة
      console.log('\n   ➕ اختبار إدراج جلسة جديدة:');
      
      if (allIssues.rows.length > 0) {
        const testIssue = allIssues.rows[0];
        console.log(`      🧪 اختبار للقضية ${testIssue.case_number}...`);
        
        // إدراج جلسة جديدة
        const newDate = new Date();
        newDate.setDate(newDate.getDate() + 2); // بعد يومين
        
        const insertResult = await pool.query(`
          INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, hearing_date
        `, [
          testIssue.id, 
          newDate.toISOString(), 
          'اختبار نهائي', 
          'scheduled', 
          'جلسة اختبار نهائي'
        ]);

        if (insertResult.rows.length > 0) {
          const hearingId = insertResult.rows[0].id;
          console.log(`      ✅ تم إدراج جلسة: ${insertResult.rows[0].hearing_date}`);
          
          // فحص التحديث
          const updatedIssue = await pool.query(`
            SELECT next_hearing FROM issues WHERE id = $1
          `, [testIssue.id]);
          
          if (updatedIssue.rows.length > 0) {
            console.log(`      📅 next_hearing بعد الإدراج: ${updatedIssue.rows[0].next_hearing}`);
            
            // التحقق من التطابق
            const insertedTime = new Date(insertResult.rows[0].hearing_date).getTime();
            const nextHearingTime = new Date(updatedIssue.rows[0].next_hearing).getTime();
            
            if (Math.abs(insertedTime - nextHearingTime) < 1000) {
              console.log(`      ✅ التحديث التلقائي يعمل بنجاح`);
            } else {
              console.log(`      ❌ التحديث التلقائي لا يعمل`);
              console.log(`         - المتوقع: ${new Date(insertedTime)}`);
              console.log(`         - الفعلي: ${new Date(nextHearingTime)}`);
            }
          }
          
          // حذف الجلسة التجريبية
          await pool.query(`DELETE FROM hearings WHERE id = $1`, [hearingId]);
          console.log(`      🗑️ تم حذف الجلسة التجريبية`);
        }
      }

      // 7. إحصائيات نهائية
      console.log('\n   📊 إحصائيات نهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM issues WHERE next_hearing IS NOT NULL) as issues_with_next_hearing,
          (SELECT COUNT(*) FROM hearings WHERE hearing_date > CURRENT_TIMESTAMP AND status IN ('scheduled', 'postponed')) as future_hearings
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - قضايا بها جلسة قادمة: ${stats.issues_with_next_hearing}`);
      console.log(`      - جلسات مستقبلية: ${stats.future_hearings}`);

      // 8. فحص نوع البيانات النهائي
      console.log('\n   🔍 فحص نوع البيانات النهائي:');
      
      const finalType = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name = 'next_hearing'
      `);

      if (finalType.rows.length > 0) {
        const col = finalType.rows[0];
        console.log(`      ✅ نوع البيانات النهائي: ${col.column_name} - ${col.data_type}`);
      }

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(40) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح نوع البيانات');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تغيير نوع البيانات من date إلى timestamp');
  console.log('2. ✅ تحديث دالة update_next_hearing_for_issue');
  console.log('3. ✅ تحديث جميع القضايا الحالية');
  console.log('4. ✅ اختبار التحديث التلقائي');
  console.log('5. ✅ العمود next_hearing يعمل بشكل صحيح');
}

// تشغيل الإصلاح
fixNextHearingDatatype().catch(console.error);
