/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/legal-library/paths/route";
exports.ids = ["app/api/legal-library/paths/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fpaths%2Froute&page=%2Fapi%2Flegal-library%2Fpaths%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fpaths%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fpaths%2Froute&page=%2Fapi%2Flegal-library%2Fpaths%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fpaths%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_legal_library_paths_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/legal-library/paths/route.ts */ \"(rsc)/./src/app/api/legal-library/paths/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_legal_library_paths_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_legal_library_paths_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/legal-library/paths/route\",\n        pathname: \"/api/legal-library/paths\",\n        filename: \"route\",\n        bundlePath: \"app/api/legal-library/paths/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\legal-library\\\\paths\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_legal_library_paths_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fpaths%2Froute&page=%2Fapi%2Flegal-library%2Fpaths%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fpaths%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/legal-library/paths/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/legal-library/paths/route.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع مسارات المكتبة القانونية\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const activeOnly = searchParams.get('active') === 'true';\n        let queryStr = 'SELECT * FROM legal_library_paths';\n        const queryParams = [];\n        if (activeOnly) {\n            queryStr += ' WHERE is_active = true';\n        }\n        queryStr += ' ORDER BY is_default DESC, path_name ASC';\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(queryStr, queryParams);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('خطأ في جلب مسارات المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب المسارات'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مسار جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { path_name, path_value, description, is_default, scan_enabled } = body;\n        if (!path_name || !path_value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المسار والقيمة مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم تكرار المسار\n        const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM legal_library_paths WHERE path_value = $1', [\n            path_value\n        ]);\n        if (existingResult.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'هذا المسار موجود بالفعل'\n            }, {\n                status: 409\n            });\n        }\n        // التحقق من الحد الأقصى للمسارات\n        const countResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT COUNT(*) as count FROM legal_library_paths');\n        const currentCount = parseInt(countResult.rows[0].count);\n        const maxPathsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT setting_value FROM system_settings WHERE setting_key = $1', [\n            'legal_library_max_paths'\n        ]);\n        const maxPaths = maxPathsResult.rows.length > 0 ? parseInt(maxPathsResult.rows[0].setting_value) : 10;\n        if (currentCount >= maxPaths) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `تم الوصول للحد الأقصى من المسارات (${maxPaths})`\n            }, {\n                status: 400\n            });\n        }\n        // إذا كان المسار الجديد افتراضي، إلغاء الافتراضي من المسارات الأخرى\n        if (is_default) {\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('UPDATE legal_library_paths SET is_default = false');\n        }\n        // إضافة المسار الجديد\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO legal_library_paths (path_name, path_value, description, is_active, is_default, scan_enabled)\n      VALUES ($1, $2, $3, true, $4, $5)\n      RETURNING *\n    `, [\n            path_name,\n            path_value,\n            description || '',\n            is_default || false,\n            scan_enabled !== false\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم إضافة المسار بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة مسار المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في إضافة المسار'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مسار موجود\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, path_name, path_value, description, is_active, is_default, scan_enabled } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المسار مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود المسار\n        const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM legal_library_paths WHERE id = $1', [\n            id\n        ]);\n        if (existingResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المسار غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من عدم تكرار المسار (إذا تم تغييره)\n        if (path_value && path_value !== existingResult.rows[0].path_value) {\n            const duplicateResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM legal_library_paths WHERE path_value = $1 AND id != $2', [\n                path_value,\n                id\n            ]);\n            if (duplicateResult.rows.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'هذا المسار موجود بالفعل'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        // إذا كان المسار الجديد افتراضي، إلغاء الافتراضي من المسارات الأخرى\n        if (is_default) {\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('UPDATE legal_library_paths SET is_default = false WHERE id != $1', [\n                id\n            ]);\n        }\n        // تحديث المسار\n        const updateFields = [];\n        const updateValues = [\n            id\n        ];\n        let paramIndex = 2;\n        if (path_name !== undefined) {\n            updateFields.push(`path_name = $${paramIndex}`);\n            updateValues.push(path_name);\n            paramIndex++;\n        }\n        if (path_value !== undefined) {\n            updateFields.push(`path_value = $${paramIndex}`);\n            updateValues.push(path_value);\n            paramIndex++;\n        }\n        if (description !== undefined) {\n            updateFields.push(`description = $${paramIndex}`);\n            updateValues.push(description);\n            paramIndex++;\n        }\n        if (is_active !== undefined) {\n            updateFields.push(`is_active = $${paramIndex}`);\n            updateValues.push(is_active);\n            paramIndex++;\n        }\n        if (is_default !== undefined) {\n            updateFields.push(`is_default = $${paramIndex}`);\n            updateValues.push(is_default);\n            paramIndex++;\n        }\n        if (scan_enabled !== undefined) {\n            updateFields.push(`scan_enabled = $${paramIndex}`);\n            updateValues.push(scan_enabled);\n            paramIndex++;\n        }\n        if (updateFields.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا توجد بيانات للتحديث'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE legal_library_paths \n      SET ${updateFields.join(', ')}\n      WHERE id = $1\n      RETURNING *\n    `, updateValues);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث المسار بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث مسار المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في تحديث المسار'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مسار\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المسار مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود المسار\n        const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM legal_library_paths WHERE id = $1', [\n            id\n        ]);\n        if (existingResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المسار غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // منع حذف المسار الافتراضي إذا كان الوحيد\n        const countResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT COUNT(*) as count FROM legal_library_paths WHERE is_active = true');\n        const activeCount = parseInt(countResult.rows[0].count);\n        if (activeCount <= 1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف المسار الوحيد المتبقي'\n            }, {\n                status: 400\n            });\n        }\n        // إذا كان المسار المحذوف افتراضي، جعل أول مسار آخر افتراضي\n        if (existingResult.rows[0].is_default) {\n            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n        UPDATE legal_library_paths \n        SET is_default = true \n        WHERE id != $1 AND is_active = true \n        ORDER BY id \n        LIMIT 1\n      `, [\n                id\n            ]);\n        }\n        // حذف المسار\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM legal_library_paths WHERE id = $1', [\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المسار بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف مسار المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف المسار'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/legal-library/paths/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Fpaths%2Froute&page=%2Fapi%2Flegal-library%2Fpaths%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Fpaths%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();