#!/usr/bin/env node

const { Client } = require('pg')

async function updateProductionDatabase() {
  console.log('🔄 تحديث قاعدة البيانات للنسخة الجديدة...')
  
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'legal_system_production',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD
  })
  
  if (!process.env.DB_PASSWORD) {
    console.error('❌ كلمة مرور قاعدة البيانات مطلوبة في متغير DB_PASSWORD')
    process.exit(1)
  }
  
  try {
    await client.connect()
    console.log('✅ تم الاتصال بقاعدة البيانات')
    
    // تحديث نظام الصلاحيات المتقدم
    console.log('🔐 تحديث نظام الصلاحيات...')
    
    // إنشاء جدول ربط المستخدمين بالأدوار
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_role_assignments (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        role_name VARCHAR(50) REFERENCES user_roles(role_name) ON DELETE CASCADE,
        assigned_by INTEGER REFERENCES users(id),
        assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        UNIQUE(user_id, role_name)
      )
    `)
    
    // إضافة فهارس للأداء
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id 
      ON user_role_assignments(user_id)
    `)
    
    // إنشاء دالة جلب الصلاحيات المجمعة
    await client.query(`
      CREATE OR REPLACE FUNCTION get_user_combined_permissions(user_id_param INTEGER)
      RETURNS TEXT[] AS $$
      DECLARE
        combined_permissions TEXT[];
      BEGIN
        SELECT ARRAY(
          SELECT DISTINCT up.permission_key
          FROM user_permissions up
          WHERE up.user_id = user_id_param AND up.is_active = true
        ) INTO combined_permissions;
        
        SELECT ARRAY(
          SELECT DISTINCT unnest(ur.permissions)
          FROM user_role_assignments ura
          JOIN user_roles ur ON ura.role_name = ur.role_name
          WHERE ura.user_id = user_id_param 
            AND ura.is_active = true 
            AND ur.is_active = true
        ) INTO combined_permissions;
        
        RETURN ARRAY(SELECT DISTINCT unnest(combined_permissions));
      END;
      $$ LANGUAGE plpgsql;
    `)
    
    console.log('✅ تم تحديث قاعدة البيانات بنجاح!')
    
  } catch (error) {
    console.error('❌ خطأ في تحديث قاعدة البيانات:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

updateProductionDatabase()