'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'

interface Beneficiary {
  id: number
  name: string
  type: 'client' | 'employee' | 'supplier'
  account_id?: number
  account_code?: string
}

interface BeneficiarySelectProps {
  value?: string
  onChange: (value: string, beneficiary?: Beneficiary) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export default function BeneficiarySelect({
  value,
  onChange,
  label,
  placeholder = "اختر المستفيد...",
  required = false,
  className = ""
}: BeneficiarySelectProps) {
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // جلب المستفيدين (العملاء والموظفين والموردين)
  const fetchBeneficiaries = async () => {
    try {
      setLoading(true)
      
      // جلب العملاء
      const clientsResponse = await fetch('/api/clients')
      const clientsData = await clientsResponse.json()
      console.log('🔍 BeneficiarySelect: بيانات العملاء:', clientsData)
      const clients = clientsData.success ? clientsData.clients.map((client: any) => ({
        id: client.id,
        name: client.name,
        type: 'client' as const,
        account_id: client.main_account_id || client.account_id,
        account_code: client.sub_account_code || client.account_code || client.parent_account_code
      })) : []

      // جلب الموظفين
      const employeesResponse = await fetch('/api/employees')
      const employeesData = await employeesResponse.json()
      console.log('🔍 BeneficiarySelect: بيانات الموظفين:', employeesData)
      const employees = employeesData.success ? employeesData.employees.map((employee: any) => ({
        id: employee.id,
        name: employee.name,
        type: 'employee' as const,
        account_id: employee.main_account_id || employee.account_id,
        account_code: employee.sub_account_code || employee.account_code || employee.parent_account_code
      })) : []

      // جلب الموردين
      const suppliersResponse = await fetch('/api/suppliers')
      const suppliersData = await suppliersResponse.json()
      console.log('🔍 BeneficiarySelect: بيانات الموردين:', suppliersData)
      const suppliers = suppliersData.success ? suppliersData.suppliers.map((supplier: any) => ({
        id: supplier.id,
        name: supplier.name,
        type: 'supplier' as const,
        account_id: supplier.main_account_id || supplier.account_id,
        account_code: supplier.sub_account_code || supplier.account_code || supplier.parent_account_code
      })) : []

      // دمج جميع المستفيدين
      const allBeneficiaries = [...clients, ...employees, ...suppliers]
      console.log('🔍 BeneficiarySelect: جميع المستفيدين:', allBeneficiaries)
      setBeneficiaries(allBeneficiaries)

    } catch (error) {
      console.error('خطأ في جلب المستفيدين:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBeneficiaries()
  }, [])

  // تصفية المستفيدين حسب البحث
  const filteredBeneficiaries = beneficiaries.filter(beneficiary =>
    beneficiary.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  console.log('🔍 BeneficiarySelect: المستفيدين المفلترين:', filteredBeneficiaries)

  // العثور على المستفيد المحدد
  const selectedBeneficiary = beneficiaries.find(b => 
    `${b.type}_${b.id}` === value
  )

  const handleSelect = (selectedValue: string) => {
    const beneficiary = beneficiaries.find(b => 
      `${b.type}_${b.id}` === selectedValue
    )
    onChange(selectedValue, beneficiary)
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'client': return '👤 عميل'
      case 'employee': return '👨‍💼 موظف'
      case 'supplier': return '🏢 مورد'
      default: return type
    }
  }

  return (
    <div className={className}>
      {label && (
        <Label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </Label>
      )}
      
      <Select value={value || ''} onValueChange={handleSelect}>
        <SelectTrigger className="w-full bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors min-h-[40px]">
          <SelectValue placeholder={loading ? "جاري التحميل..." : placeholder}>
            {selectedBeneficiary && (
              <div className="flex items-center gap-2 w-full">
                <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700 flex-shrink-0">
                  {getTypeLabel(selectedBeneficiary.type)}
                </span>
                <span className="font-medium text-gray-900 flex-1 truncate">{selectedBeneficiary.name}</span>
                {selectedBeneficiary.account_code && (
                  <span className="text-xs text-gray-500 flex-shrink-0">
                    {selectedBeneficiary.account_code}
                  </span>
                )}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="max-h-80">
          {/* حقل البحث */}
          <div className="p-3 border-b bg-gray-50">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المستفيدين..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 text-sm h-8 border-gray-200 focus:border-blue-500"
                autoFocus
              />
            </div>
          </div>

          {loading ? (
            <SelectItem value="loading" disabled>
              جاري التحميل...
            </SelectItem>
          ) : filteredBeneficiaries.length === 0 ? (
            <SelectItem value="no-results" disabled>
              {searchTerm ? 'لا توجد نتائج' : 'لا توجد مستفيدين'}
            </SelectItem>
          ) : (
            filteredBeneficiaries.map((beneficiary) => (
              <SelectItem
                key={`${beneficiary.type}_${beneficiary.id}`}
                value={`${beneficiary.type}_${beneficiary.id}`}
                className="p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors"
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="flex-shrink-0">
                    <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700 font-medium">
                      {getTypeLabel(beneficiary.type)}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900 truncate text-sm">{beneficiary.name}</div>
                    {beneficiary.account_code && (
                      <div className="text-xs text-gray-500 mt-1">
                        حساب: {beneficiary.account_code}
                      </div>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  )
}
