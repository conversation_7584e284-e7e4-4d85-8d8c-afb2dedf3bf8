# تعليمات استيراد قاعدة البيانات - النظام القانوني

## 📋 الملفات المُصدرة
- `legal_system_complete_2025-08-27.sql` - قاعدة البيانات الكاملة (موصى به)
- `legal_system_schema_2025-08-27.sql` - هيكل قاعدة البيانات فقط
- `legal_system_data_2025-08-27.sql` - البيانات فقط
- `import_database.bat` - سكريپت الاستيراد التلقائي

## 🚀 الطريقة السريعة (Windows)

1. انسخ مجلد `database_exports` إلى الخادم الجديد
2. تأكد من تثبيت PostgreSQL 13+
3. انقر نقراً مزدوجاً على `import_database.bat`
4. اتبع التعليمات على الشاشة

## 🔧 الطريقة اليدوية

### 1. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة بيانات جديدة
CREATE DATABASE legal_system_production;
```

### 2. استيراد البيانات
```bash
# Windows
psql -h localhost -U postgres -d legal_system_production -f "legal_system_complete_2025-08-27.sql"

# Linux/Mac
psql -h localhost -U postgres -d legal_system_production -f legal_system_complete_2025-08-27.sql
```

### 3. إعداد النظام
1. انسخ ملفات النظام إلى الخادم
2. أنشئ ملف `.env.local`:
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=legal_system_production
DB_USER=postgres
DB_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters
```

3. تشغيل النظام:
```bash
npm install
npm run build
npm start
```

## ✅ التحقق من نجاح الاستيراد

```sql
-- عدد الجداول
SELECT COUNT(*) as tables_count 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- عدد المستخدمين
SELECT COUNT(*) as users_count FROM users;

-- عدد الصلاحيات
SELECT COUNT(*) as permissions_count FROM permissions;

-- عدد الأدوار
SELECT COUNT(*) as roles_count FROM user_roles;
```

## 🔧 استكشاف الأخطاء

### مشكلة في الاستيراد:
- تأكد من إصدار PostgreSQL (13+ مطلوب)
- تحقق من صلاحيات المستخدم
- تأكد من وجود مساحة كافية

### مشكلة في تسجيل الدخول:
1. تشغيل: `node update-production-db.js`
2. تشغيل: `node create-admin-user.js`
3. التحقق من إعدادات .env.local

---
📅 تاريخ التصدير: ٢٧‏/٨‏/٢٠٢٥، ٥:٥٤:٢٥ م
🔢 الإصدار: 2.0.0 Production
🔐 نظام الصلاحيات: متقدم (55 صلاحية + 10 أدوار)
📊 الجداول: 15+ جدول مع البيانات الكاملة
