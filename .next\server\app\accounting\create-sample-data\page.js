(()=>{var e={};e.id=5679,e.ids=[5679],e.modules={3010:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(s,c);let d={children:["",{children:["accounting",{children:["create-sample-data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74681)),"D:\\mohaminew\\src\\app\\accounting\\create-sample-data\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\create-sample-data\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/accounting/create-sample-data/page",pathname:"/accounting/create-sample-data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30199:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(60687),a=r(43210),l=r(29523),n=r(44493),i=r(70352),c=r(61611),d=r(41862),o=r(10022),x=r(5336),m=r(93613),u=r(98254);function p(){let[e,s]=(0,a.useState)(!1),[r,p]=(0,a.useState)(null),h=async()=>{s(!0),p(null);try{let e=await fetch("/api/accounting/create-sample-accounts",{method:"POST",headers:{"Content-Type":"application/json"}}),s=await e.json();p(s)}catch(e){p({success:!1,message:"فشل في الاتصال بالخادم",error:e instanceof Error?e.message:"خطأ غير معروف"})}finally{s(!1)}};return(0,t.jsx)(u.A,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"space-y-6 p-6 bg-white min-h-screen",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إنشاء بيانات تجريبية"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"إنشاء دليل حسابات تجريبي للنظام القانوني"})]})}),(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-6 w-6 mr-3 text-blue-600"}),"البيانات التجريبية"]}),(0,t.jsx)(n.BT,{children:"سيتم إنشاء دليل حسابات شامل يتضمن جميع أنواع الحسابات المطلوبة للنظام القانوني"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-800",children:"الأصول:"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:"• النقدية والبنوك"}),(0,t.jsx)("li",{children:"• العملاء (حساب رئيسي)"}),(0,t.jsx)("li",{children:"• الأصول الثابتة"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800",children:"الخصوم:"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:"• الموظفين (حساب رئيسي)"}),(0,t.jsx)("li",{children:"• الموردين (حساب رئيسي)"}),(0,t.jsx)("li",{children:"• الضرائب المستحقة"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-purple-800",children:"الإيرادات:"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:"• أتعاب الاستشارات"}),(0,t.jsx)("li",{children:"• أتعاب التقاضي"}),(0,t.jsx)("li",{children:"• أتعاب صياغة العقود"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-red-800",children:"المصروفات:"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:"• رواتب الموظفين"}),(0,t.jsx)("li",{children:"• إيجار المكتب"}),(0,t.jsx)("li",{children:"• المصروفات التشغيلية"})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"إنشاء البيانات"}),(0,t.jsx)(n.BT,{children:"اضغط على الزر أدناه لإنشاء دليل الحسابات التجريبي"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(l.$,{onClick:h,disabled:e,className:"w-full md:w-auto",children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2 animate-spin"}),"جاري الإنشاء..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"إنشاء البيانات التجريبية"]})})})]}),r&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[r.success?(0,t.jsx)(x.A,{className:"h-6 w-6 mr-3 text-green-600"}):(0,t.jsx)(m.A,{className:"h-6 w-6 mr-3 text-red-600"}),"نتائج الإنشاء"]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)(i.Fc,{className:r.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,t.jsx)(i.TN,{className:r.success?"text-green-800":"text-red-800",children:r.message})}),r.success&&r.accountsCreated&&(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"تفاصيل الإنشاء:"}),(0,t.jsxs)("p",{className:"text-sm",children:["تم إنشاء ",r.accountsCreated," حساب بنجاح"]})]}),!r.success&&r.error&&(0,t.jsxs)("div",{className:"mt-4 p-4 bg-red-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-red-800 mb-2",children:"تفاصيل الخطأ:"}),(0,t.jsx)("p",{className:"text-sm text-red-600",children:r.error})]})]})]}),r?.success&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"الخطوات التالية"})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsx)("p",{children:"✅ تم إنشاء دليل الحسابات التجريبي بنجاح"}),(0,t.jsx)("p",{children:"✅ يمكنك الآن الذهاب إلى دليل الحسابات لمراجعة البيانات"}),(0,t.jsx)("p",{children:"✅ يمكنك تطبيق التصميم المحاسبي لربط العملاء والموظفين"})]}),(0,t.jsxs)("div",{className:"mt-4 space-x-2 space-x-reverse",children:[(0,t.jsx)(l.$,{onClick:()=>window.location.href="/accounting/chart-of-accounts",variant:"outline",children:"عرض دليل الحسابات"}),(0,t.jsx)(l.$,{onClick:()=>window.location.href="/accounting/implement-structure",variant:"outline",children:"تطبيق التصميم المحاسبي"})]})]})]})]})]})})})}},33873:e=>{"use strict";e.exports=require("path")},41862:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45882:(e,s,r)=>{Promise.resolve().then(r.bind(r,74681))},61611:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70352:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>o,TN:()=>x});var t=r(60687),a=r(43210),l=r(49384);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=l.$;var c=r(4780);let d=((e,s)=>r=>{var t;if((null==s?void 0:s.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=s,c=Object.keys(a).map(e=>{let s=null==r?void 0:r[e],t=null==l?void 0:l[e];if(null===s)return null;let i=n(s)||n(t);return a[e][i]}),d=r&&Object.entries(r).reduce((e,s)=>{let[r,t]=s;return void 0===t||(e[r]=t),e},{});return i(e,c,null==s||null==(t=s.compoundVariants)?void 0:t.reduce((e,s)=>{let{class:r,className:t,...a}=s;return Object.entries(a).every(e=>{let[s,r]=e;return Array.isArray(r)?r.includes({...l,...d}[s]):({...l,...d})[s]===r})?[...e,r,t]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:s,...r},a)=>(0,t.jsx)("div",{ref:a,role:"alert",className:(0,c.cn)(d({variant:s}),e),...r}));o.displayName="Alert",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h5",{ref:r,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let x=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",e),...s}));x.displayName="AlertDescription"},74681:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\create-sample-data\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\create-sample-data\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},86050:(e,s,r)=>{Promise.resolve().then(r.bind(r,30199))},93613:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,8409,7932],()=>r(3010));module.exports=t})();