(()=>{var e={};e.id=6592,e.ids=[6592],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.d(t,{P:()=>i});var r=a(64939),o=a(29021),c=a.n(o),s=a(33873),l=a.n(s),u=e([r]);r=(u.then?(await u)():u)[0];let p=null;try{let e=l().join(process.cwd(),"routing.config.json"),t=c().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],a=p.default_config;return{database:t.database,user:a.db_user,host:a.db_host,password:process.env.DB_PASSWORD||a.db_password,port:a.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),m=new r.Pool(d);async function i(e,t){let a=await m.connect();try{return await a.query(e,t)}finally{a.release()}}n()}catch(e){n(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},83672:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.r(t),a.d(t,{POST:()=>s});var r=a(32190),o=a(5069),c=e([o]);async function s(e){try{console.log("\uD83D\uDE80 إنشاء بيانات تجريبية لدليل الحسابات..."),await (0,o.P)(`
      CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id SERIAL PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_name_en VARCHAR(255),
        account_type VARCHAR(50) NOT NULL,
        parent_id INTEGER REFERENCES chart_of_accounts(id),
        account_level INTEGER DEFAULT 1,
        is_main_account BOOLEAN DEFAULT FALSE,
        is_sub_account BOOLEAN DEFAULT FALSE,
        is_control_account BOOLEAN DEFAULT FALSE,
        linked_table VARCHAR(100),
        auto_create_sub_accounts BOOLEAN DEFAULT FALSE,
        sub_account_prefix VARCHAR(10),
        account_nature VARCHAR(20) DEFAULT 'مدين',
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        allow_posting BOOLEAN DEFAULT TRUE,
        allow_transactions BOOLEAN DEFAULT TRUE,
        is_active BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_linked_record BOOLEAN DEFAULT FALSE,
        original_table VARCHAR(100),
        linked_record_id INTEGER
      )
    `),await (0,o.P)("DELETE FROM chart_of_accounts");let e=0;for(let t of[{code:"********",name:"الأصول",type:"أصول",level:1,nature:"مدين"},{code:"********",name:"الأصول المتداولة",type:"أصول",level:2,nature:"مدين"},{code:"********",name:"النقدية والبنوك",type:"أصول",level:3,nature:"مدين"},{code:"********",name:"الصندوق الرئيسي",type:"أصول",level:4,nature:"مدين"},{code:"********",name:"البنك الأهلي",type:"أصول",level:4,nature:"مدين"},{code:"********",name:"بنك الراجحي",type:"أصول",level:4,nature:"مدين"},{code:"********",name:"المدينون",type:"أصول",level:2,nature:"مدين"},{code:"********",name:"العملاء",type:"أصول",level:3,nature:"مدين",control:!0,linked:"clients"},{code:"********",name:"الأصول الثابتة",type:"أصول",level:2,nature:"مدين"},{code:"********",name:"الأثاث والمعدات",type:"أصول",level:3,nature:"مدين"},{code:"********",name:"أثاث المكتب",type:"أصول",level:4,nature:"مدين"},{code:"13010002",name:"أجهزة الكمبيوتر",type:"أصول",level:4,nature:"مدين"},{code:"20000000",name:"الخصوم",type:"خصوم",level:1,nature:"دائن"},{code:"21000000",name:"الخصوم المتداولة",type:"خصوم",level:2,nature:"دائن"},{code:"21010000",name:"الموظفين",type:"خصوم",level:3,nature:"دائن",control:!0,linked:"employees"},{code:"21020000",name:"الموردين",type:"خصوم",level:3,nature:"دائن",control:!0,linked:"suppliers"},{code:"21030000",name:"الضرائب المستحقة",type:"خصوم",level:3,nature:"دائن"},{code:"30000000",name:"حقوق الملكية",type:"حقوق ملكية",level:1,nature:"دائن"},{code:"31000000",name:"رأس المال",type:"حقوق ملكية",level:2,nature:"دائن"},{code:"31010001",name:"رأس المال المدفوع",type:"حقوق ملكية",level:3,nature:"دائن"},{code:"32000000",name:"الأرباح المحتجزة",type:"حقوق ملكية",level:2,nature:"دائن"},{code:"40000000",name:"الإيرادات",type:"إيرادات",level:1,nature:"دائن"},{code:"41000000",name:"إيرادات الخدمات القانونية",type:"إيرادات",level:2,nature:"دائن"},{code:"41010001",name:"أتعاب الاستشارات",type:"إيرادات",level:3,nature:"دائن"},{code:"41010002",name:"أتعاب التقاضي",type:"إيرادات",level:3,nature:"دائن"},{code:"41010003",name:"أتعاب صياغة العقود",type:"إيرادات",level:3,nature:"دائن"},{code:"50000000",name:"المصروفات",type:"مصروفات",level:1,nature:"مدين"},{code:"51000000",name:"مصروفات التشغيل",type:"مصروفات",level:2,nature:"مدين"},{code:"51010001",name:"رواتب الموظفين",type:"مصروفات",level:3,nature:"مدين"},{code:"********",name:"إيجار المكتب",type:"مصروفات",level:3,nature:"مدين"},{code:"********",name:"الكهرباء والماء",type:"مصروفات",level:3,nature:"مدين"},{code:"********",name:"الاتصالات والإنترنت",type:"مصروفات",level:3,nature:"مدين"},{code:"********",name:"القرطاسية والمطبوعات",type:"مصروفات",level:3,nature:"مدين"}])await (0,o.P)(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          account_nature, is_control_account, linked_table,
          allow_posting, allow_transactions, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `,[t.code,t.name,t.type,t.level,t.nature,t.control||!1,t.linked||null,4===t.level,4===t.level,!0]),e++;return r.NextResponse.json({success:!0,message:`تم إنشاء ${e} حساب تجريبي بنجاح`,accountsCreated:e})}catch(e){return console.error("❌ خطأ في إنشاء البيانات التجريبية:",e),r.NextResponse.json({success:!1,message:"فشل في إنشاء البيانات التجريبية",error:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}o=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},88284:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.r(t),a.d(t,{patchFetch:()=>u,routeModule:()=>i,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var r=a(96559),o=a(48088),c=a(37719),s=a(83672),l=e([s]);s=(l.then?(await l)():l)[0];let i=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/create-sample-accounts/route",pathname:"/api/accounting/create-sample-accounts",filename:"route",bundlePath:"app/api/accounting/create-sample-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\create-sample-accounts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:m}=i;function u(){return(0,c.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}n()}catch(e){n(e)}})},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,580],()=>a(88284));module.exports=n})();