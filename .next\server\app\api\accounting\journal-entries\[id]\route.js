(()=>{var e={};e.id=5892,e.ids=[5892],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{P:()=>d});var n=r(64939),a=r(29021),o=r.n(a),c=r(33873),i=r.n(c),u=e([n]);n=(u.then?(await u)():u)[0];let _=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");_=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!_?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(_&&_.routes[e]){let t=_.routes[e],r=_.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new n.Pool(p);async function d(e,t){let r=await l.connect();try{return await r.query(e,t)}finally{r.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23096:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>p});var n=r(96559),a=r(48088),o=r(37719),c=r(36492),i=e([c]);c=(i.then?(await i)():i)[0];let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/journal-entries/[id]/route",pathname:"/api/accounting/journal-entries/[id]",filename:"route",bundlePath:"app/api/accounting/journal-entries/[id]/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\journal-entries\\[id]\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:_,workUnitAsyncStorage:p,serverHooks:l}=d;function u(){return(0,o.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36492:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>u,GET:()=>c,PUT:()=>i});var n=r(32190),a=r(5069),o=e([a]);async function c(e,{params:t}){try{let{searchParams:r}=new URL(e.url),s="true"===r.get("include_details"),{id:o}=await t,c=await (0,a.P)(`
      SELECT
        je.*,
        c.currency_code,
        c.symbol as currency_symbol,
        cc.center_name as cost_center_name,
        u.username as created_by_username
      FROM journal_entries je
      LEFT JOIN currencies c ON je.currency_id = c.id
      LEFT JOIN cost_centers cc ON je.cost_center_id = cc.id
      LEFT JOIN users u ON je.created_by_user_id = u.id
      WHERE je.id = $1
    `,[o]);if(0===c.rows.length)return n.NextResponse.json({success:!1,error:"القيد غير موجود"},{status:404});let i=c.rows[0];return s&&(i.details=(await (0,a.P)(`
        SELECT
          jed.*,
          ca.account_name,
          ca.account_code,
          c.currency_code,
          pm.method_name as payment_method_name,
          cc.center_name as cost_center_name
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts ca ON jed.account_id = ca.id
        LEFT JOIN currencies c ON jed.currency_id = c.id
        LEFT JOIN payment_methods pm ON jed.payment_method_id = pm.id
        LEFT JOIN cost_centers cc ON jed.cost_center_id = cc.id
        WHERE jed.journal_entry_id = $1
        ORDER BY jed.line_number
      `,[o])).rows),n.NextResponse.json({success:!0,entry:i,message:"تم جلب القيد بنجاح"})}catch(e){return console.error("خطأ في جلب القيد:",e),n.NextResponse.json({success:!1,error:"فشل في جلب القيد",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e,{params:t}){try{let r=await e.json(),{id:s}=await t,{entry_date:o,description:c,total_debit:i,total_credit:u,currency_id:d,cost_center_id:_,case_id:p,case_number:l,reference_number:E,status:m,details:y=[]}=r,j=await (0,a.P)("SELECT id, status FROM journal_entries WHERE id = $1",[s]);if(0===j.rows.length)return n.NextResponse.json({success:!1,error:"القيد غير موجود"},{status:404});if("approved"===j.rows[0].status)return n.NextResponse.json({success:!1,error:"لا يمكن تعديل القيد المعتمد"},{status:400});if(Math.abs(i-u)>.01)return n.NextResponse.json({success:!1,error:"القيد غير متوازن"},{status:400});await (0,a.P)("BEGIN");try{let e=await (0,a.P)(`
        UPDATE journal_entries
        SET
          entry_date = $2,
          description = $3,
          total_debit = $4,
          total_credit = $5,
          currency_id = $6,
          cost_center_id = $7,
          case_id = $8,
          case_number = $9,
          reference_number = $10,
          status = COALESCE($11, status),
          updated_date = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `,[s,o,c,i,u,d,_,p,l,E,m]);if(y.length>0)for(let e of(await (0,a.P)("DELETE FROM journal_entry_details WHERE journal_entry_id = $1",[s]),y))await (0,a.P)(`
            INSERT INTO journal_entry_details (
              journal_entry_id, line_number, account_id, debit_amount, credit_amount,
              currency_id, exchange_rate, cost_center_id, payment_method_id,
              description, reference_number, case_id
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          `,[s,e.line_number,e.account_id,e.debit_amount||0,e.credit_amount||0,e.currency_id||d,e.exchange_rate||1,e.cost_center_id,e.payment_method_id,e.description,e.reference_number,e.case_id||(p&&"0"!==p?parseInt(p):null)]);return await (0,a.P)("COMMIT"),n.NextResponse.json({success:!0,entry:e.rows[0],message:"تم تحديث القيد اليومي بنجاح"})}catch(e){throw await (0,a.P)("ROLLBACK"),e}}catch(e){return console.error("خطأ في تحديث القيد اليومي:",e),n.NextResponse.json({success:!1,error:"فشل في تحديث القيد اليومي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function u(e,{params:t}){try{let{id:e}=await t,r=await (0,a.P)("SELECT id, status FROM journal_entries WHERE id = $1",[e]);if(0===r.rows.length)return n.NextResponse.json({success:!1,error:"القيد غير موجود"},{status:404});if("approved"===r.rows[0].status)return n.NextResponse.json({success:!1,error:"لا يمكن حذف القيد المعتمد"},{status:400});await (0,a.P)("BEGIN");try{return await (0,a.P)("DELETE FROM journal_entry_details WHERE journal_entry_id = $1",[e]),await (0,a.P)("DELETE FROM journal_entries WHERE id = $1",[e]),await (0,a.P)("COMMIT"),n.NextResponse.json({success:!0,message:"تم حذف القيد اليومي بنجاح"})}catch(e){throw await (0,a.P)("ROLLBACK"),e}}catch(e){return console.error("خطأ في حذف القيد اليومي:",e),n.NextResponse.json({success:!1,error:"فشل في حذف القيد اليومي",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}a=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(23096));module.exports=s})();