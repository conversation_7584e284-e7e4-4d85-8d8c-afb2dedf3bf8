{"/home/<USER>": "/home", "/issues/page": "/issues", "/percentages/page": "/percentages", "/_not-found/page": "/_not-found", "/case-distribution/page": "/case-distribution", "/users/page": "/users", "/api/account-linking-settings/route": "/api/account-linking-settings", "/api/account-linking/route": "/api/account-linking", "/api/accounting/account-linking/create-system-accounts/route": "/api/accounting/account-linking/create-system-accounts", "/api/accounting/account-linking/route": "/api/accounting/account-linking", "/api/accounting/chart-of-accounts/[id]/route": "/api/accounting/chart-of-accounts/[id]", "/api/accounting/chart-of-accounts/link/route": "/api/accounting/chart-of-accounts/link", "/api/accounting/chart-of-accounts/relink/route": "/api/accounting/chart-of-accounts/relink", "/api/accounting/chart-of-accounts/route": "/api/accounting/chart-of-accounts", "/api/accounting/create-sample-accounts/route": "/api/accounting/create-sample-accounts", "/api/accounting/currencies/route": "/api/accounting/currencies", "/api/accounting/fix-parent-accounts/route": "/api/accounting/fix-parent-accounts", "/api/accounting/debtors-creditors/route": "/api/accounting/debtors-creditors", "/api/accounting/fix-vouchers-accounts/route": "/api/accounting/fix-vouchers-accounts", "/api/accounting/implement-proper-structure/route": "/api/accounting/implement-proper-structure", "/api/accounting/default-accounts/route": "/api/accounting/default-accounts", "/api/accounting/invoices/route": "/api/accounting/invoices", "/api/accounting/journal-entries/[id]/route": "/api/accounting/journal-entries/[id]", "/api/accounting/journal-entries/route": "/api/accounting/journal-entries", "/api/accounting/main-accounts/route": "/api/accounting/main-accounts", "/api/accounting/opening-balances/bulk/route": "/api/accounting/opening-balances/bulk", "/api/accounting/opening-balances/route": "/api/accounting/opening-balances", "/api/accounting/payment-methods/route": "/api/accounting/payment-methods", "/api/accounting/payment-vouchers/route": "/api/accounting/payment-vouchers", "/api/accounting/payments/route": "/api/accounting/payments", "/api/accounting/projects/route": "/api/accounting/projects", "/api/accounting/receipt-vouchers/route": "/api/accounting/receipt-vouchers", "/api/accounting/reports/[reportType]/route": "/api/accounting/reports/[reportType]", "/api/accounting/reports/account-statement/route": "/api/accounting/reports/account-statement", "/api/accounting/reports/export/route": "/api/accounting/reports/export", "/api/accounting/update-all-references/route": "/api/accounting/update-all-references", "/api/accounting/update-api-references/route": "/api/accounting/update-api-references", "/api/accounting/vouchers/route": "/api/accounting/vouchers", "/api/ai/auto-reply/route": "/api/ai/auto-reply", "/api/ai/local-models/route": "/api/ai/local-models", "/api/ai/settings/route": "/api/ai/settings", "/api/ai/test-reply/route": "/api/ai/test-reply", "/api/announcements/route": "/api/announcements", "/api/auth/clients/route": "/api/auth/clients", "/api/auth/simple/route": "/api/auth/simple", "/api/auth/test/route": "/api/auth/test", "/api/auth/users/route": "/api/auth/users", "/api/auto-link-account/route": "/api/auto-link-account", "/api/branches/route": "/api/branches", "/api/case-distribution/route": "/api/case-distribution", "/api/chart-of-accounts-new/[id]/route": "/api/chart-of-accounts-new/[id]", "/api/chart-of-accounts-new/route": "/api/chart-of-accounts-new", "/api/chart-of-accounts/[id]/route": "/api/chart-of-accounts/[id]", "/api/chart-of-accounts/link-tables/route": "/api/chart-of-accounts/link-tables", "/api/chart-of-accounts/route": "/api/chart-of-accounts", "/api/chart-of-accounts/main/route": "/api/chart-of-accounts/main", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/logs/route": "/api/chat/logs", "/api/chat/messages/read/route": "/api/chat/messages/read", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/route": "/api/chat", "/api/client-accounts/route": "/api/client-accounts", "/api/client-portal/auth/route": "/api/client-portal/auth", "/api/client-portal/auth/simple/route": "/api/client-portal/auth/simple", "/api/client-portal/cases/route": "/api/client-portal/cases", "/api/client-portal/dashboard/route": "/api/client-portal/dashboard", "/api/client-portal/documents/route": "/api/client-portal/documents", "/api/client-portal/notifications/[id]/route": "/api/client-portal/notifications/[id]", "/api/client-portal/notifications/route": "/api/client-portal/notifications", "/api/client-portal/requests/route": "/api/client-portal/requests", "/api/clients/route": "/api/clients", "/api/companies/[id]/route": "/api/companies/[id]", "/api/companies/route": "/api/companies", "/api/cost-centers/route": "/api/cost-centers", "/api/company/route": "/api/company", "/api/courts/route": "/api/courts", "/api/create-sample-clients-employees/route": "/api/create-sample-clients-employees", "/api/currencies/route": "/api/currencies", "/api/database/columns/route": "/api/database/columns", "/api/database/data/route": "/api/database/data", "/api/database/stats/route": "/api/database/stats", "/api/database/tables/route": "/api/database/tables", "/api/documents/download/[id]/route": "/api/documents/download/[id]", "/api/documents/upload/route": "/api/documents/upload", "/api/employees/route": "/api/employees", "/api/documents/route": "/api/documents", "/api/follows/route": "/api/follows", "/api/follows/service-allocation/route": "/api/follows/service-allocation", "/api/follows/user-issues/route": "/api/follows/user-issues", "/api/footer-links/[id]/route": "/api/footer-links/[id]", "/api/footer-links/init/route": "/api/footer-links/init", "/api/footer-links/route": "/api/footer-links", "/api/governorates/route": "/api/governorates", "/api/hearings/route": "/api/hearings", "/api/init/route": "/api/init", "/api/internal-services/route": "/api/internal-services", "/api/invoices/route": "/api/invoices", "/api/issue-types/route": "/api/issue-types", "/api/issues/[id]/route": "/api/issues/[id]", "/api/issues/route": "/api/issues", "/api/issues/undistributed/route": "/api/issues/undistributed", "/api/journal-entries-new/route": "/api/journal-entries-new", "/api/journal-entries/route": "/api/journal-entries", "/api/legal-files/stats/route": "/api/legal-files/stats", "/api/lawyer-earnings/route": "/api/lawyer-earnings", "/api/legal-library/download/route": "/api/legal-library/download", "/api/legal-library/files/route": "/api/legal-library/files", "/api/legal-library/paths/route": "/api/legal-library/paths", "/api/legal-library/route": "/api/legal-library", "/api/legal-library/view/route": "/api/legal-library/view", "/api/lineages/route": "/api/lineages", "/api/main-accounts/[id]/route": "/api/main-accounts/[id]", "/api/main-accounts/route": "/api/main-accounts", "/api/migrate-accounts/route": "/api/migrate-accounts", "/api/movements/route": "/api/movements", "/api/navigation-pages/all/route": "/api/navigation-pages/all", "/api/notifications/route": "/api/notifications", "/api/opening-balances/route": "/api/opening-balances", "/api/payment-vouchers/route": "/api/payment-vouchers", "/api/percentages/route": "/api/percentages", "/api/permissions/route": "/api/permissions", "/api/public-stats/route": "/api/public-stats", "/api/public-announcements/route": "/api/public-announcements", "/api/receipt-vouchers/route": "/api/receipt-vouchers", "/api/reports/route": "/api/reports", "/api/reset-database/route": "/api/reset-database", "/api/search/navigation/route": "/api/search/navigation", "/api/seed-data/route": "/api/seed-data", "/api/seed-users/route": "/api/seed-users", "/api/service-distributions/route": "/api/service-distributions", "/api/services/[slug]/route": "/api/services/[slug]", "/api/services/route": "/api/services", "/api/serviceslow/[id]/route": "/api/serviceslow/[id]", "/api/serviceslow/seed/route": "/api/serviceslow/seed", "/api/serviceslow/slug/[slug]/route": "/api/serviceslow/slug/[slug]", "/api/settings/announcements/[id]/route": "/api/settings/announcements/[id]", "/api/serviceslow/route": "/api/serviceslow", "/api/settings/announcements/route": "/api/settings/announcements", "/api/settings/public-announcements/[id]/route": "/api/settings/public-announcements/[id]", "/api/settings/public-announcements/route": "/api/settings/public-announcements", "/api/setup-integrated-ledgersmb/route": "/api/setup-integrated-ledgersmb", "/api/setup-simple/route": "/api/setup-simple", "/api/setup-step-by-step/route": "/api/setup-step-by-step", "/api/setup/vouchers-tables/route": "/api/setup/vouchers-tables", "/api/statistics/route": "/api/statistics", "/api/simple-receipt/route": "/api/simple-receipt", "/api/suppliers/route": "/api/suppliers", "/api/system-settings/route": "/api/system-settings", "/api/table-stats/route": "/api/table-stats", "/api/test-db/route": "/api/test-db", "/api/test-operations/route": "/api/test-operations", "/api/test-connection/route": "/api/test-connection", "/api/upload/logo/route": "/api/upload/logo", "/api/test-tables/route": "/api/test-tables", "/api/time-tracking/route": "/api/time-tracking", "/api/test-database-exists/route": "/api/test-database-exists", "/api/user-permissions/route": "/api/user-permissions", "/api/user-roles/assignments/route": "/api/user-roles/assignments", "/api/user-roles/route": "/api/user-roles", "/api/users/[id]/route": "/api/users/[id]", "/api/users/route": "/api/users", "/api/website-services/route": "/api/website-services", "/favicon.ico/route": "/favicon.ico", "/accounting/account-linking/page": "/accounting/account-linking", "/accounting/chart-of-accounts/page": "/accounting/chart-of-accounts", "/accounting/create-sample-data/page": "/accounting/create-sample-data", "/accounting/default-accounts/page": "/accounting/default-accounts", "/accounting/implement-structure/page": "/accounting/implement-structure", "/accounting/journal-entries/page": "/accounting/journal-entries", "/accounting/debtors-creditors/page": "/accounting/debtors-creditors", "/accounting/link-accounts/page": "/accounting/link-accounts", "/accounting/main-accounts/page": "/accounting/main-accounts", "/accounting/page": "/accounting", "/accounting/opening-balances/page": "/accounting/opening-balances", "/accounting/reports/account-statement/page": "/accounting/reports/account-statement", "/accounting/payment-vouchers/page": "/accounting/payment-vouchers", "/accounting/receipt-vouchers/page": "/accounting/receipt-vouchers", "/accounting/reports/page": "/accounting/reports", "/admin/ai-settings/page": "/admin/ai-settings", "/admin/database-viewer/page": "/admin/database-viewer", "/branches/page": "/branches", "/admin/serviceslow/page": "/admin/serviceslow", "/case-reports/page": "/case-reports", "/chat-logs/page": "/chat-logs", "/client-login/page": "/client-login", "/clients/page": "/clients", "/client-accounts/page": "/client-accounts", "/company/page": "/company", "/client-portal/page": "/client-portal", "/courts/page": "/courts", "/create-sample-clients-employees/page": "/create-sample-clients-employees", "/dashboard/page": "/dashboard", "/documents/archive/page": "/documents/archive", "/documents/page": "/documents", "/documents/upload/page": "/documents/upload", "/employee-reports/page": "/employee-reports", "/financial-reports/page": "/financial-reports", "/employees/page": "/employees", "/governorates/page": "/governorates", "/follows/page": "/follows", "/issue-types/page": "/issue-types", "/invoices/page": "/invoices", "/issues/new/page": "/issues/new", "/journal-entries-new/page": "/journal-entries-new", "/lawyer-earnings/page": "/lawyer-earnings", "/legal-library/page": "/legal-library", "/login/page": "/login", "/movements/page": "/movements", "/opening-balances/page": "/opening-balances", "/page": "/", "/receipt-vouchers/page": "/receipt-vouchers", "/payment-vouchers/page": "/payment-vouchers", "/public-home/page": "/public-home", "/reports/page": "/reports", "/roles/page": "/roles", "/services/page": "/services", "/services/[slug]/page": "/services/[slug]", "/serviceslow/[slug]/page": "/serviceslow/[slug]", "/serviceslow/page": "/serviceslow", "/settings/navigation-pages/page": "/settings/navigation-pages", "/settings/cost-centers/page": "/settings/cost-centers", "/setup-integrated/page": "/setup-integrated", "/settings/announcements/page": "/settings/announcements", "/settings/footer-links/page": "/settings/footer-links", "/setup-simple/page": "/setup-simple", "/test-db/page": "/test-db", "/setup/page": "/setup", "/test-issue-select/page": "/test-issue-select", "/test-select/page": "/test-select", "/test-selects/page": "/test-selects", "/test/page": "/test", "/time-tracking/page": "/time-tracking", "/under-construction/page": "/under-construction", "/trial-balance/page": "/trial-balance", "/website-admin/page": "/website-admin"}