import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع المحاكم
export async function GET() {
  try {
    console.log('GET Courts API: Fetching all courts')

    const result = await query(`
      SELECT
        c.id,
        c.name,
        c.type,
        c.location,
        c.address,
        c.phone,
        c.email,
        c.created_date,
        c.governorate_id,
        COALESCE(g.name, 'غير محدد') as governorate_name
      FROM courts c
      LEFT JOIN governorates g ON c.governorate_id = g.id
      WHERE c.is_active = true
      ORDER BY c.name
    `)

    console.log('GET Courts API: Found', result.rows.length, 'courts')

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('GET Courts API: Error fetching courts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المحاكم' },
      { status: 500 }
    )
  }
}

// POST - إضافة محكمة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, type, governorate_id, address, phone, is_active = true
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المحكمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO courts (name, type, governorate_id, address, phone, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, type, governorate_id, address, phone, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المحكمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المحكمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث محكمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, type, governorate_id, address, phone, is_active
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المحكمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE courts
      SET name = $1, type = $2, governorate_id = $3, address = $4,
          phone = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [name, type, governorate_id, address, phone, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المحكمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المحكمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف محكمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المحكمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM courts WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحكمة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المحكمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المحكمة' },
      { status: 500 }
    )
  }
}
