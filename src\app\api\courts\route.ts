import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع المحاكم
export async function GET() {
  try {
    console.log('GET Courts API: Fetching all courts')

    const result = await query(`
      SELECT
        id,
        name,
        type,
        location,
        address,
        phone,
        email,
        created_at
      FROM courts
      ORDER BY name
    `)

    console.log('GET Courts API: Found', result.rows.length, 'courts')

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('GET Courts API: Error fetching courts:', error)

    // في حالة عدم وجود الجدول، إرجاع بيانات تجريبية
    const sampleData = [
      {
        id: 1,
        name: 'المحكمة العليا - صنعاء',
        type: 'محكمة عليا',
        governorate_id: 1,
        governorate_name: 'صنعاء',
        address: 'شارع الزبيري - صنعاء',
        phone: '01-123456',
        employee_id: 1, // رقم الموظف المسؤول
        employee_name: 'ماجد أحمد',
        issue_id: 1, // رقم القضية المرتبطة
        issue_title: 'قضية تجارية - شركة الأمل',
        is_active: true,
        created_date: '2024-01-01'
      },
      {
        id: 2,
        name: 'محكمة الاستئناف التجارية - صنعاء',
        type: 'محكمة استئناف',
        governorate_id: 1,
        governorate_name: 'صنعاء',
        address: 'شارع الستين - صنعاء',
        phone: '01-234567',
        employee_id: 2, // رقم الموظف المسؤول
        employee_name: 'يحيى علي',
        issue_id: 2, // رقم القضية المرتبطة
        issue_title: 'قضية عقارية - النزاع العقاري',
        is_active: true,
        created_date: '2024-01-01'
      },
      {
        id: 3,
        name: 'المحكمة التجارية الابتدائية - صنعاء',
        type: 'محكمة تجارية',
        governorate_id: 1,
        governorate_name: 'صنعاء',
        address: 'شارع الحصبة - صنعاء',
        phone: '01-345678',
        employee_id: 3,
        employee_name: 'أحمد صالح',
        issue_id: 3,
        issue_title: 'قضية تجارية - نزاع شراكة',
        is_active: true,
        created_date: '2024-01-01'
      },
      {
        id: 4,
        name: 'محكمة الأحوال الشخصية - صنعاء',
        type: 'محكمة أحوال شخصية',
        governorate_id: 1,
        governorate_name: 'صنعاء',
        address: 'شارع الثورة - صنعاء',
        phone: '01-456789',
        employee_id: 4,
        employee_name: 'محمد صالح',
        issue_id: 4,
        issue_title: 'قضية أحوال شخصية - طلاق',
        is_active: true,
        created_date: '2024-01-01'
      },
      {
        id: 5,
        name: 'المحكمة الجنائية - عدن',
        type: 'محكمة جنائية',
        governorate_id: 2,
        governorate_name: 'عدن',
        address: 'كريتر - عدن',
        phone: '02-123456',
        employee_id: 1,
        employee_name: 'ماجد أحمد',
        issue_id: 5,
        issue_title: 'قضية جنائية - سرقة',
        is_active: true,
        created_date: '2024-01-01'
      }
    ]

    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching courts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المحاكم' },
      { status: 500 }
    )
  }
}

// POST - إضافة محكمة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, type, governorate_id, address, phone, is_active = true
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المحكمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO courts (name, type, governorate_id, address, phone, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, type, governorate_id, address, phone, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المحكمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المحكمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث محكمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, type, governorate_id, address, phone, is_active
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المحكمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE courts
      SET name = $1, type = $2, governorate_id = $3, address = $4,
          phone = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [name, type, governorate_id, address, phone, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المحكمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المحكمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف محكمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المحكمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM courts WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحكمة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المحكمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting court:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المحكمة' },
      { status: 500 }
    )
  }
}
