(()=>{var t={};t.id=6486,t.ids=[6486],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26589:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{POST:()=>i});var n=r(96559),o=r(48088),c=r(37719),s=r(32190);async function i(t){try{let{type:e,format:r,dateFrom:a,dateTo:n}=await t.json(),o=await fetch(`${t.nextUrl.origin}/api/accounting/reports/${e}?from=${a}&to=${n}`),c=await o.json();if(!c.success)return s.NextResponse.json({success:!1,error:"فشل في جلب بيانات التقرير"},{status:500});if("pdf"===r)return await d(e,c.data,a,n);if("excel"===r)return await l(e,c.data,a,n);return s.NextResponse.json({success:!1,error:"تنسيق التصدير غير مدعوم"},{status:400})}catch(t){return console.error("Error exporting report:",t),s.NextResponse.json({success:!1,error:"فشل في تصدير التقرير"},{status:500})}}async function d(t,e,r,a){let n=function(t,e,r,a){let n=t=>t.toLocaleString("ar-SA",{style:"currency",currency:"SAR",minimumFractionDigits:2}),o=t=>new Date(t).toLocaleDateString("ar-SA"),c="",s="",i="";switch(t){case"trial_balance":c="ميزان المراجعة",s=`
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>مدين</th>
          <th>دائن</th>
        </tr>
      `,i=e.map(t=>`
        <tr>
          <td>${t.account_code}</td>
          <td>${t.account_name}</td>
          <td>${t.debit_balance>0?n(t.debit_balance):"-"}</td>
          <td>${t.credit_balance>0?n(t.credit_balance):"-"}</td>
        </tr>
      `).join("");break;case"income_statement":c="قائمة الدخل",s=`
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>المبلغ</th>
        </tr>
      `,i=e.map(t=>`
        <tr>
          <td>${t.account_code}</td>
          <td>${t.account_name}</td>
          <td class="${"I"===t.category?"revenue":"expense"}">${n(t.amount)}</td>
        </tr>
      `).join("");break;case"balance_sheet":c="الميزانية العمومية",s=`
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>المبلغ</th>
        </tr>
      `,i=e.map(t=>`
        <tr>
          <td>${t.account_code}</td>
          <td>${t.account_name}</td>
          <td class="${t.category}">${n(t.amount)}</td>
        </tr>
      `).join("");break;case"cash_flow":c="قائمة التدفق النقدي",s=`
        <tr>
          <th>التاريخ</th>
          <th>الوصف</th>
          <th>التدفق الداخل</th>
          <th>التدفق الخارج</th>
          <th>الرصيد</th>
        </tr>
      `,i=e.map(t=>`
        <tr>
          <td>${o(t.date)}</td>
          <td>${t.description}</td>
          <td class="inflow">${t.inflow>0?n(t.inflow):"-"}</td>
          <td class="outflow">${t.outflow>0?n(t.outflow):"-"}</td>
          <td class="balance">${n(t.balance)}</td>
        </tr>
      `).join("")}return`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>${c}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .period { font-size: 14px; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f5f5f5; font-weight: bold; }
        .revenue { color: #16a085; }
        .expense { color: #e74c3c; }
        .inflow { color: #27ae60; }
        .outflow { color: #e74c3c; }
        .balance { font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">${c}</div>
        <div class="period">من ${o(r)} إلى ${o(a)}</div>
      </div>
      <table>
        <thead>${s}</thead>
        <tbody>${i}</tbody>
      </table>
    </body>
    </html>
  `}(t,e,r,a),o=Buffer.from(n,"utf-8");return new s.NextResponse(o,{headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="${t}_${r}_${a}.pdf"`}})}async function l(t,e,r,a){let n=function(t,e,r,a){let n="";switch(t){case"trial_balance":n="رمز الحساب,اسم الحساب,مدين,دائن\n"+e.map(t=>`${t.account_code},"${t.account_name}",${t.debit_balance},${t.credit_balance}`).join("\n");break;case"income_statement":n="رمز الحساب,اسم الحساب,الفئة,المبلغ\n"+e.map(t=>`${t.account_code},"${t.account_name}",${t.category},${t.amount}`).join("\n");break;case"balance_sheet":n="رمز الحساب,اسم الحساب,الفئة,المبلغ\n"+e.map(t=>`${t.account_code},"${t.account_name}",${t.category},${t.amount}`).join("\n");break;case"cash_flow":n="التاريخ,الوصف,التدفق الداخل,التدفق الخارج,الرصيد\n"+e.map(t=>`${t.date},"${t.description}",${t.inflow},${t.outflow},${t.balance}`).join("\n")}return n}(t,e,0,0),o=Buffer.from(n,"utf-8");return new s.NextResponse(o,{headers:{"Content-Type":"application/vnd.ms-excel","Content-Disposition":`attachment; filename="${t}_${r}_${a}.csv"`}})}let u=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounting/reports/export/route",pathname:"/api/accounting/reports/export",filename:"route",bundlePath:"app/api/accounting/reports/export/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\reports\\export\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:m}=u;function f(){return(0,c.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),a=e.X(0,[4447,580],()=>r(26589));module.exports=a})();