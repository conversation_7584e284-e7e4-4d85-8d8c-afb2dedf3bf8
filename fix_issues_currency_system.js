// إصلاح نظام العملة والمشاكل في القضايا
const { Pool } = require('pg');

async function fixIssuesCurrencySystem() {
  console.log('🔧 إصلاح نظام العملة والمشاكل في القضايا...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. إنشاء جدول العملات إذا لم يكن موجوداً
      console.log('\n   💰 إنشاء جدول العملات:');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS currencies (
          id SERIAL PRIMARY KEY,
          currency_code VARCHAR(3) UNIQUE NOT NULL,
          currency_name VARCHAR(100) NOT NULL,
          symbol VARCHAR(10),
          exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
          is_base_currency BOOLEAN DEFAULT FALSE,
          is_active BOOLEAN DEFAULT TRUE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('      ✅ تم إنشاء جدول العملات');

      // إدراج العملات الأساسية
      await pool.query(`
        INSERT INTO currencies (currency_code, currency_name, symbol, exchange_rate, is_base_currency, is_active)
        SELECT * FROM (VALUES
          ('YER', 'ريال يمني', 'ر.ي', 1.0000, TRUE, TRUE),
          ('USD', 'دولار أمريكي', '$', 1500.0000, FALSE, TRUE),
          ('SAR', 'ريال سعودي', 'ر.س', 400.0000, FALSE, TRUE),
          ('EUR', 'يورو', '€', 1600.0000, FALSE, TRUE)
        ) AS v(currency_code, currency_name, symbol, exchange_rate, is_base_currency, is_active)
        WHERE NOT EXISTS (SELECT 1 FROM currencies WHERE currency_code = v.currency_code)
      `);
      console.log('      ✅ تم إدراج العملات الأساسية');

      // 2. إضافة عمود العملة لجدول القضايا
      console.log('\n   💱 إضافة عمود العملة لجدول القضايا:');
      
      const currencyColumnExists = await pool.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'currency_id'
      `);

      if (currencyColumnExists.rows.length === 0) {
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN currency_id INTEGER REFERENCES currencies(id) DEFAULT 1
        `);
        console.log('      ✅ تم إضافة عمود currency_id');
      } else {
        console.log('      ✅ عمود currency_id موجود');
      }

      // 3. إضافة عمود المبلغ بالريال اليمني
      const amountYerColumnExists = await pool.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'amount_yer'
      `);

      if (amountYerColumnExists.rows.length === 0) {
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN amount_yer DECIMAL(15,2) GENERATED ALWAYS AS (
            CASE 
              WHEN currency_id = 1 THEN case_amount
              ELSE case_amount * (SELECT exchange_rate FROM currencies WHERE id = currency_id)
            END
          ) STORED
        `);
        console.log('      ✅ تم إضافة عمود amount_yer (محسوب تلقائياً)');
      } else {
        console.log('      ✅ عمود amount_yer موجود');
      }

      // 4. تحديث القضايا الموجودة لتستخدم الريال اليمني كافتراضي
      const updateResult = await pool.query(`
        UPDATE issues 
        SET currency_id = 1
        WHERE currency_id IS NULL
        RETURNING case_number
      `);
      
      if (updateResult.rows.length > 0) {
        console.log(`      ✅ تم تحديث ${updateResult.rows.length} قضية لاستخدام الريال اليمني`);
      }

      // 5. إنشاء دالة لحساب المبلغ بالريال اليمني
      console.log('\n   🔢 إنشاء دالة حساب المبلغ بالريال اليمني:');
      
      await pool.query(`
        CREATE OR REPLACE FUNCTION calculate_amount_yer(p_case_amount DECIMAL, p_currency_id INTEGER)
        RETURNS DECIMAL AS $$
        DECLARE
          exchange_rate DECIMAL;
        BEGIN
          IF p_currency_id = 1 THEN
            RETURN p_case_amount;
          END IF;
          
          SELECT c.exchange_rate INTO exchange_rate
          FROM currencies c
          WHERE c.id = p_currency_id AND c.is_active = TRUE;
          
          IF exchange_rate IS NULL THEN
            RETURN p_case_amount;
          END IF;
          
          RETURN p_case_amount * exchange_rate;
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('      ✅ تم إنشاء دالة calculate_amount_yer');

      // 6. فحص البيانات الحالية
      console.log('\n   📊 فحص البيانات الحالية:');
      
      const currentData = await pool.query(`
        SELECT 
          i.case_number,
          i.case_amount,
          i.currency_id,
          c.currency_code,
          c.symbol,
          c.exchange_rate,
          calculate_amount_yer(i.case_amount, i.currency_id) as amount_yer_calculated
        FROM issues i
        LEFT JOIN currencies c ON i.currency_id = c.id
        ORDER BY i.case_number
        LIMIT 5
      `);

      console.log('      📋 عينة من القضايا:');
      currentData.rows.forEach(row => {
        console.log(`         ${row.case_number}:`);
        console.log(`            - المبلغ الأصلي: ${row.case_amount} ${row.symbol || row.currency_code}`);
        console.log(`            - المبلغ بالريال: ${row.amount_yer_calculated} ر.ي`);
        console.log(`            - سعر الصرف: ${row.exchange_rate}`);
      });

      // 7. إحصائيات العملات
      console.log('\n   📈 إحصائيات العملات:');
      
      const currencyStats = await pool.query(`
        SELECT 
          c.currency_name,
          c.symbol,
          COUNT(i.id) as issues_count,
          SUM(i.case_amount) as total_amount,
          SUM(calculate_amount_yer(i.case_amount, i.currency_id)) as total_amount_yer
        FROM currencies c
        LEFT JOIN issues i ON c.id = i.currency_id
        WHERE c.is_active = TRUE
        GROUP BY c.id, c.currency_name, c.symbol
        ORDER BY issues_count DESC
      `);

      currencyStats.rows.forEach(stat => {
        console.log(`      ${stat.currency_name} (${stat.symbol}):`);
        console.log(`         - عدد القضايا: ${stat.issues_count}`);
        console.log(`         - إجمالي المبلغ: ${stat.total_amount || 0} ${stat.symbol}`);
        console.log(`         - إجمالي بالريال: ${stat.total_amount_yer || 0} ر.ي`);
      });

      // 8. فحص الأعمدة النهائية
      console.log('\n   🔍 فحص الأعمدة النهائية:');
      
      const finalColumns = await pool.query(`
        SELECT 
          column_name, 
          data_type, 
          column_default,
          is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' 
        AND column_name IN ('case_amount', 'currency_id', 'amount_yer', 'start_date', 'status')
        ORDER BY column_name
      `);

      console.log('      الأعمدة المحدثة:');
      finalColumns.rows.forEach(col => {
        console.log(`         - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        if (col.column_default) {
          console.log(`           default: ${col.column_default}`);
        }
      });

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(40) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح نظام العملة');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ إنشاء جدول العملات مع العملات الأساسية');
  console.log('2. ✅ إضافة عمود currency_id لجدول القضايا');
  console.log('3. ✅ إضافة عمود amount_yer (محسوب تلقائياً)');
  console.log('4. ✅ إنشاء دالة حساب المبلغ بالريال اليمني');
  console.log('5. ✅ تحديث القضايا الموجودة');
  
  console.log('\n💡 معلومات مهمة:');
  console.log('- currency_id: معرف العملة (افتراضي: 1 = ريال يمني)');
  console.log('- amount_yer: المبلغ بالريال اليمني (محسوب تلقائياً)');
  console.log('- case_amount: المبلغ بالعملة الأصلية');
  console.log('- يتم حساب amount_yer تلقائياً باستخدام سعر الصرف');
}

// تشغيل الإصلاح
fixIssuesCurrencySystem().catch(console.error);
