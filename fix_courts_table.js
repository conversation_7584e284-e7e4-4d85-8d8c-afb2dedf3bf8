// إصلاح جدول المحاكم وإضافة البيانات الأساسية
const { Pool } = require('pg');

async function fixCourtsTable() {
  console.log('🔧 إصلاح جدول المحاكم...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // فحص الأعمدة الموجودة في جدول المحاكم
      const columnsResult = await pool.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'courts'
        ORDER BY ordinal_position
      `);

      console.log('   📋 الأعمدة الموجودة في جدول المحاكم:');
      const existingColumns = columnsResult.rows.map(col => col.column_name);
      existingColumns.forEach(col => {
        console.log(`      - ${col}`);
      });

      // إضافة الأعمدة المفقودة
      const requiredColumns = [
        { name: 'location', type: 'VARCHAR(255)', comment: 'موقع المحكمة' },
        { name: 'address', type: 'TEXT', comment: 'عنوان المحكمة' },
        { name: 'phone', type: 'VARCHAR(20)', comment: 'هاتف المحكمة' },
        { name: 'email', type: 'VARCHAR(255)', comment: 'بريد المحكمة الإلكتروني' }
      ];

      console.log('\n   ➕ إضافة الأعمدة المفقودة:');
      for (const column of requiredColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            await pool.query(`
              ALTER TABLE courts 
              ADD COLUMN ${column.name} ${column.type}
            `);
            console.log(`      ✅ تم إضافة العمود: ${column.name}`);
          } catch (error) {
            console.log(`      ❌ خطأ في إضافة العمود ${column.name}: ${error.message}`);
          }
        } else {
          console.log(`      ✅ العمود موجود: ${column.name}`);
        }
      }

      // إدراج المحاكم الأساسية
      console.log('\n   🏛️ إدراج المحاكم الأساسية:');
      
      const defaultCourts = [
        { 
          name: 'المحكمة الجنائية الابتدائية', 
          type: 'جنائية', 
          location: 'صنعاء',
          address: 'شارع الزبيري، صنعاء',
          phone: '01-123456'
        },
        { 
          name: 'المحكمة المدنية الابتدائية', 
          type: 'مدنية', 
          location: 'صنعاء',
          address: 'شارع الحصبة، صنعاء',
          phone: '01-123457'
        },
        { 
          name: 'المحكمة التجارية', 
          type: 'تجارية', 
          location: 'صنعاء',
          address: 'شارع التحرير، صنعاء',
          phone: '01-123458'
        },
        { 
          name: 'محكمة الأحوال الشخصية', 
          type: 'أحوال شخصية', 
          location: 'صنعاء',
          address: 'شارع الستين، صنعاء',
          phone: '01-123459'
        },
        { 
          name: 'المحكمة الإدارية', 
          type: 'إدارية', 
          location: 'صنعاء',
          address: 'شارع الجمهورية، صنعاء',
          phone: '01-123460'
        },
        { 
          name: 'محكمة الاستئناف', 
          type: 'استئناف', 
          location: 'صنعاء',
          address: 'شارع الثورة، صنعاء',
          phone: '01-123461'
        },
        { 
          name: 'المحكمة العليا', 
          type: 'عليا', 
          location: 'صنعاء',
          address: 'شارع الوحدة، صنعاء',
          phone: '01-123462'
        }
      ];

      for (const court of defaultCourts) {
        try {
          await pool.query(`
            INSERT INTO courts (name, type, location, address, phone)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (name) DO UPDATE SET
              type = EXCLUDED.type,
              location = EXCLUDED.location,
              address = EXCLUDED.address,
              phone = EXCLUDED.phone,
              updated_at = CURRENT_TIMESTAMP
          `, [court.name, court.type, court.location, court.address, court.phone]);
          
          console.log(`      ✅ تم إدراج/تحديث: ${court.name}`);
        } catch (error) {
          console.log(`      ❌ خطأ في إدراج محكمة ${court.name}: ${error.message}`);
        }
      }

      // عرض المحاكم الموجودة
      console.log('\n   📊 المحاكم الموجودة:');
      const courtsResult = await pool.query(`
        SELECT id, name, type, location, phone 
        FROM courts 
        ORDER BY id
      `);

      courtsResult.rows.forEach(court => {
        console.log(`      ${court.id}. ${court.name} (${court.type}) - ${court.location} - ${court.phone || 'بدون هاتف'}`);
      });

      console.log(`\n   📈 إجمالي المحاكم: ${courtsResult.rows.length}`);

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح جدول المحاكم');
}

// تشغيل الإصلاح
fixCourtsTable().catch(console.error);
