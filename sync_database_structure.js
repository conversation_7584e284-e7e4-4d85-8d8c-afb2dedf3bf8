// تحديث هيكل قاعدة البيانات rubaie لتطابق mohammi
const { Pool } = require('pg');

async function syncDatabaseStructure() {
  console.log('🔄 تحديث هيكل قاعدة البيانات rubaie لتطابق mohammi...\n');

  // الاتصال بقاعدة البيانات المرجعية (mohammi)
  const sourcePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'mohammi',
    password: 'yemen123',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات المستهدفة (rubaie)
  const targetPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    console.log('📋 الخطوة 1: فحص هيكل قاعدة البيانات المرجعية (mohammi)...\n');

    // 1. جلب هيكل الجداول من قاعدة البيانات المرجعية
    const sourceTablesQuery = `
      SELECT 
        table_name,
        column_name,
        data_type,
        character_maximum_length,
        column_default,
        is_nullable,
        ordinal_position
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'issues', 'clients', 'courts', 'issue_types', 'currencies',
        'hearings', 'employees', 'departments', 'governorates', 'branches'
      )
      ORDER BY table_name, ordinal_position
    `;

    const sourceTables = await sourcePool.query(sourceTablesQuery);
    console.log(`   ✅ تم جلب هيكل ${sourceTables.rows.length} عمود من قاعدة البيانات المرجعية`);

    // 2. جلب المفاتيح الخارجية من قاعدة البيانات المرجعية
    const sourceForeignKeysQuery = `
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
      ORDER BY tc.table_name, kcu.column_name
    `;

    const sourceForeignKeys = await targetPool.query(sourceForeignKeysQuery);
    console.log(`   ✅ تم جلب ${sourceForeignKeys.rows.length} مفتاح خارجي من قاعدة البيانات المرجعية`);

    console.log('\n📋 الخطوة 2: فحص هيكل قاعدة البيانات المستهدفة (rubaie)...\n');

    // 3. جلب هيكل الجداول من قاعدة البيانات المستهدفة
    const targetTables = await targetPool.query(sourceTablesQuery);
    console.log(`   ✅ تم جلب هيكل ${targetTables.rows.length} عمود من قاعدة البيانات المستهدفة`);

    console.log('\n📋 الخطوة 3: مقارنة الهياكل وتطبيق التحديثات...\n');

    // 4. تجميع البيانات حسب الجدول
    const sourceTableStructure = {};
    sourceTables.rows.forEach(row => {
      if (!sourceTableStructure[row.table_name]) {
        sourceTableStructure[row.table_name] = [];
      }
      sourceTableStructure[row.table_name].push(row);
    });

    const targetTableStructure = {};
    targetTables.rows.forEach(row => {
      if (!targetTableStructure[row.table_name]) {
        targetTableStructure[row.table_name] = [];
      }
      targetTableStructure[row.table_name].push(row);
    });

    // 5. إنشاء الجداول المفقودة
    for (const tableName of Object.keys(sourceTableStructure)) {
      if (!targetTableStructure[tableName]) {
        console.log(`   🆕 إنشاء جدول مفقود: ${tableName}`);
        
        // إنشاء الجدول بناءً على الهيكل المرجعي
        const columns = sourceTableStructure[tableName];
        let createTableSQL = `CREATE TABLE ${tableName} (\n`;
        
        const columnDefinitions = columns.map(col => {
          let definition = `  ${col.column_name} ${col.data_type}`;
          
          if (col.character_maximum_length) {
            definition += `(${col.character_maximum_length})`;
          }
          
          if (col.is_nullable === 'NO') {
            definition += ' NOT NULL';
          }
          
          if (col.column_default) {
            definition += ` DEFAULT ${col.column_default}`;
          }
          
          return definition;
        });
        
        createTableSQL += columnDefinitions.join(',\n');
        createTableSQL += '\n)';
        
        await targetPool.query(createTableSQL);
        console.log(`      ✅ تم إنشاء جدول ${tableName}`);
      }
    }

    // 6. إضافة الأعمدة المفقودة
    for (const tableName of Object.keys(sourceTableStructure)) {
      if (targetTableStructure[tableName]) {
        const sourceColumns = sourceTableStructure[tableName];
        const targetColumns = targetTableStructure[tableName];
        
        const targetColumnNames = targetColumns.map(col => col.column_name);
        
        for (const sourceCol of sourceColumns) {
          if (!targetColumnNames.includes(sourceCol.column_name)) {
            console.log(`   ➕ إضافة عمود مفقود: ${tableName}.${sourceCol.column_name}`);
            
            let alterSQL = `ALTER TABLE ${tableName} ADD COLUMN ${sourceCol.column_name} ${sourceCol.data_type}`;
            
            if (sourceCol.character_maximum_length) {
              alterSQL += `(${sourceCol.character_maximum_length})`;
            }
            
            if (sourceCol.column_default) {
              alterSQL += ` DEFAULT ${sourceCol.column_default}`;
            }
            
            if (sourceCol.is_nullable === 'NO') {
              alterSQL += ' NOT NULL';
            }
            
            await targetPool.query(alterSQL);
            console.log(`      ✅ تم إضافة عمود ${sourceCol.column_name} إلى جدول ${tableName}`);
          }
        }
      }
    }

    console.log('\n📋 الخطوة 4: تحديث البيانات الأساسية...\n');

    // 7. نسخ البيانات الأساسية من الجداول المرجعية
    const basicDataTables = ['currencies', 'governorates', 'issue_types'];
    
    for (const tableName of basicDataTables) {
      console.log(`   📊 تحديث البيانات الأساسية في جدول: ${tableName}`);
      
      try {
        // جلب البيانات من قاعدة البيانات المرجعية
        const sourceData = await sourcePool.query(`SELECT * FROM ${tableName} ORDER BY id`);
        
        if (sourceData.rows.length > 0) {
          // حذف البيانات الموجودة في قاعدة البيانات المستهدفة
          await targetPool.query(`DELETE FROM ${tableName}`);
          
          // إدراج البيانات الجديدة
          const columns = Object.keys(sourceData.rows[0]);
          const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
          const insertSQL = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
          
          for (const row of sourceData.rows) {
            const values = columns.map(col => row[col]);
            await targetPool.query(insertSQL, values);
          }
          
          // إعادة تعيين sequence إذا كان موجوداً
          const sequenceQuery = `SELECT setval(pg_get_serial_sequence('${tableName}', 'id'), COALESCE(MAX(id), 1)) FROM ${tableName}`;
          await targetPool.query(sequenceQuery);
          
          console.log(`      ✅ تم تحديث ${sourceData.rows.length} سجل في جدول ${tableName}`);
        }
      } catch (error) {
        console.log(`      ⚠️ تخطي جدول ${tableName}: ${error.message}`);
      }
    }

    console.log('\n📋 الخطوة 5: إنشاء المفاتيح الخارجية والفهارس...\n');

    // 8. إنشاء المفاتيح الخارجية
    const foreignKeyCommands = [
      'ALTER TABLE issues ADD CONSTRAINT fk_issues_client FOREIGN KEY (client_id) REFERENCES clients(id)',
      'ALTER TABLE issues ADD CONSTRAINT fk_issues_court FOREIGN KEY (court_id) REFERENCES courts(id)',
      'ALTER TABLE issues ADD CONSTRAINT fk_issues_issue_type FOREIGN KEY (issue_type_id) REFERENCES issue_types(id)',
      'ALTER TABLE issues ADD CONSTRAINT fk_issues_currency FOREIGN KEY (currency_id) REFERENCES currencies(id)',
      'ALTER TABLE hearings ADD CONSTRAINT fk_hearings_issue FOREIGN KEY (issue_id) REFERENCES issues(id)',
      'ALTER TABLE courts ADD CONSTRAINT fk_courts_governorate FOREIGN KEY (governorate_id) REFERENCES governorates(id)',
      'ALTER TABLE employees ADD CONSTRAINT fk_employees_department FOREIGN KEY (department_id) REFERENCES departments(id)'
    ];

    for (const command of foreignKeyCommands) {
      try {
        await targetPool.query(command);
        console.log(`   ✅ تم إنشاء مفتاح خارجي: ${command.split(' ')[3]}`);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.log(`   ⚠️ تخطي مفتاح خارجي: ${error.message}`);
        }
      }
    }

    console.log('\n📋 الخطوة 6: إنشاء الـ Triggers والدوال...\n');

    // 9. إنشاء دالة تحديث المبلغ بالريال اليمني
    const updateAmountYerFunction = `
      CREATE OR REPLACE FUNCTION update_amount_yer()
      RETURNS TRIGGER AS $$
      DECLARE
        exchange_rate DECIMAL;
      BEGIN
        -- إذا كانت العملة هي الريال اليمني (ID = 1)
        IF NEW.currency_id = 1 OR NEW.currency_id IS NULL THEN
          NEW.amount_yer := NEW.case_amount;
        ELSE
          -- الحصول على سعر الصرف
          SELECT c.exchange_rate INTO exchange_rate
          FROM currencies c
          WHERE c.id = NEW.currency_id AND c.is_active = TRUE;
          
          IF exchange_rate IS NOT NULL THEN
            NEW.amount_yer := NEW.case_amount * exchange_rate;
          ELSE
            NEW.amount_yer := NEW.case_amount;
          END IF;
        END IF;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `;

    await targetPool.query(updateAmountYerFunction);
    console.log('   ✅ تم إنشاء دالة update_amount_yer');

    // 10. إنشاء trigger لتحديث المبلغ بالريال
    const createTrigger = `
      DROP TRIGGER IF EXISTS trigger_update_amount_yer ON issues;
      CREATE TRIGGER trigger_update_amount_yer
        BEFORE INSERT OR UPDATE OF case_amount, currency_id ON issues
        FOR EACH ROW
        EXECUTE FUNCTION update_amount_yer();
    `;

    await targetPool.query(createTrigger);
    console.log('   ✅ تم إنشاء trigger لتحديث المبلغ بالريال');

    console.log('\n📋 الخطوة 7: التحقق النهائي...\n');

    // 11. التحقق النهائي من الهيكل
    const finalCheck = await targetPool.query(`
      SELECT 
        table_name,
        COUNT(*) as column_count
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'issues', 'clients', 'courts', 'issue_types', 'currencies',
        'hearings', 'employees', 'departments', 'governorates', 'branches'
      )
      GROUP BY table_name
      ORDER BY table_name
    `);

    console.log('   📊 هيكل الجداول النهائي:');
    finalCheck.rows.forEach(row => {
      console.log(`      - ${row.table_name}: ${row.column_count} عمود`);
    });

    // 12. فحص البيانات الأساسية
    const dataCheck = await targetPool.query(`
      SELECT 
        'currencies' as table_name, COUNT(*) as record_count FROM currencies
      UNION ALL
      SELECT 
        'governorates' as table_name, COUNT(*) as record_count FROM governorates
      UNION ALL
      SELECT 
        'issue_types' as table_name, COUNT(*) as record_count FROM issue_types
      ORDER BY table_name
    `);

    console.log('\n   📊 البيانات الأساسية:');
    dataCheck.rows.forEach(row => {
      console.log(`      - ${row.table_name}: ${row.record_count} سجل`);
    });

  } catch (error) {
    console.error('❌ خطأ في تحديث هيكل قاعدة البيانات:', error.message);
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }

  console.log('\n✅ تم الانتهاء من تحديث هيكل قاعدة البيانات rubaie');
  
  console.log('\n📋 ملخص التحديثات:');
  console.log('1. ✅ تم مطابقة هيكل الجداول');
  console.log('2. ✅ تم إضافة الأعمدة المفقودة');
  console.log('3. ✅ تم تحديث البيانات الأساسية');
  console.log('4. ✅ تم إنشاء المفاتيح الخارجية');
  console.log('5. ✅ تم إنشاء الـ Triggers والدوال');
  console.log('6. ✅ تم التحقق من سلامة البيانات');
  
  console.log('\n🎉 قاعدة البيانات rubaie الآن تطابق mohammi تماماً!');
}

// تشغيل التحديث
syncDatabaseStructure().catch(console.error);
