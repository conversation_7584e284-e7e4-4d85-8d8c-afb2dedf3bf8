'use client'

import { SuperSimpleAccountSelect, SuperSimpleBeneficiarySelect } from '@/components/SuperSimpleSelect'
import { useState } from 'react'

export default function TestPage() {
  const [beneficiary, setBeneficiary] = useState('')
  const [account, setAccount] = useState('')

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-8">اختبار المكونات</h1>
      
      <div className="space-y-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <SuperSimpleBeneficiarySelect
            value={beneficiary}
            onChange={setBeneficiary}
            label="اختر المستفيد"
          />
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <SuperSimpleAccountSelect
            value={account}
            onChange={setAccount}
            label="اختر الحساب"
            accountTypes={['أصول']}
          />
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>المستفيد:</strong> {beneficiary || 'لم يتم الاختيار'}</p>
          <p><strong>الحساب:</strong> {account || 'لم يتم الاختيار'}</p>
        </div>
      </div>
    </div>
  )
}
