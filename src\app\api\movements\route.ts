import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الحركات المالية
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM movements 
      ORDER BY created_date DESC
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching movements:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الحركات المالية' },
      { status: 500 }
    )
  }
}

// POST - إضافة حركة مالية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      case_number, case_title, movement_type, category, amount, 
      description, date, reference_number, status = 'pending', created_by 
    } = body

    if (!case_number || !movement_type || !amount) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية ونوع الحركة والمبلغ مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO movements (
        case_number, case_title, movement_type, category, amount, 
        description, date, reference_number, status, created_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      case_number, case_title, movement_type, category, amount, 
      description, date, reference_number, status, created_by
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الحركة المالية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating movement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الحركة المالية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حركة مالية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      id, case_number, case_title, movement_type, category, amount, 
      description, date, reference_number, status, created_by 
    } = body

    if (!id || !case_number || !movement_type || !amount) {
      return NextResponse.json(
        { success: false, error: 'المعرف ورقم القضية ونوع الحركة والمبلغ مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE movements 
      SET case_number = $1, case_title = $2, movement_type = $3, 
          category = $4, amount = $5, description = $6, date = $7, 
          reference_number = $8, status = $9, created_by = $10, 
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `, [
      case_number, case_title, movement_type, category, amount, 
      description, date, reference_number, status, created_by, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحركة المالية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating movement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحركة المالية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف حركة مالية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحركة المالية مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM movements WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحركة المالية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحركة المالية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting movement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحركة المالية' },
      { status: 500 }
    )
  }
}
