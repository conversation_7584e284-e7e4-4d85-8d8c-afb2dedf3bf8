// Hook لإدارة بيانات الشركة والتخزين المحلي
import { useState, useEffect } from 'react'

interface CompanyData {
  id: number
  name: string
  legal_name: string
  description: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  logo_url: string
  logo_image_url: string
  logo_right_text: string
  logo_left_text: string
  established_date: string
  registration_number: string
  legal_form: string
  capital: number
  tax_number: string
  is_active: boolean
  working_hours: string
  theme_color?: string
  company_name?: string
}

const STORAGE_KEY = 'company_data'
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 ساعة

export function useCompanyData() {
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // تحميل البيانات من التخزين المحلي
  const loadFromStorage = (): CompanyData | null => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (!stored) return null

      const { data, timestamp } = JSON.parse(stored)
      
      // التحقق من انتهاء صلاحية البيانات
      if (Date.now() - timestamp > CACHE_DURATION) {
        localStorage.removeItem(STORAGE_KEY)
        return null
      }

      return data
    } catch (error) {
      console.error('خطأ في تحميل البيانات من التخزين المحلي:', error)
      localStorage.removeItem(STORAGE_KEY)
      return null
    }
  }

  // حفظ البيانات في التخزين المحلي
  const saveToStorage = (data: CompanyData) => {
    try {
      const storageData = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(storageData))
    } catch (error) {
      console.error('خطأ في حفظ البيانات في التخزين المحلي:', error)
    }
  }

  // جلب البيانات من الخادم
  const fetchCompanyData = async (): Promise<CompanyData | null> => {
    try {
      const response = await fetch('/api/company')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (result.success && result.data && result.data.length > 0) {
        return result.data[0] // أخذ أول شركة
      } else {
        throw new Error('لا توجد بيانات شركة متاحة')
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الشركة:', error)
      throw error
    }
  }

  // تحديث البيانات
  const refreshData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await fetchCompanyData()
      if (data) {
        setCompanyData(data)
        saveToStorage(data)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    const initializeData = async () => {
      // محاولة تحميل البيانات من التخزين المحلي أولاً
      const cachedData = loadFromStorage()
      
      if (cachedData) {
        setCompanyData(cachedData)
        setLoading(false)
        
        // جلب البيانات المحدثة في الخلفية
        try {
          const freshData = await fetchCompanyData()
          if (freshData && JSON.stringify(freshData) !== JSON.stringify(cachedData)) {
            setCompanyData(freshData)
            saveToStorage(freshData)
          }
        } catch (error) {
          // في حالة فشل التحديث، نبقي البيانات المخزنة
          console.warn('فشل في تحديث البيانات، سيتم استخدام البيانات المخزنة')
        }
      } else {
        // لا توجد بيانات مخزنة، جلب من الخادم
        await refreshData()
      }
    }

    initializeData()
  }, [])

  // مسح البيانات المخزنة
  const clearCache = () => {
    localStorage.removeItem(STORAGE_KEY)
    setCompanyData(null)
  }

  // الحصول على لون الموضوع
  const getThemeColor = (): string => {
    // من متغيرات البيئة (نظام التوجيه)
    if (typeof window !== 'undefined') {
      const port = window.location.port
      if (port === '7443') return '#cca967' // لون مؤسسة الجرافي
      if (port === '8914') return '#2563eb' // لون شركة الربيعي
    }
    
    // من بيانات الشركة
    if (companyData?.theme_color) {
      return companyData.theme_color
    }
    
    // الافتراضي
    return '#cca967'
  }

  // الحصول على اسم الشركة المناسب
  const getCompanyName = (): string => {
    if (companyData?.company_name) return companyData.company_name
    if (companyData?.name) return companyData.name
    if (companyData?.legal_name) return companyData.legal_name
    
    // من نظام التوجيه
    if (typeof window !== 'undefined') {
      const port = window.location.port
      if (port === '7443') return 'مؤسسة الجرافي للمحاماة'
      if (port === '8914') return 'شركة الربيعي للمحاماة'
    }
    
    return 'النظام القانوني'
  }

  return {
    companyData,
    loading,
    error,
    refreshData,
    clearCache,
    getThemeColor,
    getCompanyName,
    isDataAvailable: !!companyData
  }
}
