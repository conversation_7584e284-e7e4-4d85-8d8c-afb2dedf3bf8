# دليل تشغيل النظام القانوني - نسخة الإنتاج

## 🎯 متطلبات النظام

### 1. **متطلبات الخادم**:
- **نظام التشغيل**: Windows Server 2019 أو أحدث
- **المعالج**: Intel Core i5 أو أفضل
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مُوصى)
- **التخزين**: 50 GB مساحة فارغة كحد أدنى
- **الشبكة**: اتصال إنترنت مستقر

### 2. **البرامج المطلوبة**:
- **Node.js**: الإصدار 18.0 أو أحدث
- **PostgreSQL**: الإصدار 13 أو أحدث
- **Git**: لإدارة الإصدارات (اختياري)

## 🚀 خطوات التشغيل

### الخطوة 1: تحضير البيئة
1. تأكد من تثبيت Node.js و PostgreSQL
2. أنشئ قاعدة بيانات جديدة باسم `legal_system_production`
3. أنشئ مستخدم قاعدة بيانات مخصص للنظام

### الخطوة 2: تكوين النظام
1. انسخ ملف `.env.template` إلى `.env.local`
2. عدّل إعدادات قاعدة البيانات في `.env.local`
3. أضف كلمات مرور قوية للأمان

### الخطوة 3: تشغيل النظام
#### **في Windows**:
```cmd
start-production.bat
```

#### **في Linux**:
```bash
./start-production.sh
```

## 🔐 الأمان والحماية

### 1. **كلمات المرور**:
- استخدم كلمات مرور قوية (12 حرف كحد أدنى)
- غيّر كلمات المرور الافتراضية فوراً

### 2. **قاعدة البيانات**:
- أنشئ نسخ احتياطية يومية
- قيّد الوصول لقاعدة البيانات

## ⚠️ تحذيرات مهمة

1. **لا تشارك ملف `.env.local`** - يحتوي على معلومات حساسة
2. **غيّر كلمات المرور الافتراضية** فوراً
3. **أنشئ نسخ احتياطية منتظمة**

---
**الإصدار**: 1.0.0 Production