/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/issues/[id]/route";
exports.ids = ["app/api/issues/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fissues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fissues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fissues%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fissues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fissues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fissues%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_issues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/issues/[id]/route.ts */ \"(rsc)/./src/app/api/issues/[id]/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_issues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_issues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/issues/[id]/route\",\n        pathname: \"/api/issues/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/issues/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\issues\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_issues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fissues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fissues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fissues%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/issues/[id]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/issues/[id]/route.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب قضية واحدة\nasync function GET(request, { params }) {\n    try {\n        const id = params.id;\n        console.log('GET Issue API: Fetching issue with ID:', id);\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT * FROM issues WHERE id = $1', [\n            id\n        ]);\n        console.log('GET Issue API: Query result:', result.rows);\n        if (result.rows.length === 0) {\n            console.log('GET Issue API: Issue not found with ID:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const issueData = result.rows[0];\n        console.log('GET Issue API: Returning issue data:', issueData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: issueData\n        });\n    } catch (error) {\n        console.error('GET Issue API: Error fetching issue:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات القضية'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث قضية\nasync function PUT(request, { params }) {\n    try {\n        const { id } = await params;\n        const body = await request.json();\n        const { case_number, title, description, client_id, client_name, client_phone, court_name, issue_type, status, amount, notes, contract_method, contract_date } = body;\n        console.log('Received data for update:', {\n            case_number,\n            title,\n            description,\n            client_name,\n            court_name,\n            issue_type,\n            status,\n            amount,\n            notes,\n            contract_method,\n            contract_date\n        });\n        if (!case_number || !title || !client_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رقم القضية والعنوان واسم الموكل مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود القضية\n        const existingIssue = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM issues WHERE id = $1', [\n            id\n        ]);\n        if (existingIssue.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من عدم تكرار رقم القضية (باستثناء القضية الحالية)\n        const duplicateCheck = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM issues WHERE case_number = $1 AND id != $2', [\n            case_number,\n            id\n        ]);\n        if (duplicateCheck.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رقم القضية موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث بيانات القضية\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE issues\n      SET case_number = $1, title = $2, description = $3, client_id = $4, client_name = $5,\n          client_phone = $6, court_name = $7, issue_type = $8, status = $9, amount = $10,\n          notes = $11, contract_method = $12, contract_date = $13,\n          updated_at = CURRENT_TIMESTAMP\n      WHERE id = $14\n      RETURNING *\n    `, [\n            case_number,\n            title,\n            description,\n            client_id || null,\n            client_name,\n            client_phone,\n            court_name,\n            issue_type,\n            status,\n            parseFloat(amount) || 0,\n            notes,\n            contract_method,\n            contract_date,\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث القضية بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error updating issue:', error);\n        console.error('Error details:', {\n            message: error.message,\n            stack: error.stack,\n            code: error.code\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في تحديث القضية: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف قضية\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = await params;\n        console.log('DELETE Issue API: Attempting to delete issue with ID:', id);\n        // التحقق من صحة المعرف\n        if (!id || isNaN(parseInt(id))) {\n            console.log('DELETE Issue API: Invalid ID provided:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف القضية غير صحيح'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود القضية\n        const existingIssue = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id, case_number, title FROM issues WHERE id = $1', [\n            id\n        ]);\n        console.log('DELETE Issue API: Existing issue check result:', existingIssue.rows);\n        if (existingIssue.rows.length === 0) {\n            console.log('DELETE Issue API: Issue not found with ID:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'القضية غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        const issueToDelete = existingIssue.rows[0];\n        console.log('DELETE Issue API: Found issue to delete:', issueToDelete);\n        // التحقق من وجود توزيعات مرتبطة بالقضية\n        const distributionsCheck = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM case_distributions WHERE issue_id = $1', [\n            id\n        ]);\n        if (distributionsCheck.rows.length > 0) {\n            console.log('DELETE Issue API: Issue has distributions, cannot delete');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف القضية لأنها مرتبطة بتوزيعات. يجب حذف التوزيعات أولاً.'\n            }, {\n                status: 400\n            });\n        }\n        // حذف القضية\n        const deleteResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM issues WHERE id = $1 RETURNING *', [\n            id\n        ]);\n        console.log('DELETE Issue API: Delete result:', deleteResult.rows);\n        if (deleteResult.rows.length === 0) {\n            console.log('DELETE Issue API: No rows affected during delete');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'فشل في حذف القضية - لم يتم العثور على القضية'\n            }, {\n                status: 404\n            });\n        }\n        console.log('DELETE Issue API: Successfully deleted issue:', deleteResult.rows[0]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف القضية بنجاح',\n            data: deleteResult.rows[0]\n        });\n    } catch (error) {\n        console.error('DELETE Issue API: Error deleting issue:', error);\n        console.error('DELETE Issue API: Error details:', {\n            message: error.message,\n            stack: error.stack,\n            code: error.code\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `فشل في حذف القضية: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/issues/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fissues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fissues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fissues%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();