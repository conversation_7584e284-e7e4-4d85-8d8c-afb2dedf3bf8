/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/users/route";
exports.ids = ["app/api/auth/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/users/route.ts */ \"(rsc)/./src/app/api/auth/users/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/users/route\",\n        pathname: \"/api/auth/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/users/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\auth\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/users/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/users/route.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// تسجيل دخول مبسط للمستخدمين\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log('📋 البيانات المستلمة:', {\n            username: body.username,\n            hasPassword: !!body.password\n        });\n        const { username, password } = body;\n        // التحقق من البيانات المطلوبة\n        if (!username || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم وكلمة المرور مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن المستخدم مع الأدوار والصلاحيات\n        console.log('🔍 البحث عن المستخدم:', username);\n        const userResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT u.*, e.name as employee_name, ur.display_name as role_display_name\n      FROM users u\n      LEFT JOIN employees e ON u.employee_id = e.id\n      LEFT JOIN user_roles ur ON u.role = ur.role_name\n      WHERE u.username = $1\n    `, [\n            username\n        ]);\n        if (userResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم غير موجود'\n            }, {\n                status: 401\n            });\n        }\n        const user = userResult.rows[0];\n        console.log('👤 بيانات المستخدم:', {\n            id: user.id,\n            username: user.username,\n            status: user.status\n        });\n        // تحقق من كلمة المرور\n        // للمستخدمين الحقيقيين، نتحقق من password_hash\n        // للاختبار، نقبل كلمة المرور = اسم المستخدم أو password_hash\n        const isPasswordValid = password === username || password === user.password_hash || user.password_hash && user.password_hash === password;\n        if (!isPasswordValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'كلمة المرور غير صحيحة'\n            }, {\n                status: 401\n            });\n        }\n        // التحقق من حالة المستخدم\n        if (user.status !== 'active') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'حساب المستخدم غير نشط'\n            }, {\n                status: 403\n            });\n        }\n        // استخدام الصلاحيات الأساسية حسب الدور\n        console.log('🔐 تحديد صلاحيات المستخدم حسب الدور...');\n        let finalPermissions = [];\n        if (user.role === 'admin') {\n            finalPermissions = [\n                'all_permissions',\n                'system_admin',\n                'user_management',\n                'data_management'\n            ];\n        } else if (user.role === 'manager') {\n            finalPermissions = [\n                'user_management',\n                'data_view',\n                'reports'\n            ];\n        } else if (user.role === 'lawyer') {\n            finalPermissions = [\n                'case_management',\n                'client_management',\n                'data_view'\n            ];\n        } else {\n            finalPermissions = [\n                'data_view'\n            ];\n        }\n        console.log('📋 صلاحيات المستخدم:', finalPermissions);\n        // تحديث بيانات تسجيل الدخول\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE users\n      SET last_login = CURRENT_TIMESTAMP,\n          is_online = true,\n          login_attempts = 0\n      WHERE id = $1\n    `, [\n            user.id\n        ]);\n        // إرجاع بيانات المستخدم (بدون كلمة المرور)\n        const { password_hash, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تسجيل الدخول بنجاح',\n            user: {\n                ...userWithoutPassword,\n                name: user.employee_name || user.username,\n                role_display_name: user.role_display_name,\n                permissions: finalPermissions\n            },\n            token: 'simple-token-' + user.id\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تسجيل الدخول:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في تسجيل الدخول: ' + error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - التحقق من حالة تسجيل الدخول\nasync function GET(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المصادقة مطلوب'\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const decoded = jwt.verify(token, JWT_SECRET);\n        // جلب بيانات المستخدم المحدثة\n        const userResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT u.*, e.name as employee_name\n      FROM users u\n      LEFT JOIN employees e ON u.employee_id = e.id\n      WHERE u.id = $1 AND u.status = 'active'\n    `, [\n            decoded.userId\n        ]);\n        if (userResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود أو غير نشط'\n            }, {\n                status: 401\n            });\n        }\n        const user = userResult.rows[0];\n        const { password_hash, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                name: user.employee_name || user.username\n            }\n        });\n    } catch (error) {\n        console.error('Error verifying token:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'رمز المصادقة غير صالح'\n        }, {\n            status: 401\n        });\n    }\n}\n// DELETE - تسجيل الخروج\nasync function DELETE(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المصادقة مطلوب'\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const decoded = jwt.verify(token, JWT_SECRET);\n        // تحديث حالة المستخدم إلى غير متصل\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE users\n      SET is_online = false,\n          last_logout = CURRENT_TIMESTAMP\n      WHERE id = $1\n    `, [\n            decoded.userId\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تسجيل الخروج بنجاح'\n        });\n    } catch (error) {\n        console.error('Error during logout:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في تسجيل الخروج'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();