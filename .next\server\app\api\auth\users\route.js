/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/users/route";
exports.ids = ["app/api/auth/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/users/route.ts */ \"(rsc)/./src/app/api/auth/users/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/users/route\",\n        pathname: \"/api/auth/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/users/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\auth\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_auth_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/users/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/users/route.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// تسجيل دخول مبسط للمستخدمين\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log('📋 البيانات المستلمة:', {\n            username: body.username,\n            hasPassword: !!body.password\n        });\n        const { username, password } = body;\n        // التحقق من البيانات المطلوبة\n        if (!username || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم وكلمة المرور مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن المستخدم مع الأدوار والصلاحيات\n        console.log('🔍 البحث عن المستخدم:', username);\n        const userResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT u.*, e.name as employee_name, ur.display_name as role_display_name\n      FROM users u\n      LEFT JOIN employees e ON u.employee_id = e.id\n      LEFT JOIN user_roles ur ON u.role = ur.role_name\n      WHERE u.username = $1\n    `, [\n            username\n        ]);\n        if (userResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم غير موجود'\n            }, {\n                status: 401\n            });\n        }\n        const user = userResult.rows[0];\n        console.log('👤 بيانات المستخدم:', {\n            id: user.id,\n            username: user.username,\n            status: user.status\n        });\n        // تحقق من كلمة المرور\n        // للمستخدمين الحقيقيين، نتحقق من password_hash\n        // للاختبار، نقبل كلمة المرور = اسم المستخدم أو password_hash\n        const isPasswordValid = password === username || password === user.password_hash || user.password_hash && user.password_hash === password;\n        if (!isPasswordValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'كلمة المرور غير صحيحة'\n            }, {\n                status: 401\n            });\n        }\n        // التحقق من حالة المستخدم\n        if (user.status !== 'active') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'حساب المستخدم غير نشط'\n            }, {\n                status: 403\n            });\n        }\n        // جلب صلاحيات المستخدم من الجدول الجديد\n        console.log('🔐 جلب صلاحيات المستخدم...');\n        const permissionsResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT up.permission_key\n      FROM user_permissions up\n      WHERE up.user_id = $1 AND up.is_active = true\n    `, [\n            user.id\n        ]);\n        const userPermissions = permissionsResult.rows.map((row)=>row.permission_key);\n        console.log('📋 صلاحيات المستخدم:', userPermissions.length, 'صلاحية');\n        // إذا لم توجد صلاحيات في الجدول الجديد، استخدم الصلاحيات القديمة\n        const finalPermissions = userPermissions.length > 0 ? userPermissions : user.permissions || [];\n        // تحديث بيانات تسجيل الدخول\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE users\n      SET last_login = CURRENT_TIMESTAMP,\n          is_online = true,\n          login_attempts = 0\n      WHERE id = $1\n    `, [\n            user.id\n        ]);\n        // إرجاع بيانات المستخدم (بدون كلمة المرور)\n        const { password_hash, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تسجيل الدخول بنجاح',\n            user: {\n                ...userWithoutPassword,\n                name: user.employee_name || user.username,\n                role_display_name: user.role_display_name,\n                permissions: finalPermissions\n            },\n            token: 'simple-token-' + user.id\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تسجيل الدخول:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في تسجيل الدخول: ' + error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - التحقق من حالة تسجيل الدخول\nasync function GET(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المصادقة مطلوب'\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const decoded = jwt.verify(token, JWT_SECRET);\n        // جلب بيانات المستخدم المحدثة\n        const userResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT u.*, e.name as employee_name\n      FROM users u\n      LEFT JOIN employees e ON u.employee_id = e.id\n      WHERE u.id = $1 AND u.status = 'active'\n    `, [\n            decoded.userId\n        ]);\n        if (userResult.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود أو غير نشط'\n            }, {\n                status: 401\n            });\n        }\n        const user = userResult.rows[0];\n        const { password_hash, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                name: user.employee_name || user.username\n            }\n        });\n    } catch (error) {\n        console.error('Error verifying token:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'رمز المصادقة غير صالح'\n        }, {\n            status: 401\n        });\n    }\n}\n// DELETE - تسجيل الخروج\nasync function DELETE(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'رمز المصادقة مطلوب'\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const decoded = jwt.verify(token, JWT_SECRET);\n        // تحديث حالة المستخدم إلى غير متصل\n        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE users\n      SET is_online = false,\n          last_logout = CURRENT_TIMESTAMP\n      WHERE id = $1\n    `, [\n            decoded.userId\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تسجيل الخروج بنجاح'\n        });\n    } catch (error) {\n        console.error('Error during logout:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'حدث خطأ في تسجيل الخروج'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.DB_USER || 'postgres',\n    host: process.env.DB_HOST || 'localhost',\n    database: process.env.DB_NAME || 'mohammi',\n    password: process.env.DB_PASSWORD || process.env.DB_PASSWORD || 'your_password_here',\n    port: parseInt(process.env.DB_PORT || '5432')\n});\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fusers%2Froute&page=%2Fapi%2Fauth%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fusers%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();