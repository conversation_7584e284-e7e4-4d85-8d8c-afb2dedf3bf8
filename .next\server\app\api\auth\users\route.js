(()=>{var e={};e.id=7157,e.ids=[7157],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(s,{P:()=>l});var a=r(64939),o=r(29021),n=r.n(o),u=r(33873),i=r.n(u),c=e([a]);a=(c.then?(await c)():c)[0];let p=null;try{let e=i().join(process.cwd(),"routing.config.json"),s=n().readFileSync(e,"utf8");p=JSON.parse(s)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let d=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let s=p.routes[e],r=p.default_config;return{database:s.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),m=new a.Pool(d);async function l(e,s){let r=await m.connect();try{return await r.query(e,s)}finally{r.release()}}t()}catch(e){t(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19197:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{DELETE:()=>c,GET:()=>i,POST:()=>u});var a=r(32190),o=r(5069),n=e([o]);async function u(e){try{let s=await e.json();console.log("\uD83D\uDCCB البيانات المستلمة:",{username:s.username,hasPassword:!!s.password});let{username:r,password:t}=s;if(!r||!t)return a.NextResponse.json({success:!1,error:"اسم المستخدم وكلمة المرور مطلوبان"},{status:400});console.log("\uD83D\uDD0D البحث عن المستخدم:",r);let n=await (0,o.P)(`
      SELECT u.*, e.name as employee_name, ur.display_name as role_display_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      LEFT JOIN user_roles ur ON u.role = ur.role_name
      WHERE u.username = $1
    `,[r]);if(0===n.rows.length)return a.NextResponse.json({success:!1,error:"اسم المستخدم غير موجود"},{status:401});let u=n.rows[0];if(console.log("\uD83D\uDC64 بيانات المستخدم:",{id:u.id,username:u.username,status:u.status}),!(t===r||t===u.password_hash||u.password_hash&&u.password_hash===t))return a.NextResponse.json({success:!1,error:"كلمة المرور غير صحيحة"},{status:401});if("active"!==u.status)return a.NextResponse.json({success:!1,error:"حساب المستخدم غير نشط"},{status:403});console.log("\uD83D\uDD10 تحديد صلاحيات المستخدم حسب الدور...");let i=[];i="admin"===u.role?["all_permissions","system_admin","user_management","data_management"]:"manager"===u.role?["user_management","data_view","reports"]:"lawyer"===u.role?["case_management","client_management","data_view"]:["data_view"],console.log("\uD83D\uDCCB صلاحيات المستخدم:",i),await (0,o.P)(`
      UPDATE users
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0
      WHERE id = $1
    `,[u.id]);let{password_hash:c,...l}=u;return a.NextResponse.json({success:!0,message:"تم تسجيل الدخول بنجاح",user:{...l,name:u.employee_name||u.username,role_display_name:u.role_display_name,permissions:i},token:"simple-token-"+u.id})}catch(e){return console.error("❌ خطأ في تسجيل الدخول:",e),a.NextResponse.json({success:!1,error:"حدث خطأ في تسجيل الدخول: "+e.message},{status:500})}}async function i(e){try{let s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return a.NextResponse.json({success:!1,error:"رمز المصادقة مطلوب"},{status:401});let r=s.substring(7),t=jwt.verify(r,JWT_SECRET),n=await (0,o.P)(`
      SELECT u.*, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.id = $1 AND u.status = 'active'
    `,[t.userId]);if(0===n.rows.length)return a.NextResponse.json({success:!1,error:"المستخدم غير موجود أو غير نشط"},{status:401});let u=n.rows[0],{password_hash:i,...c}=u;return a.NextResponse.json({success:!0,user:{...c,name:u.employee_name||u.username}})}catch(e){return console.error("Error verifying token:",e),a.NextResponse.json({success:!1,error:"رمز المصادقة غير صالح"},{status:401})}}async function c(e){try{let s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return a.NextResponse.json({success:!1,error:"رمز المصادقة مطلوب"},{status:401});let r=s.substring(7),t=jwt.verify(r,JWT_SECRET);return await (0,o.P)(`
      UPDATE users
      SET is_online = false,
          last_logout = CURRENT_TIMESTAMP
      WHERE id = $1
    `,[t.userId]),a.NextResponse.json({success:!0,message:"تم تسجيل الخروج بنجاح"})}catch(e){return console.error("Error during logout:",e),a.NextResponse.json({success:!1,error:"حدث خطأ في تسجيل الخروج"},{status:500})}}o=(n.then?(await n)():n)[0],t()}catch(e){t(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{},99406:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a=r(96559),o=r(48088),n=r(37719),u=r(19197),i=e([u]);u=(i.then?(await i)():i)[0];let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/users/route",pathname:"/api/auth/users",filename:"route",bundlePath:"app/api/auth/users/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\auth\\users\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:m}=l;function c(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}t()}catch(e){t(e)}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,580],()=>r(99406));module.exports=t})();