import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import { applyAccountLinking } from '@/lib/account-linking'

// GET - جلب الحسابات الرئيسية ودليل الحسابات
export async function GET(request: NextRequest) {
  try {
    // جلب الحسابات الرئيسية
    const mainAccountsResult = await query(`
      SELECT
        ma.id,
        ma.account_name,
        ma.account_code,
        ma.chart_account_id,
        ma.description,
        coa.account_code as chart_account_code,
        coa.account_name as chart_account_name
      FROM main_accounts ma
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY ma.account_name
    `)

    // جلب دليل الحسابات
    const chartAccountsResult = await query(`
      SELECT
        id,
        account_code,
        account_name,
        account_type,
        account_level
      FROM chart_of_accounts
      WHERE is_active = true
      ORDER BY account_code
    `)

    return NextResponse.json({
      success: true,
      mainAccounts: mainAccountsResult.rows,
      chartAccounts: chartAccountsResult.rows
    })

  } catch (error) {
    console.error('خطأ في جلب الحسابات الرئيسية:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الحسابات الرئيسية'
    }, { status: 500 })
  }
}

// PUT - تحديث ربط الحساب
export async function PUT(request: NextRequest) {
  try {
    const {
      id,
      chart_account_id
    } = await request.json()

    if (!id || !chart_account_id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب ومعرف الحساب الأب مطلوبان'
      }, { status: 400 })
    }

    // تحديث ربط الحساب
    const result = await query(`
      UPDATE main_accounts 
      SET 
        chart_account_id = $1,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [chart_account_id, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث ربط الحساب بنجاح',
      account: result.rows[0]
    })

  } catch (error) {
    console.error('خطأ في تحديث ربط الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث ربط الحساب'
    }, { status: 500 })
  }
}

// POST - إضافة حساب رئيسي جديد أو تحديث الربط
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // إذا كانت البيانات تحتوي على mainAccounts فهي لتحديث الربط
    if (body.mainAccounts) {
      // تحديث ربط الحسابات (الكود الموجود مسبقاً)
      const { mainAccounts } = body

      if (!mainAccounts || !Array.isArray(mainAccounts)) {
        return NextResponse.json({
          success: false,
          error: 'بيانات الحسابات مطلوبة'
        }, { status: 400 })
      }

      const linkingResults = []

      for (const account of mainAccounts) {
        // تحديث ربط الحساب
        await query(`
          UPDATE main_accounts
          SET chart_account_id = $1, account_code = $2, updated_at = CURRENT_TIMESTAMP
          WHERE id = $3
        `, [account.chart_account_id, account.account_code, account.id])

        // تطبيق الربط التلقائي للحسابات الفرعية إذا تم ربط حساب جديد
        if (account.chart_account_id) {
          console.log(`🔗 تطبيق الربط التلقائي للحساب ${account.id}`)
          const linkingResult = await applyAccountLinking(account.id, account.chart_account_id)
          linkingResults.push({
            account_id: account.id,
            linking_result: linkingResult
          })
        }
      }

      return NextResponse.json({
        success: true,
        message: 'تم تحديث ربط الحسابات بنجاح',
        linking_results: linkingResults
      })
    } else {
      // إضافة حساب رئيسي جديد
      const { account_name, description, chart_account_id } = body

      if (!account_name?.trim()) {
        return NextResponse.json({
          success: false,
          error: 'اسم الحساب مطلوب'
        }, { status: 400 })
      }

      // التحقق من عدم وجود حساب بنفس الاسم
      const existingAccount = await query(
        'SELECT id FROM main_accounts WHERE LOWER(account_name) = LOWER($1)',
        [account_name.trim()]
      )

      if (existingAccount.rows.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'يوجد حساب رئيسي بهذا الاسم مسبقاً'
        }, { status: 400 })
      }

      const result = await query(`
        INSERT INTO main_accounts (
          account_name,
          description,
          chart_account_id,
          is_required,
          created_date
        ) VALUES ($1, $2, $3, false, CURRENT_DATE)
        RETURNING *
      `, [
        account_name.trim(),
        description?.trim() || null,
        chart_account_id || null
      ])

      let linkingResult = null

      // تطبيق الربط التلقائي إذا تم ربط الحساب بدليل الحسابات
      if (chart_account_id) {
        console.log(`🔗 تطبيق الربط التلقائي للحساب الجديد ${result.rows[0].id}`)
        linkingResult = await applyAccountLinking(result.rows[0].id, chart_account_id)
      }

      return NextResponse.json({
        success: true,
        message: 'تم إضافة الحساب الرئيسي بنجاح',
        data: result.rows[0],
        linking_result: linkingResult
      })
    }
  } catch (error) {
    console.error('Error in POST main accounts:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في معالجة الطلب'
    }, { status: 500 })
  }
}
