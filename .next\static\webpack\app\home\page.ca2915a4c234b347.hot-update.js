"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx":
/*!********************************************************!*\
  !*** ./src/app/home/<USER>/simple-chat-widget.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleChatWidget: () => (/* binding */ SimpleChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ SimpleChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleChatWidget(param) {\n    let { isOpen, onClose, onOpen } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guestId, setGuestId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [companyData, setCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // إنشاء معرف زائر فريد وجلب بيانات الشركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            let guestIdentifier = localStorage.getItem('guestId');\n            if (!guestIdentifier) {\n                guestIdentifier = \"guest_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n                localStorage.setItem('guestId', guestIdentifier);\n            }\n            setGuestId(guestIdentifier);\n            // جلب بيانات الشركة\n            fetchCompanyData();\n        }\n    }[\"SimpleChatWidget.useEffect\"], []);\n    // جلب بيانات الشركة\n    const fetchCompanyData = async ()=>{\n        try {\n            const response = await fetch('/api/companies');\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && result.data.length > 0) {\n                    const company = result.data[0];\n                    setCompanyData(company);\n                    // إنشاء الرسائل الترحيبية\n                    const welcomeMessages = [\n                        {\n                            id: '1',\n                            content: \"مرحباً بك في \".concat(company.name, \"! \\uD83C\\uDFDB️\"),\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        },\n                        {\n                            id: '2',\n                            content: 'أنا مساعدك القانوني الذكي المدعوم بالذكاء الاصطناعي 🤖 يمكنني مساعدتك في الاستشارات القانونية.',\n                            sender: 'assistant',\n                            timestamp: new Date(),\n                            status: 'delivered'\n                        }\n                    ];\n                    setMessages(welcomeMessages);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching company data:', error);\n            // رسائل افتراضية\n            const defaultMessages = [\n                {\n                    id: '1',\n                    content: 'مرحباً! أنا مساعدك القانوني الذكي 🤖',\n                    sender: 'assistant',\n                    timestamp: new Date(),\n                    status: 'delivered'\n                }\n            ];\n            setMessages(defaultMessages);\n        }\n    };\n    // Auto-scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        messages\n    ]);\n    // Focus input when chat is opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatWidget.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"SimpleChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!message.trim()) return;\n        console.log('📤 Sending message:', message);\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            sender: 'user',\n            timestamp: new Date(),\n            status: 'delivered'\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setIsTyping(true);\n        try {\n            // إرسال للذكاء الاصطناعي\n            const response = await sendToAI(message);\n            // Add AI response\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                content: response.message,\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'delivered'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: 'أعتذر، حدث خطأ. يرجى المحاولة مرة أخرى.',\n                sender: 'assistant',\n                timestamp: new Date(),\n                status: 'error'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsTyping(false);\n            // إعادة التركيز على مربع النص بعد الإرسال\n            setTimeout(()=>{\n                if (inputRef.current) {\n                    inputRef.current.focus();\n                }\n            }, 100);\n        }\n    };\n    const sendToAI = async (userMessage)=>{\n        try {\n            console.log('🤖 Sending message to AI:', userMessage);\n            const response = await fetch('/api/ai/local-models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage,\n                    model: 'groq-llama-8b',\n                    conversationId: \"guest_\".concat(guestId, \"_\").concat(Date.now()),\n                    context: []\n                })\n            });\n            if (!response.ok) {\n                throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');\n            }\n            const result = await response.json();\n            console.log('🤖 AI Response:', result);\n            if (result.success && result.response) {\n                return {\n                    message: result.response\n                };\n            } else {\n                throw new Error(result.error || 'خطأ في الاستجابة');\n            }\n        } catch (error) {\n            console.error('AI Error:', error);\n            // رد احتياطي\n            const fallbackResponses = [\n                \"أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: \".concat((companyData === null || companyData === void 0 ? void 0 : companyData.phone) || '+967-1-123456'),\n                'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',\n                'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.'\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return {\n                message: randomResponse\n            };\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onOpen,\n                className: \"h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700 flex items-center justify-center\",\n                title: \"المحادثات مع الذكاء الاصطناعي\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-7 w-7 text-white\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-24 left-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b bg-blue-600 text-white rounded-t-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"المحادثات المباشرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-2 w-2 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"AI\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-100 mr-2\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 rounded-full hover:bg-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.sender === 'user' ? 'justify-end' : 'justify-start'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-sm px-4 py-3 rounded-lg \".concat(msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 opacity-70\",\n                                        children: msg.timestamp.toLocaleTimeString('ar-SA', {\n                                            hour: '2-digit',\n                                            minute: '2-digit'\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 text-gray-800 px-4 py-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"المساعد الذكي يكتب...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSendMessage,\n                    className: \"flex space-x-2 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ref: inputRef,\n                            type: \"text\",\n                            value: message,\n                            onChange: (e)=>setMessage(e.target.value),\n                            placeholder: \"اكتب رسالتك...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                            disabled: isTyping,\n                            dir: \"rtl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center\",\n                            disabled: !message.trim() || isTyping,\n                            children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\components\\\\simple-chat-widget.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatWidget, \"+wvRQPGNvkoLiajW7IFDjiYLFZs=\");\n_c = SimpleChatWidget;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>/simple-chat-widget.tsx\n"));

/***/ })

});