'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'

interface Account {
  id: string | number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
  allow_transactions: boolean
}

interface SimpleFinancialAccountSelectProps {
  value: string
  onChange: (accountId: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
  accountTypes?: string[] // فلترة حسب نوع الحساب
}

export function SimpleFinancialAccountSelect({ 
  value, 
  onChange, 
  label = "الحساب", 
  placeholder = "اختر الحساب المالي...", 
  required = false,
  className = "",
  accountTypes = [] // إذا كان فارغ، يعرض جميع الحسابات المالية
}: SimpleFinancialAccountSelectProps) {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(false)

  const fetchAccounts = async () => {
    setLoading(true)
    try {
      console.log('🔍 SimpleFinancialAccountSelect: بدء جلب الحسابات...')
      
      const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=false')
      const data = await response.json()
      console.log('🔍 SimpleFinancialAccountSelect: بيانات الحسابات:', data)
      
      if (data.success) {
        let filteredAccounts = data.accounts.filter((account: Account) => 
          account.allow_transactions
        )
        
        // فلترة حسب نوع الحساب إذا تم تحديده
        if (accountTypes.length > 0) {
          filteredAccounts = filteredAccounts.filter((account: Account) =>
            accountTypes.includes(account.account_type)
          )
        }
        
        console.log('🔍 SimpleFinancialAccountSelect: الحسابات المفلترة:', filteredAccounts)
        setAccounts(filteredAccounts)
      }
    } catch (error) {
      console.error('❌ SimpleFinancialAccountSelect: خطأ في جلب الحسابات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [])

  // العثور على الحساب المحدد
  const selectedAccount = accounts.find(account => 
    account.id.toString() === value
  )

  const handleSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(event.target.value)
  }

  // تجميع الحسابات حسب النوع
  const groupedAccounts = accounts.reduce((groups, account) => {
    const type = account.account_type
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(account)
    return groups
  }, {} as Record<string, Account[]>)

  return (
    <div className={className}>
      {label && (
        <Label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </Label>
      )}
      
      <select
        value={value || ''}
        onChange={handleSelect}
        disabled={loading}
        className="w-full p-3 border border-gray-300 rounded-md bg-gray-50 focus:border-blue-500 focus:bg-white transition-colors text-sm"
        required={required}
      >
        <option value="">
          {loading ? "جاري التحميل..." : placeholder}
        </option>
        
        {Object.entries(groupedAccounts).map(([accountType, typeAccounts]) => (
          <optgroup key={accountType} label={`${accountType} (${typeAccounts.length})`}>
            {typeAccounts.map((account) => (
              <option 
                key={account.id} 
                value={account.id.toString()}
              >
                {account.account_code} - {account.account_name}
              </option>
            ))}
          </optgroup>
        ))}
        
        {!loading && accounts.length === 0 && (
          <option value="" disabled>
            لا توجد حسابات متاحة
          </option>
        )}
      </select>
      
      {/* عرض الحساب المحدد */}
      {selectedAccount && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-sm">
          <span className="font-medium text-green-700">
            المحدد: {selectedAccount.account_code} - {selectedAccount.account_name}
          </span>
          <span className="text-green-600 mr-2">
            ({selectedAccount.account_type})
          </span>
        </div>
      )}
    </div>
  )
}
