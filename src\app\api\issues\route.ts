import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع القضايا من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM issues ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching القضايا:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات القضايا',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة القضايا جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error creating القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة القضايا' },
      { status: 500 }
    )
  }
}

// PUT - تحديث القضايا
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error updating القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث القضايا' },
      { status: 500 }
    )
  }
}

// DELETE - حذف القضايا
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف القضايا مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM issues WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error deleting القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القضايا' },
      { status: 500 }
    )
  }
}