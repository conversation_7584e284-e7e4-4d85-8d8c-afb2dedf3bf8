# ملفات تصدير قاعدة البيانات

## 📤 تم تصدير قاعدة البيانات بنجاح!

### 📋 الملفات المُصدرة:

#### **1. الملف الكامل (الموصى به)**:
- **الملف**: `legal_system_complete_2025-08-27.sql`
- **الحجم**: 443.35 KB
- **المحتوى**: هيكل قاعدة البيانات + جميع البيانات
- **الاستخدام**: للاستيراد الكامل في خادم جديد

#### **2. ملف الهيكل فقط**:
- **الملف**: `legal_system_schema_2025-08-27.sql`
- **الحجم**: 216.66 KB
- **المحتوى**: هيكل الجداول والفهارس والدوال فقط
- **الاستخدام**: لإنشاء قاعدة بيانات فارغة

#### **3. ملف البيانات فقط**:
- **الملف**: `legal_system_data_2025-08-27.sql`
- **الحجم**: 237.42 KB
- **المحتوى**: البيانات فقط بدون هيكل
- **الاستخدام**: لاستيراد البيانات في قاعدة بيانات موجودة

#### **4. ملفات المساعدة**:
- **IMPORT_INSTRUCTIONS.md**: تعليمات الاستيراد التفصيلية
- **import_database.bat**: سكريپت الاستيراد التلقائي لـ Windows

## 🚀 طريقة الاستيراد السريعة

### **للخادم الجديد (Windows)**:

1. **إنشاء قاعدة البيانات**:
```sql
CREATE DATABASE legal_system_production;
```

2. **استيراد الملف الكامل**:
```cmd
psql -h localhost -U postgres -d legal_system_production -f "legal_system_complete_2025-08-27.sql"
```

3. **إعداد النظام**:
- انسخ ملفات النظام
- أنشئ ملف `.env.local` مع إعدادات قاعدة البيانات
- شغل: `npm install && npm run build && npm start`

## 📊 إحصائيات قاعدة البيانات

### **الجداول الرئيسية**:
- **المستخدمين**: users, user_roles, user_permissions, user_role_assignments
- **القضايا**: issues, follows, hearings, movements
- **العملاء**: clients, client_portal_accounts, client_notifications
- **المحاسبة**: chart_of_accounts, vouchers, journal_entries, main_accounts
- **النظام**: companies, branches, courts, employees

### **المميزات المُصدرة**:
- ✅ **نظام الصلاحيات المتقدم**: 55 صلاحية + 10 أدوار
- ✅ **الأدوار المتعددة**: إمكانية إضافة أكثر من دور للمستخدم
- ✅ **البيانات الأساسية**: الشركات، المحاكم، الفروع
- ✅ **النظام المحاسبي**: دليل حسابات كامل مع الربط التلقائي
- ✅ **نظام الوثائق**: إدارة المستندات والملفات
- ✅ **نظام الدردشة**: تواصل مع العملاء
- ✅ **الذكاء الاصطناعي**: مساعد قانوني ذكي

## 🔧 استكشاف الأخطاء

### **إذا فشل الاستيراد**:
1. تأكد من إصدار PostgreSQL (13+ مطلوب)
2. تحقق من صلاحيات المستخدم
3. تأكد من وجود مساحة كافية على القرص
4. جرب استيراد الهيكل أولاً ثم البيانات

### **إذا لم يعمل تسجيل الدخول**:
1. تشغيل: `node update-production-db.js`
2. تشغيل: `node create-admin-user.js`
3. التحقق من إعدادات .env.local

## 📞 معلومات إضافية

- **تاريخ التصدير**: 2025-08-27
- **إصدار النظام**: 2.0.0 Production
- **حجم قاعدة البيانات**: ~902 KB
- **عدد الجداول**: 80+ جدول
- **عدد الدوال**: 15+ دالة مخصصة

---

**ملاحظة**: تأكد من تحديث إعدادات قاعدة البيانات في ملف `.env.local` بعد الاستيراد.
