(()=>{var e={};e.id=4209,e.ids=[4209],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{P:()=>d});var a=r(64939),s=r(29021),o=r.n(s),c=r(33873),i=r.n(c),u=e([a]);a=(u.then?(await u)():u)[0];let p=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");p=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!p?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let _=(()=>{let e=process.env.PORT||"7443";if(p&&p.routes[e]){let t=p.routes[e],r=p.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),l=new a.Pool(_);async function d(e,t){let r=await l.connect();try{return await r.query(e,t)}finally{r.release()}}n()}catch(e){n(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21513:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GET:()=>c});var a=r(32190),s=r(5069),o=e([s]);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("account_id"),n=t.get("date_from"),o=t.get("date_to");if(!r)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});let c=await (0,s.P)(`
      SELECT 
        id, account_code, account_name, account_type, account_nature,
        opening_balance, current_balance
      FROM chart_of_accounts 
      WHERE id = $1 AND is_active = true
    `,[r]);if(0===c.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});let i=c.rows[0],u=`
      SELECT 
        jed.journal_entry_id,
        je.entry_number,
        je.entry_date,
        je.description as entry_description,
        jed.description as line_description,
        jed.debit_amount,
        jed.credit_amount,
        jed.line_number,
        c.currency_code,
        c.symbol as currency_symbol,
        pm.method_name as payment_method_name,
        je.voucher_type,
        je.payer_name,
        je.beneficiary_name,
        je.reference_number,
        je.created_date
      FROM journal_entry_details jed
      INNER JOIN journal_entries je ON jed.journal_entry_id = je.id
      LEFT JOIN currencies c ON jed.currency_id = c.id
      LEFT JOIN payment_methods pm ON jed.payment_method_id = pm.id
      WHERE jed.account_id = $1
    `,d=[r],p=2;n&&(u+=` AND je.entry_date >= $${p}`,d.push(n),p++),o&&(u+=` AND je.entry_date <= $${p}`,d.push(o),p++),u+=" ORDER BY je.entry_date ASC, je.entry_number ASC, jed.line_number ASC";let _=(await (0,s.P)(u,d)).rows,l=i.opening_balance||0,m=0,y=0,j=_.map(e=>{let t=parseFloat(e.debit_amount)||0,r=parseFloat(e.credit_amount)||0;m+=t,y+=r,"مدين"===i.account_nature?l+=t-r:l+=r-t;let n="قيد يومي",a=e.line_description||e.entry_description;return"receipt"===e.voucher_type?(n="سند قبض",e.payer_name&&(a=`${n} من ${e.payer_name} - ${a}`)):"payment"===e.voucher_type&&(n="سند صرف",e.beneficiary_name&&(a=`${n} إلى ${e.beneficiary_name} - ${a}`)),{...e,transaction_type:n,full_description:a,running_balance:l,debit_amount:t,credit_amount:r}}),h=l,b="مدين"===i.account_nature?h>=0?"مدين":"دائن":h>=0?"دائن":"مدين";return a.NextResponse.json({success:!0,data:{account:{id:i.id,code:i.account_code,name:i.account_name,type:i.account_type,nature:i.account_nature,opening_balance:i.opening_balance||0},period:{date_from:n,date_to:o},transactions:j,summary:{total_debit:m,total_credit:y,final_balance:Math.abs(h),balance_type:b,transactions_count:_.length}},message:"تم جلب كشف الحساب بنجاح"})}catch(e){return console.error("خطأ في جلب كشف الحساب:",e),a.NextResponse.json({success:!1,error:"فشل في جلب كشف الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}s=(o.then?(await o)():o)[0],n()}catch(e){n(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},66658:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>_});var a=r(96559),s=r(48088),o=r(37719),c=r(21513),i=e([c]);c=(i.then?(await i)():i)[0];let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/reports/account-statement/route",pathname:"/api/accounting/reports/account-statement",filename:"route",bundlePath:"app/api/accounting/reports/account-statement/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\reports\\account-statement\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:p,workUnitAsyncStorage:_,serverHooks:l}=d;function u(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:_})}n()}catch(e){n(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580],()=>r(66658));module.exports=n})();