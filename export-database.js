#!/usr/bin/env node

const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')

// إعدادات قاعدة البيانات - يجب تحديثها حسب إعداداتك
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'legal_system_production',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'your_password_here'
}

async function exportDatabase() {
  console.log('📤 تصدير قاعدة البيانات للنظام القانوني')
  console.log('=' .repeat(50))
  
  // التحقق من إعدادات قاعدة البيانات
  if (DB_CONFIG.password === 'your_password_here') {
    console.log('⚠️ تحذير: يرجى تحديث إعدادات قاعدة البيانات في هذا الملف')
    console.log('أو تعيين متغيرات البيئة:')
    console.log('   DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD')
    console.log('')
  }
  
  const timestamp = new Date().toISOString().split('T')[0]
  const exportDir = './database_exports'
  
  // إنشاء مجلد التصدير
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true })
    console.log(`📁 تم إنشاء مجلد: ${exportDir}`)
  }
  
  try {
    // تصدير قاعدة البيانات الكاملة
    console.log('🗄️ تصدير قاعدة البيانات الكاملة...')
    const fullExportFile = `${exportDir}/legal_system_complete_${timestamp}.sql`
    
    await runPgDump(fullExportFile, [
      '--verbose',
      '--clean', 
      '--if-exists',
      '--create',
      '--encoding=UTF8'
    ])
    
    console.log(`✅ تم تصدير قاعدة البيانات الكاملة: ${fullExportFile}`)
    
    // إنشاء ملف تعليمات الاستيراد
    await createImportInstructions(exportDir, timestamp)
    
    // عرض معلومات الملفات المُصدرة
    showExportSummary(exportDir)
    
  } catch (error) {
    console.error('❌ خطأ في تصدير قاعدة البيانات:', error.message)
    console.log('\n💡 نصائح لحل المشكلة:')
    console.log('1. تأكد من تشغيل PostgreSQL')
    console.log('2. تحقق من صحة إعدادات الاتصال')
    console.log('3. تأكد من وجود pg_dump في PATH')
    console.log('4. تحقق من صلاحيات المستخدم')
  }
}

function runPgDump(outputFile, options = []) {
  return new Promise((resolve, reject) => {
    const baseOptions = [
      `-h ${DB_CONFIG.host}`,
      `-p ${DB_CONFIG.port}`,
      `-U ${DB_CONFIG.username}`,
      `-d ${DB_CONFIG.database}`,
      `-f "${outputFile}"`
    ]
    
    const allOptions = [...baseOptions, ...options]
    const command = `pg_dump ${allOptions.join(' ')}`
    
    console.log(`   💾 حفظ في: ${outputFile}`)
    
    const childProcess = exec(command, {
      env: { ...process.env, PGPASSWORD: DB_CONFIG.password }
    })
    
    childProcess.on('close', (code) => {
      if (code === 0) {
        resolve()
      } else {
        reject(new Error(`pg_dump خرج برمز خطأ: ${code}`))
      }
    })
    
    childProcess.stderr.on('data', (data) => {
      const message = data.toString().trim()
      if (!message.includes('NOTICE') && message) {
        console.log(`   📝 ${message}`)
      }
    })
    
    childProcess.on('error', (error) => {
      reject(new Error(`فشل في تشغيل pg_dump: ${error.message}`))
    })
  })
}

async function createImportInstructions(exportDir, timestamp) {
  const instructions = `# تعليمات استيراد قاعدة البيانات

## 📋 الملفات المُصدرة
- \`legal_system_complete_${timestamp}.sql\` - قاعدة البيانات الكاملة

## 🚀 خطوات الاستيراد

### 1. إعداد الخادم الجديد
\`\`\`bash
# تثبيت PostgreSQL 13+ إذا لم يكن مثبتاً
# إنشاء قاعدة بيانات جديدة
createdb -U postgres legal_system_production
\`\`\`

### 2. استيراد قاعدة البيانات
\`\`\`bash
# Windows
psql -h localhost -U postgres -d legal_system_production -f "legal_system_complete_${timestamp}.sql"

# Linux/Mac  
psql -h localhost -U postgres -d legal_system_production -f legal_system_complete_${timestamp}.sql
\`\`\`

### 3. إعداد النظام
1. انسخ ملفات النظام إلى الخادم الجديد
2. أنشئ ملف \`.env.local\` مع إعدادات قاعدة البيانات:
\`\`\`
DB_HOST=localhost
DB_PORT=5432
DB_NAME=legal_system_production
DB_USER=postgres
DB_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters
\`\`\`

3. تشغيل النظام:
\`\`\`bash
npm install
npm run build
npm start
\`\`\`

## ✅ التحقق من نجاح الاستيراد

\`\`\`sql
-- التحقق من الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- التحقق من البيانات
SELECT COUNT(*) as users_count FROM users;
SELECT COUNT(*) as permissions_count FROM permissions;
SELECT COUNT(*) as roles_count FROM user_roles;
\`\`\`

## 🔧 استكشاف الأخطاء

### إذا فشل الاستيراد:
1. تأكد من إصدار PostgreSQL (13+ مطلوب)
2. تحقق من صلاحيات المستخدم
3. تأكد من وجود مساحة كافية على القرص
4. جرب استيراد الملف على دفعات

### إذا لم يعمل تسجيل الدخول:
1. تشغيل: \`node update-production-db.js\`
2. تشغيل: \`node create-admin-user.js\`
3. التحقق من إعدادات .env.local

---
تاريخ التصدير: ${new Date().toLocaleString('ar-EG')}
الإصدار: 2.0.0 Production
نظام الصلاحيات: متقدم (55 صلاحية + 10 أدوار)
`

  const instructionsFile = `${exportDir}/IMPORT_INSTRUCTIONS.md`
  fs.writeFileSync(instructionsFile, instructions)
  console.log(`📝 تم إنشاء تعليمات الاستيراد: ${instructionsFile}`)
}

function showExportSummary(exportDir) {
  console.log('\n📊 ملخص التصدير')
  console.log('=' .repeat(30))
  
  const files = fs.readdirSync(exportDir)
  let totalSize = 0
  
  files.forEach(file => {
    const filePath = path.join(exportDir, file)
    const stats = fs.statSync(filePath)
    const sizeKB = (stats.size / 1024).toFixed(2)
    totalSize += stats.size
    
    console.log(`📄 ${file}`)
    console.log(`   💾 الحجم: ${sizeKB} KB`)
    console.log(`   📅 التاريخ: ${stats.mtime.toLocaleString('ar-EG')}`)
    console.log('')
  })
  
  console.log(`📦 إجمالي الحجم: ${(totalSize / 1024).toFixed(2)} KB`)
  console.log(`📁 المجلد: ${path.resolve(exportDir)}`)
  
  console.log('\n🎯 الخطوات التالية:')
  console.log('1. انسخ مجلد database_exports إلى الخادم الجديد')
  console.log('2. اتبع التعليمات في IMPORT_INSTRUCTIONS.md')
  console.log('3. اختبر تسجيل الدخول بعد الاستيراد')
}

// تشغيل السكريپت
if (require.main === module) {
  exportDatabase()
}

module.exports = { exportDatabase }
