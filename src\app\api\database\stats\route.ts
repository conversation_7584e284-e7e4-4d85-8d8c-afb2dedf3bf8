import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: process.env.DB_PASSWORD || 'your_password_here',
})

export async function GET() {
  try {
    const client = await pool.connect()
    
    // إحصائيات قاعدة البيانات
    const statsQuery = `
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') as table_count,
        (SELECT SUM(n_live_tup) FROM pg_stat_user_tables) as total_rows,
        current_database() as database_name,
        version() as postgres_version
    `
    
    const statsResult = await client.query(statsQuery)
    
    // أكثر الجداول استخداماً
    const topTablesQuery = `
      SELECT 
        relname as table_name,
        n_live_tup as row_count,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        pg_size_pretty(pg_total_relation_size(relid)) as size
      FROM pg_stat_user_tables 
      ORDER BY n_live_tup DESC 
      LIMIT 10
    `
    
    const topTablesResult = await client.query(topTablesQuery)
    
    client.release()
    
    return NextResponse.json({
      success: true,
      stats: {
        ...statsResult.rows[0],
        top_tables: topTablesResult.rows
      }
    })
    
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إحصائيات قاعدة البيانات'
    }, { status: 500 })
  }
}
