(()=>{var e={};e.id=3742,e.ids=[3742],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>d});var c=r(64939),s=r(29021),n=r.n(s),o=r(33873),i=r.n(o),u=e([c]);c=(u.then?(await u)():u)[0];let l=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],r=l.default_config;return{database:t.database,user:r.db_user,host:r.db_host,password:process.env.DB_PASSWORD||r.db_password,port:r.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new c.Pool(p);async function d(e,t){let r=await _.connect();try{return await r.query(e,t)}finally{r.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30220:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var c=r(96559),s=r(48088),n=r(37719),o=r(39718),i=e([o]);o=(i.then?(await i)():i)[0];let d=new c.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/chart-of-accounts/link/route",pathname:"/api/accounting/chart-of-accounts/link",filename:"route",bundlePath:"app/api/accounting/chart-of-accounts/link/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\chart-of-accounts\\link\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:_}=d;function u(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},33873:e=>{"use strict";e.exports=require("path")},39718:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{POST:()=>o});var c=r(32190),s=r(5069),n=e([s]);async function o(e){try{let{account_id:t,link_type:r}=await e.json();if(!t||!r)return c.NextResponse.json({success:!1,error:"معرف الحساب ونوع الربط مطلوبان"},{status:400});let a=await (0,s.P)("SELECT id, account_code, account_name FROM chart_of_accounts WHERE id = $1",[t]);if(0===a.rows.length)return c.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});let n=a.rows[0],o={created_accounts:0,linked_records:0};switch(r){case"clients":o=await i(n);break;case"employees":o=await u(n);break;case"suppliers":o=await d(n);break;default:return c.NextResponse.json({success:!1,error:"نوع ربط غير صحيح"},{status:400})}return c.NextResponse.json({success:!0,message:`تم ربط ${o.linked_records} ${function(e){switch(e){case"clients":return"عميل";case"employees":return"موظف";case"suppliers":return"مورد";default:return"سجل"}}(r)} بالحساب ${n.account_code} مباشرة`,details:o})}catch(e){return console.error("Error in chart accounts linking:",e),c.NextResponse.json({success:!1,error:"حدث خطأ في تطبيق الربط"},{status:500})}}async function i(e){try{let t=await (0,s.P)("SELECT id, name FROM clients WHERE status = $1 ORDER BY id",["active"]),r=0,a=0;for(let c of t.rows)try{let t,n=`${e.account_code}${String(c.id).padStart(3,"0")}`,o=`عميل: ${c.name}`,i=await (0,s.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);i.rows.length>0?t=i.rows[0].id:(t=(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[n,o,e.account_type,e.id,(e.account_level||1)+1])).rows[0].id,r++),await (0,s.P)("UPDATE clients SET account_id = $1 WHERE id = $2",[t,c.id]),a++}catch(e){console.error(`خطأ في ربط العميل ${c.name}:`,e)}return{created_accounts:r,linked_records:a}}catch(e){return console.error("Error in linkClients:",e),{created_accounts:0,linked_records:0}}}async function u(e){try{let t=await (0,s.P)("SELECT id, name FROM employees WHERE status = $1 ORDER BY id",["active"]),r=0,a=0;for(let c of t.rows)try{let t,n=`${e.account_code}${String(c.id).padStart(3,"0")}`,o=`موظف: ${c.name}`,i=await (0,s.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);i.rows.length>0?t=i.rows[0].id:(t=(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[n,o,e.account_type,e.id,(e.account_level||1)+1])).rows[0].id,r++),await (0,s.P)("UPDATE employees SET account_id = $1 WHERE id = $2",[t,c.id]),a++}catch(e){console.error(`خطأ في ربط الموظف ${c.name}:`,e)}return{created_accounts:r,linked_records:a}}catch(e){return console.error("Error in linkEmployees:",e),{created_accounts:0,linked_records:0}}}async function d(e){try{let t=await (0,s.P)(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `);if(0===t.rows.length)return{created_accounts:0,linked_records:0};let r=await (0,s.P)("SELECT id, name FROM suppliers WHERE status = $1 ORDER BY id",["active"]),a=0,c=0;for(let t of r.rows)try{let r,n=`${e.account_code}${String(t.id).padStart(3,"0")}`,o=`مورد: ${t.name}`,i=await (0,s.P)("SELECT id FROM chart_of_accounts WHERE account_code = $1",[n]);i.rows.length>0?r=i.rows[0].id:(r=(await (0,s.P)(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `,[n,o,e.account_type,e.id,(e.account_level||1)+1])).rows[0].id,a++),await (0,s.P)("UPDATE suppliers SET account_id = $1 WHERE id = $2",[r,t.id]),c++}catch(e){console.error(`خطأ في ربط المورد ${t.name}:`,e)}return{created_accounts:a,linked_records:c}}catch(e){return console.error("Error in linkSuppliers:",e),{created_accounts:0,linked_records:0}}}s=(n.then?(await n)():n)[0],a()}catch(e){a(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(30220));module.exports=a})();