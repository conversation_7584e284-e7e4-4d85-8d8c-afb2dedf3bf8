// إصلاح مبسط للعمود next_hearing
const { Pool } = require('pg');

async function simpleFixNextHearing() {
  console.log('🔧 إصلاح مبسط للعمود next_hearing...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. إنشاء جدول الجلسات
      console.log('\n   📅 إنشاء جدول الجلسات:');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS hearings (
          id SERIAL PRIMARY KEY,
          issue_id INTEGER NOT NULL,
          hearing_date TIMESTAMP NOT NULL,
          hearing_type VARCHAR(100),
          status VARCHAR(50) DEFAULT 'scheduled',
          notes TEXT,
          judge_name VARCHAR(255),
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('      ✅ تم إنشاء جدول الجلسات');

      // 2. إضافة العمود next_hearing
      console.log('\n   📋 إضافة العمود next_hearing:');
      
      const columnExists = await pool.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'next_hearing'
      `);

      if (columnExists.rows.length === 0) {
        await pool.query(`ALTER TABLE issues ADD COLUMN next_hearing TIMESTAMP`);
        console.log('      ✅ تم إضافة العمود next_hearing');
      } else {
        console.log('      ✅ العمود next_hearing موجود');
      }

      // 3. إضافة المفتاح الخارجي
      console.log('\n   🔗 إضافة المفتاح الخارجي:');
      
      try {
        await pool.query(`
          ALTER TABLE hearings 
          ADD CONSTRAINT fk_hearings_issue_id 
          FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE
        `);
        console.log('      ✅ تم إضافة المفتاح الخارجي');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('      ✅ المفتاح الخارجي موجود');
        } else {
          console.log(`      ⚠️ خطأ في المفتاح الخارجي: ${error.message}`);
        }
      }

      // 4. إدراج بيانات تجريبية
      console.log('\n   📅 إدراج بيانات جلسات تجريبية:');
      
      const issues = await pool.query(`SELECT id, case_number FROM issues LIMIT 3`);
      
      for (const issue of issues.rows) {
        // جلسة قادمة
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 10);
        
        try {
          await pool.query(`
            INSERT INTO hearings (issue_id, hearing_date, hearing_type, status, notes)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT DO NOTHING
          `, [issue.id, futureDate, 'مرافعة', 'scheduled', 'جلسة مرافعة']);
          
          console.log(`      ✅ ${issue.case_number}: تم إدراج جلسة`);
        } catch (error) {
          console.log(`      ⚠️ ${issue.case_number}: ${error.message}`);
        }
      }

      // 5. تحديث العمود next_hearing
      console.log('\n   🔄 تحديث العمود next_hearing:');
      
      const updateResult = await pool.query(`
        UPDATE issues 
        SET next_hearing = (
          SELECT MIN(hearing_date)
          FROM hearings 
          WHERE hearings.issue_id = issues.id 
          AND hearing_date > CURRENT_TIMESTAMP
          AND status = 'scheduled'
        )
        WHERE EXISTS (
          SELECT 1 FROM hearings 
          WHERE hearings.issue_id = issues.id 
          AND hearing_date > CURRENT_TIMESTAMP
          AND status = 'scheduled'
        )
        RETURNING case_number, next_hearing
      `);

      console.log(`      ✅ تم تحديث ${updateResult.rows.length} قضية:`);
      updateResult.rows.forEach(row => {
        console.log(`         - ${row.case_number}: ${row.next_hearing}`);
      });

      // 6. عرض النتائج
      console.log('\n   📊 النتائج النهائية:');
      
      const results = await pool.query(`
        SELECT 
          i.case_number,
          i.next_hearing,
          COUNT(h.id) as total_hearings
        FROM issues i
        LEFT JOIN hearings h ON i.id = h.issue_id
        GROUP BY i.id, i.case_number, i.next_hearing
        ORDER BY i.case_number
      `);

      results.rows.forEach(row => {
        console.log(`      📄 ${row.case_number}:`);
        console.log(`         - الجلسة القادمة: ${row.next_hearing || 'لا توجد'}`);
        console.log(`         - إجمالي الجلسات: ${row.total_hearings}`);
      });

    } catch (error) {
      console.error(`   ❌ خطأ في قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(40) + '\n');
  }

  console.log('✅ تم الانتهاء من الإصلاح المبسط');
}

// تشغيل الإصلاح
simpleFixNextHearing().catch(console.error);
