'use client';

import { useState, useEffect } from 'react';
import { Search, FileText, Download, BookOpen, Eye, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

type LegalFile = {
  id: string;
  name: string;
  originalName: string;
  path: string;
  category: string;
  type: 'pdf' | 'txt';
  size: number;
};

type DocumentCategory = {
  id: string;
  name: string;
  count: number;
  icon: any;
};

export function LegalLibrarySectionNew() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [showAllDocuments, setShowAllDocuments] = useState(false);
  const [files, setFiles] = useState<LegalFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // جلب الملفات من API
  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/legal-library');
      const result = await response.json();
      
      if (result.success) {
        setFiles(result.data);
      } else {
        setError('فشل في جلب الملفات');
      }
    } catch (error) {
      console.error('Error fetching files:', error);
      setError('حدث خطأ في جلب الملفات');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء الفئات بناءً على الملفات المتاحة
  const categories: DocumentCategory[] = [
    { id: 'all', name: 'الكل', count: files.length, icon: BookOpen },
    ...Array.from(new Set(files.map(f => f.category))).map(category => ({
      id: category,
      name: category,
      count: files.filter(f => f.category === category).length,
      icon: FileText
    }))
  ];

  // تصفية الملفات
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         file.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         file.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || file.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const displayedFiles = showAllDocuments 
    ? filteredFiles 
    : filteredFiles.slice(0, 8);

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FileText className="w-5 h-5 text-yellow-500" />;
      case 'txt':
        return <FileText className="w-5 h-5 text-yellow-400" />;
      default:
        return <FileText className="w-5 h-5 text-yellow-300" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleViewFile = (file: LegalFile) => {
    // فتح الملف للمشاهدة في نافذة جديدة
    window.open(`/api/legal-library/view?id=${file.id}`, '_blank');
  };

  const handleDownloadFile = (file: LegalFile) => {
    const link = document.createElement('a');
    link.href = `/api/legal-library/download?id=${file.id}`;
    link.download = file.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="library" className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #171717 100%)' }}>
      <div className="container mx-auto px-4">
        {/* Legal Library Quick Access Banner */}
        <div className="mb-12">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                  <BookOpen className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-1">المكتبة القانونية</h3>
                  <p className="text-blue-100">تصفح وتحميل الوثائق القانونية والقوانين اليمنية</p>
                </div>
              </div>
              <Button
                onClick={() => {
                  const searchSection = document.querySelector('#library .bg-white');
                  searchSection?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-6 py-2"
              >
                تصفح المكتبة
              </Button>
            </div>
          </div>
        </div>

        <div className="text-center mb-12">
          <span className="inline-block bg-yellow-500 text-gray-900 text-sm font-semibold px-4 py-1 rounded-full mb-4">
            المكتبة القانونية
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">وثائق قانونية للتحميل</h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            تصفح وتحميل أحدث الوثائق القانونية والأنظمة واللوائح من المسار:
            <br />
            <code className="text-sm bg-gray-800 text-yellow-400 px-2 py-1 rounded mt-2 inline-block">
              /home/<USER>/Downloads/legal-system/laws/
            </code>
          </p>
          <div className="w-20 h-1 bg-yellow-500 mx-auto mt-6"></div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="البحث في الوثائق..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10 text-right"
                  dir="rtl"
                />
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={activeCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveCategory(category.id)}
                  className={`${
                    activeCategory === category.id 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:text-blue-600'
                  }`}
                >
                  <category.icon className="w-4 h-4 ml-1" />
                  {category.name} ({category.count})
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Files Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto"></div>
            <p className="mt-4 text-gray-300">جاري تحميل الملفات...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12 bg-gray-800 rounded-xl border border-gray-600">
            <FileText className="w-12 h-12 mx-auto text-yellow-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">خطأ في تحميل الملفات</h3>
            <p className="text-gray-300 max-w-md mx-auto">{error}</p>
            <Button
              onClick={fetchFiles}
              className="mt-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900"
            >
              إعادة المحاولة
            </Button>
          </div>
        ) : filteredFiles.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {displayedFiles.map((file) => (
                <div 
                  key={file.id}
                  className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col h-full"
                >
                  <div className="p-5 flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        {getFileIcon(file.type)}
                        <span className="text-xs text-gray-500 mr-2">.{file.type.toUpperCase()}</span>
                      </div>
                      <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2" title={file.originalName}>
                      {file.name}
                    </h3>
                    <div className="flex items-center justify-between mt-4">
                      <span className="text-xs px-3 py-1 bg-blue-50 text-blue-600 rounded-full">
                        {file.category}
                      </span>
                    </div>
                  </div>
                  <div className="p-4 bg-gray-50 border-t">
                    <div className="flex gap-2">
                      <Button
                        className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-gray-900 text-sm"
                        onClick={() => handleViewFile(file)}
                      >
                        <Eye className="w-4 h-4 ml-1" />
                        مشاهدة
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1 border-yellow-500 text-yellow-500 hover:bg-yellow-50 text-sm"
                        onClick={() => handleDownloadFile(file)}
                      >
                        <Download className="w-4 h-4 ml-1" />
                        تحميل
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredFiles.length > 8 && !showAllDocuments && (
              <div className="text-center mt-8">
                <Button
                  variant="outline"
                  onClick={() => setShowAllDocuments(true)}
                  className="px-8 py-3 text-yellow-500 border-yellow-500 hover:bg-yellow-500 hover:text-gray-900"
                >
                  عرض جميع الملفات ({filteredFiles.length})
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12 bg-gray-800 rounded-xl border border-gray-600">
            <FileText className="w-12 h-12 mx-auto text-yellow-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">لا توجد ملفات متطابقة</h3>
            <p className="text-gray-300 max-w-md mx-auto">
              لم نتمكن من العثور على ملفات تطابق بحثك. حاول استخدام كلمات بحث مختلفة.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
