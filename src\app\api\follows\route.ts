import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المتابعات من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM follows ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching المتابعات:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات المتابعات',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة المتابعات جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة المتابعات بنجاح'
    })
  } catch (error) {
    console.error('Error creating المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المتابعات' },
      { status: 500 }
    )
  }
}

// PUT - تحديث المتابعات
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث المتابعات بنجاح'
    })
  } catch (error) {
    console.error('Error updating المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المتابعات' },
      { status: 500 }
    )
  }
}

// DELETE - حذف المتابعات
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المتابعات مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM follows WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف المتابعات بنجاح'
    })
  } catch (error) {
    console.error('Error deleting المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المتابعات' },
      { status: 500 }
    )
  }
}