import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع المتابعات مع تفاصيل القضايا والخدمات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    let whereClause = ''
    let params: any[] = []

    // إذا تم تمرير user_id، فلتر القضايا حسب المستخدم
    if (userId) {
      // جلب employee_id للمستخدم
      const userResult = await query(`SELECT employee_id FROM users WHERE id = $1`, [userId])
      const employeeId = userResult.rows.length > 0 ? userResult.rows[0].employee_id : userId

      whereClause = `
        WHERE i.id IN (
          SELECT DISTINCT cd.issue_id
          FROM case_distribution cd
          JOIN service_distributions sd ON cd.id = sd.case_distribution_id
          WHERE sd.lawyer_id = $1
        )
      `
      params = [employeeId]
    }

    const result = await query(`
      SELECT
        f.*,
        i.case_number,
        i.title as case_title,
        i.client_name,
        s.name as service_name,
        h.hearing_date as next_hearing_date,
        h.hearing_time as next_hearing_time,
        h.court_name as hearing_court
      FROM follows f
      LEFT JOIN issues i ON f.case_id = i.id
      LEFT JOIN services s ON f.service_id = s.id
      LEFT JOIN hearings h ON f.next_hearing_id = h.id
      ${whereClause}
      ORDER BY f.created_date DESC
    `, params)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching follows:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المتابعات' },
      { status: 500 }
    )
  }
}

// POST - إضافة متابعة جديدة
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    console.log('API المتابعات - Authorization header:', authHeader)
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('API المتابعات - رمز المصادقة مفقود أو غير صحيح')
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      case_id, service_id, report, date_field, status = 'pending', user_id, next_hearing_id
    } = body

    console.log('API المتابعات - البيانات المستلمة:', {
      case_id, service_id, user_id, report: report?.substring(0, 50)
    })

    // التحقق من صلاحيات المستخدم
    const userResult = await query(`
      SELECT u.id, u.username, u.user_type, u.permissions, u.role
      FROM users u
      WHERE u.id = $1 AND u.is_active = true
    `, [user_id])

    console.log('API المتابعات - نتيجة البحث عن المستخدم:', {
      found: userResult.rows.length > 0,
      user: userResult.rows[0]?.username
    })

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود أو غير نشط' },
        { status: 403 }
      )
    }

    const user = userResult.rows[0]
    
    
    // التحقق من الصلاحيات (معطل مؤقتاً)
    /*
    const canAddFollows = user.user_type === 'admin' ||
                         (Array.isArray(user.permissions) && (
                           user.permissions.includes('add_follows') ||
                           user.permissions.includes('manage_follows')
                         ))

    console.log('API المتابعات - فحص الصلاحيات:', {
      user_type: user.user_type,
      permissions: user.permissions,
      canAddFollows
    })

    if (!canAddFollows) {
      console.log('API المتابعات - تم رفض الصلاحية')
      return NextResponse.json(
        { success: false, error: 'غير مخول لإضافة المتابعات' },
        { status: 403 }
      )
    }
    */

    console.log('API المتابعات - الصلاحيات معطلة مؤقتاً - السماح لجميع المستخدمين')

    if (!case_id || !service_id || !report) {
      return NextResponse.json(
        { success: false, error: 'القضية ونوع الخدمة والتقرير مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO follows (
        case_id, service_id, report, date_field, status, user_id, next_hearing_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      case_id, service_id, report, date_field, status, user_id, next_hearing_id
    ])

    const followId = result.rows[0].id

    // تسجيل المبلغ المكتسب للمحامي تلقائياً
    try {
      // أولاً، جلب معلومات توزيع الخدمة لتحديد نوع المبلغ
      const allocationResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:7443'}/api/follows/service-allocation?case_id=${case_id}&service_id=${service_id}&lawyer_id=${user_id}`)
      const allocationData = await allocationResponse.json()

      let earning_amount = 100 // افتراضي 100% من المبلغ المخصص

      if (allocationData.success && allocationData.data) {
        const allocation = allocationData.data

        // 🔧 المنطق الصحيح لحساب المبلغ المكتسب:
        // جميع الخدمات: المحامي يحصل على المبلغ المخصص كاملاً عند إضافة متابعة
        // الفرق فقط في عدد المتابعات المسموحة:
        // - النسبة المئوية: متابعة واحدة فقط
        // - المبلغ الثابت: متابعات متعددة

        if (allocation.allocated_amount <= 100) {
          // نسبة مئوية: يحصل على المبلغ المخصص كاملاً (متابعة واحدة فقط)
          earning_amount = allocation.allocated_amount // المبلغ المخصص كاملاً
        } else {
          // مبلغ ثابت: يحصل على المبلغ المخصص كاملاً (متابعات متعددة مسموحة)
          earning_amount = allocation.allocated_amount // المبلغ المخصص كاملاً
        }
      }

      const earningResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:7443'}/api/follows/service-allocation`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lawyer_id: user_id,
          case_id: case_id,
          service_id: service_id,
          follow_id: followId,
          earning_percentage: earning_amount,
          notes: `مكتسب من متابعة: ${report.substring(0, 50)}...`
        })
      })

      const earningResult = await earningResponse.json()

      return NextResponse.json({
        success: true,
        message: 'تم إضافة المتابعة بنجاح',
        data: result.rows[0],
        earning: earningResult.success ? earningResult.data : null,
        earning_message: earningResult.message || null
      })
    } catch (earningError) {
      console.error('Error recording earning:', earningError)

      // إرجاع نجاح المتابعة حتى لو فشل تسجيل المبلغ
      return NextResponse.json({
        success: true,
        message: 'تم إضافة المتابعة بنجاح (تحذير: لم يتم تسجيل المبلغ المكتسب)',
        data: result.rows[0],
        earning_warning: 'فشل في تسجيل المبلغ المكتسب تلقائياً'
      })
    }
  } catch (error) {
    console.error('Error creating follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المتابعة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث متابعة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, case_id, service_id, report, date_field, status, user_id, next_hearing_id
    } = body

    // فحص الصلاحيات - مؤقتاً متاح للأدمن فقط (user_id = 1)
    if (user_id !== 1) {
      return NextResponse.json(
        { success: false, error: 'غير مخول لتعديل المتابعات. الصلاحية متاحة للأدمن فقط حالياً.' },
        { status: 403 }
      )
    }

    if (!id || !case_id || !service_id || !report) {
      return NextResponse.json(
        { success: false, error: 'المعرف والقضية ونوع الخدمة والتقرير مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE follows
      SET case_id = $1, service_id = $2, report = $3,
          date_field = $4, status = $5, user_id = $6,
          next_hearing_id = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
    `, [
      case_id, service_id, report, date_field, status, user_id, next_hearing_id, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المتابعة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المتابعة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف متابعة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const user_id = searchParams.get('user_id')

    // فحص الصلاحيات - مؤقتاً متاح للأدمن فقط (user_id = 1)
    if (user_id !== '1') {
      return NextResponse.json(
        { success: false, error: 'غير مخول لحذف المتابعات. الصلاحية متاحة للأدمن فقط حالياً.' },
        { status: 403 }
      )
    }

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المتابعة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM follows WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المتابعة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المتابعة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting follow:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المتابعة' },
      { status: 500 }
    )
  }
}
