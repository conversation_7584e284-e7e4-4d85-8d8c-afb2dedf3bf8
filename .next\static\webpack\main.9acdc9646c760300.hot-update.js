"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js ***!
  \***********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorFeedback: function() {\n        return ErrorFeedback;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _thumbsup = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-up */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.js\");\nconst _thumbsdown = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-down */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.js\");\nconst _cx = __webpack_require__(/*! ../../../../utils/cx */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction ErrorFeedback(param) {\n    let { errorCode, className } = param;\n    const [votedMap, setVotedMap] = (0, _react.useState)({});\n    const voted = votedMap[errorCode];\n    const hasVoted = voted !== undefined;\n    const disabled = true;\n    const handleFeedback = (0, _react.useCallback)(async (wasHelpful)=>{\n        // Optimistically set feedback state without loading/error states to keep implementation simple\n        setVotedMap((prev)=>({\n                ...prev,\n                [errorCode]: wasHelpful\n            }));\n        try {\n            const response = await fetch(( false || '') + \"/__nextjs_error_feedback?\" + new URLSearchParams({\n                errorCode,\n                wasHelpful: wasHelpful.toString()\n            }));\n            if (!response.ok) {\n                // Handle non-2xx HTTP responses here if needed\n                console.error('Failed to record feedback on the server.');\n            }\n        } catch (error) {\n            console.error('Failed to record feedback:', error);\n        }\n    }, [\n        errorCode\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        className: (0, _cx.cx)('error-feedback', className),\n        role: \"region\",\n        \"aria-label\": \"Error feedback\",\n        children: hasVoted ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n            className: \"error-feedback-thanks\",\n            role: \"status\",\n            \"aria-live\": \"polite\",\n            children: \"Thanks for your feedback!\"\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: \"https://nextjs.org/telemetry#error-feedback\",\n                        rel: \"noopener noreferrer\",\n                        target: \"_blank\",\n                        children: \"Was this helpful?\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(true),\n                    className: (0, _cx.cx)('feedback-button', voted === true && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsup.ThumbsUp, {\n                        \"aria-hidden\": \"true\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as not helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(false),\n                    className: (0, _cx.cx)('feedback-button', voted === false && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsdown.ThumbsDown, {\n                        \"aria-hidden\": \"true\",\n                        // Optical alignment\n                        style: {\n                            translate: '1px 1px'\n                        }\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = ErrorFeedback;\nconst styles = \"\\n  .error-feedback {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    white-space: nowrap;\\n    color: var(--color-gray-900);\\n  }\\n\\n  .error-feedback-thanks {\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    padding-right: 4px; /* To match the 4px inner padding of the thumbs up and down icons */\\n  }\\n\\n  .feedback-button {\\n    background: none;\\n    border: none;\\n    border-radius: var(--rounded-md);\\n    width: var(--size-24);\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    cursor: pointer;\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:hover {\\n      background: var(--color-gray-alpha-100);\\n    }\\n\\n    &:active {\\n      background: var(--color-gray-alpha-200);\\n    }\\n  }\\n\\n  .feedback-button[aria-disabled='true'] {\\n    opacity: 0.7;\\n    cursor: not-allowed;\\n  }\\n\\n  .feedback-button.voted {\\n    background: var(--color-gray-alpha-200);\\n  }\\n\\n  .thumbs-up-icon,\\n  .thumbs-down-icon {\\n    color: var(--color-gray-900);\\n    width: var(--size-16);\\n    height: var(--size-16);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-feedback.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorFeedback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZXJyb3Itb3ZlcmxheS1mb290ZXIvZXJyb3ItZmVlZGJhY2svZXJyb3ItZmVlZGJhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBU2dCQSxhQUFhO2VBQWJBOztJQWdHSEMsTUFBTTtlQUFOQTs7OzttQ0F6R3lCO3NDQUNiO3dDQUNFO2dDQUNSO0FBTVosdUJBQXVCLEtBQTRDO0lBQTVDLE1BQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFzQixHQUE1QztJQUM1QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR0MsQ0FBQUEsR0FBQUEsT0FBQUEsUUFBQUEsRUFBa0MsQ0FBQztJQUNuRSxNQUFNQyxRQUFRSCxRQUFRLENBQUNGLFVBQVU7SUFDakMsTUFBTU0sV0FBV0QsVUFBVUU7SUFDM0IsTUFBTUMsV0FBV0MsSUFBcUM7SUFFdEQsTUFBTUcsaUJBQWlCQyxDQUFBQSxHQUFBQSxPQUFBQSxXQUFBQSxFQUNyQixPQUFPQztRQUNMLCtGQUErRjtRQUMvRlgsWUFBWSxDQUFDWSxPQUFVO2dCQUNyQixHQUFHQSxJQUFJO2dCQUNQLENBQUNmLFVBQVUsRUFBRWM7YUFDZjtRQUVBLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQ2xCUixDQUFBQSxNQUFrQyxJQUFJLEdBQUMsR0FBRSw4QkFBMkIsSUFBSVUsZ0JBQ3pFO2dCQUNFbkI7Z0JBQ0FjLFlBQVlBLFdBQVdNLFFBQVE7WUFDakM7WUFJSixJQUFJLENBQUNKLFNBQVNLLEVBQUUsRUFBRTtnQkFDaEIsK0NBQStDO2dCQUMvQ0MsUUFBUUMsS0FBSyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzlDO0lBQ0YsR0FDQTtRQUFDdkI7S0FBVTtJQUdiLHFCQUNFLHFCQUFDd0IsT0FBQUE7UUFDQ3ZCLFdBQVd3QixDQUFBQSxHQUFBQSxJQUFBQSxFQUFBQSxFQUFHLGtCQUFrQnhCO1FBQ2hDeUIsTUFBSztRQUNMQyxjQUFXO2tCQUVWckIsV0FBQUEsV0FBQUEsR0FDQyxxQkFBQ3NCLEtBQUFBO1lBQUUzQixXQUFVO1lBQXdCeUIsTUFBSztZQUFTRyxhQUFVO3NCQUFTO2FBSXRFOzs4QkFDRSxxQkFBQ0QsS0FBQUE7OEJBQ0MsbUNBQUNFLEtBQUFBO3dCQUNDQyxNQUFLO3dCQUNMQyxLQUFJO3dCQUNKQyxRQUFPO2tDQUNSOzs7OEJBSUgscUJBQUNDLFVBQUFBO29CQUNDQyxpQkFBZTNCLFdBQVcsU0FBU0Q7b0JBQ25Db0IsY0FBVztvQkFDWFMsU0FBUzVCLFdBQVdELFlBQVksSUFBTUssZUFBZTtvQkFDckRYLFdBQVd3QixDQUFBQSxHQUFBQSxJQUFBQSxFQUFBQSxFQUFHLG1CQUFtQnBCLFVBQVUsUUFBUTtvQkFDbkRnQyxPQUNFN0IsV0FDSSw2REFDQUQ7b0JBRU4rQixNQUFLOzhCQUVMLG1DQUFDQyxVQUFBQSxRQUFRO3dCQUFDQyxlQUFZOzs7OEJBRXhCLHFCQUFDTixVQUFBQTtvQkFDQ0MsaUJBQWUzQixXQUFXLFNBQVNEO29CQUNuQ29CLGNBQVc7b0JBQ1hTLFNBQVM1QixXQUFXRCxZQUFZLElBQU1LLGVBQWU7b0JBQ3JEWCxXQUFXd0IsQ0FBQUEsR0FBQUEsSUFBQUEsRUFBQUEsRUFBRyxtQkFBbUJwQixVQUFVLFNBQVM7b0JBQ3BEZ0MsT0FDRTdCLFdBQ0ksNkRBQ0FEO29CQUVOK0IsTUFBSzs4QkFFTCxtQ0FBQ0csWUFBQUEsVUFBVTt3QkFDVEQsZUFBWTt3QkFDWixvQkFBb0I7d0JBQ3BCRSxPQUFPOzRCQUNMQyxXQUFXO3dCQUNiOzs7Ozs7QUFPZDtLQTlGZ0I3QztBQWdHVCxNQUFNQyxTQUFVIiwic291cmNlcyI6WyJEOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZXJyb3JzXFxlcnJvci1vdmVybGF5LWZvb3RlclxcZXJyb3ItZmVlZGJhY2tcXGVycm9yLWZlZWRiYWNrLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRodW1ic1VwIH0gZnJvbSAnLi4vLi4vLi4vLi4vaWNvbnMvdGh1bWJzL3RodW1icy11cCdcbmltcG9ydCB7IFRodW1ic0Rvd24gfSBmcm9tICcuLi8uLi8uLi8uLi9pY29ucy90aHVtYnMvdGh1bWJzLWRvd24nXG5pbXBvcnQgeyBjeCB9IGZyb20gJy4uLy4uLy4uLy4uL3V0aWxzL2N4J1xuXG5pbnRlcmZhY2UgRXJyb3JGZWVkYmFja1Byb3BzIHtcbiAgZXJyb3JDb2RlOiBzdHJpbmdcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5leHBvcnQgZnVuY3Rpb24gRXJyb3JGZWVkYmFjayh7IGVycm9yQ29kZSwgY2xhc3NOYW1lIH06IEVycm9yRmVlZGJhY2tQcm9wcykge1xuICBjb25zdCBbdm90ZWRNYXAsIHNldFZvdGVkTWFwXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIGJvb2xlYW4+Pih7fSlcbiAgY29uc3Qgdm90ZWQgPSB2b3RlZE1hcFtlcnJvckNvZGVdXG4gIGNvbnN0IGhhc1ZvdGVkID0gdm90ZWQgIT09IHVuZGVmaW5lZFxuICBjb25zdCBkaXNhYmxlZCA9IHByb2Nlc3MuZW52Ll9fTkVYVF9URUxFTUVUUllfRElTQUJMRURcblxuICBjb25zdCBoYW5kbGVGZWVkYmFjayA9IHVzZUNhbGxiYWNrKFxuICAgIGFzeW5jICh3YXNIZWxwZnVsOiBib29sZWFuKSA9PiB7XG4gICAgICAvLyBPcHRpbWlzdGljYWxseSBzZXQgZmVlZGJhY2sgc3RhdGUgd2l0aG91dCBsb2FkaW5nL2Vycm9yIHN0YXRlcyB0byBrZWVwIGltcGxlbWVudGF0aW9uIHNpbXBsZVxuICAgICAgc2V0Vm90ZWRNYXAoKHByZXYpID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIFtlcnJvckNvZGVdOiB3YXNIZWxwZnVsLFxuICAgICAgfSkpXG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXG4gICAgICAgICAgYCR7cHJvY2Vzcy5lbnYuX19ORVhUX1JPVVRFUl9CQVNFUEFUSCB8fCAnJ30vX19uZXh0anNfZXJyb3JfZmVlZGJhY2s/JHtuZXcgVVJMU2VhcmNoUGFyYW1zKFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBlcnJvckNvZGUsXG4gICAgICAgICAgICAgIHdhc0hlbHBmdWw6IHdhc0hlbHBmdWwudG9TdHJpbmcoKSxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICApfWBcbiAgICAgICAgKVxuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAvLyBIYW5kbGUgbm9uLTJ4eCBIVFRQIHJlc3BvbnNlcyBoZXJlIGlmIG5lZWRlZFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZWNvcmQgZmVlZGJhY2sgb24gdGhlIHNlcnZlci4nKVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcmVjb3JkIGZlZWRiYWNrOicsIGVycm9yKVxuICAgICAgfVxuICAgIH0sXG4gICAgW2Vycm9yQ29kZV1cbiAgKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjeCgnZXJyb3ItZmVlZGJhY2snLCBjbGFzc05hbWUpfVxuICAgICAgcm9sZT1cInJlZ2lvblwiXG4gICAgICBhcmlhLWxhYmVsPVwiRXJyb3IgZmVlZGJhY2tcIlxuICAgID5cbiAgICAgIHtoYXNWb3RlZCA/IChcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwiZXJyb3ItZmVlZGJhY2stdGhhbmtzXCIgcm9sZT1cInN0YXR1c1wiIGFyaWEtbGl2ZT1cInBvbGl0ZVwiPlxuICAgICAgICAgIFRoYW5rcyBmb3IgeW91ciBmZWVkYmFjayFcbiAgICAgICAgPC9wPlxuICAgICAgKSA6IChcbiAgICAgICAgPD5cbiAgICAgICAgICA8cD5cbiAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgIGhyZWY9XCJodHRwczovL25leHRqcy5vcmcvdGVsZW1ldHJ5I2Vycm9yLWZlZWRiYWNrXCJcbiAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFdhcyB0aGlzIGhlbHBmdWw/XG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGFyaWEtZGlzYWJsZWQ9e2Rpc2FibGVkID8gJ3RydWUnIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIk1hcmsgYXMgaGVscGZ1bFwiXG4gICAgICAgICAgICBvbkNsaWNrPXtkaXNhYmxlZCA/IHVuZGVmaW5lZCA6ICgpID0+IGhhbmRsZUZlZWRiYWNrKHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjeCgnZmVlZGJhY2stYnV0dG9uJywgdm90ZWQgPT09IHRydWUgJiYgJ3ZvdGVkJyl9XG4gICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgIGRpc2FibGVkXG4gICAgICAgICAgICAgICAgPyAnRmVlZGJhY2sgZGlzYWJsZWQgZHVlIHRvIHNldHRpbmcgTkVYVF9URUxFTUVUUllfRElTQUJMRUQnXG4gICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxUaHVtYnNVcCBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGFyaWEtZGlzYWJsZWQ9e2Rpc2FibGVkID8gJ3RydWUnIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIk1hcmsgYXMgbm90IGhlbHBmdWxcIlxuICAgICAgICAgICAgb25DbGljaz17ZGlzYWJsZWQgPyB1bmRlZmluZWQgOiAoKSA9PiBoYW5kbGVGZWVkYmFjayhmYWxzZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2N4KCdmZWVkYmFjay1idXR0b24nLCB2b3RlZCA9PT0gZmFsc2UgJiYgJ3ZvdGVkJyl9XG4gICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgIGRpc2FibGVkXG4gICAgICAgICAgICAgICAgPyAnRmVlZGJhY2sgZGlzYWJsZWQgZHVlIHRvIHNldHRpbmcgTkVYVF9URUxFTUVUUllfRElTQUJMRUQnXG4gICAgICAgICAgICAgICAgOiB1bmRlZmluZWRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxUaHVtYnNEb3duXG4gICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXG4gICAgICAgICAgICAgIC8vIE9wdGljYWwgYWxpZ25tZW50XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgdHJhbnNsYXRlOiAnMXB4IDFweCcsXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8Lz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGNvbnN0IHN0eWxlcyA9IGBcbiAgLmVycm9yLWZlZWRiYWNrIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiA4cHg7XG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICBjb2xvcjogdmFyKC0tY29sb3ItZ3JheS05MDApO1xuICB9XG5cbiAgLmVycm9yLWZlZWRiYWNrLXRoYW5rcyB7XG4gICAgaGVpZ2h0OiB2YXIoLS1zaXplLTI0KTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgcGFkZGluZy1yaWdodDogNHB4OyAvKiBUbyBtYXRjaCB0aGUgNHB4IGlubmVyIHBhZGRpbmcgb2YgdGhlIHRodW1icyB1cCBhbmQgZG93biBpY29ucyAqL1xuICB9XG5cbiAgLmZlZWRiYWNrLWJ1dHRvbiB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcm91bmRlZC1tZCk7XG4gICAgd2lkdGg6IHZhcigtLXNpemUtMjQpO1xuICAgIGhlaWdodDogdmFyKC0tc2l6ZS0yNCk7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICY6Zm9jdXMge1xuICAgICAgb3V0bGluZTogdmFyKC0tZm9jdXMtcmluZyk7XG4gICAgfVxuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jb2xvci1ncmF5LWFscGhhLTEwMCk7XG4gICAgfVxuXG4gICAgJjphY3RpdmUge1xuICAgICAgYmFja2dyb3VuZDogdmFyKC0tY29sb3ItZ3JheS1hbHBoYS0yMDApO1xuICAgIH1cbiAgfVxuXG4gIC5mZWVkYmFjay1idXR0b25bYXJpYS1kaXNhYmxlZD0ndHJ1ZSddIHtcbiAgICBvcGFjaXR5OiAwLjc7XG4gICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgfVxuXG4gIC5mZWVkYmFjay1idXR0b24udm90ZWQge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktYWxwaGEtMjAwKTtcbiAgfVxuXG4gIC50aHVtYnMtdXAtaWNvbixcbiAgLnRodW1icy1kb3duLWljb24ge1xuICAgIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTkwMCk7XG4gICAgd2lkdGg6IHZhcigtLXNpemUtMTYpO1xuICAgIGhlaWdodDogdmFyKC0tc2l6ZS0xNik7XG4gIH1cbmBcbiJdLCJuYW1lcyI6WyJFcnJvckZlZWRiYWNrIiwic3R5bGVzIiwiZXJyb3JDb2RlIiwiY2xhc3NOYW1lIiwidm90ZWRNYXAiLCJzZXRWb3RlZE1hcCIsInVzZVN0YXRlIiwidm90ZWQiLCJoYXNWb3RlZCIsInVuZGVmaW5lZCIsImRpc2FibGVkIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9URUxFTUVUUllfRElTQUJMRUQiLCJoYW5kbGVGZWVkYmFjayIsInVzZUNhbGxiYWNrIiwid2FzSGVscGZ1bCIsInByZXYiLCJyZXNwb25zZSIsImZldGNoIiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsIlVSTFNlYXJjaFBhcmFtcyIsInRvU3RyaW5nIiwib2siLCJjb25zb2xlIiwiZXJyb3IiLCJkaXYiLCJjeCIsInJvbGUiLCJhcmlhLWxhYmVsIiwicCIsImFyaWEtbGl2ZSIsImEiLCJocmVmIiwicmVsIiwidGFyZ2V0IiwiYnV0dG9uIiwiYXJpYS1kaXNhYmxlZCIsIm9uQ2xpY2siLCJ0aXRsZSIsInR5cGUiLCJUaHVtYnNVcCIsImFyaWEtaGlkZGVuIiwiVGh1bWJzRG93biIsInN0eWxlIiwidHJhbnNsYXRlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js\n"));

/***/ })

});