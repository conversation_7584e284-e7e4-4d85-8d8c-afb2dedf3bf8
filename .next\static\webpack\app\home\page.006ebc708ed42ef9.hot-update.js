"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ChatWidget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ChatWidget */ \"(app-pages-browser)/./src/components/ChatWidget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCompanyData */ \"(app-pages-browser)/./src/hooks/useCompanyData.ts\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst defaultCompanyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // استخدام hook بيانات الشركة مع التخزين المحلي\n    const { companyData, loading: companyLoading, error: companyError, getThemeColor, getCompanyName, isDataAvailable } = (0,_hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData)();\n    // استخدام البيانات المحملة أو الافتراضية\n    const companyDataState = companyData || defaultCompanyData;\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // مؤشر التحميل للبيانات الأساسية\n    if (companyLoading && !isDataAvailable) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-yellow-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mt-2\",\n                        children: \"يتم حفظ البيانات محلياً لتسريع التصفح مستقبلاً\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            companyError && !isDataAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-600 text-white px-4 py-2 text-center text-sm\",\n                children: \"⚠️ تعذر تحميل أحدث البيانات، يتم عرض البيانات المحفوظة محلياً\"\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWidget__WEBPACK_IMPORTED_MODULE_4__.ChatWidget, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"5ytD5RdgvlSOjH/R2sWhAHSKtMc=\", false, function() {\n    return [\n        _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData\n    ];\n});\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ })

});