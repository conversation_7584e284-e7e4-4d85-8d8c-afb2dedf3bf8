'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  User,
  Lock,
  LogIn,
  Building2,
  AlertCircle,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'

export default function ClientLoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      const response = await fetch('/api/client-portal/auth/simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: formData.username,
          password: formData.password
        })
      })

      const result = await response.json()

      if (result.success) {
        // حفظ بيانات الجلسة
        const sessionData = {
          id: result.data.client.client_id,
          username: result.data.client.username,
          name: result.data.client.client_name,
          email: result.data.client.email,
          type: 'client',
          token: result.data.token,
          sessionToken: result.data.sessionToken
        }

        localStorage.setItem('userSession', JSON.stringify(sessionData))
        localStorage.setItem('clientToken', result.data.token)

        // توجيه إلى بوابة العملاء
        router.push('/client-portal')
      } else {
        setError(result.error || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error)
      setError('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4" dir="rtl">
      <div className="w-full max-w-md">
        {/* شعار وعنوان */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-emerald-600 p-3 rounded-full">
              <Building2 className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">بوابة العملاء</h1>
          <p className="text-gray-600">نظام إدارة المكاتب القانونية</p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center">
              <LogIn className="h-5 w-5 mr-2" />
              تسجيل الدخول
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* رسالة الخطأ */}
              {error && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-700">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {/* اسم المستخدم */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم المستخدم
                </label>
                <div className="relative">
                  <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم المستخدم"
                    className="pr-10"
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              {/* كلمة المرور */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="أدخل كلمة المرور"
                    className="pr-10 pl-10"
                    required
                    disabled={loading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    disabled={loading}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    جاري تسجيل الدخول...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4 mr-2" />
                    تسجيل الدخول
                  </>
                )}
              </Button>
            </form>

            {/* معلومات تجريبية */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">بيانات تجريبية:</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>اسم المستخدم:</strong> demo_client</p>
                <p><strong>كلمة المرور:</strong> password123</p>
              </div>
            </div>

            {/* روابط إضافية */}
            <div className="mt-6 text-center space-y-2">
              <button
                type="button"
                onClick={() => router.push('/')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                العودة للصفحة الرئيسية
              </button>
            </div>
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <div className="mt-6 text-center text-sm text-gray-600">
          <p>هل تحتاج مساعدة؟ تواصل مع الإدارة</p>
        </div>
      </div>
    </div>
  )
}