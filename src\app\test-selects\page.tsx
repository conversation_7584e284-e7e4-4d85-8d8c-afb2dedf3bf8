'use client'

import { useState } from 'react'
import { SuperSimpleAccountSelect, SuperSimpleBeneficiarySelect } from '@/components/SuperSimpleSelect'

export default function TestSelectsPage() {
  const [beneficiaryValue, setBeneficiaryValue] = useState('')
  const [accountValue, setAccountValue] = useState('')

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-center">اختبار المكونات</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* اختبار مكون المستفيدين */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">اختبار مكون المستفيدين</h2>
          
          <SuperSimpleBeneficiarySelect
            label="👥 اختر المستفيد"
            value={beneficiaryValue}
            onChange={(value) => {
              setBeneficiaryValue(value)
              console.log('تم اختيار مستفيد:', value)
            }}
          />
          
          {beneficiaryValue && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-sm text-green-700">
                <strong>القيمة المحددة:</strong> {beneficiaryValue}
              </p>
            </div>
          )}
        </div>

        {/* اختبار مكون الحسابات */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">اختبار مكون الحسابات</h2>
          
          <SuperSimpleAccountSelect
            label="🏦 اختر الحساب"
            value={accountValue}
            onChange={(value) => {
              setAccountValue(value)
              console.log('تم اختيار حساب:', value)
            }}
            accountTypes={['أصول']}
          />
          
          {accountValue && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-sm text-blue-700">
                <strong>القيمة المحددة:</strong> {accountValue}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="mt-8 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">معلومات الاختبار</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>المستفيد المحدد:</strong>
            <p className="text-gray-600">{beneficiaryValue || 'لم يتم الاختيار'}</p>
          </div>
          <div>
            <strong>الحساب المحدد:</strong>
            <p className="text-gray-600">{accountValue || 'لم يتم الاختيار'}</p>
          </div>
        </div>
      </div>

      {/* تعليمات */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-2 text-yellow-800">تعليمات الاختبار</h3>
        <ul className="list-disc list-inside text-sm text-yellow-700 space-y-1">
          <li>افتح Developer Tools (F12) وانظر إلى Console للرسائل</li>
          <li>جرب اختيار مستفيد من القائمة الأولى</li>
          <li>جرب اختيار حساب من القائمة الثانية</li>
          <li>تأكد من ظهور القيم المحددة أسفل كل قائمة</li>
        </ul>
      </div>
    </div>
  )
}
