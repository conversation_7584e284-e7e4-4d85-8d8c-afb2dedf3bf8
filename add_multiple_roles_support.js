#!/usr/bin/env node

const { Client } = require('pg')

async function addMultipleRolesSupport() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: process.env.DB_PASSWORD || 'your_password_here'
  })

  try {
    await client.connect()
    console.log('🔗 متصل بقاعدة البيانات')

    // 1. إنشاء جدول ربط المستخدمين بالأدوار (Many-to-Many)
    console.log('👥 إنشاء جدول ربط المستخدمين بالأدوار...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_role_assignments (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        role_name VARCHAR(50) REFERENCES user_roles(role_name) ON DELETE CASCADE,
        assigned_by INTEGER REFERENCES users(id),
        assigned_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        UNIQUE(user_id, role_name)
      )
    `)

    // 2. إضافة فهرس للأداء
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id 
      ON user_role_assignments(user_id)
    `)

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_role_assignments_role_name 
      ON user_role_assignments(role_name)
    `)

    // 3. إنشاء دالة لجلب صلاحيات المستخدم من جميع أدواره
    console.log('🔧 إنشاء دالة جلب الصلاحيات المجمعة...')
    await client.query(`
      CREATE OR REPLACE FUNCTION get_user_combined_permissions(user_id_param INTEGER)
      RETURNS TEXT[] AS $$
      DECLARE
        combined_permissions TEXT[];
      BEGIN
        -- جلب الصلاحيات المباشرة للمستخدم
        SELECT ARRAY(
          SELECT DISTINCT up.permission_key
          FROM user_permissions up
          WHERE up.user_id = user_id_param AND up.is_active = true
        ) INTO combined_permissions;
        
        -- إضافة صلاحيات الأدوار
        SELECT ARRAY(
          SELECT DISTINCT unnest(ur.permissions)
          FROM user_role_assignments ura
          JOIN user_roles ur ON ura.role_name = ur.role_name
          WHERE ura.user_id = user_id_param 
            AND ura.is_active = true 
            AND ur.is_active = true
        ) INTO combined_permissions;
        
        -- إرجاع الصلاحيات المجمعة بدون تكرار
        RETURN ARRAY(SELECT DISTINCT unnest(combined_permissions));
      END;
      $$ LANGUAGE plpgsql;
    `)

    // 4. إدراج أدوار إضافية مفيدة
    console.log('🎭 إضافة أدوار جديدة...')
    const newRoles = [
      {
        role_name: 'secretary_accountant',
        display_name: 'سكرتير + محاسب',
        description: 'دور مختلط يجمع بين السكرتارية والمحاسبة',
        permissions: [
          'clients_view', 'clients_add', 'clients_edit',
          'follows_view', 'follows_add', 'follows_edit',
          'accounting_view', 'accounting_add', 'accounting_edit',
          'vouchers_view', 'vouchers_add', 'vouchers_edit',
          'reports_view'
        ]
      },
      {
        role_name: 'lawyer_manager',
        display_name: 'محامي + مدير',
        description: 'محامي مع صلاحيات إدارية',
        permissions: [
          'issues_view', 'issues_add', 'issues_edit',
          'clients_view', 'clients_add', 'clients_edit',
          'follows_view', 'follows_add', 'follows_edit',
          'users_view', 'reports_view', 'reports_print'
        ]
      },
      {
        role_name: 'senior_secretary',
        display_name: 'سكرتير أول',
        description: 'سكرتير مع صلاحيات إضافية',
        permissions: [
          'clients_view', 'clients_add', 'clients_edit',
          'follows_view', 'follows_add', 'follows_edit',
          'issues_view', 'reports_view',
          'courts_view', 'branches_view'
        ]
      },
      {
        role_name: 'financial_manager',
        display_name: 'مدير مالي',
        description: 'مدير مالي مع صلاحيات شاملة',
        permissions: [
          'accounting_view', 'accounting_add', 'accounting_edit',
          'vouchers_view', 'vouchers_add', 'vouchers_edit',
          'reports_view', 'reports_print', 'reports_export',
          'percentages_view', 'percentages_add', 'percentages_edit'
        ]
      }
    ]

    for (const role of newRoles) {
      await client.query(`
        INSERT INTO user_roles (role_name, display_name, description, permissions)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (role_name) DO UPDATE SET
          display_name = EXCLUDED.display_name,
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions
      `, [role.role_name, role.display_name, role.description, role.permissions])
    }

    // 5. نقل الأدوار الحالية للمستخدمين إلى الجدول الجديد
    console.log('📋 نقل الأدوار الحالية...')
    const usersWithRoles = await client.query(`
      SELECT id, role FROM users WHERE role IS NOT NULL AND role != ''
    `)

    for (const user of usersWithRoles.rows) {
      await client.query(`
        INSERT INTO user_role_assignments (user_id, role_name, assigned_by)
        VALUES ($1, $2, $1)
        ON CONFLICT (user_id, role_name) DO NOTHING
      `, [user.id, user.role])
    }

    // 6. عرض الإحصائيات
    const rolesCount = await client.query('SELECT COUNT(*) FROM user_roles')
    const assignmentsCount = await client.query('SELECT COUNT(*) FROM user_role_assignments')
    
    console.log('\n📊 إحصائيات النظام المحدث:')
    console.log(`🎭 الأدوار المتاحة: ${rolesCount.rows[0].count}`)
    console.log(`👥 تعيينات الأدوار: ${assignmentsCount.rows[0].count}`)

    // 7. عرض الأدوار المتاحة
    const allRoles = await client.query(`
      SELECT role_name, display_name, description, 
             array_length(permissions, 1) as permissions_count
      FROM user_roles 
      ORDER BY role_name
    `)

    console.log('\n🎭 الأدوار المتاحة:')
    console.table(allRoles.rows)

    console.log('\n✅ تم تحديث النظام لدعم الأدوار المتعددة بنجاح!')
    console.log('\n🎯 المميزات الجديدة:')
    console.log('   1. إمكانية إضافة أكثر من دور للمستخدم الواحد')
    console.log('   2. أدوار مختلطة (سكرتير + محاسب)')
    console.log('   3. دالة جلب الصلاحيات المجمعة')
    console.log('   4. واجهة محسنة لإدارة الأدوار')

  } catch (error) {
    console.error('❌ خطأ:', error)
  } finally {
    await client.end()
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات')
  }
}

// تشغيل السكريبت
addMultipleRolesSupport()
