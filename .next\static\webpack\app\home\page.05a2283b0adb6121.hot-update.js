"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bot.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Bot)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 8V4H8\",\n            key: \"hb8ula\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"16\",\n            height: \"12\",\n            x: \"4\",\n            y: \"8\",\n            rx: \"2\",\n            key: \"enze0r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 14h2\",\n            key: \"vft8re\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 14h2\",\n            key: \"4cs60a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 13v2\",\n            key: \"1xurst\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 13v2\",\n            key: \"rq6x2g\"\n        }\n    ]\n];\nconst Bot = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"bot\", __iconNode);\n //# sourceMappingURL=bot.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LoaderCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"loader-circle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9hZGVyLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsNkJBQStCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhNUYsbUJBQWUsa0VBQWlCLGtCQUFpQixDQUFVIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcbG9hZGVyLWNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnTTIxIDEyYTkgOSAwIDEgMS02LjIxOS04LjU2Jywga2V5OiAnMTN6YWxkJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBMb2FkZXJDaXJjbGVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qRWdNVEpoT1NBNUlEQWdNU0F4TFRZdU1qRTVMVGd1TlRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2xvYWRlci1jaXJjbGVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBMb2FkZXJDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKCdsb2FkZXItY2lyY2xlJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IExvYWRlckNpcmNsZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/message-circle.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MessageCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n            key: \"vv11sd\"\n        }\n    ]\n];\nconst MessageCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"message-circle\", __iconNode);\n //# sourceMappingURL=message-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUFDLENBQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyxpQ0FBa0M7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUNqRTtBQWFNLG9CQUFnQixrRUFBaUIsbUJBQWtCLENBQVUiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGljb25zXFxtZXNzYWdlLWNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncGF0aCcsIHsgZDogJ003LjkgMjBBOSA5IDAgMSAwIDQgMTYuMUwyIDIyWicsIGtleTogJ3Z2MTFzZCcgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgTWVzc2FnZUNpcmNsZVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTnk0NUlESXdRVGtnT1NBd0lERWdNQ0EwSURFMkxqRk1NaUF5TWxvaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL21lc3NhZ2UtY2lyY2xlXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgTWVzc2FnZUNpcmNsZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ21lc3NhZ2UtY2lyY2xlJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IE1lc3NhZ2VDaXJjbGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/minimize-2.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Minimize2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m14 10 7-7\",\n            key: \"oa77jy\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 10h-6V4\",\n            key: \"mjg0md\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m3 21 7-7\",\n            key: \"tjx5ai\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 14h6v6\",\n            key: \"rmj7iw\"\n        }\n    ]\n];\nconst Minimize2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"minimize-2\", __iconNode);\n //# sourceMappingURL=minimize-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/send.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Send)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z\",\n            key: \"1ffxy3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21.854 2.147-10.94 10.939\",\n            key: \"12cjpa\"\n        }\n    ]\n];\nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"send\", __iconNode);\n //# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\",\n            key: \"975kel\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"17ys0d\"\n        }\n    ]\n];\nconst User = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user\", __iconNode);\n //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/home/<USER>":
/*!*******************************!*\
  !*** ./src/app/home/<USER>
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ChatWidget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ChatWidget */ \"(app-pages-browser)/./src/components/ChatWidget.tsx\");\n/* harmony import */ var _components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/legal-library-section-new */ \"(app-pages-browser)/./src/app/home/<USER>/legal-library-section-new.tsx\");\n/* harmony import */ var _components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/announcements-bar */ \"(app-pages-browser)/./src/app/home/<USER>/announcements-bar.tsx\");\n/* harmony import */ var _components_map_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/map-section */ \"(app-pages-browser)/./src/app/home/<USER>/map-section.tsx\");\n/* harmony import */ var _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCompanyData */ \"(app-pages-browser)/./src/hooks/useCompanyData.ts\");\n/* harmony import */ var _styles_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles.css */ \"(app-pages-browser)/./src/app/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Default company data (fallback) - updated to match actual database\nconst defaultCompanyData = {\n    id: 1,\n    name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية',\n    legal_name: 'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',\n    description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',\n    address: 'صنعاء- شارع مجاهد- عمارة الحاشدي',\n    city: 'صنعاء',\n    country: 'اليمن',\n    phone: '+967-1-123456',\n    email: '<EMAIL>',\n    website: 'www.legalfirm.ye',\n    logo_url: '/images/company-logo.png',\n    logo_image_url: '/images/logo.png',\n    established_date: '2020-01-14',\n    registration_number: 'CR-2024-001',\n    legal_form: 'شركة محدودة المسؤولية',\n    capital: 1000000,\n    tax_number: 'TAX-*********',\n    is_active: true,\n    working_hours: 'الأحد - الخميس: 8 صباحاً - 6 مساءً'\n};\n// سيتم تعريف stats داخل المكون\n// Dynamic imports for components\nconst HeaderComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_header_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/header */ \"(app-pages-browser)/./src/app/home/<USER>/header.tsx\")).then((mod)=>mod.Header), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/header\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c = HeaderComponent;\nconst HeroSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_hero-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/hero-section */ \"(app-pages-browser)/./src/app/home/<USER>/hero-section.tsx\")).then((mod)=>mod.HeroSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/hero-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c1 = HeroSection;\nconst ServicesSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_services-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/services-section */ \"(app-pages-browser)/./src/app/home/<USER>/services-section.tsx\")).then((mod)=>mod.ServicesSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/services-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c2 = ServicesSection;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_testimonials-section_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/testimonials-section */ \"(app-pages-browser)/./src/app/home/<USER>/testimonials-section.tsx\")).then((mod)=>mod.TestimonialsSection), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/testimonials-section\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c3 = TestimonialsSection;\nconst Footer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_home_components_footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer */ \"(app-pages-browser)/./src/app/home/<USER>/footer.tsx\")).then((mod)=>mod.Footer), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\home\\\\page.tsx -> \" + \"./components/footer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n_c4 = Footer;\nfunction HomePage() {\n    _s();\n    const [isChatWidgetOpen, setIsChatWidgetOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [legalDocumentsCount, setLegalDocumentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // استخدام hook بيانات الشركة مع التخزين المحلي\n    const { companyData, loading: companyLoading, error: companyError, getThemeColor, getCompanyName, isDataAvailable } = (0,_hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData)();\n    // استخدام البيانات المحملة أو الافتراضية\n    const companyDataState = companyData || defaultCompanyData;\n    // جلب عدد الملفات القانونية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchLegalDocumentsCount = {\n                \"HomePage.useEffect.fetchLegalDocumentsCount\": async ()=>{\n                    try {\n                        const response = await fetch('/api/legal-library');\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            setLegalDocumentsCount(result.data.length);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب عدد الملفات القانونية:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchLegalDocumentsCount\"];\n            fetchLegalDocumentsCount();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // استخدام البيانات من قاعدة البيانات أو البيانات الافتراضية\n    const currentCompanyData = companyDataState || companyData;\n    // إحصائيات المكتب\n    const stats = {\n        clients: 1200,\n        issues: 5000,\n        employees: 25,\n        completedIssues: 4900,\n        newIssues: 100,\n        courts: 15,\n        successRate: 98,\n        experienceYears: 15,\n        legalDocuments: legalDocumentsCount\n    };\n    const scrollToServices = ()=>{\n        const librarySection = document.getElementById('library');\n        if (librarySection) {\n            librarySection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        } else {\n            // إذا لم توجد المكتبة، انتقل للخدمات\n            const servicesSection = document.getElementById('services');\n            servicesSection === null || servicesSection === void 0 ? void 0 : servicesSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // مؤشر التحميل للبيانات الأساسية\n    if (companyLoading && !isDataAvailable) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-yellow-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mt-2\",\n                        children: \"يتم حفظ البيانات محلياً لتسريع التصفح مستقبلاً\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        className: \"min-h-screen text-white\",\n        style: {\n            background: 'linear-gradient(135deg, #333333 0%, #**********%)',\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            companyError && !isDataAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-600 text-white px-4 py-2 text-center text-sm\",\n                children: \"⚠️ تعذر تحميل أحدث البيانات، يتم عرض البيانات المحفوظة محلياً\"\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_announcements_bar__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsBar, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderComponent, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {\n                        companyData: currentCompanyData,\n                        stats: stats,\n                        onServicesClick: scrollToServices\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"services\",\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSection, {\n                            searchQuery: searchQuery,\n                            onSearch: setSearchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legal_library_section_new__WEBPACK_IMPORTED_MODULE_5__.LegalLibrarySectionNew, {}, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        style: {\n                            background: 'linear-gradient(135deg, #333333 0%, #**********%)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_section__WEBPACK_IMPORTED_MODULE_7__.MapSection, {\n                        companyData: currentCompanyData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                companyData: currentCompanyData\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWidget__WEBPACK_IMPORTED_MODULE_4__.ChatWidget, {}, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\app\\\\home\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"O/ceB+Iu6CvIuCsB3XkHEZGk0w4=\", false, function() {\n    return [\n        _hooks_useCompanyData__WEBPACK_IMPORTED_MODULE_8__.useCompanyData\n    ];\n});\n_c5 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderComponent\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"ServicesSection\");\n$RefreshReg$(_c3, \"TestimonialsSection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 right-6 z-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 h-96 shadow-2xl border-0 transition-all duration-300 \".concat(isMinimized ? 'h-14' : 'h-96'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3 rounded-t-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-white/20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsOpen(false),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-2 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 \".concat(message.type === 'user' ? 'text-blue-100' : 'text-gray-500'),\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            size: \"sm\",\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});