import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

// إعداد قاعدة البيانات

// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: process.env.DB_PASSWORD || 'your_password_here',
  port: 5432,
})

// GET - جلب إعدادات الذكاء الاصطناعي
export async function GET() {
  try {
    const client = await pool.connect()

    try {
      // البحث عن الإعدادات في قاعدة البيانات
      const result = await client.query(
        'SELECT * FROM ai_settings WHERE id = 1'
      )

      let settings

      if (result.rows.length > 0) {
        // إعدادات موجودة في قاعدة البيانات
        const row = result.rows[0]
        settings = {
          enabled: row.enabled,
          model: row.model,
          delay_seconds: row.delay_seconds,
          working_hours_only: row.working_hours_only,
          working_hours_start: row.working_hours_start,
          working_hours_end: row.working_hours_end,
          working_days: row.working_days,
          max_responses_per_conversation: row.max_responses_per_conversation,
          keywords_trigger: row.keywords_trigger,
          excluded_keywords: row.excluded_keywords,
          auto_responses: row.auto_responses
        }
      } else {
        // إعدادات افتراضية - يعمل 24 ساعة عند التفعيل
        settings = {
          enabled: true,
          model: 'groq-llama-8b',
          delay_seconds: 3,
          working_hours_only: false, // دائماً false لتعمل 24 ساعة
          working_hours_start: '00:00',
          working_hours_end: '23:59',
          working_days: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
          max_responses_per_conversation: 20,
          keywords_trigger: ['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'مرحبا', 'السلام', 'أهلا', 'استشارة', 'قانوني', 'محامي'],
          excluded_keywords: [],
          auto_responses: {
            greeting: 'مرحباً! أنا المساعد الذكي للمكتب مدعوم بـ Llama 3.1.',
            help: 'يمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب\n• توجيهك للمحامي المناسب\n• تحليل القضايا القانونية',
            disclaimer: 'للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.',
            signature: '🤖 المساعد الذكي مدعوم بـ Llama 3.1 (مجاني)'
          }
        }

        // حفظ الإعدادات الافتراضية في قاعدة البيانات
        await client.query(`
          INSERT INTO ai_settings (
            id, enabled, model, delay_seconds, working_hours_only,
            working_hours_start, working_hours_end, working_days,
            max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses
          ) VALUES (
            1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
          )
        `, [
          settings.enabled,
          settings.model,
          settings.delay_seconds,
          settings.working_hours_only,
          settings.working_hours_start,
          settings.working_hours_end,
          settings.working_days,
          settings.max_responses_per_conversation,
          settings.keywords_trigger,
          settings.excluded_keywords,
          settings.auto_responses
        ])
      }

      return NextResponse.json({
        success: true,
        data: settings
      })

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching AI settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب إعدادات الذكاء الاصطناعي' },
      { status: 500 }
    )
  }
}

// POST - حفظ إعدادات الذكاء الاصطناعي
export async function POST(request: NextRequest) {
  try {
    const settings = await request.json()

    const client = await pool.connect()

    try {
      // تحديث أو إدراج الإعدادات
      await client.query(`
        INSERT INTO ai_settings (
          id, enabled, model, delay_seconds, working_hours_only,
          working_hours_start, working_hours_end, working_days,
          max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses,
          updated_at
        ) VALUES (
          1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
          enabled = $1,
          model = $2,
          delay_seconds = $3,
          working_hours_only = $4,
          working_hours_start = $5,
          working_hours_end = $6,
          working_days = $7,
          max_responses_per_conversation = $8,
          keywords_trigger = $9,
          excluded_keywords = $10,
          auto_responses = $11,
          updated_at = NOW()
      `, [
        settings.enabled,
        settings.model,
        settings.delay_seconds,
        settings.working_hours_only,
        settings.working_hours_start,
        settings.working_hours_end,
        settings.working_days,
        settings.max_responses_per_conversation,
        settings.keywords_trigger,
        settings.excluded_keywords,
        settings.auto_responses
      ])

      return NextResponse.json({
        success: true,
        message: 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح'
      })

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error saving AI settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ إعدادات الذكاء الاصطناعي' },
      { status: 500 }
    )
  }
}