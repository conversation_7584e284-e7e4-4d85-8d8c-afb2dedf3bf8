// إصلاح علاقة المحاكم والقضايا إلى One-to-Many
const { Pool } = require('pg');

async function fixCourtsIssuesRelation() {
  console.log('🔧 إصلاح علاقة المحاكم والقضايا إلى One-to-Many...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص العلاقة الحالية
      console.log('\n   🔍 فحص العلاقة الحالية:');
      
      const currentRelations = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND (tc.table_name = 'issue_courts' OR 
             (tc.table_name = 'issues' AND kcu.column_name = 'court_id'))
        ORDER BY tc.table_name, kcu.column_name
      `);

      console.log('      العلاقات الحالية:');
      currentRelations.rows.forEach(rel => {
        console.log(`         - ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
      });

      // 2. فحص البيانات في جدول issue_courts
      console.log('\n   📊 فحص البيانات في جدول issue_courts:');
      
      const issueCourtData = await pool.query(`
        SELECT 
          ic.issue_id,
          ic.court_id,
          i.case_number,
          c.name as court_name
        FROM issue_courts ic
        LEFT JOIN issues i ON ic.issue_id = i.id
        LEFT JOIN courts c ON ic.court_id = c.id
        ORDER BY ic.issue_id
      `);

      console.log(`      📋 عدد العلاقات الموجودة: ${issueCourtData.rows.length}`);
      issueCourtData.rows.forEach(row => {
        console.log(`         - القضية ${row.case_number} في المحكمة ${row.court_name}`);
      });

      // 3. فحص العمود court_id في جدول القضايا
      console.log('\n   🔍 فحص العمود court_id في جدول القضايا:');
      
      const courtIdExists = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issues' AND column_name = 'court_id'
      `);

      if (courtIdExists.rows.length === 0) {
        console.log('      ➕ إضافة العمود court_id إلى جدول القضايا...');
        await pool.query(`
          ALTER TABLE issues 
          ADD COLUMN court_id INTEGER
        `);
        console.log('      ✅ تم إضافة العمود court_id');
      } else {
        console.log('      ✅ العمود court_id موجود');
      }

      // 4. نقل البيانات من issue_courts إلى issues.court_id
      console.log('\n   📦 نقل البيانات من issue_courts إلى issues.court_id:');
      
      for (const row of issueCourtData.rows) {
        try {
          await pool.query(`
            UPDATE issues 
            SET court_id = $1
            WHERE id = $2
          `, [row.court_id, row.issue_id]);
          
          console.log(`      🔄 تم نقل القضية ${row.case_number} إلى المحكمة ${row.court_name}`);
        } catch (error) {
          console.log(`      ❌ خطأ في نقل القضية ${row.case_number}: ${error.message}`);
        }
      }

      // 5. حذف جدول issue_courts
      console.log('\n   🗑️ حذف جدول issue_courts:');
      
      try {
        // حذف المفاتيح الخارجية أولاً
        const issueCourtConstraints = await pool.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'issue_courts'
          AND constraint_type = 'FOREIGN KEY'
        `);

        for (const constraint of issueCourtConstraints.rows) {
          await pool.query(`ALTER TABLE issue_courts DROP CONSTRAINT IF EXISTS ${constraint.constraint_name}`);
          console.log(`      🗑️ تم حذف المفتاح الخارجي: ${constraint.constraint_name}`);
        }

        // حذف الجدول
        await pool.query(`DROP TABLE IF EXISTS issue_courts`);
        console.log('      ✅ تم حذف جدول issue_courts');
      } catch (error) {
        console.log(`      ❌ خطأ في حذف جدول issue_courts: ${error.message}`);
      }

      // 6. فحص العمود issue_id في جدول المحاكم
      console.log('\n   🔍 فحص العمود issue_id في جدول المحاكم:');
      
      const issueIdInCourts = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'courts' AND column_name = 'issue_id'
      `);

      if (issueIdInCourts.rows.length > 0) {
        console.log('      🗑️ حذف العمود issue_id من جدول المحاكم...');
        try {
          await pool.query(`ALTER TABLE courts DROP COLUMN IF EXISTS issue_id`);
          console.log('      ✅ تم حذف العمود issue_id من جدول المحاكم');
        } catch (error) {
          console.log(`      ❌ خطأ في حذف العمود issue_id: ${error.message}`);
        }
      } else {
        console.log('      ✅ العمود issue_id غير موجود في جدول المحاكم');
      }

      // 7. إنشاء العلاقة الجديدة الصحيحة
      console.log('\n   🔗 إنشاء العلاقة الجديدة الصحيحة:');
      
      try {
        await pool.query(`
          ALTER TABLE issues 
          ADD CONSTRAINT fk_issues_court_id 
          FOREIGN KEY (court_id) REFERENCES courts(id)
          ON DELETE SET NULL
          ON UPDATE CASCADE
        `);
        console.log('      ✅ تم إنشاء العلاقة: issues.court_id -> courts.id');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('      ✅ العلاقة موجودة مسبقاً');
        } else {
          console.log(`      ⚠️ خطأ في إنشاء العلاقة: ${error.message}`);
        }
      }

      // 8. اختبار العلاقة الجديدة
      console.log('\n   🧪 اختبار العلاقة الجديدة:');
      
      const newRelationTest = await pool.query(`
        SELECT 
          c.id as court_id,
          c.name as court_name,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ' ORDER BY i.case_number) as case_numbers
        FROM courts c
        LEFT JOIN issues i ON c.id = i.court_id
        GROUP BY c.id, c.name
        ORDER BY issue_count DESC, c.name
      `);

      console.log('      📊 المحاكم وقضاياها:');
      newRelationTest.rows.forEach(court => {
        console.log(`         - ${court.court_name}: ${court.issue_count} قضية`);
        if (court.case_numbers && court.issue_count > 0) {
          console.log(`           القضايا: ${court.case_numbers}`);
        }
      });

      // 9. اختبار الاستعلام الشامل الجديد
      console.log('\n   📝 اختبار الاستعلام الشامل الجديد:');
      
      const comprehensiveTest = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          cl.name as client_name,
          it.name as issue_type_name,
          c.name as court_name,
          c.type as court_type,
          i.status,
          i.amount
        FROM issues i
        LEFT JOIN clients cl ON i.client_id = cl.id
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts c ON i.court_id = c.id
        ORDER BY i.case_number
        LIMIT 3
      `);

      console.log('      ✅ الاستعلام الشامل الجديد:');
      comprehensiveTest.rows.forEach(row => {
        console.log(`         📄 ${row.case_number}: ${row.title || 'بدون عنوان'}`);
        console.log(`            العميل: ${row.client_name || 'غير محدد'}`);
        console.log(`            النوع: ${row.issue_type_name || 'غير محدد'}`);
        console.log(`            المحكمة: ${row.court_name || 'غير محدد'} (${row.court_type || 'غير محدد'})`);
        console.log(`            الحالة: ${row.status || 'غير محدد'} | المبلغ: ${row.amount || 0}`);
        console.log('');
      });

      // 10. فحص سلامة البيانات
      console.log('\n   🔍 فحص سلامة البيانات:');
      
      const dataIntegrityCheck = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issues WHERE court_id IS NULL) as issues_without_court,
          (SELECT COUNT(*) FROM issues i LEFT JOIN courts c ON i.court_id = c.id WHERE i.court_id IS NOT NULL AND c.id IS NULL) as broken_court_refs,
          (SELECT COUNT(*) FROM issues) as total_issues,
          (SELECT COUNT(*) FROM courts) as total_courts
      `);

      const integrity = dataIntegrityCheck.rows[0];
      console.log(`      - إجمالي القضايا: ${integrity.total_issues}`);
      console.log(`      - إجمالي المحاكم: ${integrity.total_courts}`);
      console.log(`      - قضايا بدون محاكم: ${integrity.issues_without_court}`);
      console.log(`      - مراجع محاكم مكسورة: ${integrity.broken_court_refs}`);

      // 11. عرض العلاقات النهائية
      console.log('\n   📋 العلاقات النهائية:');
      
      const finalRelations = await pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        ORDER BY kcu.column_name
      `);

      finalRelations.rows.forEach((rel, index) => {
        console.log(`      ${index + 1}. ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column_name}`);
      });

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح علاقة المحاكم والقضايا');
  
  console.log('\n📋 ملخص التغييرات:');
  console.log('1. ✅ تم تغيير العلاقة من Many-to-Many إلى One-to-Many');
  console.log('2. ✅ courts.id -> issues.court_id (One-to-Many)');
  console.log('3. ✅ محكمة واحدة يمكن أن تكون لها عدة قضايا');
  console.log('4. ✅ قضية واحدة لها محكمة واحدة فقط');
  console.log('5. 🗑️ تم حذف جدول issue_courts');
  console.log('6. 🗑️ تم حذف العمود issue_id من جدول المحاكم');
  console.log('7. ➕ تم إضافة العمود court_id إلى جدول القضايا');
  console.log('8. 🔗 تم إنشاء المفتاح الخارجي الصحيح');
}

// تشغيل الإصلاح
fixCourtsIssuesRelation().catch(console.error);
