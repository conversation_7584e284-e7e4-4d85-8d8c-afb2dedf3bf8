(()=>{var e={};e.id=7244,e.ids=[7244],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.d(t,{P:()=>d});var r=s(64939),c=s(29021),o=s.n(c),n=s(33873),u=s.n(n),i=e([r]);r=(i.then?(await i)():i)[0];let l=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],s=l.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new r.Pool(p);async function d(e,t){let s=await _.connect();try{return await s.query(e,t)}finally{s.release()}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29084:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{GET:()=>n,POST:()=>i,PUT:()=>u});var r=s(32190),c=s(5069),o=e([c]);async function n(){try{let e=await (0,c.P)(`
      SELECT 
        ma.id,
        ma.account_name,
        ma.account_code,
        ma.chart_account_id,
        ma.is_required,
        ma.description,
        coa.account_code as linked_account_code,
        coa.account_name as linked_account_name,
        coa.account_type as linked_account_type
      FROM main_accounts ma
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY 
        CASE 
          WHEN ma.account_name LIKE '%عملاء%' OR ma.account_name LIKE '%العملاء%' THEN 1
          WHEN ma.account_name LIKE '%موظف%' OR ma.account_name LIKE '%الموظفين%' THEN 2
          WHEN ma.account_name LIKE '%مدين%' OR ma.account_name LIKE '%المدينين%' THEN 3
          ELSE 4
        END,
        ma.id
    `);return r.NextResponse.json({success:!0,data:e.rows})}catch(e){return console.error("Error fetching default accounts:",e),r.NextResponse.json({success:!1,error:"فشل في جلب الحسابات الأساسية"},{status:500})}}async function u(e){try{let{id:t,chart_account_id:s}=await e.json();if(!t)return r.NextResponse.json({success:!1,error:"معرف الحساب الأساسي مطلوب"},{status:400});let a=await (0,c.P)("SELECT id, account_name FROM main_accounts WHERE id = $1",[t]);if(0===a.rows.length)return r.NextResponse.json({success:!1,error:"الحساب الأساسي غير موجود"},{status:404});let o=null;if(s){let e=await (0,c.P)("SELECT account_code, account_name FROM chart_of_accounts WHERE id = $1",[s]);if(0===e.rows.length)return r.NextResponse.json({success:!1,error:"الحساب المحدد غير موجود في دليل الحسابات"},{status:404});o=e.rows[0].account_code}return await (0,c.P)(`
      UPDATE main_accounts 
      SET 
        chart_account_id = $1, 
        account_code = $2,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $3
    `,[s||null,o,t]),r.NextResponse.json({success:!0,message:"تم تحديث ربط الحساب الأساسي بنجاح",data:{id:t,chart_account_id:s,account_code:o}})}catch(e){return console.error("Error updating default account:",e),r.NextResponse.json({success:!1,error:"فشل في تحديث ربط الحساب الأساسي"},{status:500})}}async function i(e){try{let{updates:t}=await e.json();if(!t||!Array.isArray(t))return r.NextResponse.json({success:!1,error:"بيانات التحديث مطلوبة"},{status:400});let s=[];for(let e of t){let{id:t,chart_account_id:a}=e;if(t)try{let e=null;if(a){let t=await (0,c.P)("SELECT account_code FROM chart_of_accounts WHERE id = $1",[a]);t.rows.length>0&&(e=t.rows[0].account_code)}await (0,c.P)(`
          UPDATE main_accounts 
          SET 
            chart_account_id = $1, 
            account_code = $2,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = $3
        `,[a||null,e,t]),s.push({id:t,success:!0,chart_account_id:a,account_code:e})}catch(e){console.error(`Error updating account ${t}:`,e),s.push({id:t,success:!1,error:e.message})}}let a=s.filter(e=>e.success).length,o=s.filter(e=>!e.success).length;return r.NextResponse.json({success:0===o,message:`تم تحديث ${a} حساب بنجاح${o>0?` وفشل في ${o} حساب`:""}`,results:s,summary:{total:s.length,success:a,failed:o}})}catch(e){return console.error("Error updating default accounts:",e),r.NextResponse.json({success:!1,error:"فشل في تحديث الحسابات الأساسية"},{status:500})}}c=(o.then?(await o)():o)[0],a()}catch(e){a(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73460:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{patchFetch:()=>i,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var r=s(96559),c=s(48088),o=s(37719),n=s(29084),u=e([n]);n=(u.then?(await u)():u)[0];let d=new r.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/accounting/default-accounts/route",pathname:"/api/accounting/default-accounts",filename:"route",bundlePath:"app/api/accounting/default-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\default-accounts\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:_}=d;function i(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,580],()=>s(73460));module.exports=a})();