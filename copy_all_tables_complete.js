// نسخ جميع الجداول والبيانات من mohammi إلى rubaie بطريقة صحيحة
const { Pool } = require('pg');

async function copyAllTablesComplete() {
  console.log('🔄 بدء نسخ جميع الجداول من mohammi إلى rubaie...\n');

  // الاتصال بقاعدة البيانات الأصلية (mohammi)
  const sourcePool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'mohammi',
    password: 'yemen123',
    port: 5432,
  });

  // الاتصال بقاعدة البيانات الهدف (rubaie)
  const targetPool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'rubaie',
    password: 'yemen123',
    port: 5432,
  });

  try {
    console.log('📋 الخطوة 1: جلب قائمة الجداول من mohammi...');
    
    // جلب قائمة جميع الجداول
    const tablesResult = await sourcePool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    const tables = tablesResult.rows.map(row => row.table_name);
    console.log(`   ✅ تم العثور على ${tables.length} جدول`);
    console.log(`   📋 الجداول: ${tables.join(', ')}`);
    
    // ترتيب الجداول حسب الأولوية (الجداول الأساسية أولاً)
    const priorityTables = [
      'companies', 'users', 'clients', 'employees', 
      'services', 'serviceslow', 'legal_library', 
      'announcements', 'footer_links', 'ai_settings',
      'conversations', 'messages'
    ];
    
    const orderedTables = [
      ...priorityTables.filter(table => tables.includes(table)),
      ...tables.filter(table => !priorityTables.includes(table))
    ];
    
    console.log('\n📋 الخطوة 2: نسخ الجداول بالترتيب الصحيح...');
    
    for (let i = 0; i < orderedTables.length; i++) {
      const tableName = orderedTables[i];
      
      console.log(`\n${'='.repeat(60)}`);
      console.log(`📄 جدول ${i + 1}/${orderedTables.length}: ${tableName}`);
      console.log(`${'='.repeat(60)}`);
      
      try {
        // الخطوة 1: جلب هيكل الجدول
        console.log(`   🔍 جلب هيكل الجدول ${tableName}...`);
        
        const structureResult = await sourcePool.query(`
          SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
          FROM information_schema.columns 
          WHERE table_name = $1 AND table_schema = 'public'
          ORDER BY ordinal_position
        `, [tableName]);
        
        const columns = structureResult.rows;
        console.log(`      ✅ ${columns.length} عمود`);
        
        // الخطوة 2: حذف الجدول إذا كان موجوداً
        console.log(`   🗑️  حذف الجدول ${tableName} إذا كان موجوداً...`);
        await targetPool.query(`DROP TABLE IF EXISTS ${tableName} CASCADE`);
        
        // الخطوة 3: إنشاء الجدول الجديد
        console.log(`   🔨 إنشاء الجدول ${tableName}...`);
        
        // جلب DDL الكامل للجدول
        const createTableResult = await sourcePool.query(`
          SELECT 
            'CREATE TABLE ' || table_name || ' (' ||
            string_agg(
              column_name || ' ' || 
              CASE 
                WHEN data_type = 'character varying' THEN 'VARCHAR(' || character_maximum_length || ')'
                WHEN data_type = 'character' THEN 'CHAR(' || character_maximum_length || ')'
                WHEN data_type = 'text' THEN 'TEXT'
                WHEN data_type = 'integer' THEN 'INTEGER'
                WHEN data_type = 'bigint' THEN 'BIGINT'
                WHEN data_type = 'smallint' THEN 'SMALLINT'
                WHEN data_type = 'boolean' THEN 'BOOLEAN'
                WHEN data_type = 'date' THEN 'DATE'
                WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMPTZ'
                WHEN data_type = 'time without time zone' THEN 'TIME'
                WHEN data_type = 'numeric' THEN 'DECIMAL(' || numeric_precision || ',' || numeric_scale || ')'
                WHEN data_type = 'real' THEN 'REAL'
                WHEN data_type = 'double precision' THEN 'DOUBLE PRECISION'
                WHEN data_type = 'json' THEN 'JSON'
                WHEN data_type = 'jsonb' THEN 'JSONB'
                WHEN data_type = 'uuid' THEN 'UUID'
                WHEN data_type = 'ARRAY' THEN 'TEXT[]'
                ELSE data_type
              END ||
              CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
              CASE WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default ELSE '' END,
              ', '
            ) || ');' as create_statement
          FROM information_schema.columns 
          WHERE table_name = $1 AND table_schema = 'public'
          GROUP BY table_name
        `, [tableName]);
        
        if (createTableResult.rows.length > 0) {
          let createStatement = createTableResult.rows[0].create_statement;
          
          // إصلاحات خاصة للـ SERIAL
          createStatement = createStatement.replace(/INTEGER NOT NULL DEFAULT nextval\([^)]+\)/g, 'SERIAL PRIMARY KEY');
          createStatement = createStatement.replace(/BIGINT NOT NULL DEFAULT nextval\([^)]+\)/g, 'BIGSERIAL PRIMARY KEY');
          
          await targetPool.query(createStatement);
          console.log(`      ✅ تم إنشاء الجدول`);
        }
        
        // الخطوة 4: جلب البيانات
        console.log(`   📊 جلب البيانات من ${tableName}...`);
        
        const dataResult = await sourcePool.query(`SELECT * FROM ${tableName}`);
        const rows = dataResult.rows;
        
        console.log(`      📈 ${rows.length} سجل`);
        
        if (rows.length === 0) {
          console.log(`      ⚠️  الجدول فارغ، تم تخطي نسخ البيانات`);
          continue;
        }
        
        // الخطوة 5: نسخ البيانات
        console.log(`   📥 نسخ البيانات إلى ${tableName}...`);
        
        for (let j = 0; j < rows.length; j++) {
          const row = rows[j];
          
          try {
            const columnNames = Object.keys(row);
            const values = Object.values(row);
            
            // تنظيف القيم
            const cleanValues = values.map(value => {
              if (value === null || value === undefined) return null;
              if (typeof value === 'object' && !Array.isArray(value)) return JSON.stringify(value);
              return value;
            });
            
            const placeholders = cleanValues.map((_, index) => `$${index + 1}`).join(', ');
            const columnsStr = columnNames.join(', ');
            
            const insertQuery = `
              INSERT INTO ${tableName} (${columnsStr}) 
              VALUES (${placeholders})
            `;
            
            await targetPool.query(insertQuery, cleanValues);
            
            if ((j + 1) % 10 === 0 || j === rows.length - 1) {
              console.log(`      📝 تم نسخ ${j + 1}/${rows.length} سجل`);
            }
            
          } catch (insertError) {
            console.log(`      ❌ خطأ في نسخ السجل ${j + 1}:`, insertError.message);
          }
        }
        
        // الخطوة 6: إعادة تعيين sequences
        console.log(`   🔄 إعادة تعيين sequences للجدول ${tableName}...`);
        
        try {
          const sequenceResult = await targetPool.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = $1 AND column_default LIKE 'nextval%'
          `, [tableName]);
          
          for (const seqCol of sequenceResult.rows) {
            const columnName = seqCol.column_name;
            await targetPool.query(`
              SELECT setval(pg_get_serial_sequence($1, $2), COALESCE(MAX(${columnName}), 1)) 
              FROM ${tableName}
            `, [tableName, columnName]);
          }
          
          console.log(`      ✅ تم إعادة تعيين sequences`);
        } catch (seqError) {
          console.log(`      ⚠️  تعذر إعادة تعيين sequences:`, seqError.message);
        }
        
        console.log(`   ✅ تم نسخ الجدول ${tableName} بنجاح`);
        
      } catch (tableError) {
        console.log(`   ❌ خطأ في نسخ الجدول ${tableName}:`, tableError.message);
      }
    }
    
    console.log('\n🎉 تم الانتهاء من نسخ جميع الجداول بنجاح!');
    
    // التحقق من النتائج
    console.log('\n📊 ملخص النتائج:');
    
    for (const tableName of orderedTables) {
      try {
        const countResult = await targetPool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        const count = countResult.rows[0].count;
        console.log(`   📋 ${tableName}: ${count} سجل`);
      } catch (countError) {
        console.log(`   ❌ ${tableName}: خطأ في العد`);
      }
    }

  } catch (error) {
    console.error('❌ خطأ عام في نسخ الجداول:', error);
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }
}

// تشغيل النسخ
copyAllTablesComplete().catch(console.error);
