"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/website-admin/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/folder-plus.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FolderPlus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 10v6\",\n            key: \"1bos4e\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 13h6\",\n            key: \"1uhe8q\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\",\n            key: \"1kt360\"\n        }\n    ]\n];\nconst FolderPlus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder-plus\", __iconNode);\n //# sourceMappingURL=folder-plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-tree.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/folder-tree.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FolderTree)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n            key: \"hod4my\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n            key: \"w4yl2u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5a2 2 0 0 0 2 2h3\",\n            key: \"f2jnh7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 3v13a2 2 0 0 0 2 2h3\",\n            key: \"k8epm1\"\n        }\n    ]\n];\nconst FolderTree = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder-tree\", __iconNode);\n //# sourceMappingURL=folder-tree.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-tree.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star-off.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star-off.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ StarOff)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43\",\n            key: \"16m0ql\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91\",\n            key: \"1vt8nq\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"2\",\n            x2: \"22\",\n            y1: \"2\",\n            y2: \"22\",\n            key: \"a6p6uj\"\n        }\n    ]\n];\nconst StarOff = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"star-off\", __iconNode);\n //# sourceMappingURL=star-off.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star-off.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LegalLibraryManager.tsx":
/*!************************************************!*\
  !*** ./src/components/LegalLibraryManager.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LegalLibraryManager: () => (/* binding */ LegalLibraryManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-tree.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Folder,FolderOpen,FolderTree,RefreshCw,RotateCcw,Save,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _LegalLibraryPathsManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LegalLibraryPathsManager */ \"(app-pages-browser)/./src/components/LegalLibraryPathsManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ LegalLibraryManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LegalLibraryManager() {\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paths, setPaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPath, setNewPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [syncing, setSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPaths, setShowPaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddPath, setShowAddPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPath, setEditingPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPathData, setNewPathData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        path_name: '',\n        path_value: '',\n        description: '',\n        is_default: false,\n        scan_enabled: true\n    });\n    // تحميل البيانات الأولية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LegalLibraryManager.useEffect\": ()=>{\n            loadLibraryData();\n            loadPaths();\n        }\n    }[\"LegalLibraryManager.useEffect\"], []);\n    // تحميل بيانات المكتبة\n    const loadLibraryData = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/legal-library/files?action=list');\n            const result = await response.json();\n            if (result.success) {\n                setFiles(result.data.files || []);\n                setSettings(result.data.settings || {});\n                setCurrentPath(result.data.path || '');\n                setNewPath(result.data.path || '');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحميل البيانات');\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل بيانات المكتبة:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تحميل المسارات\n    const loadPaths = async ()=>{\n        try {\n            const response = await fetch('/api/legal-library/paths');\n            const result = await response.json();\n            if (result.success) {\n                setPaths(result.data || []);\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحميل المسارات');\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل المسارات:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // تحديث مسار المكتبة\n    const updateLibraryPath = async ()=>{\n        if (!newPath.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('يجب إدخال مسار صحيح');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/system-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    setting_key: 'legal_library_path',\n                    setting_value: newPath.trim()\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setCurrentPath(newPath.trim());\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('تم تحديث مسار المكتبة بنجاح');\n                // إعادة تحميل البيانات\n                await loadLibraryData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحديث المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في تحديث المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // إضافة مسار جديد\n    const addNewPath = async ()=>{\n        if (!newPathData.path_name.trim() || !newPathData.path_value.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('اسم المسار والقيمة مطلوبان');\n            return;\n        }\n        try {\n            const response = await fetch('/api/legal-library/paths', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newPathData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('تم إضافة المسار بنجاح');\n                setNewPathData({\n                    path_name: '',\n                    path_value: '',\n                    description: '',\n                    is_default: false,\n                    scan_enabled: true\n                });\n                setShowAddPath(false);\n                await loadPaths();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في إضافة المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // تحديث مسار موجود\n    const updatePath = async (pathId, updates)=>{\n        try {\n            const response = await fetch('/api/legal-library/paths', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    id: pathId,\n                    ...updates\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('تم تحديث المسار بنجاح');\n                await loadPaths();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في تحديث المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في تحديث المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // حذف مسار\n    const deletePath = async (pathId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المسار؟')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/legal-library/paths?id=\".concat(pathId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('تم حذف المسار بنجاح');\n                await loadPaths();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في حذف المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // مزامنة الملفات\n    const syncFiles = async ()=>{\n        setSyncing(true);\n        try {\n            const response = await fetch('/api/legal-library/files', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'sync'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(result.message || 'تم مزامنة الملفات بنجاح');\n                // إعادة تحميل البيانات\n                await loadLibraryData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(result.error || 'خطأ في المزامنة');\n            }\n        } catch (error) {\n            console.error('خطأ في مزامنة الملفات:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setSyncing(false);\n        }\n    };\n    // تنسيق حجم الملف\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // تنسيق التاريخ\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('ar-SA', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"إدارة ملفات المكتبة القانونية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setShowPaths(!showPaths),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إدارة المسارات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"الإعدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: loadLibraryData,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            showPaths && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegalLibraryPathsManager__WEBPACK_IMPORTED_MODULE_9__.LegalLibraryPathsManager, {\n                onPathsChange: ()=>{\n                    loadPaths();\n                    loadLibraryData();\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                \"إعدادات المكتبة القانونية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"library-path\",\n                                        children: \"مسار مجلد المكتبة القانونية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"library-path\",\n                                                value: newPath,\n                                                onChange: (e)=>setNewPath(e.target.value),\n                                                placeholder: \"/home/<USER>/Downloads/legal-system/laws\",\n                                                className: \"flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: updateLibraryPath,\n                                                disabled: loading || newPath === currentPath,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"حفظ\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            \"المسار الحالي: \",\n                                            currentPath\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            settings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"الامتدادات المسموحة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: settings.legal_library_allowed_extensions || '.pdf,.doc,.docx,.txt'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"الحد الأقصى لحجم الملف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    settings.legal_library_max_file_size || 50,\n                                                    \" ميجابايت\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: \"مجلد المكتبة القانونية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: currentPath\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                        variant: \"secondary\",\n                                        children: [\n                                            files.length,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: syncFiles,\n                                        disabled: syncing,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(syncing ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            syncing ? 'جاري المزامنة...' : 'مزامنة الملفات'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملفات الموجودة (\",\n                                files.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin mx-auto text-blue-600 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"جاري تحميل الملفات...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this) : files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"لا توجد ملفات في المجلد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mt-1\",\n                                    children: [\n                                        \"أضف ملفات إلى المجلد: \",\n                                        currentPath\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: formatFileSize(file.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: file.extension\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"آخر تعديل: \",\n                                                                        formatDate(file.modified)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                variant: \"outline\",\n                                                children: file.extension\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Folder_FolderOpen_FolderTree_RefreshCw_RotateCcw_Save_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"تعليمات الاستخدام:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mt-2 space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"إدارة المسارات:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" أضف مسارات متعددة للمكتبة القانونية وحدد المسار الافتراضي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"إضافة الملفات:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" أضف الملفات مباشرة إلى أي من المجلدات المحددة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"المزامنة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            ' اضغط على \"مزامنة الملفات\" لإضافة الملفات الجديدة إلى قاعدة البيانات'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"الإعدادات:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" يمكن تخصيص الامتدادات المسموحة والحد الأقصى لحجم الملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"الامتدادات المدعومة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" PDF, DOC, DOCX, TXT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"المسارات النشطة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" يتم فحص المسارات المفعلة فقط للملفات الجديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryManager.tsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, this);\n}\n_s(LegalLibraryManager, \"JnqKYPFMRPtp6M+ZZa5A9g0Edgs=\");\n_c = LegalLibraryManager;\nvar _c;\n$RefreshReg$(_c, \"LegalLibraryManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LegalLibraryManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LegalLibraryPathsManager.tsx":
/*!*****************************************************!*\
  !*** ./src/components/LegalLibraryPathsManager.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LegalLibraryPathsManager: () => (/* binding */ LegalLibraryPathsManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star-off.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,EyeOff,FolderPlus,Plus,Save,Star,StarOff,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ LegalLibraryPathsManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LegalLibraryPathsManager(param) {\n    let { onPathsChange } = param;\n    _s();\n    const [paths, setPaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPath, setEditingPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        path_name: '',\n        path_value: '',\n        description: '',\n        is_default: false,\n        scan_enabled: true\n    });\n    // تحميل المسارات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LegalLibraryPathsManager.useEffect\": ()=>{\n            loadPaths();\n        }\n    }[\"LegalLibraryPathsManager.useEffect\"], []);\n    const loadPaths = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/legal-library/paths');\n            const result = await response.json();\n            if (result.success) {\n                setPaths(result.data || []);\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(result.error || 'خطأ في تحميل المسارات');\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل المسارات:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // إضافة مسار جديد\n    const handleAddPath = async ()=>{\n        if (!formData.path_name.trim() || !formData.path_value.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('اسم المسار والقيمة مطلوبان');\n            return;\n        }\n        try {\n            const response = await fetch('/api/legal-library/paths', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success('تم إضافة المسار بنجاح');\n                resetForm();\n                await loadPaths();\n                onPathsChange === null || onPathsChange === void 0 ? void 0 : onPathsChange();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(result.error || 'خطأ في إضافة المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // تحديث مسار\n    const handleUpdatePath = async ()=>{\n        if (!editingPath) return;\n        try {\n            const response = await fetch('/api/legal-library/paths', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    id: editingPath.id,\n                    ...formData\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success('تم تحديث المسار بنجاح');\n                resetForm();\n                await loadPaths();\n                onPathsChange === null || onPathsChange === void 0 ? void 0 : onPathsChange();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(result.error || 'خطأ في تحديث المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في تحديث المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // حذف مسار\n    const handleDeletePath = async (pathId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المسار؟')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/legal-library/paths?id=\".concat(pathId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success('تم حذف المسار بنجاح');\n                await loadPaths();\n                onPathsChange === null || onPathsChange === void 0 ? void 0 : onPathsChange();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(result.error || 'خطأ في حذف المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // تبديل حالة المسار\n    const togglePathStatus = async (pathId, field, value)=>{\n        try {\n            const response = await fetch('/api/legal-library/paths', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    id: pathId,\n                    [field]: value\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await loadPaths();\n                onPathsChange === null || onPathsChange === void 0 ? void 0 : onPathsChange();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(result.error || 'خطأ في تحديث المسار');\n            }\n        } catch (error) {\n            console.error('خطأ في تحديث المسار:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('خطأ في الاتصال بالخادم');\n        }\n    };\n    // إعداد النموذج للتعديل\n    const startEdit = (path)=>{\n        setEditingPath(path);\n        setFormData({\n            path_name: path.path_name,\n            path_value: path.path_value,\n            description: path.description,\n            is_default: path.is_default,\n            scan_enabled: path.scan_enabled\n        });\n        setShowAddForm(true);\n    };\n    // إعادة تعيين النموذج\n    const resetForm = ()=>{\n        setFormData({\n            path_name: '',\n            path_value: '',\n            description: '',\n            is_default: false,\n            scan_enabled: true\n        });\n        setEditingPath(null);\n        setShowAddForm(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"إدارة مسارات المكتبة القانونية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setShowAddForm(true),\n                        size: \"sm\",\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            \"إضافة مسار جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: editingPath ? 'تعديل المسار' : 'إضافة مسار جديد'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: resetForm,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"path_name\",\n                                                children: \"اسم المسار *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"path_name\",\n                                                value: formData.path_name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        path_name: e.target.value\n                                                    }),\n                                                placeholder: \"مثال: المسار الرئيسي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"path_value\",\n                                                children: \"مسار المجلد *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"path_value\",\n                                                value: formData.path_value,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        path_value: e.target.value\n                                                    }),\n                                                placeholder: \"D:\\\\mohaminew\\\\legal-documents\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"description\",\n                                        children: \"الوصف\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        id: \"description\",\n                                        value: formData.description,\n                                        onChange: (e)=>setFormData({\n                                                ...formData,\n                                                description: e.target.value\n                                            }),\n                                        placeholder: \"وصف مختصر للمسار\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                checked: formData.is_default,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        is_default: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"مسار افتراضي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                checked: formData.scan_enabled,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        scan_enabled: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                children: \"تفعيل الفحص\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: editingPath ? handleUpdatePath : handleAddPath,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            editingPath ? 'تحديث المسار' : 'إضافة المسار'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: resetForm,\n                                        variant: \"outline\",\n                                        children: \"إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: [\n                                \"المسارات المتاحة (\",\n                                paths.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"جاري تحميل المسارات...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this) : paths.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"لا توجد مسارات محددة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 mt-1\",\n                                    children: \"أضف مسار جديد للبدء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: paths.map((path)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: path.path_name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 space-x-reverse\",\n                                                            children: [\n                                                                path.is_default && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"bg-yellow-100 text-yellow-800\",\n                                                                    children: \"افتراضي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                path.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"bg-green-100 text-green-800\",\n                                                                    children: \"نشط\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    children: \"غير نشط\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                path.scan_enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: \"فحص مفعل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-1\",\n                                                    children: path.path_value\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 21\n                                                }, this),\n                                                path.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: path.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>togglePathStatus(path.id, 'is_default', !path.is_default),\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: path.is_default ? 'إلغاء الافتراضي' : 'جعل افتراضي',\n                                                    children: path.is_default ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-yellow-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>togglePathStatus(path.id, 'is_active', !path.is_active),\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: path.is_active ? 'إلغاء التفعيل' : 'تفعيل',\n                                                    children: path.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>startEdit(path),\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: \"تعديل\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleDeletePath(path.id),\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    title: \"حذف\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_EyeOff_FolderPlus_Plus_Save_Star_StarOff_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, path.id, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\LegalLibraryPathsManager.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(LegalLibraryPathsManager, \"Pw4br65aOXv5LLNKCeZ9vzrxSfw=\");\n_c = LegalLibraryPathsManager;\nvar _c;\n$RefreshReg$(_c, \"LegalLibraryPathsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LegalLibraryPathsManager.tsx\n"));

/***/ })

});