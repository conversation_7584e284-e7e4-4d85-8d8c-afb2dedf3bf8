// إعداد نظام المحادثة
const { Pool } = require('pg');

async function setupChatSystem() {
  console.log('🔧 إعداد نظام المحادثة...\n');

  // قواعد البيانات المطلوب تحديثها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 معالجة قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // إنشاء جدول سجلات المحادثة
      console.log('   🔨 إنشاء جدول chat_logs...');
      
      await pool.query(`
        CREATE TABLE IF NOT EXISTS chat_logs (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255),
          user_message TEXT NOT NULL,
          bot_response TEXT NOT NULL,
          response_type VARCHAR(50),
          user_ip VARCHAR(45),
          user_agent TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء فهارس
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_logs_session ON chat_logs(session_id)
      `);
      
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_logs_created_at ON chat_logs(created_at)
      `);
      
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_logs_response_type ON chat_logs(response_type)
      `);

      console.log('   ✅ تم إنشاء جدول سجلات المحادثة والفهارس');

      // التحقق من وجود جدول ai_settings
      const aiSettingsCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'ai_settings'
        )
      `);

      if (!aiSettingsCheck.rows[0].exists) {
        console.log('   🔨 إنشاء جدول ai_settings...');
        
        await pool.query(`
          CREATE TABLE ai_settings (
            id SERIAL PRIMARY KEY,
            is_enabled BOOLEAN DEFAULT true,
            welcome_message TEXT DEFAULT 'مرحباً بك! كيف يمكنني مساعدتك؟',
            default_response TEXT DEFAULT 'شكراً لتواصلك معنا. سيتم الرد عليك قريباً.',
            auto_respond BOOLEAN DEFAULT true,
            keywords_trigger TEXT[] DEFAULT ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات'],
            max_response_length INTEGER DEFAULT 500,
            response_delay INTEGER DEFAULT 1000,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // إدراج الإعدادات الافتراضية
        await pool.query(`
          INSERT INTO ai_settings (
            is_enabled, 
            welcome_message, 
            default_response, 
            auto_respond,
            keywords_trigger
          ) VALUES (
            true,
            'مرحباً بك في مكتبنا القانوني! 👋\n\nأنا مساعدك الذكي، يمكنني مساعدتك في:\n• معلومات عن خدماتنا القانونية\n• بيانات التواصل\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟',
            'شكراً لتواصلك معنا. رسالتك مهمة بالنسبة لنا وسيتم الرد عليك من قبل فريقنا المختص في أقرب وقت ممكن.',
            true,
            ARRAY['مساعدة', 'استفسار', 'سؤال', 'معلومات', 'خدمة', 'تواصل', 'مرحبا', 'السلام عليكم']
          )
        `);

        console.log('   ✅ تم إنشاء جدول ai_settings مع الإعدادات الافتراضية');
      } else {
        console.log('   ✅ جدول ai_settings موجود بالفعل');
      }

      // التحقق من النتائج
      const chatLogsCount = await pool.query('SELECT COUNT(*) as count FROM chat_logs');
      const aiSettingsCount = await pool.query('SELECT COUNT(*) as count FROM ai_settings');
      
      console.log(`   📊 سجلات المحادثة: ${chatLogsCount.rows[0].count}`);
      console.log(`   📊 إعدادات الذكاء الاصطناعي: ${aiSettingsCount.rows[0].count}`);

    } catch (error) {
      console.error(`   ❌ خطأ في معالجة قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('');
  }

  console.log('✅ تم الانتهاء من إعداد نظام المحادثة');
}

// تشغيل الإعداد
setupChatSystem().catch(console.error);
