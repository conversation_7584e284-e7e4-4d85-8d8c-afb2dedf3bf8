# دليل نظام الصلاحيات والأدوار المتقدم

## 🎯 نظرة عامة

تم تطوير نظام صلاحيات متقدم يدعم:
- **55 صلاحية مفصلة** لجميع أجزاء النظام
- **10 أدوار محددة مسبقاً** مع إمكانية إضافة المزيد
- **أدوار متعددة للمستخدم الواحد** (سكرتير + محاسب)
- **واجهات إدارة متقدمة** لسهولة الاستخدام

## 🔍 الوصول لواجهات الإدارة

### 1. **إدارة المستخدمين والأدوار**:
```
🌐 الرابط: http://localhost:7443/users
👥 الوصول: زر "إدارة الأدوار" (أيقونة بنفسجية)
🛡️ الصلاحيات: زر "إدارة الصلاحيات" (أيقونة خضراء)
```

### 2. **إدارة الأدوار المستقلة**:
```
🌐 الرابط: http://localhost:7443/roles
⚙️ الوظائف: إنشاء، تعديل، حذف الأدوار
🔧 الصلاحيات المطلوبة: users_permissions أو system_admin
```

## 🎭 الأدوار المتاحة

### **الأدوار الأساسية**:
1. **مدير النظام** (admin): جميع الصلاحيات (55 صلاحية)
2. **مدير** (manager): صلاحيات إدارية عامة
3. **مستعرض** (viewer): عرض البيانات فقط (4 صلاحيات)

### **الأدوار التخصصية**:
4. **محامي** (lawyer): إدارة القضايا والمتابعات (7 صلاحيات)
5. **سكرتير** (secretary): إدارة المتابعات والعملاء (6 صلاحيات)
6. **محاسب** (accountant): إدارة الحسابات والسندات (7 صلاحيات)

### **الأدوار المختلطة الجديدة**:
7. **سكرتير + محاسب** (secretary_accountant): دور مختلط (13 صلاحية)
8. **محامي + مدير** (lawyer_manager): محامي مع صلاحيات إدارية (12 صلاحية)
9. **سكرتير أول** (senior_secretary): سكرتير مع صلاحيات إضافية (10 صلاحيات)
10. **مدير مالي** (financial_manager): إدارة مالية شاملة (12 صلاحية)

## 🔧 كيفية إضافة أدوار متعددة للمستخدم

### **الطريقة الأولى: من صفحة المستخدمين**
1. اذهب إلى `http://localhost:7443/users`
2. في جدول المستخدمين، انقر على الزر البنفسجي 👥 (إدارة الأدوار)
3. ستفتح نافذة "إدارة أدوار المستخدم"
4. حدد الأدوار المطلوبة (يمكن تحديد أكثر من دور)
5. انقر "حفظ الأدوار"

### **مثال عملي**:
```
👤 المستخدم: أحمد محمد
🎭 الأدوار المحددة:
   ✅ سكرتير (secretary)
   ✅ محاسب (accountant)
   
🛡️ النتيجة: سيحصل على جميع صلاحيات السكرتارية + المحاسبة
📊 إجمالي الصلاحيات: 13 صلاحية مجمعة
```

## 📋 الصلاحيات المفصلة (55 صلاحية)

### **القضايا (5 صلاحيات)**:
- `issues_view`: عرض القضايا
- `issues_add`: إضافة قضية جديدة
- `issues_edit`: تعديل القضايا
- `issues_delete`: حذف القضايا
- `issues_print`: طباعة القضايا

### **العملاء (5 صلاحيات)**:
- `clients_view`: عرض العملاء
- `clients_add`: إضافة عميل جديد
- `clients_edit`: تعديل بيانات العملاء
- `clients_delete`: حذف العملاء
- `clients_print`: طباعة بيانات العملاء

### **المتابعات (5 صلاحيات)**:
- `follows_view`: عرض المتابعات
- `follows_add`: إضافة متابعة جديدة
- `follows_edit`: تعديل المتابعات
- `follows_delete`: حذف المتابعات
- `follows_print`: طباعة المتابعات

### **المحاسبة (5 صلاحيات)**:
- `accounting_view`: عرض البيانات المحاسبية
- `accounting_add`: إضافة قيود محاسبية
- `accounting_edit`: تعديل القيود المحاسبية
- `accounting_delete`: حذف القيود المحاسبية
- `accounting_print`: طباعة التقارير المحاسبية

### **السندات (5 صلاحيات)**:
- `vouchers_view`: عرض السندات
- `vouchers_add`: إضافة سند جديد
- `vouchers_edit`: تعديل السندات
- `vouchers_delete`: حذف السندات
- `vouchers_print`: طباعة السندات

### **المستخدمين (5 صلاحيات)**:
- `users_view`: عرض المستخدمين
- `users_add`: إضافة مستخدم جديد
- `users_edit`: تعديل بيانات المستخدمين
- `users_delete`: حذف المستخدمين
- `users_permissions`: إدارة صلاحيات المستخدمين

### **التقارير (3 صلاحيات)**:
- `reports_view`: عرض التقارير
- `reports_print`: طباعة التقارير
- `reports_export`: تصدير التقارير

### **الإعدادات (3 صلاحيات)**:
- `settings_view`: عرض الإعدادات
- `settings_edit`: تعديل إعدادات النظام
- `website_admin`: إدارة الموقع الإلكتروني

### **البيانات الأساسية (16 صلاحية)**:
- **المحاكم**: `courts_view`, `courts_add`, `courts_edit`, `courts_delete`
- **الفروع**: `branches_view`, `branches_add`, `branches_edit`, `branches_delete`
- **الموظفين**: `employees_view`, `employees_add`, `employees_edit`, `employees_delete`
- **النسب المالية**: `percentages_view`, `percentages_add`, `percentages_edit`, `percentages_delete`

### **صلاحيات خاصة (3 صلاحيات)**:
- `system_admin`: مدير النظام الكامل
- `backup_restore`: النسخ الاحتياطي والاستعادة
- `system_logs`: عرض سجلات النظام

## 🎯 أمثلة عملية للاستخدام

### **مثال 1: موظف استقبال**
```
🎭 الأدوار المقترحة:
   ✅ سكرتير (secretary)
   
🛡️ الصلاحيات الناتجة:
   - عرض وإضافة العملاء
   - عرض وإضافة المتابعات
   - عرض القضايا
   - عرض التقارير
```

### **مثال 2: محاسب مساعد**
```
🎭 الأدوار المقترحة:
   ✅ محاسب (accountant)
   ✅ مستعرض (viewer)
   
🛡️ الصلاحيات الناتجة:
   - جميع صلاحيات المحاسبة
   - جميع صلاحيات السندات
   - عرض التقارير المالية
   - عرض البيانات الأساسية
```

### **مثال 3: مدير فرع**
```
🎭 الأدوار المقترحة:
   ✅ محامي + مدير (lawyer_manager)
   ✅ سكرتير أول (senior_secretary)
   
🛡️ الصلاحيات الناتجة:
   - إدارة كاملة للقضايا والمتابعات
   - إدارة العملاء
   - عرض المستخدمين
   - طباعة وتصدير التقارير
   - عرض البيانات الأساسية
```

## ⚙️ إنشاء أدوار مخصصة

### **من صفحة إدارة الأدوار**:
1. اذهب إلى `http://localhost:7443/roles`
2. انقر "إضافة دور جديد"
3. املأ البيانات:
   - **اسم الدور**: مثل `senior_accountant`
   - **الاسم المعروض**: مثل "محاسب أول"
   - **الوصف**: وصف مختصر للدور
4. حدد الصلاحيات المطلوبة
5. انقر "إضافة الدور"

### **مثال: إنشاء دور "مساعد قانوني"**:
```
📝 اسم الدور: legal_assistant
📛 الاسم المعروض: مساعد قانوني
📄 الوصف: مساعد قانوني يمكنه مساعدة المحامين

🛡️ الصلاحيات المقترحة:
   ✅ issues_view (عرض القضايا)
   ✅ clients_view (عرض العملاء)
   ✅ clients_add (إضافة عملاء)
   ✅ follows_view (عرض المتابعات)
   ✅ follows_add (إضافة متابعات)
   ✅ reports_view (عرض التقارير)
   ✅ courts_view (عرض المحاكم)
```

## 🔒 نصائح الأمان

### **أفضل الممارسات**:
1. **مبدأ الحد الأدنى**: امنح أقل الصلاحيات المطلوبة فقط
2. **مراجعة دورية**: راجع صلاحيات المستخدمين شهرياً
3. **توثيق التغييرات**: سجل سبب منح أو إزالة الصلاحيات
4. **فصل الواجبات**: لا تمنح صلاحيات متضاربة لنفس المستخدم

### **تحذيرات مهمة**:
⚠️ **لا تمنح صلاحية `system_admin` إلا للمديرين الموثوقين**  
⚠️ **راجع صلاحيات الحذف بعناية** (`*_delete`)  
⚠️ **احذر من الأدوار المختلطة** قد تمنح صلاحيات غير مرغوبة  

## 📊 مراقبة الصلاحيات

### **التحقق من صلاحيات المستخدم**:
1. اذهب إلى صفحة المستخدمين
2. انقر على زر "إدارة الأدوار" للمستخدم
3. في تبويب "الصلاحيات التفصيلية" ستجد:
   - جميع الأدوار المحددة
   - الصلاحيات المجمعة من كل دور
   - إجمالي عدد الصلاحيات

### **تقرير الصلاحيات**:
```sql
-- استعلام لعرض صلاحيات مستخدم معين
SELECT 
  u.username,
  u.name,
  string_agg(ura.role_name, ', ') as roles,
  array_length(get_user_combined_permissions(u.id), 1) as total_permissions
FROM users u
LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = true
WHERE u.id = [USER_ID]
GROUP BY u.id, u.username, u.name;
```

## 🎉 الخلاصة

النظام الجديد يوفر:
- ✅ **مرونة كاملة** في إدارة الصلاحيات
- ✅ **أدوار متعددة** للمستخدم الواحد
- ✅ **واجهات سهلة** للإدارة
- ✅ **أمان محسن** مع مراقبة دقيقة
- ✅ **قابلية التوسع** لإضافة أدوار جديدة

**الآن يمكنك إنشاء أي تركيبة من الأدوار والصلاحيات حسب احتياجات مكتبك القانوني!** 🚀
