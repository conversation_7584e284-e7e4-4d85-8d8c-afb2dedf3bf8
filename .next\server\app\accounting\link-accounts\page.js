(()=>{var e={};e.id=6930,e.ids=[6930],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12701:(e,t,s)=>{Promise.resolve().then(s.bind(s,71454))},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>x});var a=s(60687),r=s(43210),l=s(58106),n=s(78272),i=s(3589),c=s(13964),d=s(4780);let o=l.bL;l.YJ;let x=l.WT,u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-gray-600 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 text-gray-700"})})]}));u.displayName=l.l9.displayName;let m=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));m.displayName=l.PP.displayName;let h=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let p=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})}));p.displayName=l.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.JU.displayName;let f=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:t})]}));f.displayName=l.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26110:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d={children:["",{children:["accounting",{children:["link-accounts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71454)),"D:\\mohaminew\\src\\app\\accounting\\link-accounts\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\link-accounts\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/accounting/link-accounts/page",pathname:"/accounting/link-accounts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70352:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>x});var a=s(60687),r=s(43210),l=s(49384);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=l.$;var c=s(4780);let d=((e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return i(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:r,defaultVariants:l}=t,c=Object.keys(r).map(e=>{let t=null==s?void 0:s[e],a=null==l?void 0:l[e];if(null===t)return null;let i=n(t)||n(a);return r[e][i]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return i(e,c,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...d}[t]):({...l,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)})("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,c.cn)(d({variant:t}),e),...s}));o.displayName="Alert",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h5",{ref:s,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let x=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",e),...t}));x.displayName="AlertDescription"},71454:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\link-accounts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\link-accounts\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(60687),r=s(43210),l=s(4780);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("label",{ref:s,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName="Label"},82845:(e,t,s)=>{Promise.resolve().then(s.bind(s,95352))},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>i});var a=s(60687),r=s(43210),l=s(4780);let n=r.createContext(void 0),i=r.forwardRef(({defaultValue:e="",value:t,onValueChange:s,children:i,className:c,...d},o)=>{let[x,u]=r.useState(e),m=t??x,h=r.useCallback(e=>{void 0===t&&u(e),s?.(e)},[t,s]);return(0,a.jsx)(n.Provider,{value:{value:m,onValueChange:h},children:(0,a.jsx)("div",{ref:o,className:(0,l.cn)("",c),...d,children:i})})});i.displayName="Tabs";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500",e),...t}));c.displayName="TabsList";let d=r.forwardRef(({className:e,value:t,...s},i)=>{let c=r.useContext(n);if(!c)throw Error("TabsTrigger must be used within a Tabs component");let d=c.value===t;return(0,a.jsx)("button",{ref:i,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",d?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900",e),onClick:()=>c.onValueChange(t),...s})});d.displayName="TabsTrigger";let o=r.forwardRef(({className:e,value:t,...s},i)=>{let c=r.useContext(n);if(!c)throw Error("TabsContent must be used within a Tabs component");return c.value!==t?null:(0,a.jsx)("div",{ref:i,className:(0,l.cn)("mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",e),...s})});o.displayName="TabsContent"},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},95352:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(60687),r=s(43210),l=s(98254),n=s(44493),i=s(29523),c=s(89667),d=s(80013),o=s(96834),x=s(15079),u=s(70352),m=s(85763),h=s(84821),p=s(84027),f=s(96474),j=s(93613),v=s(41312),g=s(93508),b=s(88233),y=s(11860),N=s(8819);function w(){let[e,t]=(0,r.useState)([]),[s,w]=(0,r.useState)([]),[_,k]=(0,r.useState)([]),[C,A]=(0,r.useState)([]),[P,R]=(0,r.useState)(!0),[T,S]=(0,r.useState)(null),[q,E]=(0,r.useState)(!1),[M,V]=(0,r.useState)({control_account_id:0,linked_table:"clients",auto_create:!0,account_prefix:"C"}),Z=async()=>{try{let e=await fetch("/api/accounting/chart-of-accounts"),t=await e.json();if(t.success){let e=t.accounts.filter(e=>e.linked_table&&e.auto_create_sub_accounts).map(e=>({id:e.id.toString(),control_account_id:e.id,control_account_name:e.account_name,linked_table:e.linked_table,auto_create:e.auto_create_sub_accounts||!1,account_prefix:"clients"===e.linked_table?"C":"E"}));A(e)}}catch(e){console.error("خطأ في جلب قواعد الربط:",e)}},J=async()=>{try{if(!M.control_account_id||!M.linked_table)return void S({type:"error",text:"يرجى ملء جميع الحقول المطلوبة"});let e=await fetch("/api/accounting/chart-of-accounts",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:M.control_account_id,linked_table:M.linked_table,auto_create_sub_accounts:M.auto_create})}),t=await e.json();t.success?(S({type:"success",text:"تم إضافة قاعدة الربط بنجاح"}),E(!1),V({control_account_id:0,linked_table:"clients",auto_create:!0,account_prefix:"C"}),await Z()):S({type:"error",text:t.error||"فشل في إضافة قاعدة الربط"})}catch(e){console.error("خطأ في إضافة قاعدة الربط:",e),S({type:"error",text:"حدث خطأ أثناء إضافة قاعدة الربط"})}},$=async e=>{if(confirm("هل أنت متأكد من حذف قاعدة الربط هذه؟"))try{let t=C.find(t=>t.id===e);if(!t)return;let s=await fetch("/api/accounting/chart-of-accounts",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:t.control_account_id,linked_table:null,auto_create_sub_accounts:!1})}),a=await s.json();a.success?(S({type:"success",text:"تم حذف قاعدة الربط بنجاح"}),await Z()):S({type:"error",text:a.error||"فشل في حذف قاعدة الربط"})}catch(e){console.error("خطأ في حذف قاعدة الربط:",e),S({type:"error",text:"حدث خطأ أثناء حذف قاعدة الربط"})}},z=(e,t)=>("clients"===e?"C":"E")+String(t).padStart(6,"0");return P?(0,a.jsx)(l.O,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin mx-auto mb-2"}),(0,a.jsx)("span",{children:"جاري التحميل..."})]})})}):(0,a.jsx)(l.O,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-emerald-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"الحسابات الافتراضية"}),(0,a.jsx)("p",{className:"text-gray-600",children:"تحديد الحسابات الافتراضية للصندوق والعملاء والموظفين والمصروفات والإيرادات مع الربط التلقائي"})]})]}),(0,a.jsxs)(i.$,{onClick:()=>E(!0),className:"bg-emerald-600 hover:bg-emerald-700",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 ml-2"}),"إضافة قاعدة ربط"]})]}),T&&(0,a.jsxs)(u.Fc,{className:"success"===T.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)(u.TN,{className:"success"===T.type?"text-green-800":"text-red-800",children:T.text})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"حسابات التحكم"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:s.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"العملاء النشطين"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:_.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"الموظفين النشطين"})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-emerald-600",children:C.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"قواعد الربط النشطة"})]})})]}),(0,a.jsxs)(m.tU,{defaultValue:"rules",className:"w-full",children:[(0,a.jsxs)(m.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(m.Xi,{value:"rules",children:"قواعد الربط"}),(0,a.jsx)(m.Xi,{value:"clients",children:"حسابات العملاء"}),(0,a.jsx)(m.Xi,{value:"employees",children:"حسابات الموظفين"})]}),(0,a.jsx)(m.av,{value:"rules",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 ml-2"}),"قواعد الربط النشطة (",C.length,")"]})}),(0,a.jsx)(n.Wu,{children:0===C.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا توجد قواعد ربط نشطة"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:'انقر على "إضافة قاعدة ربط" لبدء الإعداد'})]}):(0,a.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${"clients"===e.linked_table?"bg-green-100":"bg-purple-100"}`,children:"clients"===e.linked_table?(0,a.jsx)(v.A,{className:`h-5 w-5 ${"clients"===e.linked_table?"text-green-600":"text-purple-600"}`}):(0,a.jsx)(g.A,{className:`h-5 w-5 ${"clients"===e.linked_table?"text-green-600":"text-purple-600"}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.control_account_name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["مرتبط بـ ","clients"===e.linked_table?"العملاء":"الموظفين","- بادئة الحساب: ",e.account_prefix]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(o.E,{variant:e.auto_create?"default":"secondary",children:e.auto_create?"إنشاء تلقائي":"يدوي"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>$(e.id),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]})]},e.id))})})]})}),(0,a.jsx)(m.av,{value:"clients",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 ml-2"}),"حسابات العملاء (",s.length,")"]})}),(0,a.jsx)(n.Wu,{children:0===s.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(v.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا يوجد عملاء نشطين"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-right p-3",children:"رقم الحساب"}),(0,a.jsx)("th",{className:"text-right p-3",children:"اسم العميل"}),(0,a.jsx)("th",{className:"text-right p-3",children:"الهاتف"}),(0,a.jsx)("th",{className:"text-right p-3",children:"البريد الإلكتروني"}),(0,a.jsx)("th",{className:"text-center p-3",children:"الحالة"})]})}),(0,a.jsx)("tbody",{children:s.map(e=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"p-3 font-mono text-green-600 font-medium",children:z("clients",e.id)}),(0,a.jsx)("td",{className:"p-3 font-medium",children:e.name}),(0,a.jsx)("td",{className:"p-3 text-gray-600",children:e.phone}),(0,a.jsx)("td",{className:"p-3 text-gray-600",children:e.email||"-"}),(0,a.jsx)("td",{className:"p-3 text-center",children:(0,a.jsx)(o.E,{variant:"active"===e.status?"default":"secondary",children:"active"===e.status?"نشط":"غير نشط"})})]},e.id))})]})})})]})}),(0,a.jsx)(m.av,{value:"employees",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 ml-2"}),"حسابات الموظفين (",_.length,")"]})}),(0,a.jsx)(n.Wu,{children:0===_.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا يوجد موظفين نشطين"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-right p-3",children:"رقم الحساب"}),(0,a.jsx)("th",{className:"text-right p-3",children:"اسم الموظف"}),(0,a.jsx)("th",{className:"text-right p-3",children:"المنصب"}),(0,a.jsx)("th",{className:"text-right p-3",children:"الهاتف"}),(0,a.jsx)("th",{className:"text-center p-3",children:"الحالة"})]})}),(0,a.jsx)("tbody",{children:_.map(e=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"p-3 font-mono text-purple-600 font-medium",children:z("employees",e.id)}),(0,a.jsx)("td",{className:"p-3 font-medium",children:e.name}),(0,a.jsx)("td",{className:"p-3 text-gray-600",children:e.position}),(0,a.jsx)("td",{className:"p-3 text-gray-600",children:e.phone}),(0,a.jsx)("td",{className:"p-3 text-center",children:(0,a.jsx)(o.E,{variant:"نشط"===e.status?"default":"secondary",children:e.status})})]},e.id))})]})})})]})})]}),q&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"إضافة قاعدة ربط جديدة"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>E(!1),children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"control-account",children:"حساب التحكم"}),(0,a.jsxs)(x.l6,{value:M.control_account_id?.toString()||"",onValueChange:e=>V({...M,control_account_id:parseInt(e)}),children:[(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{placeholder:"اختر حساب التحكم"})}),(0,a.jsx)(x.gC,{children:e.map(e=>(0,a.jsxs)(x.eb,{value:e.id.toString(),children:[e.account_code," - ",e.account_name]},e.id))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"linked-table",children:"نوع الربط"}),(0,a.jsxs)(x.l6,{value:M.linked_table||"clients",onValueChange:e=>{V({...M,linked_table:e,account_prefix:"clients"===e?"C":"E"})},children:[(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"clients",children:"العملاء"}),(0,a.jsx)(x.eb,{value:"employees",children:"الموظفين"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"account-prefix",children:"بادئة رقم الحساب"}),(0,a.jsx)(c.p,{id:"account-prefix",value:M.account_prefix||"",onChange:e=>V({...M,account_prefix:e.target.value}),placeholder:"مثل: C للعملاء، E للموظفين",maxLength:2})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",id:"auto-create",checked:M.auto_create||!1,onChange:e=>V({...M,auto_create:e.target.checked}),className:"rounded"}),(0,a.jsx)(d.J,{htmlFor:"auto-create",children:"إنشاء الحسابات تلقائياً"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 space-x-reverse pt-4 border-t",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>E(!1),children:"إلغاء"}),(0,a.jsxs)(i.$,{onClick:J,className:"bg-emerald-600 hover:bg-emerald-700",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 ml-2"}),"حفظ القاعدة"]})]})]})]})})]})})}},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,8409,4036,8106,7932],()=>s(26110));module.exports=a})();