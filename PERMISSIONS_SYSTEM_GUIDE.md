# دليل نظام الصلاحيات والدخول المشترك

## 🎯 نظرة عامة

تم تفعيل نظام الدخول المشترك للمستخدمين والعملاء مع نظام صلاحيات متقدم يتيح للمستخدمين المخولين إضافة وإدارة المتابعات الجديدة.

## 🔐 نافذة الدخول المشتركة

### الوصول للنظام
- **الرابط**: `http://localhost:7443/login`
- **نوعا الدخول**: 
  - دخول مستخدم (للموظفين)
  - دخول عميل (للعملاء)

### مميزات النافذة المشتركة
- ✅ واجهة موحدة للمستخدمين والعملاء
- ✅ تبديل سهل بين أنواع الدخول
- ✅ عرض بيانات تجريبية للاختبار
- ✅ تشفير وحماية البيانات

## 👥 الأدوار والصلاحيات

### 1. مدير النظام (admin)
**المستخدم**: `admin`
**كلمة المرور**: `admin`

**الصلاحيات**:
- ✅ إدارة المستخدمين
- ✅ إدارة المتابعات (إضافة/تعديل/حذف)
- ✅ إدارة القضايا
- ✅ إدارة العملاء
- ✅ إدارة النظام المحاسبي
- ✅ عرض جميع التقارير

### 2. مدير المكتب (manager)
**المستخدم**: `majed.manager`
**كلمة المرور**: `majed.manager`

**الصلاحيات**:
- ✅ إدارة المتابعات (إضافة/تعديل/حذف)
- ✅ إدارة القضايا
- ✅ عرض التقارير
- ✅ إدارة العملاء
- ✅ اعتماد المتابعات

### 3. محامي (lawyer)
**المستخدمون**: 
- `yahya.lawyer` / `yahya.lawyer`
- `ahmed.consultant` / `ahmed.consultant`
- `ahmed.mobile` / `ahmed.mobile`

**الصلاحيات**:
- ✅ إضافة متابعات جديدة
- ✅ عرض القضايا
- ✅ إدارة الجلسات
- ✅ عرض القضايا الخاصة

### 4. سكرتير (secretary)
**المستخدم**: `fatima.secretary`
**كلمة المرور**: `fatima.secretary`

**الصلاحيات**:
- ✅ إضافة متابعات جديدة
- ✅ عرض القضايا
- ✅ إدارة الوثائق
- ✅ جدولة المواعيد

### 5. محاسب (accountant)
**المستخدم**: `mohamed.accountant`
**كلمة المرور**: `mohamed.accountant`

**الصلاحيات**:
- ✅ إدارة النظام المحاسبي
- ✅ عرض التقارير المالية
- ✅ إدارة الفواتير

## 📝 صلاحيات المتابعات

### من يمكنه إضافة متابعات جديدة؟
- ✅ **مدير النظام** - صلاحية كاملة
- ✅ **مدير المكتب** - صلاحية كاملة
- ✅ **المحامي** - إضافة فقط
- ✅ **السكرتير** - إضافة فقط
- ❌ **المحاسب** - لا يمكنه إضافة متابعات

### من يمكنه تعديل/حذف المتابعات؟
- ✅ **مدير النظام** - تعديل وحذف
- ✅ **مدير المكتب** - تعديل وحذف
- ❌ **المحامي** - عرض فقط للمتابعات الموجودة
- ❌ **السكرتير** - عرض فقط للمتابعات الموجودة

## 🚀 كيفية الاستخدام

### 1. تسجيل الدخول
1. اذهب إلى `http://localhost:7443/login`
2. اختر نوع الدخول (مستخدم أو عميل)
3. أدخل اسم المستخدم وكلمة المرور
4. اضغط "تسجيل الدخول"

### 2. الوصول لصفحة المتابعات
1. بعد تسجيل الدخول، اذهب إلى قسم "المتابعات"
2. ستظهر لك معلومات دورك والصلاحيات المتاحة
3. إذا كان لديك صلاحية إضافة متابعات، ستظهر لك أزرار الإضافة

### 3. إضافة متابعة جديدة
1. اضغط على "إضافة متابعة جديدة"
2. املأ البيانات المطلوبة
3. احفظ المتابعة

## 🔧 الإعدادات التقنية

### قاعدة البيانات
- **الجداول الجديدة**: `user_roles`
- **الأعمدة الجديدة**: `role`, `permissions` في جدول `users`

### API المحدث
- **تسجيل الدخول**: `/api/auth/users`
- **يدعم الآن**: الأدوار والصلاحيات في الاستجابة

### التخزين المحلي
```javascript
{
  "id": 1,
  "username": "admin",
  "type": "user",
  "name": "ماجد أحمد علي",
  "role": "admin",
  "role_display_name": "مدير النظام",
  "permissions": ["manage_users", "manage_follows", ...],
  "token": "simple-token-1"
}
```

## 🧪 اختبار النظام

### تشغيل اختبار الصلاحيات
```bash
cd /home/<USER>/Downloads/legal-system
node test_permissions_system.js
```

### بيانات الاختبار
| المستخدم | كلمة المرور | الدور | يمكن إضافة متابعات |
|----------|-------------|-------|------------------|
| admin | admin | مدير النظام | ✅ |
| majed.manager | majed.manager | مدير المكتب | ✅ |
| yahya.lawyer | yahya.lawyer | محامي | ✅ |
| fatima.secretary | fatima.secretary | سكرتير | ✅ |
| mohamed.accountant | mohamed.accountant | محاسب | ❌ |

## 🎨 واجهة المستخدم

### مؤشرات الصلاحيات
- **🟢 أخضر**: صلاحية كاملة (إضافة/تعديل/حذف)
- **🟡 أصفر**: صلاحية محدودة (إضافة فقط)
- **🔒 قفل**: لا توجد صلاحيات

### عرض معلومات المستخدم
- اسم المستخدم ودوره
- قائمة الصلاحيات المتاحة
- حالة الصلاحيات لكل عملية

## 🔄 التحديثات المستقبلية

### مخطط للتطوير
- [ ] إضافة صلاحيات مخصصة لكل مستخدم
- [ ] نظام الموافقات المتدرجة
- [ ] سجل العمليات والتدقيق
- [ ] إدارة الصلاحيات من الواجهة

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل
1. تأكد من تشغيل الخادم على المنفذ 7443
2. تحقق من اتصال قاعدة البيانات
3. راجع سجلات الأخطاء في وحدة التحكم

### ملفات مهمة
- `src/app/login/page.tsx` - صفحة الدخول المشتركة
- `src/app/follows/page.tsx` - صفحة المتابعات مع الصلاحيات
- `src/app/api/auth/users/route.ts` - API تسجيل الدخول
- `database/add_user_roles.js` - إعداد الأدوار والصلاحيات

---

## ✅ ملخص التفعيل

تم بنجاح تفعيل:
1. ✅ نافذة الدخول المشتركة للمستخدمين والعملاء
2. ✅ نظام الأدوار والصلاحيات المتقدم
3. ✅ صلاحية إضافة المتابعات للمستخدمين المخولين
4. ✅ واجهة مستخدم محدثة تعرض الصلاحيات
5. ✅ نظام اختبار شامل للتحقق من الوظائف

**النظام جاهز للاستخدام!** 🎉