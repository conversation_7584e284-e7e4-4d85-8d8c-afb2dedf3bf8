(()=>{var e={};e.id=6970,e.ids=[6970],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{P:()=>c,bI:()=>n});var a=t(64939),o=e([a]);if(a=(o.then?(await o)():o)[0],!process.env.DB_PASSWORD)throw Error("DB_PASSWORD environment variable is required");let i={host:"localhost",port:5432,database:"mohammi",user:"postgres",password:process.env.DB_PASSWORD||"your_password_here",ssl:!1,connectionTimeoutMillis:5e3,idleTimeoutMillis:3e4,max:20},u=new a.Pool(i);async function n(){let e=await u.connect();return{async exec(r){try{for(let t of r.split(";").filter(e=>e.trim()))t.trim()&&await e.query(t.trim())}catch(e){throw console.error("Database exec error:",e),e}},async get(r,t=[]){try{return(await e.query(r,t)).rows[0]||null}catch(e){throw console.error("Database get error:",e),e}},async all(r,t=[]){try{return(await e.query(r,t)).rows}catch(e){throw console.error("Database all error:",e),e}},async run(r,t=[]){try{if(r.trim().toUpperCase().startsWith("INSERT")){let s=r.includes("RETURNING")?r:r+" RETURNING id",a=await e.query(s,t);return{lastID:a.rows[0]?.id||null,changes:a.rowCount||0}}{let s=await e.query(r,t);return{lastID:null,changes:s.rowCount||0}}}catch(e){throw console.error("Database run error:",e),e}},close(){e.release()}}}async function c(e,r){let t;try{return t=await u.connect(),await t.query(e,r)}catch(t){throw console.error("Database query error:",t),console.error("Query:",e),console.error("Params:",r),t}finally{t&&t.release()}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22772:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{DELETE:()=>l,GET:()=>c,POST:()=>i,PUT:()=>u});var a=t(32190),o=t(6710),n=e([o]);async function c(){try{let e=await (0,o.P)("SELECT * FROM branches ORDER BY id");return a.NextResponse.json({success:!0,data:e.rows})}catch(e){return console.error("Error fetching الفروع:",e),a.NextResponse.json({success:!1,error:"فشل في جلب بيانات الفروع",message:"تأكد من وجود الجدول في قاعدة البيانات"},{status:500})}}async function i(e){try{return await e.json(),a.NextResponse.json({success:!0,message:"تم إضافة الفروع بنجاح"})}catch(e){return console.error("Error creating الفروع:",e),a.NextResponse.json({success:!1,error:"فشل في إضافة الفروع"},{status:500})}}async function u(e){try{return await e.json(),a.NextResponse.json({success:!0,message:"تم تحديث الفروع بنجاح"})}catch(e){return console.error("Error updating الفروع:",e),a.NextResponse.json({success:!1,error:"فشل في تحديث الفروع"},{status:500})}}async function l(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return a.NextResponse.json({success:!1,error:"معرف الفروع مطلوب"},{status:400});return await (0,o.P)("DELETE FROM branches WHERE id = $1",[t]),a.NextResponse.json({success:!0,message:"تم حذف الفروع بنجاح"})}catch(e){return console.error("Error deleting الفروع:",e),a.NextResponse.json({success:!1,error:"فشل في حذف الفروع"},{status:500})}}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73730:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var a=t(96559),o=t(48088),n=t(37719),c=t(22772),i=e([c]);c=(i.then?(await i)():i)[0];let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/branches/route",pathname:"/api/branches",filename:"route",bundlePath:"app/api/branches/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\branches\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:d}=l;function u(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}s()}catch(e){s(e)}})},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(73730));module.exports=s})();