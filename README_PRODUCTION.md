# النظام القانوني - نسخة الإنتاج

## 🎯 نظرة عامة

هذه نسخة الإنتاج من النظام القانوني المتكامل لإدارة المكاتب القانونية. النظام مُحسن للأمان ومنظف من جميع البيانات الحساسة والتجريبية.

## 🚀 التشغيل السريع

### Windows Server 2019:
```cmd
start-production.bat
```

### Linux:
```bash
./start-production.sh
```

## 📋 المتطلبات

- **Node.js**: 18.0 أو أحدث
- **PostgreSQL**: 13.0 أو أحدث
- **RAM**: 8 GB كحد أدنى
- **Storage**: 50 GB مساحة فارغة

## ⚙️ الإعداد

1. **نسخ ملف البيئة**:
   ```bash
   cp .env.template .env.local
   ```

2. **تعديل الإعدادات**:
   - أضف بيانات قاعدة البيانات
   - أضف كلمات مرور قوية
   - حدد منفذ الخادم

3. **إعداد قاعدة البيانات**:
   ```bash
   node setup-production-db.js
   ```

4. **إنشاء المستخدم الأول**:
   ```bash
   node create-admin-user.js
   ```

## 🔐 الأمان

- ✅ **نظام صلاحيات شامل**: 55 صلاحية مفصلة
- ✅ **حماية الصفحات**: فحص الصلاحيات لكل صفحة
- ✅ **تشفير البيانات**: حماية كلمات المرور
- ✅ **جلسات آمنة**: إدارة تسجيل الدخول
- ✅ **مراجعة العمليات**: تسجيل جميع الأنشطة

## 📊 المميزات

### إدارة القضايا:
- إضافة وتعديل القضايا
- متابعة حالة القضايا
- ربط القضايا بالعملاء
- تقارير مفصلة

### إدارة العملاء:
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ التعامل
- إدارة المستندات
- بوابة عملاء منفصلة

### النظام المحاسبي:
- دليل حسابات متكامل
- سندات القبض والصرف
- القيود اليومية
- التقارير المالية

### إدارة المستخدمين:
- نظام صلاحيات مفصل
- أدوار محددة مسبقاً
- إدارة الجلسات
- مراقبة النشاط

## 📁 هيكل المشروع

```
mohaminew/
├── src/                    # كود المصدر
├── database/              # سكريبتات قاعدة البيانات
├── public/                # الملفات العامة
├── setup-production-db.js # إعداد قاعدة البيانات
├── create-admin-user.js   # إنشاء المستخدم الأول
├── start-production.bat   # تشغيل Windows
├── start-production.sh    # تشغيل Linux
├── .env.template          # نموذج الإعدادات
└── DEPLOYMENT_GUIDE.md    # دليل التشغيل المفصل
```

## 🔧 الصيانة

### النسخ الاحتياطية:
```bash
# نسخة احتياطية يومية
pg_dump legal_system_production > backup_$(date +%Y%m%d).sql
```

### المراقبة:
- تحقق من سجلات النظام يومياً
- راقب استخدام الموارد
- تأكد من عمل النسخ الاحتياطية

### التحديثات:
```bash
# إيقاف النظام
npm stop

# تحديث التبعيات
npm install

# إعادة البناء
npm run build

# تشغيل النظام
npm start
```

## 📞 الدعم

للحصول على الدعم الفني:

1. راجع ملف `DEPLOYMENT_GUIDE.md`
2. تحقق من سجلات النظام
3. تأكد من إعدادات قاعدة البيانات
4. راجع ملف `.env.local`

## ⚠️ تحذيرات مهمة

1. **غيّر كلمات المرور الافتراضية** فوراً
2. **لا تشارك ملف .env.local** مع أي شخص
3. **أنشئ نسخ احتياطية منتظمة**
4. **راقب سجلات النظام** بانتظام
5. **حدّث النظام** عند توفر تحديثات أمنية

## 📈 الإحصائيات

- **الملفات**: 576 ملف
- **المجلدات**: 352 مجلد
- **الحجم**: 4.25 MB
- **الصلاحيات**: 55 صلاحية
- **الأدوار**: 6 أدوار

## 🏆 الحالة

✅ **جاهز للإنتاج**  
🔒 **آمن ومحمي**  
🧹 **نظيف من البيانات التجريبية**  
📚 **موثق بالكامل**  

---

**الإصدار**: 1.0.0 Production  
**تاريخ الإنشاء**: 2025-08-26  
**متوافق مع**: Windows Server 2019, Linux  
**حالة الأمان**: ✅ محقق
