(()=>{var e={};e.id=5807,e.ids=[5807],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,a)=>{"use strict";a.a(e,async(e,c)=>{try{a.d(t,{P:()=>p});var o=a(64939),s=a(29021),n=a.n(s),r=a(33873),u=a.n(r),i=e([o]);o=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let l=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],a=d.default_config;return{database:t.database,user:a.db_user,host:a.db_host,password:process.env.DB_PASSWORD||a.db_password,port:a.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new o.Pool(l);async function p(e,t){let a=await _.connect();try{return await a.query(e,t)}finally{a.release()}}c()}catch(e){c(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54158:(e,t,a)=>{"use strict";a.a(e,async(e,c)=>{try{a.r(t),a.d(t,{patchFetch:()=>i,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var o=a(96559),s=a(48088),n=a(37719),r=a(86603),u=e([r]);r=(u.then?(await u)():u)[0];let p=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/accounting/fix-parent-accounts/route",pathname:"/api/accounting/fix-parent-accounts",filename:"route",bundlePath:"app/api/accounting/fix-parent-accounts/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\fix-parent-accounts\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:_}=p;function i(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}c()}catch(e){c(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},86603:(e,t,a)=>{"use strict";a.a(e,async(e,c)=>{try{a.r(t),a.d(t,{GET:()=>u,POST:()=>r});var o=a(32190),s=a(5069),n=e([s]);async function r(e){try{console.log("\uD83D\uDD27 تحديث account_id ليشير للحساب الأب...");let e=[];console.log("\uD83D\uDD0D البحث عن الحسابات الأب...");let t=await (0,s.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات العملاء' AND linked_table = 'clients'
      LIMIT 1
    `),a=await (0,s.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات الموظفين' AND linked_table = 'employees'
      LIMIT 1
    `),c=await (0,s.P)(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات الموردين' AND linked_table = 'suppliers'
      LIMIT 1
    `),n=null,r=null,u=null;if(!(t.rows.length>0))return e.push("❌ لم يتم العثور على حساب العملاء الأب"),o.NextResponse.json({success:!1,message:"لم يتم العثور على حساب العملاء الأب",data:{results:e}},{status:400});if(n=t.rows[0].id,e.push(`✅ تم العثور على حساب العملاء الأب: ${t.rows[0].account_name} (${t.rows[0].account_code})`),!(a.rows.length>0))return e.push("❌ لم يتم العثور على حساب الموظفين الأب"),o.NextResponse.json({success:!1,message:"لم يتم العثور على حساب الموظفين الأب",data:{results:e}},{status:400});if(r=a.rows[0].id,e.push(`✅ تم العثور على حساب الموظفين الأب: ${a.rows[0].account_name} (${a.rows[0].account_code})`),!(c.rows.length>0))return e.push("❌ لم يتم العثور على حساب الموردين الأب"),o.NextResponse.json({success:!1,message:"لم يتم العثور على حساب الموردين الأب",data:{results:e}},{status:400});u=c.rows[0].id,e.push(`✅ تم العثور على حساب الموردين الأب: ${c.rows[0].account_name} (${c.rows[0].account_code})`),console.log("\uD83D\uDCDD تحديث جدول العملاء...");let i=await (0,s.P)(`
      UPDATE clients 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `,[n]);e.push(`✅ تم تحديث ${i.rowCount} عميل ليشير للحساب الأب`),console.log("\uD83D\uDCDD تحديث جدول الموظفين...");let p=await (0,s.P)(`
      UPDATE employees 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `,[r]);e.push(`✅ تم تحديث ${p.rowCount} موظف ليشير للحساب الأب`),console.log("\uD83D\uDCDD تحديث جدول الموردين...");let d=await (0,s.P)(`
      UPDATE suppliers 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `,[u]);e.push(`✅ تم تحديث ${d.rowCount} مورد ليشير للحساب الأب`),console.log("\uD83E\uDDEA اختبار النتائج...");let l=await (0,s.P)(`
      SELECT 
        c.name as client_name,
        c.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      LIMIT 3
    `);e.push(`✅ اختبار العملاء: ${l.rows.length} عميل مربوط بالحساب الأب`);let _=await (0,s.P)(`
      SELECT 
        e.name as employee_name,
        e.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM employees e
      LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
      LIMIT 3
    `);e.push(`✅ اختبار الموظفين: ${_.rows.length} موظف مربوط بالحساب الأب`);let w=await (0,s.P)(`
      SELECT 
        s.name as supplier_name,
        s.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM suppliers s
      LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
      LIMIT 3
    `);e.push(`✅ اختبار الموردين: ${w.rows.length} مورد مربوط بالحساب الأب`);let m={clientsParentId:n,employeesParentId:r,suppliersParentId:u,clientsUpdated:i.rowCount,employeesUpdated:p.rowCount,suppliersUpdated:d.rowCount,clientsTest:l.rows,employeesTest:_.rows,suppliersTest:w.rows};return console.log("\uD83C\uDF89 تم تحديث account_id للحسابات الأب بنجاح!"),o.NextResponse.json({success:!0,message:"تم تحديث account_id ليشير للحساب الأب بنجاح",data:{results:e,stats:m,parentAccounts:{clients:t.rows[0],employees:a.rows[0],suppliers:c.rows[0]}}})}catch(e){return console.error("❌ خطأ في تحديث الحسابات الأب:",e),o.NextResponse.json({success:!1,message:"فشل في تحديث الحسابات الأب",error:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function u(e){try{let e=await (0,s.P)(`
      SELECT 
        c.name,
        c.account_id,
        coa.account_name,
        coa.account_code
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      LIMIT 5
    `),t=await (0,s.P)(`
      SELECT 
        e.name,
        e.account_id,
        coa.account_name,
        coa.account_code
      FROM employees e
      LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
      LIMIT 5
    `),a=await (0,s.P)(`
      SELECT 
        s.name,
        s.account_id,
        coa.account_name,
        coa.account_code
      FROM suppliers s
      LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
      LIMIT 5
    `);return o.NextResponse.json({success:!0,data:{clients:e.rows,employees:t.rows,suppliers:a.rows},message:"تم جلب الحالة الحالية بنجاح"})}catch(e){return o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}s=(n.then?(await n)():n)[0],c()}catch(e){c(e)}})},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),c=t.X(0,[4447,580],()=>a(54158));module.exports=c})();