// إكمال إصلاح جدول أنواع القضايا وربط البيانات
const { Pool } = require('pg');

async function completeIssueTypesFix() {
  console.log('🔧 إكمال إصلاح جدول أنواع القضايا...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص هيكل جدول أنواع القضايا
      console.log('\n   📊 فحص هيكل جدول أنواع القضايا:');
      
      const columns = await pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'issue_types'
        ORDER BY ordinal_position
      `);

      console.log('      الأعمدة الموجودة:');
      const existingColumns = columns.rows.map(col => col.column_name);
      columns.rows.forEach(col => {
        console.log(`         - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });

      // 2. إضافة الأعمدة المفقودة
      console.log('\n   ➕ إضافة الأعمدة المفقودة:');
      
      const requiredColumns = [
        { name: 'category', type: 'VARCHAR(100)', comment: 'فئة نوع القضية' },
        { name: 'color', type: 'VARCHAR(7)', comment: 'لون للتمييز في الواجهة' },
        { name: 'is_active', type: 'BOOLEAN DEFAULT true', comment: 'حالة النشاط' },
        { name: 'created_at', type: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', comment: 'تاريخ الإنشاء' },
        { name: 'updated_at', type: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', comment: 'تاريخ التحديث' }
      ];

      for (const column of requiredColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            await pool.query(`
              ALTER TABLE issue_types 
              ADD COLUMN ${column.name} ${column.type}
            `);
            console.log(`      ✅ تم إضافة العمود: ${column.name}`);
          } catch (error) {
            console.log(`      ❌ خطأ في إضافة العمود ${column.name}: ${error.message}`);
          }
        } else {
          console.log(`      ✅ العمود موجود: ${column.name}`);
        }
      }

      // 3. إدراج أنواع القضايا الأساسية (مبسط)
      console.log('\n   📋 إدراج أنواع القضايا الأساسية:');
      
      const basicIssueTypes = [
        { name: 'قضية جنائية', category: 'جنائي', color: '#dc2626' },
        { name: 'قضية مدنية', category: 'مدني', color: '#2563eb' },
        { name: 'قضية تجارية', category: 'تجاري', color: '#059669' },
        { name: 'قضية أحوال شخصية', category: 'أحوال شخصية', color: '#7c3aed' },
        { name: 'قضية عمالية', category: 'عمالي', color: '#ea580c' },
        { name: 'قضية إدارية', category: 'إداري', color: '#0891b2' },
        { name: 'قضية عقارية', category: 'عقاري', color: '#65a30d' }
      ];

      for (const issueType of basicIssueTypes) {
        try {
          await pool.query(`
            INSERT INTO issue_types (name, category, color, description)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (name) DO UPDATE SET
              category = EXCLUDED.category,
              color = EXCLUDED.color,
              description = EXCLUDED.description
          `, [issueType.name, issueType.category, issueType.color, `وصف ${issueType.name}`]);
          
          console.log(`      ✅ تم إدراج/تحديث: ${issueType.name}`);
        } catch (error) {
          console.log(`      ❌ خطأ في إدراج نوع ${issueType.name}: ${error.message}`);
        }
      }

      // 4. ربط القضايا الموجودة بأنواعها
      console.log('\n   🔄 ربط القضايا الموجودة بأنواعها:');
      
      // الحصول على القضايا التي لها issue_type كنص
      const issuesWithTextType = await pool.query(`
        SELECT id, case_number, issue_type, issue_type_id
        FROM issues 
        WHERE issue_type IS NOT NULL 
        AND issue_type != ''
        AND (issue_type_id IS NULL OR issue_type_id = 0)
      `);

      console.log(`      📊 عدد القضايا المطلوب ربطها: ${issuesWithTextType.rows.length}`);

      for (const issue of issuesWithTextType.rows) {
        try {
          // البحث عن نوع القضية المطابق أو المشابه
          let issueType = await pool.query(`
            SELECT id, name FROM issue_types 
            WHERE name ILIKE $1 
            OR name ILIKE $2
            OR category ILIKE $1
            LIMIT 1
          `, [`%${issue.issue_type}%`, `%${issue.issue_type.replace('قضية ', '')}%`]);

          let typeId = null;

          if (issueType.rows.length > 0) {
            typeId = issueType.rows[0].id;
            console.log(`      🔗 ربط "${issue.case_number}" بالنوع الموجود: "${issueType.rows[0].name}" (ID: ${typeId})`);
          } else {
            // إنشاء نوع جديد إذا لم يوجد
            try {
              const newType = await pool.query(`
                INSERT INTO issue_types (name, description, category)
                VALUES ($1, $2, $3)
                RETURNING id, name
              `, [issue.issue_type, `نوع قضية: ${issue.issue_type}`, 'عام']);
              
              typeId = newType.rows[0].id;
              console.log(`      ➕ تم إنشاء نوع جديد: "${newType.rows[0].name}" (ID: ${typeId})`);
            } catch (error) {
              console.log(`      ❌ خطأ في إنشاء نوع جديد لـ "${issue.issue_type}": ${error.message}`);
              continue;
            }
          }

          // ربط القضية بالنوع
          await pool.query(`
            UPDATE issues 
            SET issue_type_id = $1
            WHERE id = $2
          `, [typeId, issue.id]);

          console.log(`      ✅ تم ربط القضية ${issue.case_number} بالنوع ${typeId}`);

        } catch (error) {
          console.log(`      ❌ خطأ في ربط القضية ${issue.case_number}: ${error.message}`);
        }
      }

      // 5. ربط القضايا التي لها أنواع نصية مختلفة
      console.log('\n   🔄 ربط القضايا بأنواع افتراضية:');
      
      const issuesWithoutType = await pool.query(`
        SELECT id, case_number, issue_type
        FROM issues 
        WHERE issue_type_id IS NULL
      `);

      if (issuesWithoutType.rows.length > 0) {
        // الحصول على النوع الافتراضي (أول نوع متاح)
        const defaultType = await pool.query(`
          SELECT id, name FROM issue_types ORDER BY id LIMIT 1
        `);

        if (defaultType.rows.length > 0) {
          const defaultTypeId = defaultType.rows[0].id;
          
          for (const issue of issuesWithoutType.rows) {
            await pool.query(`
              UPDATE issues 
              SET issue_type_id = $1
              WHERE id = $2
            `, [defaultTypeId, issue.id]);
            
            console.log(`      🔗 تم ربط القضية ${issue.case_number} بالنوع الافتراضي: ${defaultType.rows[0].name}`);
          }
        }
      }

      // 6. عرض الإحصائيات النهائية
      console.log('\n   📊 الإحصائيات النهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM issue_types) as total_types,
          (SELECT COUNT(*) FROM issues WHERE issue_type_id IS NOT NULL) as issues_with_type,
          (SELECT COUNT(*) FROM issues WHERE issue_type_id IS NULL) as issues_without_type,
          (SELECT COUNT(*) FROM issues) as total_issues
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي أنواع القضايا: ${stats.total_types}`);
      console.log(`      - قضايا مرتبطة بأنواع: ${stats.issues_with_type}`);
      console.log(`      - قضايا بدون أنواع: ${stats.issues_without_type}`);
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);

      // 7. عرض أمثلة على العلاقة
      console.log('\n   📋 أمثلة على العلاقة:');
      
      const examples = await pool.query(`
        SELECT 
          it.name as type_name,
          it.category,
          COUNT(i.id) as issue_count,
          STRING_AGG(i.case_number, ', ') as case_numbers
        FROM issue_types it
        LEFT JOIN issues i ON it.id = i.issue_type_id
        GROUP BY it.id, it.name, it.category
        ORDER BY issue_count DESC, it.name
        LIMIT 5
      `);

      examples.rows.forEach(example => {
        console.log(`      📋 ${example.type_name} (${example.category || 'غير محدد'}): ${example.issue_count} قضية`);
        if (example.case_numbers && example.issue_count > 0) {
          console.log(`         القضايا: ${example.case_numbers}`);
        }
      });

      // 8. اختبار العلاقة النهائي
      console.log('\n   🧪 اختبار العلاقة النهائي:');
      
      const relationTest = await pool.query(`
        SELECT 
          i.case_number,
          i.title,
          it.name as issue_type_name,
          it.category as issue_type_category,
          it.color as issue_type_color
        FROM issues i
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        ORDER BY i.id
        LIMIT 3
      `);

      relationTest.rows.forEach(test => {
        console.log(`      ✅ ${test.case_number}: ${test.title || 'بدون عنوان'}`);
        if (test.issue_type_name) {
          console.log(`         النوع: ${test.issue_type_name} (${test.issue_type_category || 'غير محدد'}) ${test.issue_type_color || ''}`);
        } else {
          console.log(`         النوع: غير محدد`);
        }
      });

      // 9. التحقق من العلاقة
      console.log('\n   🔍 التحقق من العلاقة:');
      
      const relationCheck = await pool.query(`
        SELECT 
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'issues'
        AND kcu.column_name = 'issue_type_id'
      `);

      if (relationCheck.rows.length > 0) {
        const relation = relationCheck.rows[0];
        console.log(`      ✅ العلاقة مؤكدة: issues.${relation.column_name} -> ${relation.foreign_table_name}.${relation.foreign_column_name}`);
      } else {
        console.log(`      ❌ العلاقة غير موجودة!`);
      }

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إكمال إصلاح علاقة أنواع القضايا');
  
  console.log('\n📋 ملخص العلاقة النهائية:');
  console.log('1. ✅ issue_types.id -> issues.issue_type_id (One-to-Many)');
  console.log('2. ✅ نوع واحد يمكن أن يكون له عدة قضايا');
  console.log('3. ✅ قضية واحدة لها نوع واحد فقط');
  console.log('4. ✅ تم ربط جميع القضايا الموجودة بأنواعها');
  console.log('5. ✅ العلاقة تعمل بشكل صحيح مع المفاتيح الخارجية');
}

// تشغيل الإصلاح
completeIssueTypesFix().catch(console.error);
