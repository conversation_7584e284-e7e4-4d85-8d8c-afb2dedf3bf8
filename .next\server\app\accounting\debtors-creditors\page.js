(()=>{var e={};e.id=4572,e.ids=[4572],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25476:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\mohaminew\\\\src\\\\app\\\\accounting\\\\debtors-creditors\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\mohaminew\\src\\app\\accounting\\debtors-creditors\\page.tsx","default")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28102:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),n=s(98254),l=s(44493),i=s(29523),c=s(89667),d=s(80013),o=s(96834),x=s(41312),h=s(31158),p=s(25541),m=s(12640),u=s(23928),b=s(80462),j=s(99270),g=s(13861);function y(){let[e,t]=(0,a.useState)([]),[s,y]=(0,a.useState)({total_debtors:0,total_creditors:0,debtors_count:0,creditors_count:0,net_position:0}),[v,N]=(0,a.useState)(!1),[f,_]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[A,C]=(0,a.useState)("all"),[L,S]=(0,a.useState)(""),P=async()=>{N(!0);try{let e=new URLSearchParams;L&&e.append("as_of_date",L);let s=await fetch(`/api/accounting/debtors-creditors?${e.toString()}`);if(s.ok){let e=await s.json();t(e.balances||[]),y(e.summary||{total_debtors:0,total_creditors:0,debtors_count:0,creditors_count:0,net_position:0})}else console.error("فشل في جلب الأرصدة")}catch(e){console.error("خطأ في جلب الأرصدة:",e)}finally{N(!1)}},R=e.filter(e=>{let t=e.account_name.toLowerCase().includes(f.toLowerCase())||e.account_code.toLowerCase().includes(f.toLowerCase())||e.linked_entity_name&&e.linked_entity_name.toLowerCase().includes(f.toLowerCase()),s=!w||e.account_type===w,r="all"===A||"debtors"===A&&"مدين"===e.balance_type||"creditors"===A&&"دائن"===e.balance_type;return t&&s&&r}),q=async()=>{try{let e=await fetch("/api/accounting/debtors-creditors/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({balances:R,summary:s,filters:{searchTerm:f,selectedAccountType:w,balanceFilter:A,dateFilter:L}})});if(e.ok){let t=await e.blob(),s=window.URL.createObjectURL(t),r=document.createElement("a");r.href=s,r.download=`debtors-creditors-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(s),document.body.removeChild(r)}}catch(e){console.error("خطأ في التصدير:",e)}},E=e=>{switch(e){case"مدين":return"bg-red-100 text-red-800";case"دائن":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},M=e=>{switch(e){case"clients":return"\uD83D\uDC64";case"employees":return"\uD83D\uDC68‍\uD83D\uDCBC";case"suppliers":return"\uD83C\uDFE2";default:return"\uD83D\uDCCB"}};return(0,r.jsx)(n.O,{children:(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"قائمة الدائنين والمدينين"}),(0,r.jsx)("p",{className:"text-gray-600",children:"عرض أرصدة جميع الحسابات والعملاء والموظفين والموردين"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,r.jsxs)(i.$,{onClick:q,variant:"outline",className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"تصدير Excel"})]}),(0,r.jsx)(i.$,{onClick:P,disabled:v,children:v?"جاري التحديث...":"تحديث البيانات"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي المدينين"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[s.total_debtors.toLocaleString()," ر.ي"]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[s.debtors_count," حساب"]})]}),(0,r.jsx)(p.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي الدائنين"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[s.total_creditors.toLocaleString()," ر.ي"]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[s.creditors_count," حساب"]})]}),(0,r.jsx)(m.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"صافي المركز"}),(0,r.jsxs)("p",{className:`text-2xl font-bold ${s.net_position>=0?"text-green-600":"text-red-600"}`,children:[s.net_position.toLocaleString()," ر.ي"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.net_position>=0?"مركز إيجابي":"مركز سلبي"})]}),(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي الحسابات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.length}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"حساب نشط"})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.J,{htmlFor:"date-filter",className:"text-sm text-gray-600",children:"كما في تاريخ"}),(0,r.jsx)(c.p,{id:"date-filter",type:"date",value:L,onChange:e=>S(e.target.value),className:"mt-1"}),(0,r.jsx)(i.$,{onClick:P,size:"sm",className:"mt-2 w-full",disabled:v,children:"تطبيق"})]})})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsxs)(l.ZB,{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(b.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"المرشحات"})]})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"search",children:"البحث"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(c.p,{id:"search",placeholder:"ابحث بالاسم أو الرمز...",value:f,onChange:e=>_(e.target.value),className:"pr-10"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{children:"نوع الحساب"}),(0,r.jsxs)("select",{value:w,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"جميع الأنواع"}),(0,r.jsx)("option",{value:"أصول",children:"أصول"}),(0,r.jsx)("option",{value:"خصوم",children:"خصوم"}),(0,r.jsx)("option",{value:"حقوق ملكية",children:"حقوق ملكية"}),(0,r.jsx)("option",{value:"إيرادات",children:"إيرادات"}),(0,r.jsx)("option",{value:"مصروفات",children:"مصروفات"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{children:"نوع الرصيد"}),(0,r.jsxs)("select",{value:A,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"جميع الأرصدة"}),(0,r.jsx)("option",{value:"debtors",children:"المدينين فقط"}),(0,r.jsx)("option",{value:"creditors",children:"الدائنين فقط"})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)(i.$,{onClick:()=>{_(""),k(""),C("all"),S("")},variant:"outline",className:"w-full",children:"مسح المرشحات"})})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsxs)(l.ZB,{children:["تفاصيل الأرصدة (",R.length," حساب)"]})}),(0,r.jsx)(l.Wu,{children:v?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{children:"جاري تحميل البيانات..."})]}):(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"bg-gray-50",children:[(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"رمز الحساب"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"اسم الحساب"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"النوع"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"الرصيد المدين"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"الرصيد الدائن"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"صافي الرصيد"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"نوع الرصيد"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"عدد المعاملات"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"آخر معاملة"}),(0,r.jsx)("th",{className:"border border-gray-300 p-3 text-right",children:"إجراءات"})]})}),(0,r.jsx)("tbody",{children:R.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"border border-gray-300 p-3 font-mono text-sm",children:e.account_code}),(0,r.jsx)("td",{className:"border border-gray-300 p-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-lg",children:M(e.original_table)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.account_name}),e.linked_entity_name&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.linked_entity_name})]})]})}),(0,r.jsxs)("td",{className:"border border-gray-300 p-3",children:[(0,r.jsx)(o.E,{variant:"outline",className:"text-xs",children:e.account_type}),e.is_linked_record&&(0,r.jsx)(o.E,{className:"bg-blue-100 text-blue-800 mr-1",size:"sm",children:"clients"===e.original_table?"عميل":"employees"===e.original_table?"موظف":"suppliers"===e.original_table?"مورد":"مرتبط"})]}),(0,r.jsx)("td",{className:"border border-gray-300 p-3 text-right",children:e.debit_balance>0?(0,r.jsx)("span",{className:"text-red-600 font-medium",children:e.debit_balance.toLocaleString()}):"-"}),(0,r.jsx)("td",{className:"border border-gray-300 p-3 text-right",children:e.credit_balance>0?(0,r.jsx)("span",{className:"text-green-600 font-medium",children:e.credit_balance.toLocaleString()}):"-"}),(0,r.jsx)("td",{className:"border border-gray-300 p-3 text-right",children:(0,r.jsx)("span",{className:`font-bold ${e.net_balance>0?"text-red-600":e.net_balance<0?"text-green-600":"text-gray-600"}`,children:Math.abs(e.net_balance).toLocaleString()})}),(0,r.jsx)("td",{className:"border border-gray-300 p-3",children:(0,r.jsx)(o.E,{className:E(e.balance_type),size:"sm",children:e.balance_type})}),(0,r.jsx)("td",{className:"border border-gray-300 p-3 text-center",children:e.transactions_count}),(0,r.jsx)("td",{className:"border border-gray-300 p-3 text-sm",children:e.last_transaction_date?new Date(e.last_transaction_date).toLocaleDateString("en-GB"):"لا توجد معاملات"}),(0,r.jsx)("td",{className:"border border-gray-300 p-3",children:(0,r.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>window.open(`/accounting/reports/account-statement?account_id=${e.id}`,"_blank"),className:"flex items-center space-x-1 space-x-reverse",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"كشف حساب"})]})})]},e.id))})]}),0===R.length&&(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(x.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"لا توجد أرصدة تطابق المعايير المحددة"})]})]})})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},57683:(e,t,s)=>{Promise.resolve().then(s.bind(s,28102))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63419:(e,t,s)=>{Promise.resolve().then(s.bind(s,25476))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var r=s(60687),a=s(43210),n=s(4780);let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("label",{ref:s,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));l.displayName="Label"},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},91066:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d={children:["",{children:["accounting",{children:["debtors-creditors",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25476)),"D:\\mohaminew\\src\\app\\accounting\\debtors-creditors\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\mohaminew\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\mohaminew\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\mohaminew\\src\\app\\accounting\\debtors-creditors\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/accounting/debtors-creditors/page",pathname:"/accounting/debtors-creditors",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,8409,7932],()=>s(91066));module.exports=r})();