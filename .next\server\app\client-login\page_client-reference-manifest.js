globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/client-login/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/home/<USER>":{"*":{"id":"(ssr)/./src/app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/page.tsx":{"*":{"id":"(ssr)/./src/app/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/page.tsx":{"*":{"id":"(ssr)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/employees/page.tsx":{"*":{"id":"(ssr)/./src/app/employees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/website-admin/page.tsx":{"*":{"id":"(ssr)/./src/app/website-admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/ai-settings/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/ai-settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/serviceslow/[slug]/page.tsx":{"*":{"id":"(ssr)/./src/app/serviceslow/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/documents/page.tsx":{"*":{"id":"(ssr)/./src/app/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/issues/page.tsx":{"*":{"id":"(ssr)/./src/app/issues/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/case-distribution/page.tsx":{"*":{"id":"(ssr)/./src/app/case-distribution/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/client-login/page.tsx":{"*":{"id":"(ssr)/./src/app/client-login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/issue-types/page.tsx":{"*":{"id":"(ssr)/./src/app/issue-types/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/accounting/chart-of-accounts/page.tsx":{"*":{"id":"(ssr)/./src/app/accounting/chart-of-accounts/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/follows/page.tsx":{"*":{"id":"(ssr)/./src/app/follows/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\mohaminew\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\mohaminew\\src\\styles\\professional-theme.css":{"id":"(app-pages-browser)/./src/styles/professional-theme.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\mohaminew\\src\\styles\\tafahum-theme.css":{"id":"(app-pages-browser)/./src/styles/tafahum-theme.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\mohaminew\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\mohaminew\\src\\app\\home\\page.tsx":{"id":"(app-pages-browser)/./src/app/home/<USER>","name":"*","chunks":[],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\mohaminew\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\clients\\page.tsx":{"id":"(app-pages-browser)/./src/app/clients/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\users\\page.tsx":{"id":"(app-pages-browser)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\employees\\page.tsx":{"id":"(app-pages-browser)/./src/app/employees/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\website-admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/website-admin/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\mohaminew\\src\\app\\admin\\ai-settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/ai-settings/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\serviceslow\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./src/app/serviceslow/[slug]/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\documents\\page.tsx":{"id":"(app-pages-browser)/./src/app/documents/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\issues\\page.tsx":{"id":"(app-pages-browser)/./src/app/issues/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\case-distribution\\page.tsx":{"id":"(app-pages-browser)/./src/app/case-distribution/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\client-login\\page.tsx":{"id":"(app-pages-browser)/./src/app/client-login/page.tsx","name":"*","chunks":["app/client-login/page","static/chunks/app/client-login/page.js"],"async":false},"D:\\mohaminew\\src\\app\\issue-types\\page.tsx":{"id":"(app-pages-browser)/./src/app/issue-types/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\accounting\\chart-of-accounts\\page.tsx":{"id":"(app-pages-browser)/./src/app/accounting/chart-of-accounts/page.tsx","name":"*","chunks":[],"async":false},"D:\\mohaminew\\src\\app\\follows\\page.tsx":{"id":"(app-pages-browser)/./src/app/follows/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\mohaminew\\src\\":[],"D:\\mohaminew\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\mohaminew\\src\\app\\not-found":[],"D:\\mohaminew\\src\\app\\page":[],"D:\\mohaminew\\src\\app\\client-login\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/professional-theme.css":{"*":{"id":"(rsc)/./src/styles/professional-theme.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/tafahum-theme.css":{"*":{"id":"(rsc)/./src/styles/tafahum-theme.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/home/<USER>":{"*":{"id":"(rsc)/./src/app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/clients/page.tsx":{"*":{"id":"(rsc)/./src/app/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/users/page.tsx":{"*":{"id":"(rsc)/./src/app/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/employees/page.tsx":{"*":{"id":"(rsc)/./src/app/employees/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/website-admin/page.tsx":{"*":{"id":"(rsc)/./src/app/website-admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/ai-settings/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/ai-settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/serviceslow/[slug]/page.tsx":{"*":{"id":"(rsc)/./src/app/serviceslow/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/documents/page.tsx":{"*":{"id":"(rsc)/./src/app/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/issues/page.tsx":{"*":{"id":"(rsc)/./src/app/issues/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/case-distribution/page.tsx":{"*":{"id":"(rsc)/./src/app/case-distribution/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/client-login/page.tsx":{"*":{"id":"(rsc)/./src/app/client-login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/issue-types/page.tsx":{"*":{"id":"(rsc)/./src/app/issue-types/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/accounting/chart-of-accounts/page.tsx":{"*":{"id":"(rsc)/./src/app/accounting/chart-of-accounts/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/follows/page.tsx":{"*":{"id":"(rsc)/./src/app/follows/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}