(()=>{var e={};e.id=4488,e.ids=[4488],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>d});var a=s(64939),n=s(29021),c=s.n(n),o=s(33873),i=s.n(o),u=e([a]);a=(u.then?(await u)():u)[0];let l=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=c().readFileSync(e,"utf8");l=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!l?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(l&&l.routes[e]){let t=l.routes[e],s=l.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new a.Pool(p);async function d(e,t){let s=await _.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36628:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>u,GET:()=>o,POST:()=>i});var a=s(32190),n=s(5069),c=e([n]);async function o(){try{let e=await (0,n.P)(`
      SELECT
        c.*,
        p.account_name as parent_name,
        CASE
          WHEN c.linked_table IS NOT NULL THEN (
            SELECT COUNT(*)
            FROM account_sub_links asl
            WHERE asl.main_account_id = c.id
          )
          ELSE 0
        END as linked_count,
        (
          SELECT COUNT(*)
          FROM chart_of_accounts child
          WHERE child.parent_id = c.id
        ) as children_count
      FROM chart_of_accounts c
      LEFT JOIN chart_of_accounts p ON c.parent_id = p.id
      ORDER BY c.account_code
    `);return a.NextResponse.json({success:!0,data:e.rows})}catch(e){return console.error("Error fetching accounts:",e),a.NextResponse.json({success:!1,error:"فشل في جلب بيانات الحسابات"},{status:500})}}async function i(e){try{let{account_id:t,table_name:s}=await e.json();if(!t||!s)return a.NextResponse.json({success:!1,error:"معرف الحساب واسم الجدول مطلوبان"},{status:400});let r=await (0,n.P)("SELECT * FROM chart_of_accounts WHERE id = $1",[t]);if(0===r.rows.length)return a.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return await (0,n.P)(`
      UPDATE chart_of_accounts
      SET linked_table = $1, auto_create_sub_accounts = true
      WHERE id = $2
    `,[s,t]),await d(t,s),a.NextResponse.json({success:!0,message:"تم ربط الحساب بنجاح"})}catch(e){return console.error("Error linking account:",e),a.NextResponse.json({success:!1,error:"فشل في ربط الحساب"},{status:500})}}async function u(e){try{let{account_id:t}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"معرف الحساب مطلوب"},{status:400});return await (0,n.P)(`
      UPDATE chart_of_accounts
      SET linked_table = NULL, auto_create_sub_accounts = false
      WHERE id = $1
    `,[t]),await (0,n.P)("DELETE FROM account_sub_links WHERE main_account_id = $1",[t]),a.NextResponse.json({success:!0,message:"تم إلغاء ربط الحساب بنجاح"})}catch(e){return console.error("Error unlinking account:",e),a.NextResponse.json({success:!1,error:"فشل في إلغاء ربط الحساب"},{status:500})}}async function d(e,t){try{let s=await (0,n.P)("SELECT account_code, sub_account_prefix FROM chart_of_accounts WHERE id = $1",[e]);if(0===s.rows.length)return;let{account_code:r,sub_account_prefix:a}=s.rows[0],c="";switch(t){case"clients":c="SELECT id, name FROM clients WHERE is_active = true";break;case"employees":c="SELECT id, name FROM employees WHERE is_active = true";break;case"courts":c="SELECT id, name FROM courts";break;case"branches":c="SELECT id, name FROM branches";break;case"cost_centers":c="SELECT id, name FROM cost_centers";break;case"issues":c="SELECT id, title as name FROM issues";break;default:return}for(let s of(await (0,n.P)(c)).rows){let a=`${r}-${s.id.toString().padStart(3,"0")}`,c=`${{clients:"حساب الموكل",employees:"حساب الموظف",courts:"حساب المحكمة",branches:"حساب الفرع",cost_centers:"مركز التكلفة",issues:"حساب القضية"}[t]||"حساب"}: ${s.name}`,o=await (0,n.P)("SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3",[e,t,s.id]);0===o.rows.length&&await (0,n.P)(`
          INSERT INTO account_sub_links
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
        `,[e,t,s.id,a,c,"النظام"])}}catch(e){console.error("Error creating sub accounts:",e)}}n=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},71648:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var a=s(96559),n=s(48088),c=s(37719),o=s(36628),i=e([o]);o=(i.then?(await i)():i)[0];let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/account-linking/route",pathname:"/api/account-linking",filename:"route",bundlePath:"app/api/account-linking/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\account-linking\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:_}=d;function u(){return(0,c.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(71648));module.exports=r})();