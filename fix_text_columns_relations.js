// إصلاح الأعمدة النصية وتحديثها من العلاقات الصحيحة
const { Pool } = require('pg');

async function fixTextColumnsRelations() {
  console.log('🔧 إصلاح الأعمدة النصية وتحديثها من العلاقات الصحيحة...\n');

  // قواعد البيانات المطلوب إصلاحها
  const databases = ['mohammi', 'rubaie'];

  for (const dbName of databases) {
    console.log(`📋 إصلاح قاعدة البيانات: ${dbName}`);
    
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    });

    try {
      // 1. فحص الوضع الحالي
      console.log('\n   🔍 فحص الوضع الحالي:');
      
      const currentData = await pool.query(`
        SELECT 
          i.id,
          i.case_number,
          i.issue_type,
          i.issue_type_id,
          i.court_name,
          i.court_id,
          it.name as actual_issue_type_name,
          c.name as actual_court_name
        FROM issues i
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts c ON i.court_id = c.id
        ORDER BY i.case_number
      `);

      console.log('      📊 البيانات الحالية:');
      currentData.rows.forEach(row => {
        console.log(`         ${row.case_number}:`);
        console.log(`            issue_type: "${row.issue_type}" | issue_type_id: ${row.issue_type_id}`);
        console.log(`            actual_type: "${row.actual_issue_type_name || 'غير موجود'}"`);
        console.log(`            court_name: "${row.court_name}" | court_id: ${row.court_id}`);
        console.log(`            actual_court: "${row.actual_court_name || 'غير موجود'}"`);
        console.log('');
      });

      // 2. تحديث عمود issue_type من العلاقة الصحيحة
      console.log('\n   📋 تحديث عمود issue_type من العلاقة الصحيحة:');
      
      const updateIssueTypes = await pool.query(`
        UPDATE issues 
        SET issue_type = it.name
        FROM issue_types it
        WHERE issues.issue_type_id = it.id
        AND (issues.issue_type IS NULL 
             OR issues.issue_type = '' 
             OR issues.issue_type != it.name)
        RETURNING issues.case_number, issues.issue_type, it.name as new_type
      `);

      if (updateIssueTypes.rows.length > 0) {
        console.log(`      ✅ تم تحديث ${updateIssueTypes.rows.length} قضية:`);
        updateIssueTypes.rows.forEach(row => {
          console.log(`         - ${row.case_number}: "${row.new_type}"`);
        });
      } else {
        console.log('      ✅ جميع أنواع القضايا محدثة');
      }

      // 3. تحديث عمود court_name من العلاقة الصحيحة
      console.log('\n   🏛️ تحديث عمود court_name من العلاقة الصحيحة:');
      
      const updateCourtNames = await pool.query(`
        UPDATE issues 
        SET court_name = c.name
        FROM courts c
        WHERE issues.court_id = c.id
        AND (issues.court_name IS NULL 
             OR issues.court_name = '' 
             OR issues.court_name != c.name)
        RETURNING issues.case_number, issues.court_name, c.name as new_court
      `);

      if (updateCourtNames.rows.length > 0) {
        console.log(`      ✅ تم تحديث ${updateCourtNames.rows.length} قضية:`);
        updateCourtNames.rows.forEach(row => {
          console.log(`         - ${row.case_number}: "${row.new_court}"`);
        });
      } else {
        console.log('      ✅ جميع أسماء المحاكم محدثة');
      }

      // 4. تنظيف القضايا التي لها أنواع نصية بدون معرفات
      console.log('\n   🧹 تنظيف القضايا التي لها أنواع نصية بدون معرفات:');
      
      const orphanIssueTypes = await pool.query(`
        SELECT id, case_number, issue_type, issue_type_id
        FROM issues 
        WHERE issue_type IS NOT NULL 
        AND issue_type != ''
        AND (issue_type_id IS NULL OR issue_type_id = 0)
      `);

      if (orphanIssueTypes.rows.length > 0) {
        console.log(`      📋 معالجة ${orphanIssueTypes.rows.length} قضية بأنواع نصية فقط:`);
        
        for (const issue of orphanIssueTypes.rows) {
          try {
            // البحث عن النوع المطابق
            const matchingType = await pool.query(`
              SELECT id, name FROM issue_types 
              WHERE name ILIKE $1 
              OR name ILIKE $2
              LIMIT 1
            `, [`%${issue.issue_type}%`, `${issue.issue_type}%`]);

            if (matchingType.rows.length > 0) {
              // ربط بالنوع الموجود
              await pool.query(`
                UPDATE issues 
                SET issue_type_id = $1, issue_type = $2
                WHERE id = $3
              `, [matchingType.rows[0].id, matchingType.rows[0].name, issue.id]);
              
              console.log(`         ✅ ${issue.case_number}: ربط بالنوع "${matchingType.rows[0].name}"`);
            } else {
              // إنشاء نوع جديد
              const newType = await pool.query(`
                INSERT INTO issue_types (name, description, category)
                VALUES ($1, $2, 'عام')
                RETURNING id, name
              `, [issue.issue_type, `نوع قضية: ${issue.issue_type}`]);
              
              await pool.query(`
                UPDATE issues 
                SET issue_type_id = $1, issue_type = $2
                WHERE id = $3
              `, [newType.rows[0].id, newType.rows[0].name, issue.id]);
              
              console.log(`         ➕ ${issue.case_number}: إنشاء نوع جديد "${newType.rows[0].name}"`);
            }
          } catch (error) {
            console.log(`         ❌ خطأ في معالجة ${issue.case_number}: ${error.message}`);
          }
        }
      } else {
        console.log('      ✅ لا توجد قضايا بأنواع نصية منفصلة');
      }

      // 5. تنظيف القضايا التي لها أسماء محاكم بدون معرفات
      console.log('\n   🧹 تنظيف القضايا التي لها أسماء محاكم بدون معرفات:');
      
      const orphanCourtNames = await pool.query(`
        SELECT id, case_number, court_name, court_id
        FROM issues 
        WHERE court_name IS NOT NULL 
        AND court_name != ''
        AND (court_id IS NULL OR court_id = 0)
      `);

      if (orphanCourtNames.rows.length > 0) {
        console.log(`      🏛️ معالجة ${orphanCourtNames.rows.length} قضية بأسماء محاكم فقط:`);
        
        for (const issue of orphanCourtNames.rows) {
          try {
            // البحث عن المحكمة المطابقة
            const matchingCourt = await pool.query(`
              SELECT id, name FROM courts 
              WHERE name ILIKE $1 
              OR name ILIKE $2
              LIMIT 1
            `, [`%${issue.court_name}%`, `${issue.court_name}%`]);

            if (matchingCourt.rows.length > 0) {
              // ربط بالمحكمة الموجودة
              await pool.query(`
                UPDATE issues 
                SET court_id = $1, court_name = $2
                WHERE id = $3
              `, [matchingCourt.rows[0].id, matchingCourt.rows[0].name, issue.id]);
              
              console.log(`         ✅ ${issue.case_number}: ربط بالمحكمة "${matchingCourt.rows[0].name}"`);
            } else {
              // إنشاء محكمة جديدة
              const newCourt = await pool.query(`
                INSERT INTO courts (name, type, location)
                VALUES ($1, 'عامة', 'غير محدد')
                RETURNING id, name
              `, [issue.court_name]);
              
              await pool.query(`
                UPDATE issues 
                SET court_id = $1, court_name = $2
                WHERE id = $3
              `, [newCourt.rows[0].id, newCourt.rows[0].name, issue.id]);
              
              console.log(`         ➕ ${issue.case_number}: إنشاء محكمة جديدة "${newCourt.rows[0].name}"`);
            }
          } catch (error) {
            console.log(`         ❌ خطأ في معالجة ${issue.case_number}: ${error.message}`);
          }
        }
      } else {
        console.log('      ✅ لا توجد قضايا بأسماء محاكم منفصلة');
      }

      // 6. إنشاء triggers لتحديث الأعمدة النصية تلقائياً
      console.log('\n   🔄 إنشاء triggers للتحديث التلقائي:');
      
      try {
        // Trigger لتحديث issue_type عند تغيير issue_type_id
        await pool.query(`
          CREATE OR REPLACE FUNCTION update_issue_type_name()
          RETURNS TRIGGER AS $$
          BEGIN
            IF NEW.issue_type_id IS NOT NULL THEN
              SELECT name INTO NEW.issue_type 
              FROM issue_types 
              WHERE id = NEW.issue_type_id;
            ELSE
              NEW.issue_type := NULL;
            END IF;
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;
        `);

        await pool.query(`
          DROP TRIGGER IF EXISTS trigger_update_issue_type_name ON issues;
          CREATE TRIGGER trigger_update_issue_type_name
            BEFORE INSERT OR UPDATE OF issue_type_id ON issues
            FOR EACH ROW
            EXECUTE FUNCTION update_issue_type_name();
        `);

        console.log('      ✅ تم إنشاء trigger لتحديث issue_type');

        // Trigger لتحديث court_name عند تغيير court_id
        await pool.query(`
          CREATE OR REPLACE FUNCTION update_court_name()
          RETURNS TRIGGER AS $$
          BEGIN
            IF NEW.court_id IS NOT NULL THEN
              SELECT name INTO NEW.court_name 
              FROM courts 
              WHERE id = NEW.court_id;
            ELSE
              NEW.court_name := NULL;
            END IF;
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;
        `);

        await pool.query(`
          DROP TRIGGER IF EXISTS trigger_update_court_name ON issues;
          CREATE TRIGGER trigger_update_court_name
            BEFORE INSERT OR UPDATE OF court_id ON issues
            FOR EACH ROW
            EXECUTE FUNCTION update_court_name();
        `);

        console.log('      ✅ تم إنشاء trigger لتحديث court_name');

      } catch (error) {
        console.log(`      ⚠️ خطأ في إنشاء triggers: ${error.message}`);
      }

      // 7. اختبار التحديث التلقائي
      console.log('\n   🧪 اختبار التحديث التلقائي:');
      
      try {
        // اختبار تحديث نوع القضية
        const testIssue = await pool.query(`
          SELECT id FROM issues LIMIT 1
        `);

        if (testIssue.rows.length > 0) {
          const issueId = testIssue.rows[0].id;
          
          // تحديث issue_type_id واختبار التحديث التلقائي
          await pool.query(`
            UPDATE issues 
            SET issue_type_id = issue_type_id
            WHERE id = $1
          `, [issueId]);
          
          console.log('      ✅ اختبار التحديث التلقائي نجح');
        }
      } catch (error) {
        console.log(`      ⚠️ خطأ في اختبار التحديث التلقائي: ${error.message}`);
      }

      // 8. عرض النتائج النهائية
      console.log('\n   📊 النتائج النهائية:');
      
      const finalResults = await pool.query(`
        SELECT 
          i.case_number,
          i.issue_type,
          i.issue_type_id,
          it.name as linked_issue_type,
          i.court_name,
          i.court_id,
          c.name as linked_court_name,
          CASE 
            WHEN i.issue_type = it.name THEN '✅'
            ELSE '❌'
          END as issue_type_match,
          CASE 
            WHEN i.court_name = c.name OR (i.court_name IS NULL AND c.name IS NULL) THEN '✅'
            ELSE '❌'
          END as court_name_match
        FROM issues i
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts c ON i.court_id = c.id
        ORDER BY i.case_number
      `);

      finalResults.rows.forEach(row => {
        console.log(`      ${row.case_number}:`);
        console.log(`         ${row.issue_type_match} النوع: "${row.issue_type}" (ID: ${row.issue_type_id})`);
        console.log(`         ${row.court_name_match} المحكمة: "${row.court_name || 'غير محدد'}" (ID: ${row.court_id || 'غير محدد'})`);
      });

      // 9. إحصائيات نهائية
      console.log('\n   📈 إحصائيات نهائية:');
      
      const finalStats = await pool.query(`
        SELECT 
          COUNT(*) as total_issues,
          COUNT(CASE WHEN issue_type_id IS NOT NULL THEN 1 END) as issues_with_type_id,
          COUNT(CASE WHEN court_id IS NOT NULL THEN 1 END) as issues_with_court_id,
          COUNT(CASE WHEN issue_type IS NOT NULL AND issue_type != '' THEN 1 END) as issues_with_type_name,
          COUNT(CASE WHEN court_name IS NOT NULL AND court_name != '' THEN 1 END) as issues_with_court_name
        FROM issues
      `);

      const stats = finalStats.rows[0];
      console.log(`      - إجمالي القضايا: ${stats.total_issues}`);
      console.log(`      - قضايا بها issue_type_id: ${stats.issues_with_type_id}`);
      console.log(`      - قضايا بها court_id: ${stats.issues_with_court_id}`);
      console.log(`      - قضايا بها issue_type: ${stats.issues_with_type_name}`);
      console.log(`      - قضايا بها court_name: ${stats.issues_with_court_name}`);

    } catch (error) {
      console.error(`   ❌ خطأ في إصلاح قاعدة البيانات ${dbName}:`, error.message);
    } finally {
      await pool.end();
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }

  console.log('✅ تم الانتهاء من إصلاح الأعمدة النصية');
  
  console.log('\n📋 ملخص الإصلاحات:');
  console.log('1. ✅ تحديث issue_type من issue_types.name بحسب issue_type_id');
  console.log('2. ✅ تحديث court_name من courts.name بحسب court_id');
  console.log('3. ✅ ربط القضايا المنفصلة بالجداول الصحيحة');
  console.log('4. ✅ إنشاء triggers للتحديث التلقائي');
  console.log('5. ✅ إلغاء الاعتماد على الأعمدة النصية المنفصلة');
  console.log('6. ✅ الاعتماد على العلاقات الصحيحة فقط');
}

// تشغيل الإصلاح
fixTextColumnsRelations().catch(console.error);
