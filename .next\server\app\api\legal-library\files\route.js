/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/legal-library/files/route";
exports.ids = ["app/api/legal-library/files/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Ffiles%2Froute&page=%2Fapi%2Flegal-library%2Ffiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Ffiles%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Ffiles%2Froute&page=%2Fapi%2Flegal-library%2Ffiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Ffiles%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_legal_library_files_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/legal-library/files/route.ts */ \"(rsc)/./src/app/api/legal-library/files/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_legal_library_files_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_legal_library_files_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/legal-library/files/route\",\n        pathname: \"/api/legal-library/files\",\n        filename: \"route\",\n        bundlePath: \"app/api/legal-library/files/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\legal-library\\\\files\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_legal_library_files_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Ffiles%2Froute&page=%2Fapi%2Flegal-library%2Ffiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Ffiles%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/legal-library/files/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/legal-library/files/route.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// دالة للحصول على مسار المكتبة القانونية من الإعدادات\nasync function getLegalLibraryPath() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT setting_value FROM system_settings WHERE setting_key = $1', [\n            'legal_library_path'\n        ]);\n        if (result.rows.length > 0) {\n            return result.rows[0].setting_value;\n        }\n        // المسار الافتراضي\n        return '/home/<USER>/Downloads/legal-system/laws';\n    } catch (error) {\n        console.error('خطأ في جلب مسار المكتبة القانونية:', error);\n        return '/home/<USER>/Downloads/legal-system/laws';\n    }\n}\n// دالة للحصول على الإعدادات\nasync function getLibrarySettings() {\n    try {\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT setting_key, setting_value, setting_type \n      FROM system_settings \n      WHERE setting_key LIKE 'legal_library_%'\n    `);\n        const settings = {};\n        result.rows.forEach((row)=>{\n            let value = row.setting_value;\n            // تحويل القيم حسب النوع\n            if (row.setting_type === 'boolean') {\n                value = value === 'true';\n            } else if (row.setting_type === 'number') {\n                value = parseInt(value);\n            }\n            settings[row.setting_key] = value;\n        });\n        return settings;\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات المكتبة:', error);\n        return {};\n    }\n}\n// دالة للتحقق من وجود المجلد وإنشاؤه إذا لم يكن موجوداً\nasync function ensureDirectoryExists(dirPath) {\n    try {\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(dirPath)) {\n            fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(dirPath, {\n                recursive: true\n            });\n            console.log(`تم إنشاء المجلد: ${dirPath}`);\n        }\n        return true;\n    } catch (error) {\n        console.error('خطأ في إنشاء المجلد:', error);\n        return false;\n    }\n}\n// دالة لفحص الملفات في المجلد\nasync function scanDirectoryFiles(dirPath, allowedExtensions) {\n    try {\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(dirPath)) {\n            return [];\n        }\n        const files = fs__WEBPACK_IMPORTED_MODULE_2___default().readdirSync(dirPath);\n        const fileList = [];\n        for (const file of files){\n            const filePath = path__WEBPACK_IMPORTED_MODULE_3___default().join(dirPath, file);\n            const stats = fs__WEBPACK_IMPORTED_MODULE_2___default().statSync(filePath);\n            if (stats.isFile()) {\n                const ext = path__WEBPACK_IMPORTED_MODULE_3___default().extname(file).toLowerCase();\n                if (allowedExtensions.length === 0 || allowedExtensions.includes(ext)) {\n                    fileList.push({\n                        name: file,\n                        path: filePath,\n                        size: stats.size,\n                        extension: ext,\n                        modified: stats.mtime,\n                        created: stats.birthtime\n                    });\n                }\n            }\n        }\n        return fileList.sort((a, b)=>b.modified.getTime() - a.modified.getTime());\n    } catch (error) {\n        console.error('خطأ في فحص الملفات:', error);\n        return [];\n    }\n}\n// GET - جلب قائمة الملفات من المجلد\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action') || 'list';\n        const settings = await getLibrarySettings();\n        const libraryPath = await getLegalLibraryPath();\n        if (action === 'settings') {\n            // إرجاع الإعدادات\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    path: libraryPath,\n                    settings: settings\n                }\n            });\n        }\n        if (action === 'list') {\n            // التحقق من وجود المجلد\n            const directoryExists = await ensureDirectoryExists(libraryPath);\n            if (!directoryExists) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: `تعذر الوصول إلى المجلد: ${libraryPath}`\n                }, {\n                    status: 500\n                });\n            }\n            // جلب الامتدادات المسموحة\n            const allowedExtensions = settings.legal_library_allowed_extensions ? settings.legal_library_allowed_extensions.split(',').map((ext)=>ext.trim()) : [\n                '.pdf',\n                '.doc',\n                '.docx',\n                '.txt'\n            ];\n            // فحص الملفات\n            const files = await scanDirectoryFiles(libraryPath, allowedExtensions);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    path: libraryPath,\n                    files: files,\n                    totalFiles: files.length,\n                    settings: settings\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير صحيح'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('خطأ في جلب ملفات المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب الملفات'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - مزامنة الملفات مع قاعدة البيانات\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action } = body;\n        if (action === 'sync') {\n            const settings = await getLibrarySettings();\n            const libraryPath = await getLegalLibraryPath();\n            // التحقق من وجود المجلد\n            const directoryExists = await ensureDirectoryExists(libraryPath);\n            if (!directoryExists) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: `تعذر الوصول إلى المجلد: ${libraryPath}`\n                }, {\n                    status: 500\n                });\n            }\n            // جلب الامتدادات المسموحة\n            const allowedExtensions = settings.legal_library_allowed_extensions ? settings.legal_library_allowed_extensions.split(',').map((ext)=>ext.trim()) : [\n                '.pdf',\n                '.doc',\n                '.docx',\n                '.txt'\n            ];\n            // فحص الملفات\n            const files = await scanDirectoryFiles(libraryPath, allowedExtensions);\n            // مزامنة مع قاعدة البيانات\n            let syncedCount = 0;\n            let errorCount = 0;\n            for (const file of files){\n                try {\n                    // التحقق من وجود الملف في قاعدة البيانات\n                    const existingResult = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM legal_library WHERE file_path = $1', [\n                        file.path\n                    ]);\n                    if (existingResult.rows.length === 0) {\n                        // إضافة الملف الجديد\n                        await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n              INSERT INTO legal_library (\n                title, description, file_path, file_size, \n                file_type, category, is_active, created_date\n              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n            `, [\n                            path__WEBPACK_IMPORTED_MODULE_3___default().basename(file.name, file.extension),\n                            `ملف تم اكتشافه تلقائياً من المجلد`,\n                            file.path,\n                            file.size,\n                            file.extension,\n                            'عام',\n                            true,\n                            new Date() // تاريخ الإنشاء\n                        ]);\n                        syncedCount++;\n                    }\n                } catch (fileError) {\n                    console.error(`خطأ في مزامنة الملف ${file.name}:`, fileError);\n                    errorCount++;\n                }\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    totalFiles: files.length,\n                    syncedFiles: syncedCount,\n                    errorFiles: errorCount,\n                    path: libraryPath\n                },\n                message: `تم مزامنة ${syncedCount} ملف جديد`\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير صحيح'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('خطأ في مزامنة ملفات المكتبة القانونية:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في المزامنة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/legal-library/files/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flegal-library%2Ffiles%2Froute&page=%2Fapi%2Flegal-library%2Ffiles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flegal-library%2Ffiles%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();