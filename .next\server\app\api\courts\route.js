/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/courts/route";
exports.ids = ["app/api/courts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourts%2Froute&page=%2Fapi%2Fcourts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourts%2Froute&page=%2Fapi%2Fcourts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_mohaminew_src_app_api_courts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/courts/route.ts */ \"(rsc)/./src/app/api/courts/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_courts_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nD_mohaminew_src_app_api_courts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/courts/route\",\n        pathname: \"/api/courts\",\n        filename: \"route\",\n        bundlePath: \"app/api/courts/route\"\n    },\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\courts\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_courts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourts%2Froute&page=%2Fapi%2Fcourts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/courts/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/courts/route.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب جميع المحاكم\nasync function GET() {\n    try {\n        console.log('GET Courts API: Fetching all courts');\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT\n        id,\n        name,\n        type,\n        location,\n        address,\n        phone,\n        email,\n        created_at\n      FROM courts\n      ORDER BY name\n    `);\n        console.log('GET Courts API: Found', result.rows.length, 'courts');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows\n        });\n    } catch (error) {\n        console.error('GET Courts API: Error fetching courts:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات المحاكم'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة محكمة جديدة\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, type, governorate_id, address, phone, is_active = true } = body;\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المحكمة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO courts (name, type, governorate_id, address, phone, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6)\n      RETURNING *\n    `, [\n            name,\n            type,\n            governorate_id,\n            address,\n            phone,\n            is_active\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إضافة المحكمة بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error creating court:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة المحكمة'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث محكمة\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, name, type, governorate_id, address, phone, is_active } = body;\n        if (!id || !name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف واسم المحكمة مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      UPDATE courts\n      SET name = $1, type = $2, governorate_id = $3, address = $4,\n          phone = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $7\n      RETURNING *\n    `, [\n            name,\n            type,\n            governorate_id,\n            address,\n            phone,\n            is_active,\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم تحديث بيانات المحكمة بنجاح',\n            data: result.rows[0]\n        });\n    } catch (error) {\n        console.error('Error updating court:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث بيانات المحكمة'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف محكمة\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المحكمة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM courts WHERE id = $1 RETURNING *', [\n            id\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المحكمة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المحكمة بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting court:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المحكمة'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/courts/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pool: () => (/* binding */ pool),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// تحميل ملف التوجيه\nlet routingConfig = null;\ntry {\n    const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'routing.config.json');\n    const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, 'utf8');\n    routingConfig = JSON.parse(configData);\n} catch (error) {\n    console.error('❌ خطأ في تحميل ملف التوجيه:', error);\n}\n// التحقق من وجود كلمة مرور قاعدة البيانات\nif (!process.env.DB_PASSWORD && !routingConfig?.default_config?.db_password) {\n    throw new Error('DB_PASSWORD environment variable is required');\n}\n// تحديد قاعدة البيانات بناءً على المنفذ\nconst getDatabaseConfig = ()=>{\n    const port = process.env.PORT || '7443';\n    if (routingConfig && routingConfig.routes[port]) {\n        const route = routingConfig.routes[port];\n        const defaultConfig = routingConfig.default_config;\n        return {\n            database: route.database,\n            user: defaultConfig.db_user,\n            host: defaultConfig.db_host,\n            password: process.env.DB_PASSWORD || defaultConfig.db_password,\n            port: defaultConfig.db_port\n        };\n    }\n    // الافتراضي إذا لم يتم العثور على التوجيه\n    console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`);\n    return {\n        database: 'mohammi',\n        user: 'postgres',\n        host: 'localhost',\n        password: process.env.DB_PASSWORD || 'yemen123',\n        port: 5432\n    };\n};\nconst dbConfig = getDatabaseConfig();\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\nasync function query(text, params) {\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } finally{\n        client.release();\n    }\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcourts%2Froute&page=%2Fapi%2Fcourts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();