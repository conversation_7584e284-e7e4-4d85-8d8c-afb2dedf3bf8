(()=>{var e={};e.id=7294,e.ids=[7294],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4358:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.r(t),a.d(t,{GET:()=>s});var c=a(32190),r=a(5069),o=e([r]);async function s(e,{params:t}){try{let{searchParams:a}=new URL(e.url),n=a.get("date_from"),r=a.get("date_to"),{reportType:o}=await t,s={accounts:[],transactions:[],summary:{totalAssets:0,totalLiabilities:0,totalEquity:0,totalRevenue:0,totalExpenses:0,netIncome:0}};switch(o){case"trial-balance":s=await i(n,r);break;case"general-ledger":s=await d(n,r);break;case"income-statement":s=await u(n,r);break;case"balance-sheet":s=await _(n,r);break;case"cash-flow":s=await E(n,r);break;case"vouchers-summary":s=await p(n,r);break;default:return c.NextResponse.json({success:!1,error:"نوع التقرير غير مدعوم"},{status:400})}return c.NextResponse.json({success:!0,...s,reportType:o,dateFrom:n,dateTo:r,generatedAt:new Date().toISOString(),message:"تم إنشاء التقرير بنجاح"})}catch(e){return console.error("خطأ في إنشاء التقرير:",e),c.NextResponse.json({success:!1,error:"فشل في إنشاء التقرير",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e,t){let a=t||new Date().toISOString().split("T")[0],n=await (0,r.P)(`
    WITH account_balances AS (
      -- أرصدة افتتاحية
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        ca.opening_balance,
        COALESCE(
          CASE
            WHEN ca.account_nature = 'مدين' THEN ca.opening_balance
            ELSE 0
          END, 0
        ) as opening_debit,
        COALESCE(
          CASE
            WHEN ca.account_nature = 'دائن' THEN ca.opening_balance
            ELSE 0
          END, 0
        ) as opening_credit
      FROM chart_of_accounts ca
      WHERE ca.allow_transactions = true AND ca.is_active = true

      UNION ALL



      -- حركات من القيود اليومية
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        0 as opening_balance,
        COALESCE(jed.debit_amount, 0) as opening_debit,
        COALESCE(jed.credit_amount, 0) as opening_credit
      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date <= $1
        AND je.status = 'approved'
      WHERE ca.allow_transactions = true AND ca.is_active = true
        AND jed.id IS NOT NULL
    )
    SELECT
      id,
      account_code,
      account_name,
      account_type,
      account_nature,
      SUM(opening_debit) as total_debit,
      SUM(opening_credit) as total_credit,
      CASE
        WHEN SUM(opening_debit) > SUM(opening_credit)
        THEN SUM(opening_debit) - SUM(opening_credit)
        ELSE 0
      END as debit_balance,
      CASE
        WHEN SUM(opening_credit) > SUM(opening_debit)
        THEN SUM(opening_credit) - SUM(opening_debit)
        ELSE 0
      END as credit_balance
    FROM account_balances
    GROUP BY id, account_code, account_name, account_type, account_nature
    HAVING SUM(opening_debit) != 0 OR SUM(opening_credit) != 0
    ORDER BY account_code
  `,[a]);return{accounts:n.rows,summary:{totalDebit:n.rows.reduce((e,t)=>e+(t.debit_balance||0),0),totalCredit:n.rows.reduce((e,t)=>e+(t.credit_balance||0),0)}}}async function u(e,t){let a=e||new Date(new Date().getFullYear(),0,1).toISOString().split("T")[0],n=t||new Date().toISOString().split("T")[0],c=(await (0,r.P)(`
    WITH income_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,

        -- من القيود اليومية (تشمل السندات والقيود اليدوية)
        COALESCE(SUM(
          CASE
            WHEN ca.account_type IN ('إيرادات', 'revenue')
            THEN jed.credit_amount - jed.debit_amount
            WHEN ca.account_type IN ('مصروفات', 'expenses')
            THEN jed.debit_amount - jed.credit_amount
            ELSE 0
          END
        ), 0) as journal_amount

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date BETWEEN $1 AND $2
        AND je.status = 'approved'
      WHERE ca.account_type IN ('إيرادات', 'revenue', 'مصروفات', 'expenses')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('إيرادات', 'revenue')
        THEN journal_amount
        ELSE 0
      END as total_revenue,
      CASE
        WHEN account_type IN ('مصروفات', 'expenses')
        THEN journal_amount
        ELSE 0
      END as total_expenses
    FROM income_accounts
    WHERE journal_amount != 0
    ORDER BY account_code
  `,[a,n])).rows,o=c.reduce((e,t)=>e+(t.total_revenue||0),0),s=c.reduce((e,t)=>e+(t.total_expenses||0),0);return{accounts:c,summary:{totalRevenue:o,totalExpenses:s,netIncome:o-s}}}async function _(e,t){let a=t||new Date().toISOString().split("T")[0],n=(await (0,r.P)(`
    WITH balance_sheet_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        ca.opening_balance,

        -- حساب الرصيد النهائي
        ca.opening_balance +
        COALESCE(SUM(
          CASE
            WHEN ca.account_nature = 'مدين'
            THEN COALESCE(jed.debit_amount, 0) - COALESCE(jed.credit_amount, 0)
            ELSE COALESCE(jed.credit_amount, 0) - COALESCE(jed.debit_amount, 0)
          END
        ), 0) +
        0 as final_balance

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date <= $1
        AND je.status = 'approved'
      WHERE ca.account_type IN ('أصول', 'assets', 'خصوم', 'liabilities', 'حقوق ملكية', 'equity')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature, ca.opening_balance
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('أصول', 'assets') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as asset_balance,
      CASE
        WHEN account_type IN ('خصوم', 'liabilities') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as liability_balance,
      CASE
        WHEN account_type IN ('حقوق ملكية', 'equity') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as equity_balance
    FROM balance_sheet_accounts
    WHERE final_balance != 0
    ORDER BY account_code
  `,[a])).rows,c=n.reduce((e,t)=>e+(t.asset_balance||0),0),o=n.reduce((e,t)=>e+(t.liability_balance||0),0),s=n.reduce((e,t)=>e+(t.equity_balance||0),0);return{accounts:n,summary:{totalAssets:c,totalLiabilities:o,totalEquity:s}}}async function d(e,t){let a=e||new Date(new Date().getFullYear(),0,1).toISOString().split("T")[0],n=t||new Date().toISOString().split("T")[0],c=await (0,r.P)(`
    SELECT
      CASE
        WHEN je.voucher_type = 'payment' THEN 'سند صرف'
        WHEN je.voucher_type = 'receipt' THEN 'سند قبض'
        WHEN je.voucher_type = 'journal' THEN 'قيد يومي'
        ELSE 'معاملة'
      END as transaction_type,
      je.entry_number as reference,
      je.entry_date as transaction_date,
      COALESCE(jed.description, je.description) as description,
      ca.account_code,
      ca.account_name,
      jed.debit_amount,
      jed.credit_amount,
      CASE
        WHEN je.voucher_type = 'payment' THEN je.beneficiary_name
        WHEN je.voucher_type = 'receipt' THEN je.payer_name
        ELSE ''
      END as party_name,
      je.voucher_type,
      pm.method_name as payment_method,
      c.currency_code
    FROM journal_entry_details jed
    JOIN journal_entries je ON jed.journal_entry_id = je.id
    JOIN chart_of_accounts ca ON jed.account_id = ca.id
    LEFT JOIN payment_methods pm ON je.payment_method_id = pm.id
    LEFT JOIN currencies c ON je.currency_id = c.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.status IN ('draft', 'approved')
      AND ca.is_active = true
    ORDER BY ca.account_code, je.entry_date, je.entry_number, jed.line_number
  `,[a,n]),o=new Map;return c.rows.forEach(e=>{let t=`${e.account_code}-${e.account_name}`;o.has(t)||o.set(t,{account_code:e.account_code,account_name:e.account_name,transactions:[],total_debit:0,total_credit:0,balance:0});let a=o.get(t);a.transactions.push(e),a.total_debit+=parseFloat(e.debit_amount||0),a.total_credit+=parseFloat(e.credit_amount||0),a.balance=a.total_debit-a.total_credit}),{transactions:c.rows,accounts:Array.from(o.values()),summary:{totalTransactions:c.rows.length,totalAccounts:o.size}}}async function p(e,t){let a=e||new Date(new Date().getFullYear(),0,1).toISOString().split("T")[0],n=t||new Date().toISOString().split("T")[0],c=await (0,r.P)(`
    SELECT
      voucher_type,
      COUNT(*) as total_count,
      SUM(amount) as total_amount,
      AVG(amount) as average_amount,
      MIN(amount) as min_amount,
      MAX(amount) as max_amount
    FROM journal_entries
    WHERE entry_date BETWEEN $1 AND $2
      AND voucher_type IN ('payment', 'receipt')
      AND status IN ('draft', 'approved')
    GROUP BY voucher_type
    ORDER BY voucher_type
  `,[a,n]),o=await (0,r.P)(`
    SELECT
      je.voucher_type,
      je.entry_number,
      je.entry_date,
      je.amount,
      je.description,
      CASE
        WHEN je.voucher_type = 'payment' THEN je.beneficiary_name
        WHEN je.voucher_type = 'receipt' THEN je.payer_name
        ELSE 'غير محدد'
      END as party_name,
      pm.method_name as payment_method,
      c.currency_code,
      c.symbol as currency_symbol,
      -- الحساب المدين
      debit_acc.account_code as debit_account_code,
      debit_acc.account_name as debit_account_name,
      -- الحساب الدائن
      credit_acc.account_code as credit_account_code,
      credit_acc.account_name as credit_account_name
    FROM journal_entries je
    LEFT JOIN payment_methods pm ON je.payment_method_id = pm.id
    LEFT JOIN currencies c ON je.currency_id = c.id
    -- ربط الحساب المدين
    LEFT JOIN journal_entry_details jed_debit ON je.id = jed_debit.journal_entry_id
      AND jed_debit.debit_amount > 0 AND jed_debit.line_number = 1
    LEFT JOIN chart_of_accounts debit_acc ON jed_debit.account_id = debit_acc.id
    -- ربط الحساب الدائن
    LEFT JOIN journal_entry_details jed_credit ON je.id = jed_credit.journal_entry_id
      AND jed_credit.credit_amount > 0 AND jed_credit.line_number = 2
    LEFT JOIN chart_of_accounts credit_acc ON jed_credit.account_id = credit_acc.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.voucher_type IN ('payment', 'receipt')
      AND je.status IN ('draft', 'approved')
    ORDER BY je.entry_date DESC, je.entry_number DESC
    LIMIT 50
  `,[a,n]),s=c.rows.find(e=>"payment"===e.voucher_type),i=c.rows.find(e=>"receipt"===e.voucher_type);return{accounts:c.rows,transactions:o.rows,summary:{totalPayments:parseFloat(s?.total_amount||0),totalReceipts:parseFloat(i?.total_amount||0),paymentCount:parseInt(s?.total_count||0),receiptCount:parseInt(i?.total_count||0),netAmount:parseFloat(i?.total_amount||0)-parseFloat(s?.total_amount||0)}}}async function E(e,t){return{accounts:[],summary:{}}}r=(o.then?(await o)():o)[0],n()}catch(e){n(e)}})},5069:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.d(t,{P:()=>_});var c=a(64939),r=a(29021),o=a.n(r),s=a(33873),i=a.n(s),u=e([c]);c=(u.then?(await u)():u)[0];let d=null;try{let e=i().join(process.cwd(),"routing.config.json"),t=o().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],a=d.default_config;return{database:t.database,user:a.db_user,host:a.db_host,password:process.env.DB_PASSWORD||a.db_password,port:a.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),E=new c.Pool(p);async function _(e,t){let a=await E.connect();try{return await a.query(e,t)}finally{a.release()}}n()}catch(e){n(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},80800:(e,t,a)=>{"use strict";a.a(e,async(e,n)=>{try{a.r(t),a.d(t,{patchFetch:()=>u,routeModule:()=>_,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var c=a(96559),r=a(48088),o=a(37719),s=a(4358),i=e([s]);s=(i.then?(await i)():i)[0];let _=new c.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/accounting/reports/[reportType]/route",pathname:"/api/accounting/reports/[reportType]",filename:"route",bundlePath:"app/api/accounting/reports/[reportType]/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\reports\\[reportType]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:E}=_;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}n()}catch(e){n(e)}})},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,580],()=>a(80800));module.exports=n})();