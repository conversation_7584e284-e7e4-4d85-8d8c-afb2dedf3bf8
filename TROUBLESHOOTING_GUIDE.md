# دليل استكشاف الأخطاء وإصلاحها

## ❌ مشكلة: "next is not recognized"

### السبب:
هذه المشكلة تحدث عندما:
1. Next.js غير مثبت
2. Node.js غير مثبت أو غير متاح في PATH
3. npm غير يعمل بشكل صحيح

### الحلول:

#### **الحل 1: استخدام npx (الأسرع)**
```cmd
# بدلاً من: npm start
# استخدم:
npx next start -p 3000

# أو للبناء:
npx next build
```

#### **الحل 2: استخدام ملفات التشغيل الجديدة**
```cmd
# للإنتاج:
start-production-fixed.bat

# للتطوير:
start-development.bat

# باستخدام npx:
start-with-npx.bat
```

#### **الحل 3: التثبيت اليدوي**
```cmd
# 1. تثبيت Node.js من https://nodejs.org/
# 2. تثبيت التبعيات:
npm install

# 3. تثبيت Next.js محلياً:
npm install next@latest react@latest react-dom@latest

# 4. البناء والتشغيل:
npx next build
npx next start -p 3000
```

## ❌ مشكلة: "npm is not recognized"

### الحل:
1. تحميل وتثبيت Node.js من: https://nodejs.org/
2. إعادة تشغيل Command Prompt
3. التحقق: `node --version` و `npm --version`

## ❌ مشكلة: "Module not found"

### الحل:
```cmd
# حذف node_modules وإعادة التثبيت:
rmdir /s node_modules
del package-lock.json
npm install
```

## ❌ مشكلة: "Port 3000 already in use"

### الحل:
```cmd
# استخدام منفذ آخر:
npx next start -p 3001

# أو إيقاف العملية المستخدمة للمنفذ:
netstat -ano | findstr :3000
taskkill /PID [PID_NUMBER] /F
```

## ❌ مشكلة: "Build failed"

### الحل:
```cmd
# تنظيف الكاش:
npm cache clean --force

# حذف .next وإعادة البناء:
rmdir /s .next
npx next build
```

## 🚀 خطوات التشغيل المضمونة

### للمبتدئين:
```cmd
# 1. تأكد من تثبيت Node.js 18+
node --version

# 2. انتقل لمجلد النظام
cd path\to\legal-system

# 3. تثبيت التبعيات
npm install

# 4. تشغيل النظام
npx next dev -p 3000
```

### للإنتاج:
```cmd
# 1. بناء النظام
npx next build

# 2. تشغيل النظام
npx next start -p 3000
```

## 📋 متطلبات النظام

### الحد الأدنى:
- **Node.js**: 18.0.0 أو أحدث
- **npm**: 8.0.0 أو أحدث
- **RAM**: 4 GB
- **Storage**: 2 GB مساحة فارغة

### الموصى به:
- **Node.js**: 20.0.0 أو أحدث
- **npm**: 10.0.0 أو أحدث
- **RAM**: 8 GB
- **Storage**: 5 GB مساحة فارغة

## 🔧 أوامر مفيدة

### فحص النظام:
```cmd
# فحص إصدارات النظام
node --version
npm --version
npx --version

# فحص التبعيات
npm list --depth=0

# فحص المنافذ المستخدمة
netstat -ano | findstr :3000
```

### تنظيف النظام:
```cmd
# تنظيف شامل
rmdir /s node_modules
rmdir /s .next
del package-lock.json
npm cache clean --force
npm install
```

## 📞 الدعم

### إذا استمرت المشاكل:
1. تأكد من تثبيت Node.js الصحيح
2. استخدم Command Prompt كمدير (Run as Administrator)
3. تحقق من إعدادات Firewall/Antivirus
4. جرب تشغيل النظام في مجلد مختلف

### ملفات السجلات:
- **Build logs**: تظهر في Command Prompt
- **Runtime logs**: في مجلد `.next/`
- **npm logs**: في `npm-debug.log`

## ✅ التحقق من نجاح التشغيل

عند نجاح التشغيل ستظهر رسالة:
```
✓ Ready on http://localhost:3000
```

ثم يمكنك فتح المتصفح والذهاب إلى:
- **الصفحة الرئيسية**: http://localhost:3000/home
- **تسجيل الدخول**: http://localhost:3000/login
- **لوحة التحكم**: http://localhost:3000/dashboard
