(()=>{var e={};e.id=9535,e.ids=[9535],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{P:()=>l});var o=s(64939),a=s(29021),n=s.n(a),c=s(33873),u=s.n(c),i=e([o]);o=(i.then?(await i)():i)[0];let d=null;try{let e=u().join(process.cwd(),"routing.config.json"),t=n().readFileSync(e,"utf8");d=JSON.parse(t)}catch(e){console.error("❌ خطأ في تحميل ملف التوجيه:",e)}if(!process.env.DB_PASSWORD&&!d?.default_config?.db_password)throw Error("DB_PASSWORD environment variable is required");let p=(()=>{let e=process.env.PORT||"7443";if(d&&d.routes[e]){let t=d.routes[e],s=d.default_config;return{database:t.database,user:s.db_user,host:s.db_host,password:process.env.DB_PASSWORD||s.db_password,port:s.db_port}}return console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${e}، سيتم استخدام الإعدادات الافتراضية`),{database:"mohammi",user:"postgres",host:"localhost",password:process.env.DB_PASSWORD||"yemen123",port:5432}})(),_=new o.Pool(p);async function l(e,t){let s=await _.connect();try{return await s.query(e,t)}finally{s.release()}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45912:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>i,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var o=s(96559),a=s(48088),n=s(37719),c=s(94001),u=e([c]);c=(u.then?(await u)():u)[0];let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounting/chart-of-accounts/[id]/route",pathname:"/api/accounting/chart-of-accounts/[id]",filename:"route",bundlePath:"app/api/accounting/chart-of-accounts/[id]/route"},resolvedPagePath:"D:\\mohaminew\\src\\app\\api\\accounting\\chart-of-accounts\\[id]\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:_}=l;function i(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},94001:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>i,GET:()=>c,PUT:()=>u});var o=s(32190),a=s(5069),n=e([a]);async function c(e,{params:t}){try{let{id:e}=await t,s=await (0,a.P)(`
      SELECT 
        id,
        account_code,
        account_name,
        account_name_en,
        level_1_code,
        level_2_code,
        level_3_code,
        level_4_code,
        account_level,
        parent_id,
        account_type,
        account_nature,
        is_active,
        allow_transactions,
        linked_table,
        auto_create_sub_accounts,
        opening_balance,
        current_balance,
        description,
        created_date
      FROM chart_of_accounts
      WHERE id = $1
    `,[e]);if(0===s.rows.length)return o.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return o.NextResponse.json({success:!0,account:s.rows[0],message:"تم جلب الحساب بنجاح"})}catch(e){return console.error("خطأ في جلب الحساب:",e),o.NextResponse.json({success:!1,error:"فشل في جلب الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function u(e,{params:t}){try{let s=await e.json(),{id:r}=await t;console.log("تحديث الحساب:",{accountId:r,body:s});let{account_name:n,account_name_en:c,account_type:u,account_nature:i,linked_table:l,auto_create_sub_accounts:d,description:p,is_active:_=!0,allow_transactions:E,notes:R}=s;if(!n||!u)return o.NextResponse.json({success:!1,error:"اسم الحساب ونوع الحساب مطلوبان"},{status:400});if(E&&l&&"none"!==l)return o.NextResponse.json({success:!1,error:"لا يمكن تفعيل المعاملات على حسابات التحكم المرتبطة بجداول خارجية"},{status:400});let w=await (0,a.P)("SELECT id, account_level FROM chart_of_accounts WHERE id = $1",[r]);if(0===w.rows.length)return o.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});let h=await (0,a.P)(`
      UPDATE chart_of_accounts
      SET
        account_name = $2,
        account_name_en = $3,
        account_type = $4,
        account_nature = $5,
        linked_table = $6,
        auto_create_sub_accounts = $7,
        description = $8,
        is_active = $9,
        allow_transactions = $10,
        notes = $11,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `,[r,n,c||null,u,i||"مدين",l||null,d||!1,p||null,void 0===_||_,void 0!==E&&E,R||null]);if(0===h.rows.length)return o.NextResponse.json({success:!1,error:"فشل في تحديث الحساب"},{status:500});return console.log("تم تحديث الحساب بنجاح:",h.rows[0]),o.NextResponse.json({success:!0,account:h.rows[0],message:"تم تحديث الحساب بنجاح"})}catch(e){return console.error("خطأ في تحديث الحساب:",e),o.NextResponse.json({success:!1,error:"فشل في تحديث الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function i(e,{params:t}){try{let{id:e}=await t,s=await (0,a.P)("SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_id = $1",[e]);if(parseInt(s.rows[0].count)>0)return o.NextResponse.json({success:!1,error:"لا يمكن حذف الحساب لوجود حسابات فرعية"},{status:400});let r=await (0,a.P)(`
      SELECT
        (SELECT COUNT(*) FROM payment_vouchers WHERE debit_account_id = $1 OR credit_account_id = $1) +
        (SELECT COUNT(*) FROM receipt_vouchers WHERE debit_account_id = $1 OR credit_account_id = $1) +
        (SELECT COUNT(*) FROM journal_entry_details WHERE account_id = $1) as total_transactions
    `,[e]);if(parseInt(r.rows[0].total_transactions)>0)return o.NextResponse.json({success:!1,error:"لا يمكن حذف الحساب لوجود معاملات مرتبطة به"},{status:400});let n=await (0,a.P)("DELETE FROM chart_of_accounts WHERE id = $1 RETURNING *",[e]);if(0===n.rows.length)return o.NextResponse.json({success:!1,error:"الحساب غير موجود"},{status:404});return o.NextResponse.json({success:!0,message:"تم حذف الحساب بنجاح"})}catch(e){return console.error("خطأ في حذف الحساب:",e),o.NextResponse.json({success:!1,error:"فشل في حذف الحساب",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}a=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(45912));module.exports=r})();