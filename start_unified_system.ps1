# سكريپت تشغيل النظام الموحد
# Unified System Startup Script

param(
    [string]$Action = "all",
    [int]$Port = 0
)

Write-Host "🌟 نظام إدارة المحاماة الموحد" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray

# دالة للتحقق من قاعدة البيانات
function Test-Database {
    param([string]$DatabaseName)
    
    $env:PGPASSWORD = "yemen123"
    $result = psql -U postgres -h localhost -lqt | Select-String $DatabaseName
    return $result -ne $null
}

# دالة لعرض حالة قواعد البيانات
function Show-DatabaseStatus {
    Write-Host "`n📊 حالة قواعد البيانات:" -ForegroundColor Cyan
    
    $databases = @("mohammi", "rubaie")
    foreach ($db in $databases) {
        $status = if (Test-Database $db) { "✅ متوفرة" } else { "❌ غير موجودة" }
        Write-Host "   🗄️ $db`: $status" -ForegroundColor White
    }
}

# دالة لنسخ قاعدة البيانات
function Copy-Database {
    Write-Host "`n📋 نسخ قاعدة البيانات..." -ForegroundColor Yellow
    
    if (-not (Test-Database "mohammi")) {
        Write-Host "❌ قاعدة البيانات المصدر 'mohammi' غير موجودة" -ForegroundColor Red
        return $false
    }
    
    # تشغيل سكريپت النسخ
    .\quick_copy_db.ps1
    
    return $LASTEXITCODE -eq 0
}

# دالة لتشغيل النظام
function Start-UnifiedSystem {
    param([string]$Mode, [int]$SpecificPort)
    
    Write-Host "`n🚀 تشغيل النظام..." -ForegroundColor Green
    
    switch ($Mode) {
        "all" {
            Write-Host "🔄 تشغيل جميع الخوادم..." -ForegroundColor Yellow
            node unified-server.js
        }
        "single" {
            Write-Host "🔄 تشغيل خادم واحد على المنفذ $SpecificPort..." -ForegroundColor Yellow
            node unified-server.js --port $SpecificPort
        }
        default {
            Write-Host "❌ وضع غير صحيح: $Mode" -ForegroundColor Red
            return $false
        }
    }
    
    return $true
}

# التحقق من Node.js
Write-Host "`n🔍 التحقق من المتطلبات..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير متوفر" -ForegroundColor Red
    exit 1
}

# التحقق من PostgreSQL
try {
    $env:PGPASSWORD = "yemen123"
    $pgVersion = psql --version
    Write-Host "✅ PostgreSQL: $pgVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL غير متوفر" -ForegroundColor Red
    exit 1
}

# عرض حالة قواعد البيانات
Show-DatabaseStatus

# التحقق من وجود قاعدة البيانات rubaie
if (-not (Test-Database "rubaie")) {
    Write-Host "`n⚠️ قاعدة البيانات 'rubaie' غير موجودة" -ForegroundColor Yellow
    $copyChoice = Read-Host "هل تريد نسخها من 'mohammi'؟ (y/n)"
    
    if ($copyChoice -eq "y" -or $copyChoice -eq "Y") {
        if (-not (Copy-Database)) {
            Write-Host "❌ فشل في نسخ قاعدة البيانات" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ لا يمكن تشغيل النظام بدون قاعدة البيانات 'rubaie'" -ForegroundColor Red
        exit 1
    }
}

# تشغيل النظام حسب المعاملات
switch ($Action.ToLower()) {
    "all" {
        Write-Host "`n🌐 تشغيل جميع الخوادم:" -ForegroundColor Cyan
        Write-Host "   📡 المنفذ 7443: نظام محمد (قاعدة البيانات: mohammi)" -ForegroundColor White
        Write-Host "   📡 المنفذ 8914: نظام الربعي (قاعدة البيانات: rubaie)" -ForegroundColor White
        
        Start-UnifiedSystem "all" 0
    }
    "mohammi" {
        Write-Host "`n🌐 تشغيل نظام محمد فقط (المنفذ 7443)" -ForegroundColor Cyan
        Start-UnifiedSystem "single" 7443
    }
    "rubaie" {
        Write-Host "`n🌐 تشغيل نظام الربعي فقط (المنفذ 8914)" -ForegroundColor Cyan
        Start-UnifiedSystem "single" 8914
    }
    "copy" {
        Write-Host "`n📋 نسخ قاعدة البيانات فقط..." -ForegroundColor Cyan
        Copy-Database
        Write-Host "✅ تم الانتهاء من النسخ" -ForegroundColor Green
    }
    "status" {
        Show-DatabaseStatus
        Write-Host "`n📊 حالة النظام:" -ForegroundColor Cyan
        Write-Host "   🔗 للتشغيل الكامل: .\start_unified_system.ps1 -Action all" -ForegroundColor White
        Write-Host "   🔗 لتشغيل محمد فقط: .\start_unified_system.ps1 -Action mohammi" -ForegroundColor White
        Write-Host "   🔗 لتشغيل الربعي فقط: .\start_unified_system.ps1 -Action rubaie" -ForegroundColor White
    }
    default {
        Write-Host "`n❌ إجراء غير صحيح: $Action" -ForegroundColor Red
        Write-Host "`n📋 الاستخدام:" -ForegroundColor Yellow
        Write-Host "   .\start_unified_system.ps1 -Action all      # تشغيل جميع الأنظمة" -ForegroundColor White
        Write-Host "   .\start_unified_system.ps1 -Action mohammi  # تشغيل نظام محمد فقط" -ForegroundColor White
        Write-Host "   .\start_unified_system.ps1 -Action rubaie   # تشغيل نظام الربعي فقط" -ForegroundColor White
        Write-Host "   .\start_unified_system.ps1 -Action copy     # نسخ قاعدة البيانات فقط" -ForegroundColor White
        Write-Host "   .\start_unified_system.ps1 -Action status   # عرض الحالة فقط" -ForegroundColor White
        exit 1
    }
}
