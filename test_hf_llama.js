// اختبار Llama 3.1 عبر Hugging Face
require('dotenv').config({ path: '.env.local' });

async function testHFLlama() {
  console.log('🔄 اختبار Llama 3.1 8B عبر Hugging Face...\n');
  
  const apiKey = process.env.HUGGINGFACE_API_KEY;
  
  if (!apiKey) {
    console.log('❌ HUGGINGFACE_API_KEY غير متوفر');
    return;
  }
  
  console.log('✅ تم العثور على HUGGINGFACE_API_KEY');
  console.log(`🔑 المفتاح يبدأ بـ: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}\n`);
  
  // نماذج مختلفة للاختبار
  const modelsToTest = [
    {
      name: 'Llama 3.1 8B Instruct',
      endpoint: 'https://api-inference.huggingface.co/models/meta-llama/Llama-3.1-8B-Instruct'
    },
    {
      name: 'Llama 3.2 3B Instruct',
      endpoint: 'https://api-inference.huggingface.co/models/meta-llama/Llama-3.2-3B-Instruct'
    },
    {
      name: 'Llama 3.2 1B Instruct',
      endpoint: 'https://api-inference.huggingface.co/models/meta-llama/Llama-3.2-1B-Instruct'
    }
  ];
  
  for (const model of modelsToTest) {
    console.log(`${'='.repeat(50)}`);
    console.log(`🧪 اختبار: ${model.name}`);
    console.log(`${'='.repeat(50)}`);
    
    try {
      const response = await fetch(model.endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inputs: "مرحباً، هل يمكنك مساعدتي في استشارة قانونية؟",
          parameters: {
            max_new_tokens: 100,
            temperature: 0.7,
            return_full_text: false
          }
        })
      });
      
      console.log(`📡 رمز الاستجابة: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ نجح الاختبار!');
        
        let aiMessage = '';
        if (Array.isArray(data) && data.length > 0) {
          aiMessage = data[0].generated_text || data[0].text || JSON.stringify(data[0]);
        } else if (data.generated_text) {
          aiMessage = data.generated_text;
        } else {
          aiMessage = JSON.stringify(data);
        }
        
        console.log('🤖 رد النموذج:');
        console.log(`   "${aiMessage}"`);
        console.log('🎉 هذا النموذج يعمل!');
        
      } else {
        const errorData = await response.text();
        console.log('❌ فشل الاختبار');
        console.log(`📝 رسالة الخطأ: ${errorData.substring(0, 200)}`);
        
        if (response.status === 403) {
          console.log('🔑 مشكلة في الصلاحيات');
        } else if (response.status === 503) {
          console.log('⏰ النموذج قيد التحميل');
        }
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
    }
    
    console.log('\n');
    
    // انتظار قصير بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log(`${'='.repeat(50)}`);
  console.log('📋 ملخص الاختبار:');
  console.log('🔍 تم اختبار عدة نماذج من Hugging Face');
  console.log('💡 إذا فشلت جميع النماذج بخطأ 403:');
  console.log('   • المشكلة في صلاحيات الحساب');
  console.log('   • قد تحتاج ترقية الحساب أو طلب وصول');
  console.log('   • أو استخدام Groq بدلاً من Hugging Face');
  console.log(`${'='.repeat(50)}`);
}

// تشغيل الاختبار
testHFLlama().catch(console.error);
