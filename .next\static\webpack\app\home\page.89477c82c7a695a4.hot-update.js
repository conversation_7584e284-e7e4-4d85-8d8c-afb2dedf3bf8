"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Mail,MessageCircle,Minimize2,Phone,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatWidget(param) {\n    let { className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"ChatWidget.useState\"]);\n    const [companyInfo, setCompanyInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [welcomeMessage, setWelcomeMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // تحميل رسالة الترحيب عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && messages.length === 0) {\n                console.log('🔄 تحميل رسالة الترحيب...');\n                loadWelcomeMessage();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // التمرير التلقائي للرسائل الجديدة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWidget.useEffect\"], [\n        messages,\n        isTyping\n    ]);\n    // تركيز على حقل الإدخال عند فتح المحادثة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && !isMinimized && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        isMinimized\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const loadWelcomeMessage = async ()=>{\n        try {\n            const response = await fetch('/api/chat');\n            const result = await response.json();\n            if (result.success) {\n                setWelcomeMessage(result.data.welcomeMessage);\n                setCompanyInfo(result.data.companyInfo);\n                // إضافة رسالة الترحيب\n                const welcomeMsg = {\n                    id: \"welcome_\".concat(Date.now()),\n                    type: 'bot',\n                    content: result.data.welcomeMessage,\n                    timestamp: new Date(),\n                    responseType: 'greeting'\n                };\n                setMessages([\n                    welcomeMsg\n                ]);\n            }\n        } catch (error) {\n            console.error('خطأ في تحميل رسالة الترحيب:', error);\n            // رسالة ترحيب افتراضية\n            const defaultWelcome = {\n                id: \"welcome_\".concat(Date.now()),\n                type: 'bot',\n                content: 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\n                timestamp: new Date(),\n                responseType: 'greeting'\n            };\n            setMessages([\n                defaultWelcome\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: inputMessage.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    sessionId: sessionId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تأخير قصير لمحاكاة الكتابة\n                setTimeout(()=>{\n                    const botMessage = {\n                        id: \"bot_\".concat(Date.now()),\n                        type: 'bot',\n                        content: result.data.message,\n                        timestamp: new Date(),\n                        responseType: result.data.type\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            botMessage\n                        ]);\n                    setIsTyping(false);\n                    // تحديث معلومات الشركة إذا كانت متوفرة\n                    if (result.data.companyInfo) {\n                        setCompanyInfo(result.data.companyInfo);\n                    }\n                }, 1000 + Math.random() * 1000) // تأخير عشوائي بين 1-2 ثانية\n                ;\n            } else {\n                throw new Error(result.error || 'خطأ في الإرسال');\n            }\n        } catch (error) {\n            console.error('خطأ في إرسال الرسالة:', error);\n            setTimeout(()=>{\n                const errorMessage = {\n                    id: \"error_\".concat(Date.now()),\n                    type: 'bot',\n                    content: 'عذراً، حدث خطأ في الإرسال. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',\n                    timestamp: new Date(),\n                    responseType: 'error'\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n                setIsTyping(false);\n            }, 500);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatMessage = (content)=>{\n        // تحويل النص إلى HTML مع دعم التنسيق البسيط\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // نص عريض\n        .replace(/\\n/g, '<br>') // أسطر جديدة\n        .replace(/📞|📧|📍|🕐|🏛️|📋/g, '<span class=\"text-blue-600\">$&</span>') // أيقونات ملونة\n        ;\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setIsOpen(true),\n                className: \"h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-6 z-50 \".concat(className),\n        style: {\n            zIndex: 9999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-80 shadow-2xl border-0 transition-all duration-300 \".concat(isMinimized ? 'h-14' : 'h-96', \" bg-white\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3 \".concat(isMinimized ? 'rounded-lg' : 'rounded-t-lg'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-white/20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"المساعد الذكي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: \"متصل الآن\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsMinimized(!isMinimized),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        title: isMinimized ? 'توسيع' : 'تصغير',\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsOpen(false),\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0 text-white hover:bg-white/20\",\n                                        title: \"إغلاق\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col h-80\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-3 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2 space-x-reverse max-w-[85%] \".concat(message.type === 'user' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'),\n                                                    children: message.type === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-2 \".concat(message.type === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: formatMessage(message.content)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 \".concat(message.type === 'user' ? 'text-blue-100' : 'text-gray-500'),\n                                                            children: formatTime(message.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            ref: inputRef,\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            disabled: isLoading,\n                                            className: \"flex-1 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: sendMessage,\n                                            disabled: !inputMessage.trim() || isLoading,\n                                            size: \"sm\",\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, this),\n                                companyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500\",\n                                    children: [\n                                        companyInfo.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:\".concat(companyInfo.phone),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"اتصال\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, this),\n                                        companyInfo.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:\".concat(companyInfo.email),\n                                            className: \"flex items-center space-x-1 space-x-reverse hover:text-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Mail_MessageCircle_Minimize2_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"إيميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\mohaminew\\\\src\\\\components\\\\ChatWidget.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWidget, \"myW+bh/X9/AkbTPghWDpQ0F98kg=\");\n_c = ChatWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});