'use client'

import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, Search, User } from 'lucide-react'

interface Client {
  id: number
  name: string
  phone: string
  email: string
  national_id: string
  address: string
}

interface ClientSelectProps {
  value: string
  onChange: (clientId: string, clientData: Client | null) => void
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
}

export function ClientSelect({ value, onChange, label = "الموكل", placeholder = "اختر الموكل", required = false, disabled = false }: ClientSelectProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const isMountedRef = useRef(true)

  const fetchClients = async () => {
    console.log('🔄 ClientSelect: بدء جلب العملاء...')
    if (!isMountedRef.current) return

    setIsLoading(true)
    try {
      console.log('📡 ClientSelect: إرسال طلب إلى /api/clients')
      const response = await fetch('/api/clients')
      console.log('📡 ClientSelect: استجابة API العملاء:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('📊 ClientSelect: نتيجة API العملاء:', result)

      if (!isMountedRef.current) return

      if (result.success) {
        console.log('✅ ClientSelect: نجح جلب العملاء، العدد:', result.data?.length || 0)
        console.log('👥 ClientSelect: قائمة العملاء:', result.data)
        // التحقق من وجود البيانات في المكان الصحيح
        const clientData = result.clients || result.data || []
        if (Array.isArray(clientData)) {
          setClients(clientData)
          console.log('✅ ClientSelect: تم تحديث قائمة العملاء بنجاح، العدد:', clientData.length)
        } else {
          console.error('❌ ClientSelect: البيانات ليست مصفوفة:', clientData)
          setClients([])
        }
      } else {
        console.error('❌ ClientSelect: فشل في جلب العملاء:', result.error)
        setClients([])
      }
    } catch (error) {
      console.error('💥 ClientSelect: خطأ في جلب العملاء:', error)
      if (isMountedRef.current) {
        setClients([])
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false)
        console.log('🏁 ClientSelect: انتهى جلب العملاء')
      }
    }
  }

  useEffect(() => {
    console.log('🔄 ClientSelect: useEffect - بدء تحميل العملاء...')
    fetchClients()

    return () => {
      isMountedRef.current = false
      console.log('🧹 ClientSelect: تنظيف المكون')
    }
  }, [])

  useEffect(() => {
    if (value && Array.isArray(clients) && clients.length > 0) {
      const client = clients.find(c => c && c.id && c.id.toString() === value)
      if (client) {
        setSelectedClient(client)
      } else if (!value) {
        setSelectedClient(null)
      }
    } else if (!value) {
      setSelectedClient(null)
    }
  }, [value, clients])

  const filteredClients = Array.isArray(clients) ? clients.filter(client => {
    if (!client || typeof client !== 'object') return false
    return (
      (client.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (client.phone || '').includes(searchTerm) ||
      (client.national_id || '').includes(searchTerm)
    )
  }) : []

  // لوق لحالة العملاء
  console.log('📊 ClientSelect: حالة العملاء الحالية:')
  console.log('   - إجمالي العملاء:', clients.length)
  console.log('   - العملاء المفلترة:', filteredClients.length)
  console.log('   - نص البحث:', searchTerm)
  console.log('   - العميل المختار:', selectedClient)
  console.log('   - القيمة الحالية:', value)
  console.log('   - حالة التحميل:', isLoading)
  console.log('   - النافذة مفتوحة:', isOpen)

  const handleSelect = (client: Client) => {
    setSelectedClient(client)
    onChange(client.id.toString(), client)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedClient(null)
    onChange('', null)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <div
          className={`w-full px-3 py-2 border border-gray-300 rounded-md flex items-center justify-between ${
            disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer bg-white'
          }`}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2 text-gray-400" />
            <span className={selectedClient ? 'text-gray-900' : 'text-gray-500'}>
              {selectedClient ? selectedClient.name : placeholder}
            </span>
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الموكلين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة الموكلين */}
            <div className="max-h-48 overflow-y-auto">
              {isLoading ? (
                <div className="p-3 text-center text-gray-500">جاري التحميل...</div>
              ) : filteredClients.length > 0 ? (
                <>
                  {selectedClient && (
                    <div
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b text-red-600"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredClients.map((client) => (
                    <div
                      key={client.id}
                      className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelect(client)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{client.name}</div>
                          <div className="text-sm text-gray-500">
                            {client.phone} • {client.national_id}
                          </div>
                        </div>
                        <User className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد موكلين'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل الموكل المختار */}
      {selectedClient && (
        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="text-sm text-blue-800">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>الهاتف:</strong> {selectedClient.phone}</div>
              <div><strong>الهوية:</strong> {selectedClient.national_id}</div>
              <div className="col-span-2"><strong>العنوان:</strong> {selectedClient.address}</div>
              {selectedClient.email && (
                <div className="col-span-2"><strong>البريد:</strong> {selectedClient.email}</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="client_id" />
    </div>
  )
}
