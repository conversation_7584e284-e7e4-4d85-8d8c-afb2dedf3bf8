// اختبار الردود المحسنة بعد الإصلاح
require('dotenv').config({ path: '.env.local' });

async function testFixedResponses() {
  console.log('🔄 اختبار الردود المحسنة بعد الإصلاح...\n');
  
  const apiKey = process.env.GROQ_API_KEY;
  
  if (!apiKey) {
    console.log('❌ GROQ_API_KEY غير متوفر');
    return;
  }
  
  // أسئلة للاختبار
  const testQuestions = [
    {
      type: 'محدد',
      question: 'ما هي حقوق العامل في قانون العمل اليمني؟',
      expected: 'يجب أن يجيب مباشرة عن حقوق العامل'
    },
    {
      type: 'محدد',
      question: 'كيف أطلق زوجتي حسب القانون اليمني؟',
      expected: 'يجب أن يجيب مباشرة عن إجراءات الطلاق'
    },
    {
      type: 'محدد',
      question: 'ما هي إجراءات رفع دعوى في المحكمة؟',
      expected: 'يجب أن يجيب مباشرة عن إجراءات المحكمة'
    },
    {
      type: 'عام',
      question: 'أحتاج استشارة قانونية',
      expected: 'يجب أن يطلب التفاصيل'
    },
    {
      type: 'عام',
      question: 'مساعدة',
      expected: 'يجب أن يطلب التفاصيل'
    }
  ];
  
  console.log(`📋 سيتم اختبار ${testQuestions.length} أسئلة مختلفة...\n`);
  
  for (let i = 0; i < testQuestions.length; i++) {
    const test = testQuestions[i];
    
    console.log(`${'='.repeat(60)}`);
    console.log(`🧪 اختبار ${i + 1}: ${test.type}`);
    console.log(`❓ السؤال: "${test.question}"`);
    console.log(`🎯 المتوقع: ${test.expected}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama-3.1-8b-instant',
          messages: [
            {
              role: 'system',
              content: `أنت محامي يمني خبير. اقرأ السؤال بعناية وحدد نوعه:

إذا كان السؤال يحتوي على كلمات محددة مثل:
- "ما هي" أو "كيف" أو "إجراءات" أو "حقوق" أو "قانون" أو "طلاق" أو "عمل" أو "عقد" أو "محكمة" أو "ميراث"
فهذا سؤال محدد - أجب مباشرة بالمعلومات القانونية في 3-4 نقاط.

إذا كان السؤال عاماً مثل:
- "أحتاج استشارة" أو "مساعدة" أو "سؤال" فقط
فاطلب التفاصيل.

أمثلة:
- "ما هي حقوق العامل؟" = سؤال محدد → أجب مباشرة
- "كيف أطلق زوجتي؟" = سؤال محدد → أجب مباشرة  
- "أحتاج استشارة" = سؤال عام → اطلب التفاصيل`
            },
            {
              role: 'user',
              content: `السؤال: "${test.question}"\n\nحلل السؤال وأجب بناءً على نوعه.`
            }
          ],
          max_tokens: 200,
          temperature: 0.7
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        const aiResponse = data.choices[0].message.content;
        
        console.log('✅ نجح الاختبار!');
        console.log('🤖 رد النموذج:');
        console.log(`"${aiResponse}"`);
        
        // تحليل الرد
        const asksForDetails = aiResponse.includes('توضيح') || aiResponse.includes('تفاصيل') || aiResponse.includes('محدد');
        const givesDirectAnswer = !asksForDetails && (
          aiResponse.includes('حقوق') || 
          aiResponse.includes('إجراءات') || 
          aiResponse.includes('طلاق') || 
          aiResponse.includes('قانون') ||
          aiResponse.includes('محكمة') ||
          aiResponse.includes('عمل')
        );
        
        console.log('\n📊 تحليل الرد:');
        console.log(`   • يطلب التفاصيل: ${asksForDetails ? '✅ نعم' : '❌ لا'}`);
        console.log(`   • يعطي إجابة مباشرة: ${givesDirectAnswer ? '✅ نعم' : '❌ لا'}`);
        
        // تقييم الرد
        let evaluation = '';
        if (test.type === 'عام') {
          if (asksForDetails) {
            evaluation = '✅ ممتاز - يطلب التفاصيل كما هو مطلوب';
          } else {
            evaluation = '❌ خطأ - يجب أن يطلب التفاصيل';
          }
        } else {
          if (givesDirectAnswer && !asksForDetails) {
            evaluation = '✅ ممتاز - يعطي إجابة مباشرة';
          } else if (asksForDetails) {
            evaluation = '❌ خطأ - لا يجب أن يطلب التفاصيل للأسئلة المحددة';
          } else {
            evaluation = '⚠️ غير واضح - الرد لا يحتوي على معلومات قانونية واضحة';
          }
        }
        
        console.log(`   • التقييم: ${evaluation}`);
        
      } else {
        console.log('❌ فشل الاختبار');
        console.log(`📄 رمز الخطأ: ${response.status}`);
      }
      
    } catch (error) {
      console.log('❌ خطأ في الشبكة:', error.message);
    }
    
    console.log('\n');
    
    // انتظار قصير بين الاختبارات
    if (i < testQuestions.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log(`${'='.repeat(60)}`);
  console.log('📋 ملخص الاختبار:');
  console.log('🎯 النموذج يجب أن:');
  console.log('   • يجيب مباشرة على الأسئلة المحددة');
  console.log('   • يطلب التفاصيل للأسئلة العامة فقط');
  console.log('   • يميز بين نوعي الأسئلة بدقة');
  console.log(`${'='.repeat(60)}`);
}

// تشغيل الاختبار
testFixedResponses().catch(console.error);
